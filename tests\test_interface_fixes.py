#!/usr/bin/env python3
"""
测试接口修复
验证所有接口使用是否正确
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_message_codec_interfaces():
    """测试MessageCodec接口"""
    print("测试MessageCodec接口...")
    
    try:
        from src.messaging.message_codec import MessageCodec
        
        # 测试编码
        test_data = {"test": "data", "number": 123}
        encoded = MessageCodec.encode(test_data)
        print(f"  编码成功: {len(encoded)} bytes")
        
        # 测试解码
        decoded = MessageCodec.decode(encoded)
        print(f"  解码成功: {decoded}")
        
        # 验证数据一致性
        if decoded == test_data:
            print("  数据一致性验证通过")
            return True
        else:
            print("  数据一致性验证失败")
            return False
            
    except Exception as e:
        print(f"  MessageCodec接口测试失败: {e}")
        return False

def test_trade_signal_creation():
    """测试TradeSignal创建"""
    print("测试TradeSignal创建...")
    
    try:
        from src.messaging.message_types import TradeSignal, SignalType, PositionSignalData
        
        # 创建PositionSignalData
        signal_data = PositionSignalData(
            symbol='EURUSD',
            volume=0.1,
            action='BUY',
            price=1.1234,
            sl=1.1200,
            tp=1.1300,
            magic=12345,
            account_id='TEST_ACCOUNT',
            copy_magic_number=12345
        )
        print("  PositionSignalData创建成功")
        
        # 创建TradeSignal
        signal = TradeSignal(
            type=SignalType.POSITION_OPEN,
            master_id='TEST_MASTER',
            ticket=1000001,
            data=signal_data,
            timestamp=**********.0
        )
        print("  TradeSignal创建成功")
        
        # 验证字段
        if signal.type == SignalType.POSITION_OPEN and signal.master_id == 'TEST_MASTER':
            print("  TradeSignal字段验证通过")
            return True
        else:
            print("  TradeSignal字段验证失败")
            return False
            
    except Exception as e:
        print(f"  TradeSignal创建测试失败: {e}")
        return False

def test_message_envelope_creation():
    """测试MessageEnvelope创建"""
    print("测试MessageEnvelope创建...")
    
    try:
        from src.messaging.message_types import MessageEnvelope, TradeSignal, SignalType, PositionSignalData
        
        # 创建TradeSignal
        signal_data = PositionSignalData(
            symbol='EURUSD',
            volume=0.1,
            action='BUY',
            price=1.1234,
            sl=1.1200,
            tp=1.1300,
            magic=12345,
            account_id='TEST_ACCOUNT',
            copy_magic_number=12345
        )
        
        signal = TradeSignal(
            type=SignalType.POSITION_OPEN,
            master_id='TEST_MASTER',
            ticket=1000001,
            data=signal_data,
            timestamp=**********.0
        )
        
        # 创建MessageEnvelope
        envelope = MessageEnvelope.create_trade_signal(
            signal=signal,
            subject='TEST.TRADE.SIGNAL'
        )
        print("  MessageEnvelope创建成功")
        
        # 验证字段
        if envelope.subject == 'TEST.TRADE.SIGNAL' and envelope.payload is not None:
            print("  MessageEnvelope字段验证通过")
            return True
        else:
            print("  MessageEnvelope字段验证失败")
            return False
            
    except Exception as e:
        print(f"  MessageEnvelope创建测试失败: {e}")
        return False

def test_mt5_request_handler_imports():
    """测试MT5RequestHandler导入"""
    print("测试MT5RequestHandler导入...")
    
    try:
        from src.core.mt5_request_handler import MT5RequestHandler
        print("  MT5RequestHandler导入成功")
        
        # 检查是否使用了正确的MessageCodec接口
        import inspect
        source = inspect.getsource(MT5RequestHandler)
        
        if 'MessageCodec.encode' in source and 'MessageCodec.decode' in source:
            print("  MT5RequestHandler使用正确的MessageCodec接口")
            return True
        elif 'encode_message' in source or 'decode_message' in source:
            print("  MT5RequestHandler仍在使用旧的接口")
            return False
        else:
            print("  MT5RequestHandler接口使用检查通过")
            return True
            
    except Exception as e:
        print(f"  MT5RequestHandler导入测试失败: {e}")
        return False

def test_jetstream_client_basic():
    """测试JetStreamClient基本功能"""
    print("测试JetStreamClient基本功能...")
    
    try:
        from src.messaging.jetstream_client import JetStreamClient
        
        # 创建配置
        config = {
            'servers': ['nats://localhost:4222'],
            'stream_name': 'TEST_STREAM',
            'subjects': ['TEST.>']
        }
        
        # 创建客户端
        client = JetStreamClient(config)
        print("  JetStreamClient创建成功")
        
        return True
        
    except Exception as e:
        print(f"  JetStreamClient测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始接口修复验证测试...")
    print("=" * 50)
    
    tests = [
        test_message_codec_interfaces,
        test_trade_signal_creation,
        test_message_envelope_creation,
        test_mt5_request_handler_imports,
        test_jetstream_client_basic,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            print(f"  结果: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"  测试异常: {e}")
            results.append(False)
        print()
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print("=" * 50)
    print("接口修复验证结果:")
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("所有接口修复验证通过！")
        return 0
    elif success_rate >= 80:
        print("大部分接口修复验证通过，系统基本正常。")
        return 0
    else:
        print("接口修复验证失败较多，需要进一步修复。")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
