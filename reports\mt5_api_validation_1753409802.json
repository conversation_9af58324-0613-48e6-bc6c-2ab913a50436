{"validation_timestamp": 1753409779.2478323, "test_results": {"basic_semaphore": {"test_name": "semaphore_control", "max_allowed_concurrent": 4, "total_attempts": 20, "successful_attempts": 20, "failed_attempts": 0, "avg_connection_time": 0.9236647129058838, "max_observed_concurrent": 4, "semaphore_violations": 0, "api_limit_respected": true, "error_patterns": {}}, "api_overload_protection": {"test_name": "api_overload_protection", "max_allowed_concurrent": 8, "total_attempts": 100, "successful_attempts": 100, "failed_attempts": 0, "avg_connection_time": 0.7306104803085327, "max_observed_concurrent": 4, "semaphore_violations": 0, "api_limit_respected": true, "error_patterns": {}}, "concurrency_levels": [{"test_name": "semaphore_control", "max_allowed_concurrent": 1, "total_attempts": 3, "successful_attempts": 3, "failed_attempts": 0, "avg_connection_time": 0.6137524445851644, "max_observed_concurrent": 4, "semaphore_violations": 0, "api_limit_respected": false, "error_patterns": {}}, {"test_name": "semaphore_control", "max_allowed_concurrent": 2, "total_attempts": 6, "successful_attempts": 6, "failed_attempts": 0, "avg_connection_time": 0.6167314052581787, "max_observed_concurrent": 4, "semaphore_violations": 0, "api_limit_respected": false, "error_patterns": {}}, {"test_name": "semaphore_control", "max_allowed_concurrent": 4, "total_attempts": 12, "successful_attempts": 12, "failed_attempts": 0, "avg_connection_time": 0.6114646593729655, "max_observed_concurrent": 4, "semaphore_violations": 0, "api_limit_respected": true, "error_patterns": {}}, {"test_name": "semaphore_control", "max_allowed_concurrent": 8, "total_attempts": 24, "successful_attempts": 24, "failed_attempts": 0, "avg_connection_time": 0.6203903357187907, "max_observed_concurrent": 8, "semaphore_violations": 0, "api_limit_respected": true, "error_patterns": {}}, {"test_name": "semaphore_control", "max_allowed_concurrent": 16, "total_attempts": 48, "successful_attempts": 48, "failed_attempts": 0, "avg_connection_time": 0.6210013429323832, "max_observed_concurrent": 16, "semaphore_violations": 0, "api_limit_respected": true, "error_patterns": {}}, {"test_name": "semaphore_control", "max_allowed_concurrent": 32, "total_attempts": 96, "successful_attempts": 96, "failed_attempts": 0, "avg_connection_time": 0.6304376944899559, "max_observed_concurrent": 32, "semaphore_violations": 0, "api_limit_respected": true, "error_patterns": {}}]}, "overall_assessment": {"semaphore_effective": true, "overload_protection_works": true, "scalability_rating": "excellent", "performance_metrics": {"avg_connection_time": 0.6189629803929064, "connection_time_variance": 0.0067263029634634115}, "recommendations": []}}