#!/usr/bin/env python3
"""
基础设施管理脚本
用于启动、停止和管理NATS、Redis、Prometheus等服务
"""
import subprocess
import time
import sys
import os
import json
import asyncio
from pathlib import Path
from typing import Dict, List

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))


class InfrastructureManager:
    """基础设施管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.docker_compose_file = self.project_root / "docker-compose.test.yml"
        self.services = [
            'nats',
            'redis', 
            'redis-sentinel',
            'prometheus',
            'pushgateway'
        ]
        self.optional_services = [
            'grafana',
            'nats-surveyor',
            'redis-commander'
        ]
    
    def start_services(self, include_optional: bool = False):
        """启动基础设施服务"""
        print("🚀 启动基础设施服务...")
        
        if not self.docker_compose_file.exists():
            print(f"❌ Docker Compose文件不存在: {self.docker_compose_file}")
            return False
        
        try:
            # 启动核心服务
            services_to_start = self.services.copy()
            if include_optional:
                services_to_start.extend(self.optional_services)
            
            print(f"启动服务: {', '.join(services_to_start)}")
            
            cmd = [
                'docker-compose',
                '-f', str(self.docker_compose_file),
                'up', '-d'
            ] + services_to_start
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 服务启动成功")
                
                # 等待服务就绪
                print("⏳ 等待服务就绪...")
                self._wait_for_services()
                
                # 显示服务状态
                self.show_status()
                return True
            else:
                print(f"❌ 服务启动失败: {result.stderr}")
                return False
                
        except FileNotFoundError:
            print("❌ Docker Compose未安装或不在PATH中")
            return False
        except Exception as e:
            print(f"❌ 启动服务时发生错误: {e}")
            return False
    
    def stop_services(self):
        """停止基础设施服务"""
        print("🛑 停止基础设施服务...")
        
        try:
            cmd = [
                'docker-compose',
                '-f', str(self.docker_compose_file),
                'down'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 服务停止成功")
                return True
            else:
                print(f"❌ 服务停止失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 停止服务时发生错误: {e}")
            return False
    
    def restart_services(self, include_optional: bool = False):
        """重启基础设施服务"""
        print("🔄 重启基础设施服务...")
        
        if self.stop_services():
            time.sleep(2)  # 等待完全停止
            return self.start_services(include_optional)
        return False
    
    def show_status(self):
        """显示服务状态"""
        print("\n📊 服务状态:")
        print("-" * 60)
        
        try:
            cmd = [
                'docker-compose',
                '-f', str(self.docker_compose_file),
                'ps'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(result.stdout)
            else:
                print(f"❌ 获取状态失败: {result.stderr}")
                
        except Exception as e:
            print(f"❌ 获取状态时发生错误: {e}")
    
    def show_logs(self, service: str = None, follow: bool = False):
        """显示服务日志"""
        print(f"📋 显示{'所有服务' if not service else service}日志...")
        
        try:
            cmd = [
                'docker-compose',
                '-f', str(self.docker_compose_file),
                'logs'
            ]
            
            if follow:
                cmd.append('-f')
            
            if service:
                cmd.append(service)
            
            if follow:
                # 实时跟踪日志
                subprocess.run(cmd)
            else:
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    print(result.stdout)
                else:
                    print(f"❌ 获取日志失败: {result.stderr}")
                    
        except Exception as e:
            print(f"❌ 获取日志时发生错误: {e}")
    
    def _wait_for_services(self, timeout: int = 60):
        """等待服务就绪"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查服务健康状态
                cmd = [
                    'docker-compose',
                    '-f', str(self.docker_compose_file),
                    'ps', '--format', 'json'
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    # 解析JSON输出
                    services_status = []
                    for line in result.stdout.strip().split('\n'):
                        if line.strip():
                            try:
                                service_info = json.loads(line)
                                services_status.append(service_info)
                            except json.JSONDecodeError:
                                continue
                    
                    # 检查所有核心服务是否运行
                    running_services = [
                        s for s in services_status 
                        if s.get('State') == 'running' and any(
                            svc in s.get('Service', '') for svc in self.services
                        )
                    ]
                    
                    if len(running_services) >= len(self.services):
                        print("✅ 所有核心服务已就绪")
                        return True
                
                print("⏳ 等待服务启动...")
                time.sleep(3)
                
            except Exception as e:
                print(f"⚠️ 检查服务状态时出错: {e}")
                time.sleep(3)
        
        print("⚠️ 等待服务就绪超时")
        return False
    
    def run_tests(self):
        """运行基础设施测试"""
        print("🧪 运行基础设施测试...")
        
        try:
            # 运行基础设施测试
            test_script = self.project_root / "tests" / "test_infrastructure_services.py"
            
            if test_script.exists():
                cmd = [sys.executable, str(test_script)]
                result = subprocess.run(cmd, cwd=self.project_root)
                return result.returncode == 0
            else:
                print(f"❌ 测试脚本不存在: {test_script}")
                return False
                
        except Exception as e:
            print(f"❌ 运行测试时发生错误: {e}")
            return False
    
    def show_service_urls(self):
        """显示服务访问URL"""
        print("\n🌐 服务访问地址:")
        print("-" * 60)
        
        urls = {
            'NATS监控': 'http://localhost:8222',
            'Redis': 'redis://localhost:6379',
            'Redis Sentinel': 'redis://localhost:26379',
            'Prometheus': 'http://localhost:9090',
            'Pushgateway': 'http://localhost:9091',
            'Grafana': 'http://localhost:3000 (admin/admin123)',
            'NATS Surveyor': 'http://localhost:7777',
            'Redis Commander': 'http://localhost:8081'
        }
        
        for service, url in urls.items():
            print(f"   {service}: {url}")
        
        print("\n💡 提示:")
        print("   - 使用 'docker-compose -f docker-compose.test.yml logs <service>' 查看特定服务日志")
        print("   - 使用 'docker-compose -f docker-compose.test.yml exec <service> sh' 进入容器")
    
    def cleanup(self):
        """清理资源"""
        print("🧹 清理基础设施资源...")
        
        try:
            # 停止并删除容器、网络、卷
            cmd = [
                'docker-compose',
                '-f', str(self.docker_compose_file),
                'down', '-v', '--remove-orphans'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 资源清理成功")
                return True
            else:
                print(f"❌ 资源清理失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 清理资源时发生错误: {e}")
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='基础设施管理工具')
    parser.add_argument('action', choices=[
        'start', 'stop', 'restart', 'status', 'logs', 'test', 'urls', 'cleanup'
    ], help='要执行的操作')
    parser.add_argument('--service', help='特定服务名称 (用于logs操作)')
    parser.add_argument('--follow', '-f', action='store_true', help='跟踪日志输出')
    parser.add_argument('--optional', action='store_true', help='包含可选服务')
    
    args = parser.parse_args()
    
    manager = InfrastructureManager()
    
    if args.action == 'start':
        success = manager.start_services(args.optional)
        if success:
            manager.show_service_urls()
    elif args.action == 'stop':
        success = manager.stop_services()
    elif args.action == 'restart':
        success = manager.restart_services(args.optional)
        if success:
            manager.show_service_urls()
    elif args.action == 'status':
        manager.show_status()
        success = True
    elif args.action == 'logs':
        manager.show_logs(args.service, args.follow)
        success = True
    elif args.action == 'test':
        success = manager.run_tests()
    elif args.action == 'urls':
        manager.show_service_urls()
        success = True
    elif args.action == 'cleanup':
        success = manager.cleanup()
    else:
        print(f"❌ 未知操作: {args.action}")
        success = False
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
