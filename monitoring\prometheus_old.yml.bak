global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Pushgateway监控
  - job_name: 'pushgateway'
    static_configs:
      - targets: ['pushgateway:9091']

  # NATS监控
  - job_name: 'nats'
    static_configs:
      - targets: ['nats:8222']
    metrics_path: '/varz'
    scrape_interval: 10s

  # Redis监控 (如果有Redis exporter)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 10s

  # MT5交易系统监控 (从Pushgateway获取)
  - job_name: 'mt5-trading-system'
    honor_labels: true
    static_configs:
      - targets: ['pushgateway:9091']
    metrics_path: '/metrics'
    scrape_interval: 5s

  # 系统监控 (如果有node_exporter)
  - job_name: 'node'
    static_configs:
      - targets: ['host.docker.internal:9100']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
