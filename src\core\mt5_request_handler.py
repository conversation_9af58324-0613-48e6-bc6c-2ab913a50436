#!/usr/bin/env python3
# 🚨 AUTO-MIGRATED: Old messaging components replaced with QueueManager
# See MIGRATION_GUIDE.md for details

"""
MT5请求处理器 - 分布式系统核心组件
处理来自子进程的MT5.REQUEST.*请求，将它们路由到MT5ProcessManager
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional
from ..messaging.queue_manager import QueueManager
from ..utils.logger import get_logger
from .process_manager import ProcessManager

logger = get_logger(__name__)


class MT5RequestHandler:
    """
    MT5请求处理器 - 分布式架构的关键组件
    
    职责：
    1. 订阅 MT5.REQUEST.* 主题
    2. 解析并处理来自子进程的请求
    3. 将请求路由到 ProcessManager
    4. 返回响应给请求方
    """
    
    def __init__(self, queue_manager: QueueManager, mt5_process_manager: ProcessManager):
        self.queue_manager = queue_manager
        self.mt5_process_manager = mt5_process_manager
        self.running = False
        self._subscription = None  # NATS订阅实例
        
        # 统计信息
        self.stats = {
            'requests_processed': 0,
            'requests_failed': 0,
            'get_positions_count': 0,
            'get_account_info_count': 0,
            'send_order_count': 0,
            'unknown_commands': 0
        }
        
        logger.info("✅ MT5RequestHandler初始化完成")
    
    async def start(self) -> bool:
        """启动请求处理器 - 增强诊断版本"""
        try:
            logger.info("🔍 开始启动MT5RequestHandler...")

            if not self.queue_manager.is_connected():
                logger.error("JetStream客户端未连接，无法启动请求处理器")
                return False

            # 订阅所有MT5请求 - 使用基础NATS而不是JetStream
            # 因为MT5.REQUEST.*是request/response模式，不需要持久化
            if not self.queue_manager.nc or not self.queue_manager.nc.is_connected:
                logger.error("底层NATS连接不可用")
                return False

            logger.info(f"📡 NATS连接状态: {self.queue_manager.nc.is_connected}")
            logger.info(f"📡 准备订阅主题: MT5.REQUEST.*")

            # 使用底层NATS客户端直接订阅request主题
            subscription = await self.queue_manager.nc.subscribe(
                subject="MT5.REQUEST.*",
                cb=self._handle_mt5_request_nats
            )

            if subscription:
                self._subscription = subscription
                logger.info(f"✅ 订阅成功! 订阅对象: {subscription}")
                logger.info(f"✅ 订阅主题: MT5.REQUEST.*")
                logger.info(f"✅ 回调函数: {self._handle_mt5_request_nats}")
                success = True
            else:
                logger.error("❌ 订阅返回None")
                success = False

            if success:
                self.running = True
                logger.info("🚀 MT5RequestHandler启动成功，开始处理分布式请求")

                # 测试订阅是否工作 - 发送一个测试消息给自己
                await self._test_subscription()

                return True
            else:
                logger.error("❌ MT5RequestHandler启动失败：订阅失败")
                return False

        except Exception as e:
            logger.error(f"启动MT5RequestHandler失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    async def _test_subscription(self):
        """测试订阅是否正常工作"""
        try:
            logger.info("🧪 开始测试订阅功能...")

            # 创建一个测试请求
            test_data = {
                'account_id': 'TEST',
                'command': 'test_connection',
                'timestamp': time.time()
            }

            # 发送测试请求
            from ..messaging.message_types import MessageEnvelope
            payload = json.dumps(test_data)

            # 使用request方法发送测试消息
            try:
                response = await asyncio.wait_for(
                    self.queue_manager.nc.request("MT5.REQUEST.TEST", payload, timeout=2.0),
                    timeout=3.0
                )
                logger.info("✅ 订阅测试成功 - 能够接收和响应请求")
            except asyncio.TimeoutError:
                logger.warning("⚠️ 订阅测试超时 - 可能存在问题")
            except Exception as e:
                logger.warning(f"⚠️ 订阅测试失败: {e}")

        except Exception as e:
            logger.error(f"测试订阅时发生异常: {e}")

    async def stop(self):
        """停止请求处理器"""
        try:
            self.running = False
            # 取消NATS订阅
            if self._subscription:
                await self._subscription.unsubscribe()
                self._subscription = None
            logger.info("🛑 MT5RequestHandler已停止")
        except Exception as e:
            logger.error(f"停止MT5RequestHandler失败: {e}")
    
    async def _handle_mt5_request_nats(self, msg):
        """处理来自NATS的MT5请求消息 - 增强诊断版本"""
        try:
            logger.info(f"🎯 收到NATS请求! 主题: {msg.subject}, Reply: {msg.reply}")
            logger.info(f"📦 消息大小: {len(msg.data)} bytes")

            # 解析NATS消息
            from ..messaging.message_types import MessageEnvelope
            request_data = json.loads(msg.data.decode('utf-8'))

            logger.info(f"📋 解析的请求数据: {request_data}")

            # 创建模拟JetStream消息对象以兼容现有处理逻辑
            class NATSMessageWrapper:
                def __init__(self, data, reply_subject):
                    self.payload = data
                    self.reply = reply_subject

            wrapped_message = NATSMessageWrapper(request_data, msg.reply)

            # 使用现有的处理逻辑
            logger.info(f"🔄 开始处理请求...")
            await self._handle_mt5_request(wrapped_message)
            logger.info(f"✅ 请求处理完成")

        except Exception as e:
            logger.error(f"处理NATS MT5请求失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

            # 尝试发送错误响应
            try:
                if msg.reply:
                    error_response = {
                        'status': 'error',
                        'error': str(e),
                        'timestamp': time.time()
                    }
                    from ..messaging.message_types import MessageEnvelope
                    payload = json.dumps(error_response)
                    await self.queue_manager.nc.publish(msg.reply, payload)
                    logger.info("📤 错误响应已发送")
            except Exception as reply_error:
                logger.error(f"发送错误响应失败: {reply_error}")
    
    async def _handle_mt5_request(self, message):
        """处理MT5请求消息 - 增强诊断版本"""
        try:
            start_time = time.time()

            # 解析消息
            request_data = message.payload
            account_id = request_data.get('account_id')
            command = request_data.get('command')

            logger.info(f"📥 处理MT5请求: {command} from {account_id}")
            logger.info(f"📋 完整请求数据: {request_data}")

            if not account_id or not command:
                logger.error(f"无效的请求格式: {request_data}")
                await self._send_error_response(message, "无效的请求格式")
                return

            # 特殊处理测试请求
            if command == 'test_connection':
                logger.info("🧪 处理测试连接请求")
                test_response = {
                    'status': 'success',
                    'message': 'MT5RequestHandler is working',
                    'timestamp': time.time()
                }
                await self._send_success_response(message, test_response)
                return

            # 根据命令类型处理
            try:
                logger.info(f"🔄 开始处理命令: {command}")

                if command == 'get_positions':
                    response_data = await self._handle_get_positions(account_id, request_data)
                    self.stats['get_positions_count'] += 1

                elif command == 'get_account_info':
                    response_data = await self._handle_get_account_info(account_id, request_data)
                    self.stats['get_account_info_count'] += 1

                elif command == 'send_order':
                    response_data = await self._handle_send_order(account_id, request_data)
                    self.stats['send_order_count'] += 1

                else:
                    logger.warning(f"未知命令类型: {command}")
                    self.stats['unknown_commands'] += 1
                    await self._send_error_response(message, f"未知命令类型: {command}")
                    return

                logger.info(f"📤 准备发送响应: {response_data.get('status', 'unknown')}")

                # 发送成功响应
                await self._send_success_response(message, response_data)
                self.stats['requests_processed'] += 1

                process_time = (time.time() - start_time) * 1000
                logger.info(f"✅ 请求处理完成: {command} from {account_id} - 耗时: {process_time:.1f}ms")

            except Exception as e:
                logger.error(f"处理{command}命令失败: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                await self._send_error_response(message, str(e))
                self.stats['requests_failed'] += 1

        except Exception as e:
            logger.error(f"处理MT5请求消息失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            self.stats['requests_failed'] += 1
    
    async def _handle_get_positions(self, account_id: str, request_data: Dict) -> Dict[str, Any]:
        """处理获取持仓请求 - 增强调试版本"""
        try:
            logger.info(f"🔍 开始处理持仓请求: {account_id}")
            
            # 首先检查账户是否存在和连接状态
            account_available = self.mt5_process_manager.is_account_available(account_id)
            logger.info(f"📊 账户可用性检查: {account_id} -> {account_available}")

            # 获取详细的进程状态信息用于诊断
            process_status = self.mt5_process_manager.process_status.get(account_id)
            logger.info(f"📋 进程状态详情: {account_id} -> {process_status}")

            # 获取所有进程状态用于诊断
            all_processes = list(self.mt5_process_manager.process_status.keys())
            logger.info(f"📋 所有已知进程: {all_processes}")

            if not account_available:
                logger.warning(f"⚠️ 账户进程不可用: {account_id}")

                # 如果进程状态为None，可能是时序问题，尝试等待一下
                if process_status is None:
                    logger.info(f"🔄 进程状态为空，等待进程初始化: {account_id}")
                    import asyncio
                    await asyncio.sleep(2)  # 等待2秒

                    # 重新检查
                    account_available = self.mt5_process_manager.is_account_available(account_id)
                    process_status = self.mt5_process_manager.process_status.get(account_id)
                    logger.info(f"🔄 重新检查结果: {account_id} -> available: {account_available}, status: {process_status}")

                    if not account_available:
                        return {
                            'status': 'error',
                            'error': f'Account {account_id} not found after waiting'
                        }
                else:
                    return {
                        'status': 'error',
                        'error': f'Account {account_id} not available'
                    }

            # 检查账户是否已连接到MT5服务器（宽容检查）
            try:
                connected = self.mt5_process_manager.is_account_connected(account_id)
                logger.info(f"📊 账户连接状态检查: {account_id} -> {connected}")
                
                if not connected:
                    logger.info(f"🔗 账户连接状态未确认: {account_id} - 继续尝试获取数据（可能刚启动）")
                    # 不阻塞操作，终端可能需要时间建立连接
                else:
                    logger.info(f"✅ 账户连接状态良好: {account_id}")
            except Exception as conn_error:
                logger.warning(f"⚠️ 连接状态检查失败: {account_id}, 错误: {conn_error} - 继续尝试获取数据")

            # 通过MT5ProcessManager获取持仓
            logger.info(f"📤 发送持仓请求到MT5进程管理器: {account_id}")
            response = await self.mt5_process_manager.send_command_async(
                account_id, 'get_positions'
            )
            
            logger.info(f"📥 收到MT5进程管理器响应: {account_id} -> {response.status}")
            if hasattr(response, 'data') and response.data:
                logger.debug(f"📥 响应数据详情: {account_id} -> type={type(response.data)}, content={response.data}")

            if response.status == 'success':
                positions_data = response.data or {}
                # 确保返回正确的数据格式
                if isinstance(positions_data, dict) and 'positions' in positions_data:
                    actual_positions = positions_data['positions']
                elif isinstance(positions_data, list):
                    actual_positions = positions_data
                else:
                    actual_positions = []
                
                logger.info(f"✅ 持仓数据获取成功: {account_id}, 持仓数量: {len(actual_positions)}")
                return {
                    'status': 'success',
                    'data': {
                        'positions': actual_positions,
                        'account_id': account_id,
                        'timestamp': request_data.get('timestamp')
                    }
                }
            else:
                logger.warning(f"❌ 获取持仓失败: {account_id}, 错误: {response.error_message}")
                return {
                    'status': 'error',
                    'error': response.error_message or '获取持仓失败'
                }

        except Exception as e:
            logger.error(f"💥 获取持仓异常: {account_id}, 错误: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _handle_get_account_info(self, account_id: str, request_data: Dict) -> Dict[str, Any]:
        """处理获取账户信息请求"""
        try:
            # 首先检查账户是否存在和连接状态
            if not self.mt5_process_manager.is_account_available(account_id):
                logger.debug(f"账户进程不可用: {account_id}")
                return {
                    'status': 'error',
                    'error': f'Account {account_id} not found'
                }

            # 检查账户是否已连接到MT5服务器
            if not self.mt5_process_manager.is_account_connected(account_id):
                logger.debug(f"账户未连接到MT5服务器: {account_id}")
                return {
                    'status': 'error',
                    'error': f'Account {account_id} not connected to MT5 server'
                }

            # 通过MT5ProcessManager获取账户信息
            response = await self.mt5_process_manager.send_command_async(
                account_id, 'get_account_info'
            )

            if response.status == 'success':
                return {
                    'status': 'success',
                    'data': response.data
                }
            else:
                logger.debug(f"获取账户信息失败: {account_id}, 错误: {response.error_message}")
                return {
                    'status': 'error',
                    'error': response.error_message or '获取账户信息失败'
                }

        except Exception as e:
            logger.error(f"获取账户信息异常: {account_id}, 错误: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _handle_send_order(self, account_id: str, request_data: Dict) -> Dict[str, Any]:
        """处理发送订单请求"""
        try:
            # 提取订单参数
            order_request = request_data.get('request', {})
            
            if not order_request:
                return {
                    'status': 'error',
                    'error': '缺少订单请求参数'
                }
            
            # 通过MT5ProcessManager发送订单
            response = await self.mt5_process_manager.send_command_async(
                account_id, 'send_order', {'request': order_request}
            )
            
            if response.status == 'success':
                return {
                    'status': 'success',
                    'data': response.data
                }
            else:
                logger.error(f"发送订单失败: {response.error_message}")
                return {
                    'status': 'error',
                    'error': response.error_message or '发送订单失败'
                }
                
        except Exception as e:
            logger.error(f"发送订单异常: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _send_success_response(self, original_message, response_data: Dict[str, Any]):
        """发送成功响应"""
        try:
            if hasattr(original_message, 'reply'):
                # 使用NATS的reply功能发送响应
                from ..messaging.message_types import MessageEnvelope
                # json.dumps返回字符串，需要编码为bytes
                payload = json.dumps(response_data)
                await self.queue_manager.nc.publish(
                    original_message.reply,
                    payload
                )
                logger.debug(f"✅ 成功响应已发送: {response_data.get('status', 'unknown')}")
            else:
                logger.warning("消息没有reply字段，无法发送响应")

        except Exception as e:
            logger.error(f"发送成功响应失败: {e}")

    async def _send_error_response(self, original_message, error_message: str):
        """发送错误响应"""
        try:
            response_data = {
                'status': 'error',
                'error': error_message,
                'timestamp': time.time()
            }

            if hasattr(original_message, 'reply'):
                from ..messaging.message_types import MessageEnvelope
                # json.dumps返回字符串，需要编码为bytes
                payload = json.dumps(response_data)
                await self.queue_manager.nc.publish(
                    original_message.reply,
                    payload
                )
                logger.debug(f"✅ 错误响应已发送: {error_message}")
            else:
                logger.warning("消息没有reply字段，无法发送错误响应")

        except Exception as e:
            logger.error(f"发送错误响应失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'running': self.running,
            'uptime_seconds': time.time() if self.running else 0
        }