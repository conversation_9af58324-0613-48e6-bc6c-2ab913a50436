#!/usr/bin/env python3
"""
无锁分片优先级队列 - 统一优先级队列实现 (SSOT)
使用lock-free数据结构和分片技术提升并发性能

特性:
1. 分片设计减少锁竞争
2. 环形缓冲区提供无锁快速入队
3. 批量处理提高吞吐量
4. 负载均衡和动态分片
5. 统一优先级分析器
"""

import asyncio
import time
import hashlib
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from enum import IntEnum, Enum
from collections import deque
import heapq
from concurrent.futures import ThreadPoolExecutor
import threading

from src.utils.logger import get_logger
from src.utils.metrics import get_metrics_collector

logger = get_logger(__name__)
metrics = get_metrics_collector()


class MessagePriority(Enum):
    """统一消息优先级 - 五个等级 - 使用HTML仪表盘标准名称"""
    SYSTEM_CRITICAL = 0    # 系统级风险 - 保证金不足、紧急平仓、风控强制全平
    RISK_COMMAND = 1       # 订单级风控 - 止损/止盈执行、订单修改、部分平仓
    SIGNAL_EXECUTION = 2   # 策略信号执行 - 开仓信号、策略主动平仓、反向跟单
    REALTIME_QUERY = 3     # 实时数据查询 - 最新报价、账户信息、持仓查询
    BACKGROUND_TASK = 4    # 后台任务 - 历史数据、日志记录、UI更新、统计分析


@dataclass
class ShardedMessage:
    """分片消息"""
    priority: MessagePriority
    timestamp: float
    data: Dict[str, Any]
    message_id: str
    shard_id: int
    retry_count: int = 0
    max_retries: int = 3
    callback: Optional[Callable] = None
    
    def __lt__(self, other):
        """堆排序比较"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.timestamp < other.timestamp


@dataclass
class PriorityMessage:
    """优先级消息 - 保持向后兼容"""
    priority: MessagePriority
    timestamp: float
    message_id: str
    data: Any
    callback: Optional[Callable] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __lt__(self, other):
        """比较函数，用于堆排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.timestamp < other.timestamp


class RingBuffer:
    """无锁环形缓冲区"""
    
    def __init__(self, size: int = 1024):
        self.size = size
        self.buffer = [None] * size
        self.write_pos = 0
        self.read_pos = 0
        self._lock = threading.RLock()  # 最小化锁使用
    
    def put(self, item) -> bool:
        """非阻塞写入"""
        with self._lock:
            next_write = (self.write_pos + 1) % self.size
            if next_write == self.read_pos:
                return False  # 缓冲区满
            
            self.buffer[self.write_pos] = item
            self.write_pos = next_write
            return True
    
    def get(self) -> Optional[Any]:
        """非阻塞读取"""
        with self._lock:
            if self.read_pos == self.write_pos:
                return None  # 缓冲区空
            
            item = self.buffer[self.read_pos]
            self.buffer[self.read_pos] = None  # 清理引用
            self.read_pos = (self.read_pos + 1) % self.size
            return item
    
    def size_used(self) -> int:
        """已使用大小"""
        with self._lock:
            if self.write_pos >= self.read_pos:
                return self.write_pos - self.read_pos
            return self.size - (self.read_pos - self.write_pos)
    
    def is_empty(self) -> bool:
        """是否为空"""
        return self.read_pos == self.write_pos
    
    def is_full(self) -> bool:
        """是否已满"""
        next_write = (self.write_pos + 1) % self.size
        return next_write == self.read_pos


class LockFreePriorityQueueShard:
    """无锁优先级队列分片"""
    
    def __init__(self, shard_id: int, ring_buffer_size: int = 2048):
        self.shard_id = shard_id
        self.ring_buffer = RingBuffer(ring_buffer_size)
        
        # 优先级分层缓冲区
        self.priority_buffers: Dict[MessagePriority, deque] = {
            priority: deque() for priority in MessagePriority
        }
        
        # 最小堆存储(用于快速优先级排序)
        self.priority_heap: List[ShardedMessage] = []
        self._heap_lock = threading.RLock()  # 最小化锁
        
        # 批量处理缓冲区
        self.batch_buffer: List[ShardedMessage] = []
        self.batch_size = 50
        self.last_batch_time = time.time()
        self.batch_timeout = 0.001  # 1ms批量超时
        
        # 统计信息
        self.stats = {
            'enqueued': 0,
            'dequeued': 0,
            'batch_processed': 0,
            'ring_buffer_full': 0,
            'heap_operations': 0
        }
    
    def enqueue_fast(self, message: ShardedMessage) -> bool:
        """快速入队（优先使用环形缓冲区）"""
        # 1. 尝试环形缓冲区（最快路径）
        if self.ring_buffer.put(message):
            self.stats['enqueued'] += 1
            return True
        
        # 2. 环形缓冲区满，使用优先级缓冲区
        priority_buffer = self.priority_buffers[message.priority]
        priority_buffer.append(message)
        self.stats['enqueued'] += 1
        self.stats['ring_buffer_full'] += 1
        
        return True
    
    def dequeue_batch(self, max_batch_size: int = 50) -> List[ShardedMessage]:
        """批量出队（提高吞吐量）"""
        batch = []
        current_time = time.time()
        
        # 1. 从环形缓冲区批量读取
        while len(batch) < max_batch_size:
            message = self.ring_buffer.get()
            if message is None:
                break
            batch.append(message)
        
        # 2. 如果环形缓冲区空，从优先级缓冲区获取
        if not batch:
            # 按优先级顺序处理
            for priority in MessagePriority:
                priority_buffer = self.priority_buffers[priority]
                while priority_buffer and len(batch) < max_batch_size:
                    batch.append(priority_buffer.popleft())
        
        # 3. 如果还需要更多消息，从堆中获取
        if len(batch) < max_batch_size // 2:
            with self._heap_lock:
                while self.priority_heap and len(batch) < max_batch_size:
                    batch.append(heapq.heappop(self.priority_heap))
                    self.stats['heap_operations'] += 1
        
        # 4. 按优先级排序批量消息
        if batch:
            batch.sort(key=lambda m: (m.priority.value, m.timestamp))
            self.stats['dequeued'] += len(batch)
            self.stats['batch_processed'] += 1
        
        return batch
    
    def promote_to_heap(self):
        """将优先级缓冲区的消息提升到堆"""
        promoted_count = 0
        
        for priority in MessagePriority:
            priority_buffer = self.priority_buffers[priority]
            
            # 批量移动到堆中
            while priority_buffer:
                message = priority_buffer.popleft()
                with self._heap_lock:
                    heapq.heappush(self.priority_heap, message)
                promoted_count += 1
                
                # 限制一次提升的数量
                if promoted_count >= 100:
                    break
            
            if promoted_count >= 100:
                break
        
        self.stats['heap_operations'] += promoted_count
        return promoted_count
    
    def get_load_factor(self) -> float:
        """获取负载因子"""
        ring_usage = self.ring_buffer.size_used() / self.ring_buffer.size
        buffer_usage = sum(len(buf) for buf in self.priority_buffers.values())
        heap_usage = len(self.priority_heap)
        
        total_capacity = self.ring_buffer.size + 1000  # 假设缓冲区容量
        total_usage = self.ring_buffer.size_used() + buffer_usage + heap_usage
        
        return min(total_usage / total_capacity, 1.0)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取分片统计"""
        return {
            **self.stats,
            'shard_id': self.shard_id,
            'ring_buffer_used': self.ring_buffer.size_used(),
            'ring_buffer_size': self.ring_buffer.size,
            'priority_buffer_sizes': {
                p.name: len(buf) for p, buf in self.priority_buffers.items()
            },
            'heap_size': len(self.priority_heap),
            'load_factor': self.get_load_factor()
        }


class LockFreePriorityQueue:
    """
    无锁分片优先级队列 - 统一实现 (SSOT)
    
    特性:
    1. 分片设计减少锁竞争
    2. 环形缓冲区提供无锁快速入队
    3. 批量处理提高吞吐量
    4. 负载均衡和动态分片
    """
    
    def __init__(self, shard_count: int = 8, worker_count: int = 6, max_sizes: Dict[MessagePriority, int] = None):
        self.shard_count = shard_count
        self.worker_count = worker_count
        
        # 从配置文件加载队列大小，消除硬编码
        if max_sizes is None:
            max_sizes = self._load_queue_sizes_from_config()
        self.max_sizes = max_sizes
        
        # 创建分片
        self.shards: List[LockFreePriorityQueueShard] = [
            LockFreePriorityQueueShard(i) for i in range(shard_count)
        ]
        
        # 工作线程池
        self.executor = ThreadPoolExecutor(max_workers=worker_count, thread_name_prefix="lockfree_queue")
        
        # 消息处理器
        self.processors: Dict[MessagePriority, Callable] = {}
        
        # 工作任务
        self._worker_tasks: List[asyncio.Task] = []
        self._batch_processor_tasks: List[asyncio.Task] = []
        self._running = False
        
        # 负载均衡
        self._round_robin_counter = 0
        self._shard_loads: List[float] = [0.0] * shard_count
        
        # 统计信息
        self.global_stats = {
            'total_enqueued': 0,
            'total_dequeued': 0,
            'total_processed': 0,
            'total_batches': 0,
            'load_balance_switches': 0,
            'avg_processing_time_ms': 0.0
        }
        
        logger.info(f"✅ 无锁分片优先级队列初始化完成 - {shard_count}分片, {worker_count}工作线程")
    
    def _get_shard_id(self, message_data: Dict[str, Any]) -> int:
        """智能分片策略"""
        # 1. 基于账户ID的一致性哈希
        account_id = message_data.get('target_account', message_data.get('account_id', ''))
        if account_id:
            hash_val = int(hashlib.md5(account_id.encode()).hexdigest()[:8], 16)
            return hash_val % self.shard_count
        
        # 2. 负载均衡分片选择
        min_load_shard = min(range(self.shard_count), key=lambda i: self._shard_loads[i])
        
        # 3. 轮询作为备选
        if self._shard_loads[min_load_shard] > 0.8:  # 如果最小负载分片也超载
            self._round_robin_counter = (self._round_robin_counter + 1) % self.shard_count
            return self._round_robin_counter
        
        return min_load_shard
    
    def _load_queue_sizes_from_config(self) -> Dict[MessagePriority, int]:
        """从配置文件加载队列大小，消除硬编码"""
        try:
            from ..core.config_manager import get_config_manager
            config_manager = get_config_manager()
            
            # 从配置中获取队列大小
            queue_config = config_manager.get('messaging.priority_queue.max_sizes', {})
            
            return {
                MessagePriority.SYSTEM_CRITICAL: queue_config.get('system_critical', 100),
                MessagePriority.RISK_COMMAND: queue_config.get('risk_command', 500),
                MessagePriority.SIGNAL_EXECUTION: queue_config.get('signal_execution', 1000),
                MessagePriority.REALTIME_QUERY: queue_config.get('realtime_query', 800),
                MessagePriority.BACKGROUND_TASK: queue_config.get('background_task', 2000)
            }
        except Exception as e:
            logger.warning(f"加载队列配置失败，使用紧急默认值: {e}")
            # 紧急默认值，只在配置加载失败时使用
            return {
                MessagePriority.SYSTEM_CRITICAL: 50,   # 小一点，确保安全
                MessagePriority.RISK_COMMAND: 200,
                MessagePriority.SIGNAL_EXECUTION: 300,
                MessagePriority.REALTIME_QUERY: 200,
                MessagePriority.BACKGROUND_TASK: 500
            }
    
    async def enqueue(self, priority: MessagePriority, data: Dict[str, Any], 
                     message_id: str = None, callback: Callable = None) -> bool:
        """异步入队（无锁）"""
        if message_id is None:
            message_id = f"{priority.name}_{int(time.time() * 1000000)}"
        
        # 选择分片
        shard_id = self._get_shard_id(data)
        shard = self.shards[shard_id]
        
        # 创建分片消息
        message = ShardedMessage(
            priority=priority,
            timestamp=time.time(),
            data=data,
            message_id=message_id,
            shard_id=shard_id,
            callback=callback
        )
        
        # 快速入队
        success = shard.enqueue_fast(message)
        
        if success:
            self.global_stats['total_enqueued'] += 1
            
            # 异步记录指标
            asyncio.create_task(
                self._record_enqueue_metrics(priority, shard_id)
            )
        
        return success
    
    async def _record_enqueue_metrics(self, priority: MessagePriority, shard_id: int):
        """异步记录入队指标"""
        try:
            metrics.increment("lockfree_queue_enqueued_total", 1, {
                'priority': priority.name,
                'shard': str(shard_id)
            })
        except Exception as e:
            logger.debug(f"记录入队指标失败: {e}")
    
    def register_processor(self, priority: MessagePriority, processor: Callable):
        """注册优先级处理器"""
        self.processors[priority] = processor
        logger.info(f"注册无锁队列{priority.name}优先级处理器")
    
    async def start_workers(self):
        """启动工作线程"""
        if self._running:
            return
        
        self._running = True
        
        # 启动批量处理器（每个分片一个）
        for shard in self.shards:
            task = asyncio.create_task(
                self._batch_processor_worker(shard)
            )
            self._batch_processor_tasks.append(task)
        
        # 启动消息处理工作线程
        for i in range(self.worker_count):
            task = asyncio.create_task(
                self._message_processor_worker(f"lockfree_worker_{i}")
            )
            self._worker_tasks.append(task)
        
        # 启动负载监控任务
        monitor_task = asyncio.create_task(self._load_monitor_task())
        self._worker_tasks.append(monitor_task)
        
        logger.info(f"🚀 无锁队列启动: {len(self._batch_processor_tasks)}个批量处理器, {self.worker_count}个工作线程")
    
    async def stop_workers(self):
        """停止工作线程"""
        if not self._running:
            return
        
        self._running = False
        
        # 停止所有任务
        all_tasks = self._worker_tasks + self._batch_processor_tasks
        for task in all_tasks:
            task.cancel()
        
        if all_tasks:
            await asyncio.gather(*all_tasks, return_exceptions=True)
        
        # 关闭线程池
        self.executor.shutdown(wait=False)
        
        self._worker_tasks.clear()
        self._batch_processor_tasks.clear()
        
        logger.info("🛑 无锁队列已停止")
    
    async def _batch_processor_worker(self, shard: LockFreePriorityQueueShard):
        """批量处理器工作线程"""
        logger.debug(f"启动分片{shard.shard_id}批量处理器")
        
        while self._running:
            try:
                # 批量出队
                batch = shard.dequeue_batch(max_batch_size=50)
                
                if not batch:
                    # 尝试提升缓冲区消息到堆
                    promoted = shard.promote_to_heap()
                    if promoted == 0:
                        await asyncio.sleep(0.001)  # 1ms等待
                    continue
                
                # 提交批量处理任务
                for message in batch:
                    asyncio.create_task(
                        self._process_single_message(message)
                    )
                
                self.global_stats['total_batches'] += 1
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"分片{shard.shard_id}批量处理异常: {e}")
                await asyncio.sleep(0.01)
        
        logger.debug(f"分片{shard.shard_id}批量处理器停止")
    
    async def _message_processor_worker(self, worker_name: str):
        """消息处理工作线程"""
        logger.debug(f"启动无锁消息处理器: {worker_name}")
        
        while self._running:
            try:
                await asyncio.sleep(0.001)  # 让出控制权
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"无锁工作线程异常 {worker_name}: {e}")
                await asyncio.sleep(0.01)
        
        logger.debug(f"无锁消息处理器停止: {worker_name}")
    
    async def _process_single_message(self, message: ShardedMessage):
        """处理单个消息"""
        start_time = time.perf_counter()
        
        try:
            processor = self.processors.get(message.priority)
            
            if processor:
                await processor(message.data)
            elif message.callback:
                await message.callback(message.data)
            else:
                logger.warning(f"无锁队列: 没有找到{message.priority.name}优先级的处理器")
            
            # 更新统计
            processing_time = (time.perf_counter() - start_time) * 1000
            self.global_stats['total_processed'] += 1
            
            # 更新平均处理时间
            total = self.global_stats['total_processed']
            current_avg = self.global_stats['avg_processing_time_ms']
            self.global_stats['avg_processing_time_ms'] = (
                (current_avg * (total - 1) + processing_time) / total
            )
            
            # 异步记录指标
            asyncio.create_task(
                self._record_processing_metrics(message.priority, processing_time)
            )
            
        except Exception as e:
            logger.error(f"无锁队列消息处理失败 {message.message_id}: {e}")
            
            # 重试逻辑
            if message.retry_count < message.max_retries:
                message.retry_count += 1
                shard = self.shards[message.shard_id]
                shard.enqueue_fast(message)
                logger.info(f"无锁队列消息重试 {message.message_id} (第{message.retry_count}次)")
    
    async def _record_processing_metrics(self, priority: MessagePriority, processing_time: float):
        """异步记录处理指标"""
        try:
            metrics.record("lockfree_queue_processing_time_ms", processing_time, {
                'priority': priority.name
            })
            metrics.increment("lockfree_queue_processed_total", 1, {
                'priority': priority.name
            })
        except Exception as e:
            logger.debug(f"记录处理指标失败: {e}")
    
    async def _load_monitor_task(self):
        """负载监控任务"""
        while self._running:
            try:
                # 更新分片负载
                for i, shard in enumerate(self.shards):
                    self._shard_loads[i] = shard.get_load_factor()
                
                # 记录负载指标
                for i, load in enumerate(self._shard_loads):
                    metrics.record("lockfree_queue_shard_load", load, {
                        'shard': str(i)
                    })
                
                await asyncio.sleep(5)  # 每5秒监控一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"负载监控异常: {e}")
                await asyncio.sleep(5)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取全局统计"""
        shard_stats = [shard.get_stats() for shard in self.shards]
        
        return {
            'global_stats': self.global_stats,
            'shard_count': self.shard_count,
            'worker_count': self.worker_count,
            'running': self._running,
            'shard_loads': self._shard_loads,
            'shard_stats': shard_stats,
            'total_queue_size': sum(
                stats['ring_buffer_used'] + 
                sum(stats['priority_buffer_sizes'].values()) + 
                stats['heap_size']
                for stats in shard_stats
            )
        }
    
    def is_empty(self) -> bool:
        """检查所有分片是否为空"""
        return all(
            shard.ring_buffer.is_empty() and
            all(len(buf) == 0 for buf in shard.priority_buffers.values()) and
            len(shard.priority_heap) == 0
            for shard in self.shards
        )
    
    def size(self) -> int:
        """获取队列总大小"""
        return sum(
            shard.ring_buffer.size_used() + 
            sum(len(buf) for buf in shard.priority_buffers.values()) +
            len(shard.priority_heap)
            for shard in self.shards
        )
    
    def size_by_priority(self, priority: MessagePriority) -> int:
        """获取指定优先级队列大小"""
        total_size = 0
        for shard in self.shards:
            total_size += len(shard.priority_buffers[priority])
        return total_size


# ==================== 统一优先级分析器 ====================

class PriorityAnalyzer:
    """统一优先级分析器 - 五个等级 (0, 1, 2, 3, 4) - 基于'风险 > 机会 > 数据 > 后勤'原则"""

    @staticmethod
    def get_command_priority(method: str, params: Dict[str, Any]) -> MessagePriority:
        """
        确定命令优先级 - 统一五个等级
        
        优先级分层（基于"风险 > 机会 > 数据 > 后勤"原则）:
        0. SYSTEM_CRITICAL - 系统级风险：保证金不足、紧急平仓、风控强制全平
        1. RISK_COMMAND - 订单级风控：止损/止盈执行、订单修改、部分平仓
        2. SIGNAL_EXECUTION - 策略信号执行：开仓信号、策略主动平仓
        3. REALTIME_QUERY - 实时数据查询：最新报价、账户信息、持仓查询
        4. BACKGROUND_TASK - 后台任务：历史数据、日志记录、UI更新
        """

        # === SYSTEM_CRITICAL PRIORITY (0) - 系统级风险 ===

        # 1. 紧急平仓和风险控制
        if method == 'send_order':
            analyzed_priority = PriorityAnalyzer._analyze_trade_signal_priority(params)
            if analyzed_priority == MessagePriority.SYSTEM_CRITICAL:
                return MessagePriority.SYSTEM_CRITICAL
            elif analyzed_priority == MessagePriority.RISK_COMMAND:
                return MessagePriority.RISK_COMMAND

            # 检查系统级风险情况
            trade_action = params.get('action', '').upper()
            signal_type = params.get('signal_type', '').upper()

            # 系统级风险信号
            if (trade_action in ['EMERGENCY_CLOSE', 'FORCE_CLOSE'] or
                signal_type in ['EMERGENCY_CLOSE', 'MARGIN_CALL', 'STOP_LOSS_HIT']):
                return MessagePriority.SYSTEM_CRITICAL

        # 2. 系统紧急操作
        if method == 'emergency_stop' or params.get('emergency', False):
            return MessagePriority.SYSTEM_CRITICAL

        # === RISK_COMMAND PRIORITY (1) - 订单级风控 ===

        # 1. 平仓和修改操作
        if method == 'send_order':
            trade_action = params.get('action', '').upper()
            signal_type = params.get('signal_type', '').upper()
            order_type = params.get('type', 0)

            # 平仓信号
            if trade_action in ['CLOSE'] or signal_type == 'POSITION_CLOSE':
                return MessagePriority.RISK_COMMAND

            # 止损/止盈修改
            if (trade_action in ['MODIFY', 'MODIFY_SL', 'MODIFY_TP'] or
                signal_type == 'POSITION_MODIFY' or
                params.get('sl', 0) != 0 or params.get('tp', 0) != 0):
                return MessagePriority.RISK_COMMAND

            # 止损单类型 (BUY_STOP=4, SELL_STOP=5)
            if order_type in [4, 5]:
                return MessagePriority.RISK_COMMAND

            # 包含止损价格的订单
            if params.get('sl', 0) > 0:
                return MessagePriority.RISK_COMMAND

        # 2. 订单取消操作
        if method == 'cancel_order' or params.get('action') == 'CANCEL':
            return MessagePriority.RISK_COMMAND

        # 3. 关键系统操作
        if method == 'health_check' and params.get('critical', False):
            return MessagePriority.RISK_COMMAND

        # === SIGNAL_EXECUTION PRIORITY (2) - 策略信号执行 ===

        # 1. 新开仓信号
        if method == 'send_order':
            signal_type = params.get('signal_type', '').upper()
            trade_action = params.get('action', '').upper()

            # 开仓信号
            if (signal_type == 'POSITION_OPEN' or
                trade_action in ['BUY', 'SELL', 'OPEN']):
                return MessagePriority.SIGNAL_EXECUTION

            # 限价单和止损限价单
            order_type = params.get('type', 0)
            if order_type in [2, 3, 6, 7]:  # LIMIT和STOP_LIMIT类型
                return MessagePriority.SIGNAL_EXECUTION

        # 2. 策略主动平仓（非止损触发）
        if method == 'send_order' and params.get('reason') == 'strategy':
            return MessagePriority.SIGNAL_EXECUTION

        # 3. 反向跟单开仓
        if method == 'send_order' and params.get('copy_mode') == 'reverse':
            return MessagePriority.SIGNAL_EXECUTION

        # === REALTIME_QUERY PRIORITY (3) - 实时数据查询 ===

        # 1. 获取最新报价
        if method in ['get_tick_data', 'get_symbol_tick', 'get_last_tick']:
            return MessagePriority.REALTIME_QUERY

        # 2. 查询账户实时信息
        if method in ['get_account_info', 'get_margin_info', 'get_balance']:
            return MessagePriority.REALTIME_QUERY

        # 3. 查询当前持仓
        if method in ['get_positions', 'get_open_positions', 'get_position_info']:
            return MessagePriority.REALTIME_QUERY

        # 4. 查询当前挂单
        if method in ['get_orders', 'get_pending_orders']:
            return MessagePriority.REALTIME_QUERY

        # 5. 查询服务器连接状态
        if method in ['health_check', 'check_connection', 'get_terminal_info']:
            return MessagePriority.REALTIME_QUERY

        # === BACKGROUND_TASK PRIORITY (4) - 后台与非实时任务 ===

        # 1. 历史数据查询
        if method in ['get_rates', 'get_history', 'get_historical_data', 'get_bars']:
            return MessagePriority.BACKGROUND_TASK

        # 2. 日志和报告
        if method in ['write_log', 'save_trade', 'generate_report']:
            return MessagePriority.BACKGROUND_TASK

        # 3. UI更新
        if method.startswith('ui_') or method.endswith('_update'):
            return MessagePriority.BACKGROUND_TASK

        # 4. 系统心跳
        if method in ['heartbeat', 'send_heartbeat', 'ping']:
            return MessagePriority.BACKGROUND_TASK

        # 5. 市场信息查询（非实时）
        if method in ['get_symbol_info', 'get_market_info', 'get_trading_hours']:
            return MessagePriority.BACKGROUND_TASK

        # 6. 一般性查询（非关键）
        if method.startswith('get_') and not any(key in method for key in ['position', 'order', 'account', 'tick']):
            return MessagePriority.BACKGROUND_TASK

        # Default to realtime query for safety
        return MessagePriority.REALTIME_QUERY

    @staticmethod
    def _analyze_trade_signal_priority(params: Dict[str, Any]) -> MessagePriority:
        """
        深度分析交易信号优先级 - 统一五个等级
        """

        signal_data = params.get('signal_data') or params.get('data') or params
        signal_type = signal_data.get('signal_type') or signal_data.get('type', '')
        if isinstance(signal_type, str):
            signal_type = signal_type.upper()

        # === SYSTEM_CRITICAL PRIORITY (0) - 系统级风险 ===
        system_critical_signals = [
            'EMERGENCY_CLOSE',   # 紧急平仓
            'STOP_LOSS_HIT',     # 止损触发
            'MARGIN_CALL',       # 保证金不足
            'FORCE_CLOSE',       # 强制平仓
            'RISK_LIMIT_HIT'     # 风险限制触发
        ]

        if signal_type in system_critical_signals:
            return MessagePriority.SYSTEM_CRITICAL

        # 检查系统级风险动作
        trade_action = signal_data.get('action', '').upper()
        if trade_action in ['EMERGENCY', 'FORCE_CLOSE', 'EMERGENCY_CLOSE']:
            return MessagePriority.SYSTEM_CRITICAL

        # 检查系统级风险关键词
        comment = signal_data.get('comment', '').upper()
        critical_keywords = ['EMERGENCY', 'MARGIN', 'FORCE', 'RISK_LIMIT']
        if any(keyword in comment for keyword in critical_keywords):
            return MessagePriority.SYSTEM_CRITICAL

        # === RISK_COMMAND PRIORITY (1) - 订单级风控 ===
        risk_command_signals = [
            'POSITION_CLOSE',    # 平仓信号
            'POSITION_MODIFY',   # 修改持仓（止损/止盈）
            'ORDER_CANCEL',      # 取消订单
        ]

        if signal_type in risk_command_signals:
            return MessagePriority.RISK_COMMAND

        # 订单级风控动作
        if trade_action in ['CLOSE', 'MODIFY', 'SLTP', 'REMOVE', 'CANCEL']:
            return MessagePriority.RISK_COMMAND

        # 检查订单类型
        order_type = signal_data.get('order_type') or signal_data.get('type')
        if isinstance(order_type, int):
            # MT5 订单类型: 4=BUY_STOP, 5=SELL_STOP (止损单)
            if order_type in [4, 5]:
                return MessagePriority.RISK_COMMAND

        # 检查止损/止盈
        sl = signal_data.get('sl', 0) or signal_data.get('stop_loss', 0)
        tp = signal_data.get('tp', 0) or signal_data.get('take_profit', 0)

        if sl > 0 or tp > 0:
            return MessagePriority.RISK_COMMAND

        # 订单级风控关键词
        risk_keywords = ['STOP', 'LOSS', 'CLOSE']
        if any(keyword in comment for keyword in risk_keywords):
            return MessagePriority.RISK_COMMAND

        # === SIGNAL_EXECUTION PRIORITY (2) - 策略信号执行 ===
        if signal_type in ['POSITION_OPEN', 'ORDER_PLACE'] or trade_action in ['BUY', 'SELL', 'OPEN']:
            return MessagePriority.SIGNAL_EXECUTION

        # === REALTIME_QUERY PRIORITY (3) - 实时数据查询 ===
        realtime_query_signals = [
            'ACCOUNT_QUERY',      # 账户信息查询
            'POSITION_QUERY',     # 持仓查询
            'ORDER_QUERY',        # 订单查询
            'PRICE_QUERY',        # 价格查询
            'TICK_DATA',          # Tick数据
            'CONNECTION_CHECK'    # 连接检查
        ]
        
        if signal_type in realtime_query_signals:
            return MessagePriority.REALTIME_QUERY
            
        # 检查查询类型的动作
        if trade_action in ['QUERY', 'CHECK', 'GET_INFO']:
            return MessagePriority.REALTIME_QUERY

        # === BACKGROUND_TASK PRIORITY (4) - 后台任务 ===
        background_task_signals = [
            'HEARTBEAT',          # 心跳
            'STATUS_UPDATE',      # 状态更新
            'MARKET_DATA',        # 市场数据（非实时）
            'LOG_ENTRY',          # 日志记录
            'REPORT_GENERATION',  # 报告生成
            'UI_UPDATE',          # UI更新
            'HISTORICAL_DATA'     # 历史数据
        ]
        
        if signal_type in background_task_signals:
            return MessagePriority.BACKGROUND_TASK
            
        # 检查后台任务关键词
        background_keywords = ['LOG', 'REPORT', 'HISTORY', 'UI', 'BACKUP']
        if any(keyword in comment for keyword in background_keywords):
            return MessagePriority.BACKGROUND_TASK

        # 默认为实时查询优先级
        return MessagePriority.REALTIME_QUERY
    
    @staticmethod
    def get_message_priority(method: str, signal_data: Dict[str, Any]) -> MessagePriority:
        """
        统一消息路由优先级分析 - 包含路由特定的逻辑
        """
        try:
            # 首先使用基础的命令优先级分析
            base_priority = PriorityAnalyzer.get_command_priority(method, signal_data)
            
            # 路由特定的优先级调整
            # 紧急标志 - 提升到最高优先级
            if signal_data.get('urgent', False):
                return MessagePriority.SYSTEM_CRITICAL
                
            # 广播消息 - 降低优先级，避免阻塞点对点消息
            if signal_data.get('broadcast', False):
                return MessagePriority.BACKGROUND_TASK
                
            # 系统消息调整
            message_type = signal_data.get('message_type', '').upper()
            if message_type in ['SYSTEM_SHUTDOWN', 'EMERGENCY_STOP']:
                return MessagePriority.SYSTEM_CRITICAL
            elif message_type in ['HEARTBEAT', 'STATUS_UPDATE']:
                return MessagePriority.BACKGROUND_TASK
                
            # 返回基础优先级
            return base_priority
            
        except Exception as e:
            # 失败时返回默认优先级
            return MessagePriority.REALTIME_QUERY


# ==================== 统一便捷函数 ====================

# 统一实例管理 - 遵循SSOT原则
_priority_queue_instance: Optional[LockFreePriorityQueue] = None


def get_priority_queue(shard_count: int = 8, worker_count: int = 6) -> LockFreePriorityQueue:
    """获取优先级队列实例（单例） - 使用无锁实现"""
    global _priority_queue_instance
    
    if _priority_queue_instance is None:
        _priority_queue_instance = LockFreePriorityQueue(shard_count, worker_count)
    
    return _priority_queue_instance


# 向后兼容别名
PriorityMessageQueue = LockFreePriorityQueue


# 便捷函数 - 使用HTML标准名称
async def enqueue_system_critical(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队系统级风险优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.SYSTEM_CRITICAL, data, message_id, callback)


async def enqueue_risk_command(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队订单级风控优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.RISK_COMMAND, data, message_id, callback)


async def enqueue_signal_execution(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队策略信号执行优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.SIGNAL_EXECUTION, data, message_id, callback)


async def enqueue_realtime_query(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队实时数据查询优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.REALTIME_QUERY, data, message_id, callback)


async def enqueue_background_task(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队后台任务优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.BACKGROUND_TASK, data, message_id, callback)


async def enqueue_command(method: str, params: Dict[str, Any],
                         message_id: str = None, callback: Callable = None) -> bool:
    """
    根据命令自动确定优先级并入队 - 统一接口
    """
    priority = PriorityAnalyzer.get_command_priority(method, params)

    command_data = {
        'method': method,
        'params': params,
        'priority': priority.value,  # 0, 1, 2, 3, 4
        'priority_name': priority.name,  # SYSTEM_CRITICAL, RISK_COMMAND, SIGNAL_EXECUTION, REALTIME_QUERY, BACKGROUND_TASK
        'timestamp': time.time()
    }

    return await get_priority_queue().enqueue(priority, command_data, message_id, callback)


# 向后兼容的别名
enqueue_rpc_command = enqueue_command


# ==================== 优先级工具函数 ====================

def get_priority_from_string(priority_str: str) -> MessagePriority:
    """从字符串获取优先级 - 使用HTML标准名称"""
    priority_map = {
        'system_critical': MessagePriority.SYSTEM_CRITICAL,
        'risk_command': MessagePriority.RISK_COMMAND,
        'signal_execution': MessagePriority.SIGNAL_EXECUTION,
        'realtime_query': MessagePriority.REALTIME_QUERY,
        'background_task': MessagePriority.BACKGROUND_TASK
    }
    result = priority_map.get(priority_str.lower())
    if result is None:
        logger.warning(f"未知的优先级字符串: {priority_str}, 请使用HTML标准名称")
        return MessagePriority.REALTIME_QUERY
    return result

def get_priority_from_int(priority_int: int) -> MessagePriority:
    """从整数获取优先级"""
    priority_map = {
        0: MessagePriority.SYSTEM_CRITICAL,   # 系统级风险
        1: MessagePriority.RISK_COMMAND,      # 订单级风控
        2: MessagePriority.SIGNAL_EXECUTION,  # 策略信号执行
        3: MessagePriority.REALTIME_QUERY,    # 实时数据查询
        4: MessagePriority.BACKGROUND_TASK    # 后台任务
    }
    result = priority_map.get(priority_int)
    if result is None:
        logger.warning(f"未知的优先级整数: {priority_int}, 仅支持 0-4")
        return MessagePriority.REALTIME_QUERY
    return result