# 🚨 AUTO-MIGRATED: Old messaging components replaced with QueueManager
# See MIGRATION_GUIDE.md for details

"""
独立终端角色管理器
允许用户手动、独立地设置每个终端的角色，不受配置组限制
"""
import asyncio
import time
import json
from typing import Dict, List, Optional, Set, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

from .terminal_manager import MultiTerminalManager
from ..monitors.master_monitor import HighFrequencyMasterMonitor, MonitorConfig
from ..executors.slave_executor import OptimizedSlaveExecutor, ExecutorConfig
from ..messaging.queue_manager import QueueManager
from ..config.redis_client import RedisClient
from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector


logger = get_logger(__name__)
metrics = get_metrics_collector()


class TerminalRole(Enum):
    """终端角色"""
    IDLE = "idle"
    MASTER = "master"
    SLAVE = "slave"


@dataclass
class RoleChangeEvent:
    """角色变更事件"""
    event_id: str
    terminal_id: str
    old_role: str
    new_role: str
    timestamp: float
    user: str = "manual"
    success: bool = True
    error_message: Optional[str] = None


class IndependentRoleManager:
    """独立角色管理器"""
    
    def __init__(self, terminal_manager: MultiTerminalManager, 
                 queue_manager: QueueManager, redis_client: RedisClient):
        self.terminal_manager = terminal_manager
        self.nats = queue_manager
        self.redis = redis_client
        
        # 服务管理
        self.active_monitors: Dict[str, HighFrequencyMasterMonitor] = {}
        self.active_executors: Dict[str, OptimizedSlaveExecutor] = {}
        
        # 角色变更历史
        self.role_change_history: List[RoleChangeEvent] = []
        
        # 状态跟踪
        self.running = False
        
        # 回调函数
        self.role_change_callbacks: List[Callable] = []
        
        # Redis键
        self.role_history_key = "mt5:role_change_history"
        self.terminal_roles_key = "mt5:terminal_roles"
        
    async def start(self):
        """启动独立角色管理器"""
        if self.running:
            return
            
        logger.info("启动独立角色管理器")
        self.running = True
        
        try:
            # 加载历史记录
            await self.load_role_history()
            
            # 恢复之前的角色设置
            await self.restore_terminal_roles()
            
            logger.info("独立角色管理器已启动")
            
        except Exception as e:
            logger.error(f"启动独立角色管理器失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止独立角色管理器"""
        if not self.running:
            return
            
        logger.info("停止独立角色管理器")
        self.running = False
        
        # 停止所有服务
        await self.stop_all_services()
        
        # 保存状态
        await self.save_role_history()
        await self.save_terminal_roles()
        
        logger.info("独立角色管理器已停止")
    
    async def set_terminal_role(self, terminal_id: str, role: TerminalRole, 
                               user: str = "manual") -> bool:
        """设置终端角色"""
        try:
            # 检查终端是否存在
            terminal_status = await self.terminal_manager.get_terminal_status(terminal_id)
            if not terminal_status:
                raise ValueError(f"终端不存在: {terminal_id}")
            
            old_role = terminal_status.get('role', 'idle')
            new_role = role.value
            
            # 如果角色没有变化，直接返回
            if old_role == new_role:
                logger.info(f"终端 {terminal_id} 角色未变化: {new_role}")
                return True
            
            logger.info(f"设置终端角色: {terminal_id} {old_role} -> {new_role}")
            
            # 创建变更事件
            event = RoleChangeEvent(
                event_id=f"role_change_{int(time.time() * 1000)}",
                terminal_id=terminal_id,
                old_role=old_role,
                new_role=new_role,
                timestamp=time.time(),
                user=user
            )
            
            try:
                # 1. 停止旧角色的服务
                await self.stop_terminal_services(terminal_id)
                
                # 2. 设置新角色
                await self.terminal_manager.set_terminal_role(terminal_id, new_role)
                
                # 3. 启动新角色的服务
                if new_role == TerminalRole.MASTER.value:
                    await self.start_master_service(terminal_id)
                elif new_role == TerminalRole.SLAVE.value:
                    await self.start_slave_service(terminal_id)
                
                # 记录成功事件
                event.success = True
                self.role_change_history.append(event)
                
                logger.info(f"终端角色设置成功: {terminal_id} -> {new_role}")
                
                # 通知回调函数
                await self._notify_role_change_callbacks(terminal_id, old_role, new_role)
                
                # 保存状态
                await self.save_terminal_roles()
                
                return True
                
            except Exception as e:
                # 记录失败事件
                event.success = False
                event.error_message = str(e)
                self.role_change_history.append(event)
                
                logger.error(f"设置终端角色失败: {e}")
                raise
                
        except Exception as e:
            logger.error(f"设置终端角色失败 {terminal_id}: {e}")
            return False
    
    async def get_terminal_role(self, terminal_id: str) -> Optional[str]:
        """获取终端角色"""
        terminal_status = await self.terminal_manager.get_terminal_status(terminal_id)
        if terminal_status:
            return terminal_status.get('role', 'idle')
        return None
    
    async def get_all_terminal_roles(self) -> Dict[str, str]:
        """获取所有终端角色"""
        terminals = await self.terminal_manager.get_all_terminals_status()
        return {
            terminal['terminal_id']: terminal.get('role', 'idle')
            for terminal in terminals
        }
    
    async def get_terminals_by_role(self, role: TerminalRole) -> List[str]:
        """根据角色获取终端列表"""
        all_roles = await self.get_all_terminal_roles()
        return [
            terminal_id for terminal_id, terminal_role in all_roles.items()
            if terminal_role == role.value
        ]
    
    async def batch_set_roles(self, role_assignments: Dict[str, TerminalRole], 
                             user: str = "manual") -> Dict[str, bool]:
        """批量设置角色"""
        results = {}
        
        for terminal_id, role in role_assignments.items():
            try:
                success = await self.set_terminal_role(terminal_id, role, user)
                results[terminal_id] = success
            except Exception as e:
                logger.error(f"批量设置角色失败 {terminal_id}: {e}")
                results[terminal_id] = False
        
        return results
    
    async def reset_all_roles(self, user: str = "manual") -> bool:
        """重置所有终端为空闲状态"""
        try:
            terminals = await self.terminal_manager.get_all_terminals_status()
            
            for terminal in terminals:
                terminal_id = terminal['terminal_id']
                await self.set_terminal_role(terminal_id, TerminalRole.IDLE, user)
            
            logger.info("所有终端角色已重置为空闲")
            return True
            
        except Exception as e:
            logger.error(f"重置所有角色失败: {e}")
            return False
    
    async def start_master_service(self, terminal_id: str):
        """启动主账户服务"""
        try:
            terminal_status = await self.terminal_manager.get_terminal_status(terminal_id)
            if not terminal_status:
                raise ValueError(f"终端不存在: {terminal_id}")
            
            # 构建账户配置
            account_config = {
                'id': terminal_status['account_id'],
                'login': terminal_status['login'],
                'server': terminal_status['server']
            }
            
            # 创建监控器配置
            monitor_config = MonitorConfig(
                base_polling_interval=0.001,
                adaptive_enabled=True,
                batch_size=10
            )
            
            # 获取所有从账户ID
            slave_terminals = await self.get_terminals_by_role(TerminalRole.SLAVE)
            slave_ids = []
            for slave_terminal_id in slave_terminals:
                slave_status = await self.terminal_manager.get_terminal_status(slave_terminal_id)
                if slave_status:
                    slave_ids.append(slave_status['account_id'])
            
            # 创建监控器
            monitor = HighFrequencyMasterMonitor(
                account_config=account_config,
                queue_manager=self.nats,
                monitor_config=monitor_config,
                slave_ids=slave_ids
            )
            
            # 启动监控器
            await monitor.start()
            self.active_monitors[terminal_id] = monitor
            
            logger.info(f"主账户服务已启动: {terminal_id}")
            
        except Exception as e:
            logger.error(f"启动主账户服务失败 {terminal_id}: {e}")
            raise
    
    async def start_slave_service(self, terminal_id: str):
        """启动从账户服务"""
        try:
            terminal_status = await self.terminal_manager.get_terminal_status(terminal_id)
            if not terminal_status:
                raise ValueError(f"终端不存在: {terminal_id}")
            
            # 构建账户配置
            account_config = {
                'id': terminal_status['account_id'],
                'login': terminal_status['login'],
                'server': terminal_status['server']
            }
            
            # 创建执行器配置
            executor_config = ExecutorConfig(
                max_retries=3,
                execution_timeout=5.0,
                batch_processing=True
            )
            
            # 创建执行器
            executor = OptimizedSlaveExecutor(
                account_config=account_config,
                queue_manager=self.nats,
                executor_config=executor_config
            )
            
            # 启动执行器
            await executor.start()
            self.active_executors[terminal_id] = executor
            
            logger.info(f"从账户服务已启动: {terminal_id}")
            
        except Exception as e:
            logger.error(f"启动从账户服务失败 {terminal_id}: {e}")
            raise
    
    async def stop_terminal_services(self, terminal_id: str):
        """停止终端服务"""
        # 停止监控器
        if terminal_id in self.active_monitors:
            try:
                await self.active_monitors[terminal_id].stop()
                del self.active_monitors[terminal_id]
                logger.info(f"主账户服务已停止: {terminal_id}")
            except Exception as e:
                logger.error(f"停止主账户服务失败 {terminal_id}: {e}")
        
        # 停止执行器
        if terminal_id in self.active_executors:
            try:
                await self.active_executors[terminal_id].stop()
                del self.active_executors[terminal_id]
                logger.info(f"从账户服务已停止: {terminal_id}")
            except Exception as e:
                logger.error(f"停止从账户服务失败 {terminal_id}: {e}")
    
    async def stop_all_services(self):
        """停止所有服务"""
        # 停止所有监控器
        for terminal_id in list(self.active_monitors.keys()):
            await self.stop_terminal_services(terminal_id)
        
        # 停止所有执行器
        for terminal_id in list(self.active_executors.keys()):
            await self.stop_terminal_services(terminal_id)
    
    async def refresh_master_slaves(self):
        """刷新主账户的从账户列表"""
        try:
            # 获取所有从账户
            slave_terminals = await self.get_terminals_by_role(TerminalRole.SLAVE)
            slave_ids = []
            
            for slave_terminal_id in slave_terminals:
                slave_status = await self.terminal_manager.get_terminal_status(slave_terminal_id)
                if slave_status:
                    slave_ids.append(slave_status['account_id'])
            
            # 更新所有主账户监控器的从账户列表
            for terminal_id, monitor in self.active_monitors.items():
                try:
                    monitor.slave_ids = slave_ids
                    logger.debug(f"已更新主账户 {terminal_id} 的从账户列表: {slave_ids}")
                except Exception as e:
                    logger.error(f"更新主账户从账户列表失败 {terminal_id}: {e}")
            
        except Exception as e:
            logger.error(f"刷新主从关系失败: {e}")
    
    async def get_role_change_history(self, limit: int = 50) -> List[RoleChangeEvent]:
        """获取角色变更历史"""
        return self.role_change_history[-limit:]
    
    async def get_system_summary(self) -> Dict:
        """获取系统摘要"""
        all_roles = await self.get_all_terminal_roles()
        
        master_count = len([r for r in all_roles.values() if r == TerminalRole.MASTER.value])
        slave_count = len([r for r in all_roles.values() if r == TerminalRole.SLAVE.value])
        idle_count = len([r for r in all_roles.values() if r == TerminalRole.IDLE.value])
        
        return {
            "total_terminals": len(all_roles),
            "master_terminals": master_count,
            "slave_terminals": slave_count,
            "idle_terminals": idle_count,
            "active_monitors": len(self.active_monitors),
            "active_executors": len(self.active_executors),
            "recent_changes": len(self.role_change_history[-10:])
        }
    
    def add_role_change_callback(self, callback: Callable):
        """添加角色变更回调"""
        self.role_change_callbacks.append(callback)
    
    async def save_role_history(self):
        """保存角色变更历史"""
        try:
            history_data = [asdict(event) for event in self.role_change_history[-100:]]
            await self.redis.set(
                self.role_history_key,
                json.dumps(history_data, default=str)
            )
        except Exception as e:
            logger.error(f"保存角色历史失败: {e}")
    
    async def load_role_history(self):
        """加载角色变更历史"""
        try:
            history_data = await self.redis.get(self.role_history_key)
            if history_data:
                history_list = json.loads(history_data)
                self.role_change_history = [RoleChangeEvent(**event) for event in history_list]
                logger.info(f"已加载 {len(self.role_change_history)} 条角色变更历史")
        except Exception as e:
            logger.error(f"加载角色历史失败: {e}")
    
    async def save_terminal_roles(self):
        """保存终端角色状态"""
        try:
            roles = await self.get_all_terminal_roles()
            await self.redis.set(
                self.terminal_roles_key,
                json.dumps(roles)
            )
        except Exception as e:
            logger.error(f"保存终端角色失败: {e}")
    
    async def restore_terminal_roles(self):
        """恢复终端角色状态"""
        try:
            roles_data = await self.redis.get(self.terminal_roles_key)
            if roles_data:
                saved_roles = json.loads(roles_data)
                
                for terminal_id, role in saved_roles.items():
                    try:
                        # 检查终端是否仍然存在
                        terminal_status = await self.terminal_manager.get_terminal_status(terminal_id)
                        if terminal_status:
                            await self.set_terminal_role(terminal_id, TerminalRole(role), "restore")
                    except Exception as e:
                        logger.warning(f"恢复终端角色失败 {terminal_id}: {e}")
                
                logger.info("终端角色状态已恢复")
        except Exception as e:
            logger.error(f"恢复终端角色失败: {e}")
    
    async def _notify_role_change_callbacks(self, terminal_id: str, old_role: str, new_role: str):
        """通知角色变更回调"""
        for callback in self.role_change_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(terminal_id, old_role, new_role)
                else:
                    callback(terminal_id, old_role, new_role)
            except Exception as e:
                logger.error(f"角色变更回调失败: {e}")
        
        # 刷新主从关系
        await self.refresh_master_slaves()
