#!/bin/bash

# MT5 Trading System Security Validation Script
# 企业级安全验证和测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE} $1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

# 全局变量
SECURITY_SCORE=0
TOTAL_CHECKS=0
FAILED_CHECKS=0
WARNINGS=0

# 增加检查计数
add_check() {
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
}

# 通过检查
pass_check() {
    SECURITY_SCORE=$((SECURITY_SCORE + 1))
    log_success "$1"
}

# 失败检查
fail_check() {
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
    log_error "$1"
}

# 警告检查
warn_check() {
    WARNINGS=$((WARNINGS + 1))
    log_warning "$1"
}

# 1. 环境变量安全检查
check_environment_variables() {
    log_header "1. Environment Variables Security Check"
    
    # 必需的环境变量
    required_vars=(
        "JWT_SECRET"
        "MASTER_ENCRYPTION_KEY" 
        "DB_ENCRYPTION_KEY"
        "REDIS_PASSWORD"
    )
    
    for var in "${required_vars[@]}"; do
        add_check
        if [ -z "${!var}" ]; then
            fail_check "Missing critical environment variable: $var"
        else
            # 检查变量长度
            var_length=${#!var}
            if [ $var_length -lt 32 ]; then
                warn_check "$var is too short ($var_length chars, recommended: 32+)"
            else
                pass_check "$var is properly configured (length: $var_length)"
            fi
        fi
    done
    
    # 检查密码强度
    log_info "Checking password strength..."
    python3 -c "
import os
import re
import sys

def check_password_strength(password, name):
    issues = []
    if len(password) < 32:
        issues.append(f'{name}: Too short (minimum 32 characters for keys)')
    if password.isalnum():
        issues.append(f'{name}: Should contain special characters')
    if password.islower():
        issues.append(f'{name}: Should contain mixed case')
    
    return issues

# Check key strength
keys_to_check = ['JWT_SECRET', 'MASTER_ENCRYPTION_KEY', 'DB_ENCRYPTION_KEY']
all_issues = []

for key in keys_to_check:
    value = os.getenv(key, '')
    if value:
        issues = check_password_strength(value, key)
        all_issues.extend(issues)

if all_issues:
    for issue in all_issues:
        print(f'WARNING: {issue}')
    sys.exit(1)
else:
    print('All keys meet security requirements')
    sys.exit(0)
"
    
    if [ $? -eq 0 ]; then
        add_check
        pass_check "All keys meet security strength requirements"
    else
        add_check
        warn_check "Some keys may not meet optimal security strength"
    fi
}

# 2. 文件权限检查
check_file_permissions() {
    log_header "2. File Permissions Security Check"
    
    # 检查敏感配置文件权限
    sensitive_files=(
        "config/security/encryption_keys.yaml"
        "config/security/api_permissions.yaml"
        ".env"
    )
    
    for file in "${sensitive_files[@]}"; do
        add_check
        if [ -f "$file" ]; then
            perms=$(stat -c "%a" "$file" 2>/dev/null || stat -f "%A" "$file" 2>/dev/null)
            if [ "$perms" = "600" ] || [ "$perms" = "0600" ]; then
                pass_check "$file has correct permissions ($perms)"
            else
                fail_check "$file has incorrect permissions ($perms), should be 600"
                log_info "Fix with: chmod 600 $file"
            fi
        else
            warn_check "$file not found"
        fi
    done
    
    # 检查脚本执行权限
    script_files=(
        "run.sh"
        "scripts/security_check.sh"
    )
    
    for file in "${script_files[@]}"; do
        add_check
        if [ -f "$file" ]; then
            if [ -x "$file" ]; then
                pass_check "$file is executable"
            else
                warn_check "$file is not executable"
                log_info "Fix with: chmod +x $file"
            fi
        else
            warn_check "$file not found"
        fi
    done
}

# 3. TLS/SSL证书检查
check_tls_certificates() {
    log_header "3. TLS/SSL Certificates Check"
    
    add_check
    if [ -n "$TLS_CERT_FILE" ] && [ -f "$TLS_CERT_FILE" ]; then
        # 检查证书有效期
        expiry_date=$(openssl x509 -enddate -noout -in "$TLS_CERT_FILE" 2>/dev/null | cut -d= -f2)
        if [ $? -eq 0 ]; then
            # 检查是否即将过期（30天内）
            expiry_epoch=$(date -d "$expiry_date" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$expiry_date" +%s 2>/dev/null)
            current_epoch=$(date +%s)
            days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
            
            if [ $days_until_expiry -lt 0 ]; then
                fail_check "TLS certificate has expired on $expiry_date"
            elif [ $days_until_expiry -lt 30 ]; then
                warn_check "TLS certificate expires soon: $expiry_date ($days_until_expiry days)"
            else
                pass_check "TLS certificate is valid until $expiry_date ($days_until_expiry days)"
            fi
        else
            warn_check "Could not read TLS certificate expiry date"
        fi
        
        # 检查证书强度
        add_check
        key_length=$(openssl x509 -noout -text -in "$TLS_CERT_FILE" 2>/dev/null | grep "Public-Key:" | grep -o '[0-9]*' | head -1)
        if [ -n "$key_length" ]; then
            if [ "$key_length" -ge 2048 ]; then
                pass_check "TLS certificate key length is sufficient ($key_length bits)"
            else
                fail_check "TLS certificate key length is too weak ($key_length bits, minimum: 2048)"
            fi
        else
            warn_check "Could not determine TLS certificate key length"
        fi
    else
        warn_check "TLS certificate not configured or file not found"
    fi
    
    add_check
    if [ -n "$TLS_KEY_FILE" ] && [ -f "$TLS_KEY_FILE" ]; then
        key_perms=$(stat -c "%a" "$TLS_KEY_FILE" 2>/dev/null || stat -f "%A" "$TLS_KEY_FILE" 2>/dev/null)
        if [ "$key_perms" = "600" ] || [ "$key_perms" = "0600" ]; then
            pass_check "TLS private key has correct permissions ($key_perms)"
        else
            fail_check "TLS private key has incorrect permissions ($key_perms), should be 600"
        fi
    else
        warn_check "TLS private key not configured or file not found"
    fi
}

# 4. 配置文件安全检查
check_configuration_security() {
    log_header "4. Configuration Files Security Check"
    
    # 检查是否有硬编码的密码
    add_check
    log_info "Scanning for hardcoded passwords..."
    
    password_patterns=(
        "password.*=.*['\"][^'\"]*['\"]"
        "secret.*=.*['\"][^'\"]*['\"]"
        "key.*=.*['\"][^'\"]*['\"]"
        "token.*=.*['\"][^'\"]*['\"]"
    )
    
    hardcoded_found=false
    for pattern in "${password_patterns[@]}"; do
        if grep -r -i -E "$pattern" config/ --include="*.yaml" --include="*.yml" >/dev/null 2>&1; then
            if ! grep -r -i -E "$pattern" config/ --include="*.yaml" --include="*.yml" | grep -q '\$'; then
                hardcoded_found=true
                break
            fi
        fi
    done
    
    if [ "$hardcoded_found" = true ]; then
        fail_check "Hardcoded passwords/secrets found in configuration files"
        log_info "Use environment variables instead: \${VARIABLE_NAME}"
    else
        pass_check "No hardcoded passwords found in configuration files"
    fi
    
    # 检查配置文件YAML语法
    add_check
    log_info "Validating YAML configuration syntax..."
    
    yaml_valid=true
    for yaml_file in $(find config/ -name "*.yaml" -o -name "*.yml"); do
        if ! python3 -c "import yaml; yaml.safe_load(open('$yaml_file'))" 2>/dev/null; then
            yaml_valid=false
            log_error "Invalid YAML syntax in $yaml_file"
        fi
    done
    
    if [ "$yaml_valid" = true ]; then
        pass_check "All YAML configuration files have valid syntax"
    else
        fail_check "Some YAML configuration files have syntax errors"
    fi
}

# 5. 网络安全检查
check_network_security() {
    log_header "5. Network Security Check"
    
    # 检查开放端口
    add_check
    log_info "Checking for open ports..."
    
    if command -v netstat >/dev/null 2>&1; then
        open_ports=$(netstat -tuln 2>/dev/null | grep LISTEN | wc -l)
        if [ "$open_ports" -gt 10 ]; then
            warn_check "Many ports are open ($open_ports), review for security"
        else
            pass_check "Reasonable number of open ports ($open_ports)"
        fi
    else
        warn_check "netstat not available, cannot check open ports"
    fi
    
    # 检查防火墙状态
    add_check
    if command -v ufw >/dev/null 2>&1; then
        if ufw status | grep -q "Status: active"; then
            pass_check "UFW firewall is active"
        else
            warn_check "UFW firewall is not active"
        fi
    elif command -v firewall-cmd >/dev/null 2>&1; then
        if firewall-cmd --state 2>/dev/null | grep -q "running"; then
            pass_check "Firewalld is running"
        else
            warn_check "Firewalld is not running"
        fi
    else
        warn_check "No firewall management tool detected"
    fi
}

# 6. Python依赖安全检查
check_python_dependencies() {
    log_header "6. Python Dependencies Security Check"
    
    add_check
    if [ -f "requirements.txt" ]; then
        log_info "Checking for known vulnerabilities in Python packages..."
        
        # 使用pip-audit检查漏洞（如果可用）
        if command -v pip-audit >/dev/null 2>&1; then
            if pip-audit -r requirements.txt --format=json >/dev/null 2>&1; then
                pass_check "No known vulnerabilities found in Python dependencies"
            else
                warn_check "Potential vulnerabilities found in Python dependencies"
                log_info "Run 'pip-audit -r requirements.txt' for details"
            fi
        else
            # 使用safety检查（如果可用）
            if command -v safety >/dev/null 2>&1; then
                if safety check -r requirements.txt >/dev/null 2>&1; then
                    pass_check "No known vulnerabilities found in Python dependencies"
                else
                    warn_check "Potential vulnerabilities found in Python dependencies"
                    log_info "Run 'safety check -r requirements.txt' for details"
                fi
            else
                warn_check "No dependency security scanner available (install pip-audit or safety)"
            fi
        fi
    else
        warn_check "requirements.txt not found"
    fi
    
    # 检查过时的包
    add_check
    log_info "Checking for outdated packages..."
    
    if command -v pip >/dev/null 2>&1; then
        outdated_count=$(pip list --outdated --format=json 2>/dev/null | python3 -c "import sys, json; print(len(json.load(sys.stdin)))" 2>/dev/null || echo "0")
        if [ "$outdated_count" -eq 0 ]; then
            pass_check "All Python packages are up to date"
        elif [ "$outdated_count" -lt 5 ]; then
            warn_check "Some Python packages are outdated ($outdated_count packages)"
        else
            warn_check "Many Python packages are outdated ($outdated_count packages)"
        fi
    else
        warn_check "pip not available, cannot check package versions"
    fi
}

# 7. 日志和监控安全检查
check_logging_security() {
    log_header "7. Logging and Monitoring Security Check"
    
    # 检查日志目录权限
    add_check
    if [ -d "logs" ]; then
        log_perms=$(stat -c "%a" logs 2>/dev/null || stat -f "%A" logs 2>/dev/null)
        if [ "$log_perms" = "750" ] || [ "$log_perms" = "0750" ] || [ "$log_perms" = "755" ] || [ "$log_perms" = "0755" ]; then
            pass_check "Logs directory has appropriate permissions ($log_perms)"
        else
            warn_check "Logs directory permissions may be too permissive ($log_perms)"
        fi
    else
        warn_check "Logs directory not found"
    fi
    
    # 检查日志配置
    add_check
    if [ -f "config/logging.yaml" ]; then
        if grep -q "level.*DEBUG" config/logging.yaml; then
            warn_check "Debug logging is enabled (may log sensitive information)"
        else
            pass_check "Appropriate logging level configured"
        fi
    else
        warn_check "Logging configuration file not found"
    fi
    
    # 检查审计日志配置
    add_check
    if grep -q "audit.*enabled.*true" config/security/api_permissions.yaml 2>/dev/null; then
        pass_check "Security audit logging is enabled"
    else
        warn_check "Security audit logging is not explicitly enabled"
    fi
}

# 8. 运行时安全检查
check_runtime_security() {
    log_header "8. Runtime Security Check"
    
    # 检查运行用户
    add_check
    current_user=$(whoami)
    if [ "$current_user" = "root" ]; then
        fail_check "Running as root user (security risk)"
        log_info "Create a dedicated user: sudo useradd -r -s /bin/false mt5user"
    else
        pass_check "Not running as root user (current: $current_user)"
    fi
    
    # 检查进程权限
    add_check
    if [ -f "/proc/version" ]; then
        # Linux系统
        if grep -q "SELINUX\|AppArmor" /proc/version 2>/dev/null; then
            pass_check "Security framework detected in kernel"
        else
            warn_check "No additional security framework detected (consider SELinux/AppArmor)"
        fi
    else
        warn_check "Cannot detect security framework"
    fi
    
    # 检查临时文件安全
    add_check
    if [ -w "/tmp" ]; then
        tmp_perms=$(stat -c "%a" /tmp 2>/dev/null || stat -f "%A" /tmp 2>/dev/null)
        if echo "$tmp_perms" | grep -q "1"; then
            pass_check "Temporary directory has sticky bit set"
        else
            warn_check "Temporary directory may not have sticky bit set"
        fi
    else
        warn_check "Cannot check temporary directory permissions"
    fi
}

# 9. 加密功能测试
test_encryption_functionality() {
    log_header "9. Encryption Functionality Test"
    
    add_check
    log_info "Testing encryption/decryption functionality..."
    
    python3 -c "
import os
import sys
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

try:
    # Test basic encryption
    key = Fernet.generate_key()
    f = Fernet(key)
    
    test_data = b'Test encryption data'
    encrypted = f.encrypt(test_data)
    decrypted = f.decrypt(encrypted)
    
    if decrypted == test_data:
        print('SUCCESS: Basic encryption test passed')
    else:
        print('ERROR: Basic encryption test failed')
        sys.exit(1)
    
    # Test key derivation
    password = b'test_password'
    salt = os.urandom(16)
    
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    
    derived_key = kdf.derive(password)
    if len(derived_key) == 32:
        print('SUCCESS: Key derivation test passed')
    else:
        print('ERROR: Key derivation test failed')
        sys.exit(1)
        
    sys.exit(0)
    
except ImportError as e:
    print(f'ERROR: Missing cryptography library: {e}')
    sys.exit(1)
except Exception as e:
    print(f'ERROR: Encryption test failed: {e}')
    sys.exit(1)
"
    
    if [ $? -eq 0 ]; then
        pass_check "Encryption functionality test passed"
    else
        fail_check "Encryption functionality test failed"
    fi
}

# 10. API安全测试
test_api_security() {
    log_header "10. API Security Test"
    
    # 检查是否有API在运行
    add_check
    if curl -s -f http://localhost:8000/health >/dev/null 2>&1; then
        pass_check "API health endpoint is accessible"
        
        # 测试未认证访问
        add_check
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/accounts 2>/dev/null | grep -q "401\|403"; then
            pass_check "Protected endpoints require authentication"
        else
            warn_check "Protected endpoints may not require authentication"
        fi
        
        # 测试安全头
        add_check
        security_headers=$(curl -s -I http://localhost:8000/health 2>/dev/null | grep -i "x-content-type-options\|x-frame-options\|x-xss-protection")
        if [ -n "$security_headers" ]; then
            pass_check "Security headers are present"
        else
            warn_check "Security headers may be missing"
        fi
        
    else
        warn_check "API is not running, skipping API security tests"
    fi
}

# 生成安全报告
generate_security_report() {
    log_header "Security Assessment Report"
    
    # 计算安全评分
    if [ $TOTAL_CHECKS -gt 0 ]; then
        security_percentage=$((SECURITY_SCORE * 100 / TOTAL_CHECKS))
    else
        security_percentage=0
    fi
    
    echo ""
    log_info "==================== SUMMARY ===================="
    log_info "Total Security Checks: $TOTAL_CHECKS"
    log_info "Passed Checks: $SECURITY_SCORE"
    log_info "Failed Checks: $FAILED_CHECKS"
    log_info "Warnings: $WARNINGS"
    log_info "Security Score: $security_percentage%"
    echo ""
    
    # 安全等级评估
    if [ $security_percentage -ge 90 ]; then
        log_success "🔒 EXCELLENT: Your system has excellent security"
        security_level="EXCELLENT"
    elif [ $security_percentage -ge 80 ]; then
        log_success "🔐 GOOD: Your system has good security with minor issues"
        security_level="GOOD"
    elif [ $security_percentage -ge 70 ]; then
        log_warning "⚠️  MODERATE: Your system has moderate security, improvements needed"
        security_level="MODERATE"
    elif [ $security_percentage -ge 60 ]; then
        log_warning "🔓 WEAK: Your system has weak security, immediate action required"
        security_level="WEAK"
    else
        log_error "💀 CRITICAL: Your system has critical security issues"
        security_level="CRITICAL"
    fi
    
    echo ""
    log_info "==================== RECOMMENDATIONS ===================="
    
    if [ $FAILED_CHECKS -gt 0 ]; then
        log_error "Priority 1: Fix all failed security checks"
    fi
    
    if [ $WARNINGS -gt 0 ]; then
        log_warning "Priority 2: Address security warnings"
    fi
    
    if [ $security_percentage -lt 90 ]; then
        log_info "Priority 3: Review and enhance security configuration"
        log_info "- Enable all security features in production"
        log_info "- Implement regular security audits"
        log_info "- Set up automated security monitoring"
    fi
    
    echo ""
    log_info "For detailed security implementation guide, see:"
    log_info "- config/security/SECURITY_GUIDE.md"
    log_info "- config/README.md"
    
    # 写入报告文件
    {
        echo "# MT5 Trading System Security Assessment Report"
        echo "Generated: $(date)"
        echo ""
        echo "## Summary"
        echo "- Total Checks: $TOTAL_CHECKS"
        echo "- Passed: $SECURITY_SCORE"
        echo "- Failed: $FAILED_CHECKS" 
        echo "- Warnings: $WARNINGS"
        echo "- Security Score: $security_percentage%"
        echo "- Security Level: $security_level"
        echo ""
        echo "## Recommendations"
        echo "See console output for detailed recommendations."
    } > security_assessment_report.txt
    
    log_info "Report saved to: security_assessment_report.txt"
    
    # 退出码
    if [ $FAILED_CHECKS -gt 0 ]; then
        exit 1
    elif [ $security_percentage -lt 80 ]; then
        exit 2
    else
        exit 0
    fi
}

# 主函数
main() {
    log_header "MT5 Trading System Security Validation"
    log_info "Starting comprehensive security assessment..."
    echo ""
    
    # 执行所有安全检查
    check_environment_variables
    check_file_permissions
    check_tls_certificates
    check_configuration_security
    check_network_security
    check_python_dependencies
    check_logging_security
    check_runtime_security
    test_encryption_functionality
    test_api_security
    
    # 生成报告
    generate_security_report
}

# 运行主函数
main "$@"