"""
统一的MockJetStream实现
用于所有测试，确保接口一致性
"""
import asyncio
import time
from typing import Dict, List, Any, Optional, Callable
import logging

logger = logging.getLogger(__name__)


class MockJetStreamClient:
    """统一的模拟JetStream客户端"""
    
    def __init__(self):
        self.published_messages: List[Dict[str, Any]] = []
        self.subscriptions: List[Dict[str, Any]] = []
        self.subscribers: Dict[str, List[Callable]] = {}
        self.connected = True
        self.publish_delay = 0.0  # 模拟网络延迟
        
    async def publish(self,
                     subject_or_topic_arg=None,
                     data_arg=None,
                     subject: Optional[str] = None,
                     topic: Optional[str] = None,
                     data: Any = None,
                     headers: Optional[Dict] = None,
                     **kwargs) -> bool:
        """
        统一的发布接口
        支持多种调用方式：
        - publish(topic, data)  # 位置参数
        - publish(subject=topic, data=data)  # 关键字参数
        """
        # 🔥 关键修复：处理位置参数调用
        if subject_or_topic_arg is not None:
            # 位置参数调用：publish(topic, data)
            actual_topic = subject_or_topic_arg
            actual_data = data_arg
        else:
            # 关键字参数调用
            actual_topic = subject or topic
            actual_data = data

        if actual_topic is None:
            raise ValueError("必须提供subject或topic参数")
        
        # 模拟网络延迟
        if self.publish_delay > 0:
            await asyncio.sleep(self.publish_delay)
        
        # 记录发布的消息
        message = {
            'topic': actual_topic,
            'subject': actual_topic,
            'data': actual_data,
            'headers': headers or {},
            'timestamp': time.time(),
            'kwargs': kwargs
        }
        self.published_messages.append(message)

        logger.debug(f"📡 模拟发布消息: {actual_topic}")

        # 模拟消息分发给订阅者
        await self._distribute_message(actual_topic, actual_data)
        
        return True
    
    async def subscribe(self,
                       subject_or_topic=None,
                       callback=None,
                       subject: Optional[str] = None,
                       topic: Optional[str] = None,
                       **kwargs):
        """
        统一的订阅接口
        支持多种调用方式：
        - subscribe(topic, callback)
        - subscribe(subject=topic, callback=callback)
        """
        # 处理位置参数
        if subject_or_topic is not None and callback is None:
            # 可能是 subscribe(topic, callback) 的形式，但callback在kwargs中
            if len(kwargs) > 0 and 'callback' not in kwargs:
                # 第二个位置参数可能是callback
                callback = kwargs.pop('callback', None)

        # 处理主题参数
        actual_topic = subject_or_topic or subject or topic
        if actual_topic is None:
            raise ValueError("必须提供subject或topic参数")

        # 处理回调参数
        if callback is None:
            # 检查是否在kwargs中
            callback = kwargs.get('callback')
            if callback is None:
                raise ValueError("必须提供callback参数")
        
        # 记录订阅
        subscription = {
            'topic': actual_topic,
            'subject': actual_topic,
            'callback': callback,
            'timestamp': time.time(),
            'kwargs': kwargs
        }
        self.subscriptions.append(subscription)
        
        # 添加到订阅者列表
        if actual_topic not in self.subscribers:
            self.subscribers[actual_topic] = []
        self.subscribers[actual_topic].append(callback)
        
        logger.debug(f"📥 模拟订阅主题: {actual_topic}")
    
    async def _distribute_message(self, topic: str, data: Any):
        """分发消息给订阅者"""
        try:
            # 🔥 关键修复：只对交易信号主题进行TradeSignal转换
            signal_obj = None

            # 只对MT5.TRADES.*主题的消息尝试转换为TradeSignal
            logger.debug(f"🔍 检查信号转换条件: topic={topic}, data_type={type(data)}")
            if isinstance(data, dict):
                logger.debug(f"🔍 数据键: {list(data.keys())}")

            if topic.startswith('MT5.TRADES.') and isinstance(data, dict) and 'signal_id' in data and 'account_id' in data:
                try:
                    from src.messaging.message_types import TradeSignal, SignalType

                    # 处理signal_type枚举
                    signal_type_str = data.get('signal_type', '')
                    signal_type = None
                    if signal_type_str:
                        try:
                            signal_type = SignalType(signal_type_str)
                        except ValueError:
                            # 如果不是有效的枚举值，尝试按名称匹配
                            for st in SignalType:
                                if st.value == signal_type_str or st.name == signal_type_str:
                                    signal_type = st
                                    break

                    # 处理timestamp
                    timestamp = data.get('timestamp')
                    if isinstance(timestamp, str):
                        # ISO格式字符串
                        from datetime import datetime
                        timestamp = datetime.fromisoformat(timestamp)
                    elif isinstance(timestamp, (int, float)):
                        # Unix时间戳
                        from datetime import datetime
                        timestamp = datetime.fromtimestamp(timestamp)

                    # 检查是否是新的Pydantic格式
                    logger.debug(f"🔍 检查数据格式，键: {list(data.keys()) if isinstance(data, dict) else 'not dict'}")

                    # 检查是否包含Pydantic TradeSignal的必需字段
                    has_pydantic_fields = (
                        'type' in data and
                        'master_id' in data and
                        'data' in data and
                        'timestamp' in data and
                        'ticket' in data
                    )

                    if has_pydantic_fields:
                        # 新Pydantic格式，直接创建TradeSignal对象
                        logger.debug(f"🔧 检测到新Pydantic格式TradeSignal")
                        try:
                            # 清理数据，只保留TradeSignal需要的字段
                            clean_data = {
                                'type': data['type'],
                                'master_id': data['master_id'],
                                'slave_ids': data.get('slave_ids', []),
                                'ticket': data['ticket'],
                                'data': data['data'],
                                'timestamp': data['timestamp'],
                                'capture_latency_ms': data.get('capture_latency_ms'),
                                'processing_latency_ms': data.get('processing_latency_ms'),
                                'metadata': data.get('metadata', {})
                            }
                            signal_obj = TradeSignal(**clean_data)
                            logger.debug(f"✅ 成功创建新格式TradeSignal对象: {signal_obj.master_id}_{signal_obj.ticket}")
                        except Exception as e:
                            logger.error(f"创建新格式TradeSignal失败: {e}")
                            logger.error(f"数据内容: {data}")
                            signal_obj = None
                    else:
                        # 旧格式，尝试创建旧TradeSignal对象
                        logger.debug(f"🔧 尝试创建旧格式TradeSignal，signal_type={signal_type}, timestamp={timestamp}")
                        try:
                            signal_obj = TradeSignal(
                                signal_id=data.get('signal_id', ''),
                                account_id=data.get('account_id', ''),
                                symbol=data.get('symbol', ''),
                                action=data.get('action', ''),
                                volume=data.get('volume', 0.0),
                                price=data.get('price'),
                                sl=data.get('sl'),
                                tp=data.get('tp'),
                                signal_type=signal_type,
                                timestamp=timestamp,
                                metadata=data.get('metadata', {}),
                                position_id=data.get('position_id')
                            )
                            logger.debug(f"✅ 成功创建旧格式TradeSignal对象: {signal_obj.signal_id}")
                        except Exception as e:
                            logger.error(f"创建旧格式TradeSignal失败: {e}")
                            signal_obj = None

                except Exception as e:
                    logger.error(f"无法创建TradeSignal对象: {e}")
                    logger.error(f"原始数据: {data}")
                    logger.error(f"数据类型: {type(data)}")
                    if isinstance(data, dict):
                        logger.error(f"数据键: {list(data.keys())}")
                    import traceback
                    logger.error(f"异常堆栈: {traceback.format_exc()}")

            # 精确匹配
            if topic in self.subscribers:
                for callback in self.subscribers[topic]:
                    try:
                        # 对于交易信号使用TradeSignal对象，其他消息使用原始数据
                        callback_data = signal_obj if signal_obj else data
                        await callback(callback_data)
                    except Exception as e:
                        logger.error(f"订阅者回调异常: {e}")
                        logger.debug(f"主题: {topic}, 数据类型: {type(callback_data)}")

            # 通配符匹配
            for sub_topic, callbacks in self.subscribers.items():
                if self._match_wildcard(sub_topic, topic):
                    for callback in callbacks:
                        try:
                            # 🔥 关键修复：只对匹配的交易信号主题调用回调
                            if sub_topic == 'MT5.TRADES.*':
                                # 只处理MT5.TRADES.*主题的消息
                                if not topic.startswith('MT5.TRADES.'):
                                    logger.debug(f"跳过非交易信号主题: {topic}")
                                    continue

                                # 确保有有效的TradeSignal对象
                                if signal_obj is None:
                                    logger.warning(f"交易信号主题但无有效TradeSignal对象: {topic}")
                                    continue

                                callback_data = signal_obj
                            else:
                                # 其他主题使用原始数据
                                callback_data = data

                            await callback(callback_data)
                        except Exception as e:
                            logger.error(f"通配符订阅者回调异常: {e}")
                            logger.debug(f"主题: {topic}, 订阅: {sub_topic}, 数据类型: {type(callback_data)}")

        except Exception as e:
            logger.error(f"消息分发异常: {e}")
    
    def _match_wildcard(self, pattern: str, topic: str) -> bool:
        """通配符匹配"""
        if pattern.endswith('*'):
            prefix = pattern[:-1]
            return topic.startswith(prefix) and topic != pattern
        return False
    
    def get_published_count(self, topic_pattern: Optional[str] = None) -> int:
        """获取发布消息数量"""
        if topic_pattern is None:
            return len(self.published_messages)
        
        count = 0
        for msg in self.published_messages:
            topic = msg.get('topic', '')
            if topic_pattern in topic:
                count += 1
        return count
    
    def get_published_messages(self, topic_pattern: Optional[str] = None) -> List[Dict]:
        """获取发布的消息"""
        if topic_pattern is None:
            return self.published_messages.copy()
        
        filtered = []
        for msg in self.published_messages:
            topic = msg.get('topic', '')
            if topic_pattern in topic:
                filtered.append(msg)
        return filtered
    
    def clear_messages(self):
        """清空消息记录"""
        self.published_messages.clear()
    
    def set_publish_delay(self, delay: float):
        """设置发布延迟（用于测试网络延迟）"""
        self.publish_delay = delay
    
    def disconnect(self):
        """模拟断开连接"""
        self.connected = False
        self.subscribers.clear()
    
    def reconnect(self):
        """模拟重新连接"""
        self.connected = True
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.connected
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'published_count': len(self.published_messages),
            'subscription_count': len(self.subscriptions),
            'subscriber_count': sum(len(callbacks) for callbacks in self.subscribers.values()),
            'connected': self.connected,
            'publish_delay': self.publish_delay
        }


# 便捷函数
def create_mock_jetstream() -> MockJetStreamClient:
    """创建模拟JetStream客户端"""
    return MockJetStreamClient()


def create_mock_jetstream_with_delay(delay: float) -> MockJetStreamClient:
    """创建带延迟的模拟JetStream客户端"""
    client = MockJetStreamClient()
    client.set_publish_delay(delay)
    return client
