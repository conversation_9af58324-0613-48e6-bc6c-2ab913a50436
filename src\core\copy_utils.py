#!/usr/bin/env python3
"""
统一的跟单工具模块
整合所有跟单相关的工具函数，消除重复
基于配置驱动，不定义额外的枚举和数据结构
"""

import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timezone
# 处理导入问题 - 支持相对导入和绝对导入
try:
    from .config_manager import get_config_manager, get_config_value
except ImportError:
    # 如果相对导入失败，创建Mock对象
    from unittest.mock import Mock
    get_config_manager = Mock
    get_config_value = Mock

logger = logging.getLogger(__name__)


class VolumeCalculator:
    """统一的手数计算器"""
    
    @staticmethod
    def calculate_copy_volume(
        original_volume: float,
        relationship_config: Dict[str, Any],
        source_balance: Optional[float] = None,
        target_balance: Optional[float] = None
    ) -> float:
        """
        统一的手数计算方法
        整合所有现有的计算逻辑，基于配置驱动
        """
        try:
            # 获取基础配置 
            config_manager = get_config_manager()
            master_account = relationship_config.get('master_account')
            slave_account = relationship_config.get('slave_account')

            # 从配置文件读取 
            copy_ratio = relationship_config.get('copy_ratio')
            if copy_ratio is None and master_account and slave_account:
                copy_ratio = config_manager.get_copy_ratio(master_account, slave_account)

            limits = relationship_config.get('limits', {})
            risk_mgmt = relationship_config.get('risk_management', {})
            
            # 基础手数计算
            base_volume = original_volume * copy_ratio
            
            # 应用风险倍数 
            risk_multiplier = risk_mgmt.get('risk_multiplier')
            if risk_multiplier is None and master_account and slave_account:
                risk_multiplier = config_manager.get_risk_multiplier(master_account, slave_account)
            adjusted_volume = base_volume * risk_multiplier
            
            # 应用动态比例（如果配置了余额信息）
            if source_balance and target_balance and source_balance > 0:
                balance_mode = relationship_config.get('balance_mode', 'fixed')
                if balance_mode == 'proportional':
                    balance_ratio = target_balance / source_balance
                    adjusted_volume = original_volume * balance_ratio
            
            # 应用限制  
            min_volume = limits.get('min_volume_per_trade')
            max_volume = limits.get('max_volume_per_trade')

            # 如果配置中没有，从统一配置管理器获取
            if min_volume is None and slave_account:
                volume_limits = config_manager.get_volume_limits(slave_account)
                min_volume = volume_limits['min_volume']
            if max_volume is None and slave_account:
                volume_limits = config_manager.get_volume_limits(slave_account)
                max_volume = volume_limits['max_volume']

            final_volume = max(min_volume, min(adjusted_volume, max_volume))
            
            # 标准化到最小步长 
            round_to = relationship_config.get('round_to')
            if round_to is None:
                round_to = get_config_value('default_volume_round_to') or 0.01
            final_volume = round(final_volume / round_to) * round_to
            
            logger.debug(
                f"手数计算: 原始={original_volume}, 比例={copy_ratio}, "
                f"风险倍数={risk_multiplier}, 结果={final_volume}"
            )
            
            return final_volume
            
        except Exception as e:
            logger.error(f"手数计算失败: {e}")
            return relationship_config.get('limits', {}).get('min_volume_per_trade', 0.01)



class CopyProcessor:
    """统一的跟单策略处理器"""
    
    def __init__(self):
        self.volume_calculator = VolumeCalculator()
    
    def process_copy_signal(
        self,
        original_signal: Dict[str, Any],
        relationship_config: Dict[str, Any],
        source_balance: Optional[float] = None,
        target_balance: Optional[float] = None
    ) -> Optional[Dict[str, Any]]:
        """
        处理跟单信号
        基于配置驱动，支持所有跟单模式
        """
        try:
            # 检查关系是否启用
            if not relationship_config.get('enabled', True):
                logger.debug("跟单关系已禁用")
                return None
            
            # 检查品种过滤
            if not self._is_symbol_allowed(original_signal.get('symbol'), relationship_config):
                logger.debug(f"品种 {original_signal.get('symbol')} 不在允许列表中")
                return None
            
            # 检查时间限制
            if not self._is_time_allowed(relationship_config):
                logger.debug("当前时间不在允许的交易时间内")
                return None
            
            # 复制信号
            copy_signal = original_signal.copy()
            
            # 计算跟单手数
            original_volume = original_signal.get('volume', 0.01)
            copy_volume = self.volume_calculator.calculate_copy_volume(
                original_volume=original_volume,
                relationship_config=relationship_config,
                source_balance=source_balance,
                target_balance=target_balance
            )
            copy_signal['volume'] = copy_volume
            
            # 处理跟单模式
            copy_mode = relationship_config.get('copy_mode', 'forward')
            if copy_mode == 'reverse':
                copy_signal = self._apply_reverse_mode(copy_signal, relationship_config)
            elif copy_mode == 'bidirectional':
                # 双向跟单逻辑
                pass
            
            # 添加跟单标识
            copy_signal['is_copy_trade'] = True
            copy_signal['source_account'] = relationship_config.get('master_account')
            copy_signal['target_account'] = relationship_config.get('slave_account')
            copy_signal['copy_mode'] = copy_mode
            
            return copy_signal
            
        except Exception as e:
            logger.error(f"跟单信号处理失败: {e}")
            return None
    
    def _is_symbol_allowed(self, symbol: str, config: Dict[str, Any]) -> bool:
        """检查品种是否允许"""
        symbol_filter = config.get('symbol_filter', {})
        mode = symbol_filter.get('mode', 'all')
        
        if mode == 'all':
            return True
        elif mode == 'whitelist':
            return symbol in symbol_filter.get('symbols', [])
        elif mode == 'blacklist':
            return symbol not in symbol_filter.get('excluded_symbols', [])
        
        return True
    
    def _is_time_allowed(self, config: Dict[str, Any]) -> bool:
        """检查时间是否允许"""
        time_restrictions = config.get('time_restrictions', {})
        if not time_restrictions:
            return True

        # 检查活跃时间
        active_hours = time_restrictions.get('active_hours', {})
        if active_hours:
            if not self._is_within_active_hours(active_hours):
                return False

        # 检查活跃日期
        active_days = time_restrictions.get('active_days', [])
        if active_days:
            if not self._is_within_active_days(active_days):
                return False

        return True

    def _is_within_active_hours(self, active_hours: Dict[str, str]) -> bool:
        """检查是否在活跃时间内"""
        try:
            from datetime import datetime, timezone
            import pytz

            # 获取时区
            tz_name = active_hours.get('timezone', 'UTC')
            if tz_name == 'UTC':
                tz = timezone.utc
            else:
                tz = pytz.timezone(tz_name)

            # 获取当前时间
            current_time = datetime.now(tz).time()

            # 解析开始和结束时间
            start_str = active_hours.get('start', '00:00')
            end_str = active_hours.get('end', '24:00')

            # 处理24:00的特殊情况
            if end_str == '24:00':
                end_str = '23:59'

            start_time = datetime.strptime(start_str, '%H:%M').time()
            end_time = datetime.strptime(end_str, '%H:%M').time()

            # 检查时间范围
            if start_time <= end_time:
                # 正常时间范围 (如 08:00 - 18:00)
                return start_time <= current_time <= end_time
            else:
                # 跨日时间范围 (如 22:00 - 06:00)
                return current_time >= start_time or current_time <= end_time

        except Exception as e:
            logger.warning(f"时间检查异常: {e}")
            return True  # 异常时默认允许

    def _is_within_active_days(self, active_days: List[str]) -> bool:
        """检查是否在活跃日期内"""
        try:
            from datetime import datetime

            # 获取当前星期几
            current_day = datetime.now().strftime('%A').lower()

            # 标准化活跃日期列表
            normalized_days = [day.lower() for day in active_days]

            return current_day in normalized_days

        except Exception as e:
            logger.warning(f"日期检查异常: {e}")
            return True  # 异常时默认允许
    
    def _apply_reverse_mode(self, signal: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """应用反向模式"""
        # 反转交易方向
        action = signal.get('action', '').upper()
        if action == 'BUY':
            signal['action'] = 'SELL'
        elif action == 'SELL':
            signal['action'] = 'BUY'
        
        # 处理止损止盈反转
        risk_mgmt = config.get('risk_management', {})
        if risk_mgmt.get('reverse_sl_tp', False):
            sl = signal.get('sl')
            tp = signal.get('tp')
            if sl and tp:
                signal['sl'] = tp
                signal['tp'] = sl
        
        return signal


# ============================================================================
# 统一的配置验证器
# ============================================================================

class CopyConfigValidator:
    """跟单配置验证器"""
    
    @staticmethod
    def validate_relationship(config: Dict[str, Any]) -> List[str]:
        """验证跟单关系配置"""
        errors = []
        
        # 必需字段检查
        required_fields = ['master_account', 'slave_account']
        for field in required_fields:
            if not config.get(field):
                errors.append(f"缺少必需字段: {field}")
        
        # 跟单比例检查 - 使用配置管理器获取默认值
        copy_ratio = config.get('copy_ratio')
        if copy_ratio is None:
            # 从配置管理器获取默认值
            master_account = config.get('master_account')
            slave_account = config.get('slave_account')
            if master_account and slave_account:
                config_manager = get_config_manager()
                copy_ratio = config_manager.get_copy_ratio(master_account, slave_account)

        if copy_ratio is not None and (not isinstance(copy_ratio, (int, float)) or copy_ratio <= 0):
            errors.append("copy_ratio必须是正数")

        # 手数限制检查 - 从配置读取默认值
        limits = config.get('limits', {})
        min_vol = limits.get('min_volume_per_trade')
        max_vol = limits.get('max_volume_per_trade')

        # 如果没有配置，从统一配置管理器获取
        slave_account = config.get('slave_account')
        if (min_vol is None or max_vol is None) and slave_account:
            config_manager = get_config_manager()
            volume_limits = config_manager.get_volume_limits(slave_account)
            if min_vol is None:
                min_vol = volume_limits['min_volume']
            if max_vol is None:
                max_vol = volume_limits['max_volume']

        if min_vol is not None and max_vol is not None and min_vol >= max_vol:
            errors.append("最小手数必须小于最大手数")
        
        # 跟单模式检查
        copy_mode = config.get('copy_mode', 'forward')
        valid_modes = ['forward', 'reverse', 'bidirectional', 'disabled']
        if copy_mode not in valid_modes:
            errors.append(f"无效的跟单模式: {copy_mode}")
        
        return errors


# ============================================================================
# 统一的工具函数
# ============================================================================

def normalize_copy_mode(mode: str) -> str:
    """标准化跟单模式"""
    mode = mode.lower().strip()
    
    # 映射旧格式到新格式
    mode_mapping = {
        'regular': 'forward',
        'normal': 'forward',
        'same': 'forward',
        'opposite': 'reverse',
        'inverse': 'reverse',
        'both': 'bidirectional',
        'off': 'disabled',
        'none': 'disabled'
    }
    
    return mode_mapping.get(mode, mode)


def get_relationship_key(master_account: str, slave_account: str) -> str:
    """生成关系键"""
    return f"{master_account}_to_{slave_account}"


def is_valid_account_pair(master: str, slave: str) -> bool:
    """检查账户对是否有效"""
    if not master or not slave:
        return False
    
    if master == slave:
        return False
    
    return True


# ============================================================================
# 导出接口
# ============================================================================

__all__ = [
    'VolumeCalculator',
    'CopyProcessor', 
    'CopyConfigValidator',
    'normalize_copy_mode',
    'get_relationship_key',
    'is_valid_account_pair'
]
