# MT5跟单系统混合部署指南

## 概述

由于Docker容器无法直接访问宿主机的MT5终端程序，我们采用混合部署架构：

- **Docker容器**：运行NATS、Redis、系统协调器等基础设施
- **宿主机**：运行MT5客户端连接程序，通过网络连接到Docker服务

## 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    宿主机 (Windows)                          │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   MT5终端1      │  │   MT5终端2      │                   │
│  │   (ACC001)      │  │   (ACC002)      │                   │
│  └─────────────────┘  └─────────────────┘                   │
│           │                     │                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           MT5宿主机客户端                                │ │
│  │     (start_host_client.py)                             │ │
│  └─────────────────────────────────────────────────────────┘ │
│                           │                                 │
└───────────────────────────┼─────────────────────────────────┘
                            │ 网络连接 (localhost:4222, :6379)
┌───────────────────────────┼─────────────────────────────────┐
│                    Docker容器                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │    NATS     │  │    Redis    │  │    系统协调器        │   │
│  │   :4222     │  │   :6379     │  │   (控制平面)        │   │
│  └─────────────┘  └─────────────┘  └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 部署步骤

### 1. 启动Docker基础设施

```bash
# 进入docker目录
cd docker

# 启动基础设施服务（使用hybrid profile）
docker-compose --profile hybrid up -d redis nats pushgateway prometheus grafana redis-commander mt5-coordinator-only

# 检查服务状态
docker-compose ps
```

### 2. 验证服务连接

```bash
# 检查NATS服务
curl http://localhost:8222/healthz

# 检查Redis服务
redis-cli ping

# 检查端口监听
netstat -an | findstr ":4222"
netstat -an | findstr ":6379"
```

### 3. 配置环境变量

创建或更新 `.env` 文件：

```bash
# MT5账户密码
MT5_ACC001_PASSWORD=your_password_here
MT5_ACC002_PASSWORD=your_password_here

# 主机配置
HOST_ID=host-001
HOST_REGION=local
```

### 4. 启动宿主机MT5客户端

#### 方法1：使用混合部署脚本（推荐）

```cmd
# 一键启动完整混合部署（Docker + 宿主机客户端）
scripts\start_hybrid_deployment.bat
```

#### 方法2：分步启动

```cmd
# 先启动Docker基础设施
cd docker
docker-compose --profile hybrid up -d redis nats pushgateway prometheus grafana redis-commander mt5-coordinator-only

# 再启动宿主机客户端
cd ..
set PYTHONPATH=%CD%
python scripts\start_host_client.py --config config\host_client_config.yaml
```

### 5. 验证系统运行

#### 检查Docker日志

```bash
# 查看协调器日志
docker logs mt5-coordinator-infra

# 查看NATS日志
docker logs mt5-nats-infra

# 查看Redis日志
docker logs mt5-redis-infra
```

#### 检查宿主机客户端日志

客户端日志会显示在控制台和 `logs/mt5_host_client.log` 文件中。

#### 监控界面

- **NATS监控**: http://localhost:8222
- **Redis管理**: http://localhost:8081
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)

## 配置说明

### 宿主机客户端配置 (config/host_client_config.yaml)

```yaml
# 服务连接配置
services:
  nats:
    servers: ["nats://localhost:4222"]  # 连接到Docker暴露的端口
  redis:
    host: "localhost"
    port: 6379

# 账户配置引用（避免重复定义）
accounts:
  config_dir: "config/accounts"  # 引用现有账户配置目录

  # 账户角色映射
  role_mapping:
    ACC001: "master"    # 基于现有的 high_frequency_master 模板
    ACC002: "slave"     # 基于现有的 conservative_slave 模板

  # 启用的账户列表
  enabled_accounts:
    - "ACC001"
    - "ACC002"
```

**注意**: 账户的详细配置（登录信息、终端路径等）已在 `config/accounts/ACC001.yaml` 和 `config/accounts/ACC002.yaml` 中定义，宿主机客户端会自动加载这些配置。

### Docker基础设施配置

基础设施服务通过 `docker-compose-infrastructure.yml` 配置，包括：

- **NATS**: 消息队列，端口4222
- **Redis**: 数据存储，端口6379
- **协调器**: 系统控制平面
- **监控服务**: Prometheus、Grafana等

## 故障排除

### 1. 连接问题

**问题**: 宿主机客户端无法连接到Docker服务

**解决方案**:
```bash
# 检查Docker服务是否运行
docker ps

# 检查端口是否暴露
docker port mt5-nats-infra
docker port mt5-redis-infra

# 检查防火墙设置
netsh advfirewall firewall show rule name="Docker"
```

### 2. MT5连接问题

**问题**: MT5终端无法连接或登录失败

**解决方案**:
```bash
# 检查MT5终端路径
dir "D:\metatrader5\v1-mt5\terminal64.exe"

# 检查环境变量
echo %MT5_ACC001_PASSWORD%

# 手动测试MT5连接
python test_mt5_connection.py
```

### 3. 账户注册问题

**问题**: 系统显示没有活跃账户

**解决方案**:
```bash
# 检查账户注册日志
grep "账户注册" logs/mt5_host_client.log

# 检查NATS消息流
docker exec mt5-nats-infra nats stream ls

# 检查Redis中的账户信息
redis-cli hgetall "mt5:accounts"
```

## 性能优化

### 1. 网络延迟优化

- 确保Docker和宿主机在同一网络
- 使用localhost而不是容器名连接
- 调整NATS和Redis连接池大小

### 2. MT5轮询优化

```yaml
# 在host_client_config.yaml中调整
performance:
  polling_interval: 0.1  # 100ms轮询间隔
  batch_size: 50         # 批处理大小
  max_concurrent_operations: 10  # 最大并发操作
```

### 3. 资源限制

```yaml
# 在docker-compose-infrastructure.yml中添加
services:
  redis:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

## 扩展部署

### 多主机部署

1. 在每台主机上运行基础设施服务
2. 配置NATS集群
3. 在每台主机上运行对应的MT5客户端

### 高可用部署

1. 部署多个NATS节点
2. 配置Redis主从复制
3. 实现故障转移机制

## 安全考虑

1. **网络安全**: 使用防火墙限制端口访问
2. **认证**: 配置NATS和Redis认证
3. **加密**: 启用TLS连接
4. **访问控制**: 限制Docker容器权限
