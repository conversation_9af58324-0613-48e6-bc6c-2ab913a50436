#!/usr/bin/env python3
"""
运行所有纠正后的测试
验证接口修复是否成功
"""

import asyncio
import subprocess
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_test(test_file: str, timeout: int = 60) -> dict:
    """运行单个测试文件"""
    print(f"\n🧪 运行测试: {test_file}")
    print("=" * 60)
    
    start_time = time.time()
    try:
        result = subprocess.run(
            [sys.executable, test_file],
            cwd=str(project_root),
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        duration = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ 测试通过 ({duration:.1f}s)")
            return {
                'file': test_file,
                'status': 'PASS',
                'duration': duration,
                'output': result.stdout,
                'error': result.stderr
            }
        else:
            print(f"❌ 测试失败 ({duration:.1f}s)")
            print(f"返回码: {result.returncode}")
            if result.stderr:
                print(f"错误输出:\n{result.stderr}")
            return {
                'file': test_file,
                'status': 'FAIL',
                'duration': duration,
                'output': result.stdout,
                'error': result.stderr,
                'returncode': result.returncode
            }
            
    except subprocess.TimeoutExpired:
        print(f"⏰ 测试超时 ({timeout}s)")
        return {
            'file': test_file,
            'status': 'TIMEOUT',
            'duration': timeout,
            'output': '',
            'error': f'Test timed out after {timeout} seconds'
        }
    except Exception as e:
        print(f"💥 测试异常: {e}")
        return {
            'file': test_file,
            'status': 'ERROR',
            'duration': time.time() - start_time,
            'output': '',
            'error': str(e)
        }

def main():
    """主函数"""
    print("🚀 开始运行所有纠正后的测试...")
    
    # 定义测试文件列表
    test_files = [
        # test_flow 目录中的测试
        "tests/test_flow/test_real_four_layer_architecture.py",
        "tests/test_flow/test_real_component_integration.py",
        "tests/test_flow/test_end_to_end_message_flow.py",
        "tests/test_flow/test_rpc_communication.py",
        "tests/test_flow/test_multi_account_concurrent.py",
        "tests/test_flow/test_fault_recovery.py",
        
        # 增强测试文件
        "tests/test_enhanced_end_to_end.py",
        "tests/test_enhanced_multi_account.py", 
        "tests/test_enhanced_fault_recovery.py",
        "tests/test_performance_benchmark.py",
    ]
    
    results = []
    total_start_time = time.time()
    
    for test_file in test_files:
        if Path(test_file).exists():
            result = run_test(test_file, timeout=90)
            results.append(result)
        else:
            print(f"⚠️ 测试文件不存在: {test_file}")
            results.append({
                'file': test_file,
                'status': 'NOT_FOUND',
                'duration': 0,
                'output': '',
                'error': 'File not found'
            })
    
    total_duration = time.time() - total_start_time
    
    # 统计结果
    passed = sum(1 for r in results if r['status'] == 'PASS')
    failed = sum(1 for r in results if r['status'] == 'FAIL')
    timeout = sum(1 for r in results if r['status'] == 'TIMEOUT')
    error = sum(1 for r in results if r['status'] == 'ERROR')
    not_found = sum(1 for r in results if r['status'] == 'NOT_FOUND')
    
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    print(f"总测试数: {len(results)}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"⏰ 超时: {timeout}")
    print(f"💥 异常: {error}")
    print(f"📁 未找到: {not_found}")
    print(f"⏱️ 总耗时: {total_duration:.1f}s")
    print(f"📈 成功率: {(passed / len(results) * 100):.1f}%")
    
    print("\n📋 详细结果:")
    for result in results:
        status_icon = {
            'PASS': '✅',
            'FAIL': '❌', 
            'TIMEOUT': '⏰',
            'ERROR': '💥',
            'NOT_FOUND': '📁'
        }.get(result['status'], '❓')
        
        print(f"  {status_icon} {result['file']} ({result['duration']:.1f}s)")
        
        if result['status'] == 'FAIL' and result.get('error'):
            # 只显示错误的前几行
            error_lines = result['error'].split('\n')[:3]
            for line in error_lines:
                if line.strip():
                    print(f"      {line}")
    
    # 显示接口修复状态
    print("\n🔧 接口修复状态:")
    interface_issues = []
    
    for result in results:
        if result['status'] in ['FAIL', 'ERROR']:
            error_text = result.get('error', '') + result.get('output', '')
            
            if 'encode_message' in error_text:
                interface_issues.append(f"  ❌ {result['file']}: encode_message 接口问题")
            elif 'decode_message' in error_text:
                interface_issues.append(f"  ❌ {result['file']}: decode_message 接口问题")
            elif 'TradeSignal' in error_text and 'validation' in error_text:
                interface_issues.append(f"  ❌ {result['file']}: TradeSignal 字段问题")
            elif 'PositionSignalData' in error_text:
                interface_issues.append(f"  ❌ {result['file']}: PositionSignalData 导入问题")
    
    if interface_issues:
        print("  发现接口问题:")
        for issue in interface_issues:
            print(issue)
    else:
        print("  ✅ 未发现接口相关问题")
    
    # 推荐下一步行动
    print("\n🎯 推荐下一步行动:")
    if passed == len(results):
        print("  🎉 所有测试都通过了！系统状态良好。")
    elif passed >= len(results) * 0.8:
        print("  👍 大部分测试通过，系统基本稳定。")
        print("  🔍 建议检查失败的测试，可能是环境或配置问题。")
    elif passed >= len(results) * 0.5:
        print("  ⚠️ 约一半测试通过，需要进一步调试。")
        print("  🔧 重点检查接口使用和依赖关系。")
    else:
        print("  🚨 大部分测试失败，需要系统性修复。")
        print("  🛠️ 建议逐个修复接口问题，从基础组件开始。")
    
    # 返回成功率作为退出码
    success_rate = passed / len(results)
    if success_rate >= 0.8:
        return 0  # 成功
    elif success_rate >= 0.5:
        return 1  # 部分成功
    else:
        return 2  # 失败

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
