#!/usr/bin/env python3
"""
Protobuf vs JSON性能对比测试
分析在当前MT5系统中使用二进制序列化的效果
"""

import asyncio
import json
import time
import sys
import statistics
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.messaging.message_codec import MessageCodec
    from src.messaging.protobuf_codec import ProtobufCodec, ProtobufConfig
    from src.messaging.message_types import TradeSignal
    
    COMPONENTS_AVAILABLE = True
    print("✅ 性能测试组件导入成功")
    
except ImportError as e:
    print(f"❌ 导入组件失败: {e}")
    sys.exit(1)


class PerformanceTestSuite:
    """性能测试套件"""
    
    def __init__(self):
        self.json_codec = MessageCodec()
        self.protobuf_codec = ProtobufCodec(ProtobufConfig(
            compression_enabled=True,
            cache_enabled=True
        ))
        
        # 测试数据
        self.test_signals = self._generate_test_signals()
        print(f"生成了 {len(self.test_signals)} 个测试信号")
    
    def _generate_test_signals(self) -> List[Dict[str, Any]]:
        """生成测试交易信号"""
        signals = []
        
        symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD']
        actions = ['BUY', 'SELL', 'CLOSE']
        
        for i in range(1000):  # 生成1000个信号
            signal = {
                'signal_id': f'SIG_{i:06d}',
                'account_id': f'ACC_{(i % 10) + 1:03d}',
                'symbol': symbols[i % len(symbols)],
                'action': actions[i % len(actions)],
                'volume': round(0.01 + (i % 100) * 0.01, 2),
                'price': 1.1000 + (i % 1000) * 0.0001,
                'timestamp': int(time.time() * 1000) + i,
                'ticket': 1000000 + i,
                'stop_loss': 1.0900 + (i % 500) * 0.0001,
                'take_profit': 1.1100 + (i % 500) * 0.0001,
                'comment': f'Test signal {i} with some additional data',
                'magic_number': 12345 + (i % 100)
            }
            signals.append(signal)
        
        return signals
    
    def test_encoding_performance(self, iterations: int = 100) -> Dict[str, Any]:
        """测试编码性能"""
        print(f"\n🔧 测试编码性能 ({iterations} 次迭代)...")
        
        # JSON编码测试
        json_times = []
        json_sizes = []
        
        for _ in range(iterations):
            start_time = time.perf_counter()
            
            for signal in self.test_signals:
                encoded = self.json_codec.encode(signal)
                json_sizes.append(len(encoded))
            
            json_times.append(time.perf_counter() - start_time)
        
        # Protobuf编码测试
        protobuf_times = []
        protobuf_sizes = []
        
        for _ in range(iterations):
            start_time = time.perf_counter()
            
            for signal in self.test_signals:
                encoded = self.protobuf_codec.encode_trade_signal(signal)
                protobuf_sizes.append(len(encoded))
            
            protobuf_times.append(time.perf_counter() - start_time)
        
        # 计算统计数据
        json_avg_time = statistics.mean(json_times)
        protobuf_avg_time = statistics.mean(protobuf_times)
        
        json_avg_size = statistics.mean(json_sizes)
        protobuf_avg_size = statistics.mean(protobuf_sizes)
        
        # 性能提升计算
        time_improvement = ((json_avg_time - protobuf_avg_time) / json_avg_time) * 100
        size_improvement = ((json_avg_size - protobuf_avg_size) / json_avg_size) * 100
        
        results = {
            'json': {
                'avg_time_seconds': json_avg_time,
                'avg_size_bytes': json_avg_size,
                'total_size_kb': sum(json_sizes) / 1024,
                'throughput_msgs_per_sec': len(self.test_signals) / json_avg_time
            },
            'protobuf': {
                'avg_time_seconds': protobuf_avg_time,
                'avg_size_bytes': protobuf_avg_size,
                'total_size_kb': sum(protobuf_sizes) / 1024,
                'throughput_msgs_per_sec': len(self.test_signals) / protobuf_avg_time
            },
            'improvement': {
                'time_percent': time_improvement,
                'size_percent': size_improvement,
                'throughput_multiplier': (len(self.test_signals) / protobuf_avg_time) / (len(self.test_signals) / json_avg_time)
            }
        }
        
        print(f"  📊 JSON编码: {json_avg_time:.4f}s, {json_avg_size:.0f} bytes/msg")
        print(f"  📊 Protobuf编码: {protobuf_avg_time:.4f}s, {protobuf_avg_size:.0f} bytes/msg")
        print(f"  🚀 时间提升: {time_improvement:+.1f}%, 大小提升: {size_improvement:+.1f}%")
        
        return results
    
    def test_decoding_performance(self, iterations: int = 100) -> Dict[str, Any]:
        """测试解码性能"""
        print(f"\n🔍 测试解码性能 ({iterations} 次迭代)...")
        
        # 预编码测试数据
        json_encoded = [self.json_codec.encode(signal) for signal in self.test_signals]
        protobuf_encoded = [self.protobuf_codec.encode_trade_signal(signal) for signal in self.test_signals]
        
        # JSON解码测试
        json_times = []
        
        for _ in range(iterations):
            start_time = time.perf_counter()
            
            for encoded in json_encoded:
                decoded = self.json_codec.decode(encoded)
            
            json_times.append(time.perf_counter() - start_time)
        
        # Protobuf解码测试
        protobuf_times = []
        
        for _ in range(iterations):
            start_time = time.perf_counter()
            
            for encoded in protobuf_encoded:
                decoded = self.protobuf_codec.decode_trade_signal(encoded)
            
            protobuf_times.append(time.perf_counter() - start_time)
        
        # 计算统计数据
        json_avg_time = statistics.mean(json_times)
        protobuf_avg_time = statistics.mean(protobuf_times)
        
        time_improvement = ((json_avg_time - protobuf_avg_time) / json_avg_time) * 100
        
        results = {
            'json': {
                'avg_time_seconds': json_avg_time,
                'throughput_msgs_per_sec': len(self.test_signals) / json_avg_time
            },
            'protobuf': {
                'avg_time_seconds': protobuf_avg_time,
                'throughput_msgs_per_sec': len(self.test_signals) / protobuf_avg_time
            },
            'improvement': {
                'time_percent': time_improvement,
                'throughput_multiplier': (len(self.test_signals) / protobuf_avg_time) / (len(self.test_signals) / json_avg_time)
            }
        }
        
        print(f"  📊 JSON解码: {json_avg_time:.4f}s")
        print(f"  📊 Protobuf解码: {protobuf_avg_time:.4f}s")
        print(f"  🚀 时间提升: {time_improvement:+.1f}%")
        
        return results
    
    def test_batch_performance(self, batch_sizes: List[int] = [10, 50, 100, 500]) -> Dict[str, Any]:
        """测试批量处理性能"""
        print(f"\n📦 测试批量处理性能...")
        
        results = {}
        
        for batch_size in batch_sizes:
            print(f"  测试批量大小: {batch_size}")
            
            # 创建批量数据
            batch_signals = self.test_signals[:batch_size]
            
            # JSON批量编码
            start_time = time.perf_counter()
            json_batch_encoded = self.json_codec.encode({"signals": batch_signals})
            json_batch_time = time.perf_counter() - start_time
            
            # Protobuf批量编码
            start_time = time.perf_counter()
            protobuf_batch_encoded = self.protobuf_codec.encode_batch_signals(batch_signals)
            protobuf_batch_time = time.perf_counter() - start_time
            
            # JSON批量解码
            start_time = time.perf_counter()
            json_batch_decoded = self.json_codec.decode(json_batch_encoded)
            json_decode_time = time.perf_counter() - start_time
            
            # Protobuf批量解码
            start_time = time.perf_counter()
            protobuf_batch_decoded = self.protobuf_codec.decode_batch_signals(protobuf_batch_encoded)
            protobuf_decode_time = time.perf_counter() - start_time
            
            # 计算性能指标
            json_total_time = json_batch_time + json_decode_time
            protobuf_total_time = protobuf_batch_time + protobuf_decode_time
            
            time_improvement = ((json_total_time - protobuf_total_time) / json_total_time) * 100
            size_improvement = ((len(json_batch_encoded) - len(protobuf_batch_encoded)) / len(json_batch_encoded)) * 100
            
            results[batch_size] = {
                'json': {
                    'encode_time': json_batch_time,
                    'decode_time': json_decode_time,
                    'total_time': json_total_time,
                    'size_bytes': len(json_batch_encoded)
                },
                'protobuf': {
                    'encode_time': protobuf_batch_time,
                    'decode_time': protobuf_decode_time,
                    'total_time': protobuf_total_time,
                    'size_bytes': len(protobuf_batch_encoded)
                },
                'improvement': {
                    'time_percent': time_improvement,
                    'size_percent': size_improvement
                }
            }
            
            print(f"    JSON: {json_total_time:.4f}s, {len(json_batch_encoded)} bytes")
            print(f"    Protobuf: {protobuf_total_time:.4f}s, {len(protobuf_batch_encoded)} bytes")
            print(f"    提升: {time_improvement:+.1f}% 时间, {size_improvement:+.1f}% 大小")
        
        return results
    
    def test_cache_performance(self, iterations: int = 50) -> Dict[str, Any]:
        """测试缓存性能"""
        print(f"\n💾 测试缓存性能 ({iterations} 次迭代)...")
        
        # 使用相同的数据多次解码以测试缓存效果
        test_signal = self.test_signals[0]
        encoded_signal = self.protobuf_codec.encode_trade_signal(test_signal)
        
        # 第一次解码（缓存未命中）
        start_time = time.perf_counter()
        for _ in range(iterations):
            decoded = self.protobuf_codec.decode_trade_signal(encoded_signal)
        first_run_time = time.perf_counter() - start_time
        
        # 第二次解码（缓存命中）
        start_time = time.perf_counter()
        for _ in range(iterations):
            decoded = self.protobuf_codec.decode_trade_signal(encoded_signal)
        second_run_time = time.perf_counter() - start_time
        
        cache_improvement = ((first_run_time - second_run_time) / first_run_time) * 100
        
        # 获取缓存统计
        cache_stats = self.protobuf_codec.get_cache_stats()
        cache_health = self.protobuf_codec.get_cache_health()
        
        results = {
            'first_run_time': first_run_time,
            'second_run_time': second_run_time,
            'cache_improvement_percent': cache_improvement,
            'cache_stats': cache_stats,
            'cache_health': cache_health
        }
        
        print(f"  📊 首次运行: {first_run_time:.4f}s")
        print(f"  📊 缓存运行: {second_run_time:.4f}s")
        print(f"  🚀 缓存提升: {cache_improvement:+.1f}%")
        if cache_stats:
            print(f"  💾 缓存命中率: {cache_stats['hit_rate_percent']:.1f}%")
        
        return results
    
    def analyze_system_impact(self, encoding_results: Dict, decoding_results: Dict) -> Dict[str, Any]:
        """分析对当前系统的影响"""
        print(f"\n🎯 分析对当前MT5系统的影响...")
        
        # 假设系统参数
        daily_messages = 100000  # 每日消息量
        peak_messages_per_second = 100  # 峰值消息/秒
        network_bandwidth_mbps = 100  # 网络带宽
        
        # 计算每日节省
        json_daily_size_mb = (encoding_results['json']['avg_size_bytes'] * daily_messages) / (1024 * 1024)
        protobuf_daily_size_mb = (encoding_results['protobuf']['avg_size_bytes'] * daily_messages) / (1024 * 1024)
        daily_bandwidth_saved_mb = json_daily_size_mb - protobuf_daily_size_mb
        
        # 计算峰值处理能力
        json_peak_capacity = encoding_results['json']['throughput_msgs_per_sec']
        protobuf_peak_capacity = encoding_results['protobuf']['throughput_msgs_per_sec']
        
        # 延迟影响
        json_msg_latency_ms = (encoding_results['json']['avg_time_seconds'] + decoding_results['json']['avg_time_seconds']) * 1000
        protobuf_msg_latency_ms = (encoding_results['protobuf']['avg_time_seconds'] + decoding_results['protobuf']['avg_time_seconds']) * 1000
        latency_reduction_ms = json_msg_latency_ms - protobuf_msg_latency_ms
        
        analysis = {
            'bandwidth_impact': {
                'daily_json_size_mb': json_daily_size_mb,
                'daily_protobuf_size_mb': protobuf_daily_size_mb,
                'daily_savings_mb': daily_bandwidth_saved_mb,
                'daily_savings_percent': (daily_bandwidth_saved_mb / json_daily_size_mb) * 100
            },
            'performance_impact': {
                'json_peak_capacity': json_peak_capacity,
                'protobuf_peak_capacity': protobuf_peak_capacity,
                'capacity_improvement_percent': ((protobuf_peak_capacity - json_peak_capacity) / json_peak_capacity) * 100
            },
            'latency_impact': {
                'json_latency_ms': json_msg_latency_ms,
                'protobuf_latency_ms': protobuf_msg_latency_ms,
                'latency_reduction_ms': latency_reduction_ms,
                'latency_reduction_percent': (latency_reduction_ms / json_msg_latency_ms) * 100
            },
            'recommendation': self._generate_recommendation(encoding_results, decoding_results)
        }
        
        print(f"  📊 每日带宽节省: {daily_bandwidth_saved_mb:.1f} MB ({(daily_bandwidth_saved_mb / json_daily_size_mb) * 100:.1f}%)")
        print(f"  📊 峰值处理能力提升: {((protobuf_peak_capacity - json_peak_capacity) / json_peak_capacity) * 100:+.1f}%")
        print(f"  📊 消息延迟减少: {latency_reduction_ms:.2f} ms ({(latency_reduction_ms / json_msg_latency_ms) * 100:.1f}%)")
        
        return analysis
    
    def _generate_recommendation(self, encoding_results: Dict, decoding_results: Dict) -> Dict[str, Any]:
        """生成使用建议"""
        time_improvement = encoding_results['improvement']['time_percent']
        size_improvement = encoding_results['improvement']['size_percent']
        
        if time_improvement > 20 and size_improvement > 30:
            recommendation = "强烈推荐"
            reason = "显著的性能和带宽优势"
        elif time_improvement > 10 and size_improvement > 20:
            recommendation = "推荐"
            reason = "明显的性能和带宽优势"
        elif time_improvement > 0 and size_improvement > 10:
            recommendation = "考虑使用"
            reason = "有一定的优势，但需要权衡实现复杂度"
        else:
            recommendation = "不推荐"
            reason = "优势不明显，不值得增加复杂度"
        
        return {
            'level': recommendation,
            'reason': reason,
            'considerations': [
                "需要安装protobuf依赖",
                "需要编译.proto文件",
                "增加了系统复杂度",
                "需要处理向后兼容性"
            ]
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有性能测试"""
        print("🚀 开始Protobuf vs JSON性能对比测试...")
        
        results = {}
        
        # 编码性能测试
        results['encoding'] = self.test_encoding_performance()
        
        # 解码性能测试
        results['decoding'] = self.test_decoding_performance()
        
        # 批量处理性能测试
        results['batch'] = self.test_batch_performance()
        
        # 缓存性能测试
        results['cache'] = self.test_cache_performance()
        
        # 系统影响分析
        results['system_impact'] = self.analyze_system_impact(
            results['encoding'], 
            results['decoding']
        )
        
        return results


async def main():
    """主函数"""
    if not COMPONENTS_AVAILABLE:
        print("❌ 性能测试组件不可用，无法运行测试")
        return False
    
    test_suite = PerformanceTestSuite()
    
    try:
        results = await test_suite.run_all_tests()
        
        print(f"\n🎉 性能测试完成!")
        print(f"\n📋 总结:")
        
        recommendation = results['system_impact']['recommendation']
        print(f"  推荐级别: {recommendation['level']}")
        print(f"  推荐理由: {recommendation['reason']}")
        
        print(f"\n⚠️ 注意事项:")
        for consideration in recommendation['considerations']:
            print(f"  - {consideration}")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
