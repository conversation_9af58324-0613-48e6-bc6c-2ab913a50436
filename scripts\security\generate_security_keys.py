#!/usr/bin/env python3
"""
MT5 Trading System Security Keys Generator
安全密钥生成工具
"""

import os
import secrets
import string
import hashlib
from pathlib import Path

def generate_secure_key(length=32, include_special=True):
    """生成安全的随机密钥"""
    characters = string.ascii_letters + string.digits
    if include_special:
        characters += "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    return ''.join(secrets.choice(characters) for _ in range(length))

def generate_jwt_secret():
    """生成JWT密钥"""
    return secrets.token_urlsafe(32)

def generate_encryption_key():
    """生成加密密钥"""
    return secrets.token_hex(32)  # 64字符的十六进制密钥

def generate_api_key():
    """生成API密钥"""
    return secrets.token_urlsafe(24)

def main():
    """主函数"""
    print("🔐 MT5 Trading System - 安全密钥生成器")
    print("=" * 50)
    
    # 生成各种密钥
    keys = {
        'JWT_SECRET': generate_jwt_secret(),
        'MASTER_ENCRYPTION_KEY': generate_encryption_key(),
        'DB_ENCRYPTION_KEY': generate_encryption_key(),
        'CONFIG_ENCRYPTION_KEY': generate_encryption_key(),
        'SESSION_SECRET': generate_secure_key(32),
        'PASSWORD_SALT': generate_secure_key(16),
        'ADMIN_API_KEY': generate_api_key(),
        'API_SECRET_KEY': generate_secure_key(32),
        'REDIS_PASSWORD': generate_secure_key(24),
        'DB_PASSWORD': generate_secure_key(20),
        'GRAFANA_ADMIN_PASSWORD': generate_secure_key(16)
    }
    
    print("\n🔑 生成的安全密钥:")
    print("-" * 50)
    
    for key_name, key_value in keys.items():
        print(f"{key_name}={key_value}")
    
    # 创建.env文件
    env_file = Path(".env")
    if env_file.exists():
        print(f"\n⚠️  .env文件已存在")
        response = input("是否覆盖现有的.env文件? (y/N): ")
        if response.lower() != 'y':
            print("❌ 取消操作")
            return
    
    print(f"\n📝 正在创建.env文件...")
    
    # 从.env.example读取模板
    env_example = Path(".env.example")
    if env_example.exists():
        with open(env_example, 'r', encoding='utf-8') as f:
            template = f.read()
        
        # 替换模板中的占位符
        content = template
        for key_name, key_value in keys.items():
            # 查找并替换对应的占位符
            if key_name == 'JWT_SECRET':
                content = content.replace('your_jwt_secret_key_minimum_32_characters_long_for_security', key_value)
            elif key_name == 'MASTER_ENCRYPTION_KEY':
                content = content.replace('your_master_encryption_key_minimum_32_characters_long_for_security', key_value)
            elif key_name == 'DB_ENCRYPTION_KEY':
                content = content.replace('your_database_encryption_key_minimum_32_characters_long_for_security', key_value)
            elif key_name == 'CONFIG_ENCRYPTION_KEY':
                content = content.replace('your_config_encryption_key_minimum_32_characters_long_for_security', key_value)
            elif key_name == 'SESSION_SECRET':
                content = content.replace('your_session_secret_key_minimum_32_characters', key_value)
            elif key_name == 'PASSWORD_SALT':
                content = content.replace('your_unique_password_salt_minimum_16_characters', key_value)
            elif key_name == 'ADMIN_API_KEY':
                content = content.replace('your_admin_api_key_with_full_permissions', key_value)
            elif key_name == 'API_SECRET_KEY':
                content = content.replace('your_secret_key_here', key_value)
            elif key_name == 'REDIS_PASSWORD':
                content = content.replace('your_secure_redis_password', key_value)
            elif key_name == 'DB_PASSWORD':
                content = content.replace('your_secure_database_password', key_value)
            elif key_name == 'GRAFANA_ADMIN_PASSWORD':
                content = content.replace('your_grafana_admin_password', key_value)
    else:
        # 如果没有模板，创建基本的.env文件
        content = "# MT5 Trading System Environment Variables\n"
        content += "# 自动生成的安全密钥\n\n"
        
        for key_name, key_value in keys.items():
            content += f"{key_name}={key_value}\n"
    
    # 写入.env文件
    with open(".env", 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ .env文件创建成功!")
    
    # 设置文件权限
    try:
        os.chmod(".env", 0o600)
        print("✅ 文件权限设置为600 (仅所有者可读写)")
    except Exception as e:
        print(f"⚠️  无法设置文件权限: {e}")
    
    print("\n🔒 安全提示:")
    print("- 请确保不要将.env文件提交到版本控制系统")
    print("- 定期更新这些密钥以增强安全性")
    print("- 在生产环境中，考虑使用密钥管理服务")
    print("- 备份这些密钥到安全的位置")
    
    print(f"\n📄 密钥已保存到: {env_file.absolute()}")

if __name__ == "__main__":
    main()