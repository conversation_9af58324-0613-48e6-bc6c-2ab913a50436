// MT5分布式交易系统 - Protocol Buffers消息定义
// 高性能二进制序列化格式，替代JSON提升性能

syntax = "proto3";

package mt5.trading;

// 交易信号消息
message TradeSignal {
    string signal_id = 1;           // 信号ID
    string account_id = 2;          // 账户ID
    string symbol = 3;              // 交易品种
    string action = 4;              // 操作类型 (BUY/SELL/CLOSE)
    double volume = 5;              // 交易量
    double price = 6;               // 价格
    int64 timestamp = 7;            // 时间戳
    int64 ticket = 8;               // 订单号
    
    // 可选字段
    double stop_loss = 9;           // 止损价格
    double take_profit = 10;        // 止盈价格
    string comment = 11;            // 备注
    int32 magic_number = 12;        // 魔术数字
    
    // 路由信息
    RouteInfo route_info = 13;
    
    // 优先级
    Priority priority = 14;
    
    // 批量信号支持
    repeated TradeSignal batch_signals = 15;
}

// 路由信息
message RouteInfo {
    string source_host = 1;         // 源主机
    string target_host = 2;         // 目标主机
    string source_account = 3;      // 源账户
    repeated string target_accounts = 4; // 目标账户列表
    int64 route_timestamp = 5;      // 路由时间戳
    string route_id = 6;            // 路由ID
    bool is_forwarded = 7;          // 是否已转发
}

// 优先级枚举
enum Priority {
    LOW = 0;                        // 低优先级 - 状态更新
    NORMAL = 1;                     // 普通优先级 - 修改信号
    HIGH = 2;                       // 高优先级 - 开仓信号
    CRITICAL = 3;                   // 关键优先级 - 平仓信号
}

// 账户状态消息
message AccountStatus {
    string account_id = 1;          // 账户ID
    string host_id = 2;             // 主机ID
    bool is_online = 3;             // 是否在线
    double balance = 4;             // 余额
    double equity = 5;              // 净值
    double margin = 6;              // 保证金
    double free_margin = 7;         // 可用保证金
    int64 timestamp = 8;            // 时间戳
    
    // 连接信息
    string server = 9;              // 服务器
    int32 login = 10;               // 登录号
    string currency = 11;           // 货币
    
    // 统计信息
    int32 open_positions = 12;      // 持仓数量
    int32 pending_orders = 13;      // 挂单数量
}

// 系统控制消息
message SystemControl {
    string command = 1;             // 命令类型
    string host_id = 2;             // 主机ID
    map<string, string> parameters = 3; // 参数
    int64 timestamp = 4;            // 时间戳
    string request_id = 5;          // 请求ID
}

// 心跳消息
message Heartbeat {
    string host_id = 1;             // 主机ID
    string ip_address = 2;          // IP地址
    int64 timestamp = 3;            // 时间戳
    double load_factor = 4;         // 负载因子
    repeated string mt5_accounts = 5; // MT5账户列表
    
    // 性能指标
    double cpu_usage = 6;           // CPU使用率
    double memory_usage = 7;        // 内存使用率
    double disk_usage = 8;          // 磁盘使用率
    
    // 网络延迟
    map<string, double> latency_to_hosts = 9; // 到其他主机的延迟
}

// 错误消息
message ErrorMessage {
    string error_id = 1;            // 错误ID
    string error_code = 2;          // 错误代码
    string error_message = 3;       // 错误消息
    string component = 4;           // 组件名称
    string host_id = 5;             // 主机ID
    int64 timestamp = 6;            // 时间戳
    
    // 上下文信息
    map<string, string> context = 7; // 错误上下文
    string stack_trace = 8;         // 堆栈跟踪
}

// 性能指标消息
message PerformanceMetrics {
    string host_id = 1;             // 主机ID
    string component = 2;           // 组件名称
    int64 timestamp = 3;            // 时间戳
    
    // 延迟指标
    double avg_latency_ms = 4;      // 平均延迟
    double p95_latency_ms = 5;      // P95延迟
    double p99_latency_ms = 6;      // P99延迟
    
    // 吞吐量指标
    double messages_per_second = 7;  // 消息/秒
    double bytes_per_second = 8;     // 字节/秒
    
    // 错误率
    double error_rate = 9;          // 错误率
    int64 total_messages = 10;      // 总消息数
    int64 error_messages = 11;      // 错误消息数
    
    // 资源使用
    double cpu_usage = 12;          // CPU使用率
    double memory_usage_mb = 13;    // 内存使用(MB)
}

// 批量消息容器
message BatchMessage {
    repeated TradeSignal signals = 1;        // 交易信号批次
    repeated AccountStatus statuses = 2;     // 账户状态批次
    repeated SystemControl controls = 3;     // 系统控制批次
    repeated Heartbeat heartbeats = 4;       // 心跳批次
    repeated ErrorMessage errors = 5;        // 错误消息批次
    repeated PerformanceMetrics metrics = 6; // 性能指标批次
    
    // 批次信息
    string batch_id = 7;            // 批次ID
    int64 batch_timestamp = 8;      // 批次时间戳
    int32 batch_size = 9;           // 批次大小
    bool is_compressed = 10;        // 是否压缩
}
