# 智能路由简化配置
# 统一message_router智能路由所需的核心配置

# 本地账户列表 (从 accounts 目录动态加载)
local_accounts: []  # 自动从 config/accounts/*.yaml 加载

# 远程账户映射 (从关系配置动态加载)
remote_account_mappings: {}  # 自动从 config/relationships/copy_trading.yaml 加载

# 路由策略配置
routing_strategies:
  trade_signals: "zero_copy_first"      # 交易信号优先零拷贝
  account_queries: "cache_first"        # 账户查询优先缓存
  system_commands: "reliable_queue"     # 系统命令使用可靠队列

# 开发测试配置
development:
  # 测试账户映射
  test_accounts:
    "TEST_LOCAL_001": "{{ host_id }}"
    "TEST_REMOTE_001": "test-remote-host"
  
  # 调试开关
  debug_routing: false
  log_decisions: false