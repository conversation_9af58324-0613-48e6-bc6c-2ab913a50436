<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5分布式统计套利系统架构图</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #0a0e1a;
            color: #e0e0e0;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1, h2 {
            text-align: center;
            color: #4a9eff;
        }
        
        .architecture-diagram {
            background: #151923;
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(74, 158, 255, 0.1);
        }
        
        .layer {
            margin: 20px 0;
            padding: 20px;
            background: #1e2633;
            border-radius: 8px;
            border: 2px solid #2a3f5f;
            position: relative;
        }
        
        .layer-title {
            font-size: 18px;
            font-weight: bold;
            color: #4a9eff;
            margin-bottom: 15px;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .service-box {
            background: #243447;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #3a5579;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .service-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 158, 255, 0.3);
            border-color: #4a9eff;
        }
        
        .service-name {
            font-weight: bold;
            color: #6fb3ff;
            margin-bottom: 5px;
        }
        
        .service-details {
            font-size: 12px;
            color: #8a96a8;
        }
        
        .connection-line {
            position: absolute;
            background: linear-gradient(to bottom, #4a9eff, #3a8ff3);
            width: 2px;
            height: 40px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .metrics-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .metric-card {
            background: #1e2633;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #2a3f5f;
            text-align: center;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #4a9eff;
            margin: 10px 0;
        }
        
        .metric-label {
            color: #8a96a8;
            font-size: 14px;
        }
        
        .flow-diagram {
            margin: 30px 0;
            padding: 20px;
            background: #1e2633;
            border-radius: 8px;
        }
        
        .flow-step {
            display: inline-block;
            background: #243447;
            padding: 10px 20px;
            border-radius: 20px;
            margin: 5px;
            color: #6fb3ff;
            position: relative;
        }
        
        .flow-arrow {
            display: inline-block;
            margin: 0 10px;
            color: #4a9eff;
        }
        
        .geographic-distribution {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .region-box {
            background: #243447;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #3a5579;
        }
        
        .region-name {
            font-size: 20px;
            color: #4a9eff;
            margin-bottom: 10px;
        }
        
        .region-details {
            color: #8a96a8;
        }
        
        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .advantage-card {
            background: #1e2633;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4a9eff;
        }
        
        .advantage-title {
            color: #6fb3ff;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .advantage-content {
            color: #8a96a8;
            line-height: 1.6;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .active-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #4aff88;
            border-radius: 50%;
            margin-right: 5px;
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MT5分布式统计套利系统架构</h1>
        
        <div class="architecture-diagram">
            <h2>系统架构分层设计</h2>
            
            <!-- 接入层 -->
            <div class="layer">
                <div class="layer-title">
                    <span class="active-indicator"></span>
                    接入层 (API Gateway Layer)
                </div>
                <div class="services-grid">
                    <div class="service-box">
                        <div class="service-name">Kong Gateway</div>
                        <div class="service-details">
                            • API路由管理<br>
                            • 限流熔断<br>
                            • 认证授权
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">Nginx Plus</div>
                        <div class="service-details">
                            • 负载均衡<br>
                            • SSL终止<br>
                            • 请求缓存
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">Service Mesh</div>
                        <div class="service-details">
                            • 服务发现<br>
                            • 流量管理<br>
                            • 监控追踪
                        </div>
                    </div>
                </div>
                <div class="connection-line" style="bottom: -40px;"></div>
            </div>
            
            <!-- 业务服务层 -->
            <div class="layer">
                <div class="layer-title">
                    <span class="active-indicator"></span>
                    业务服务层 (Business Service Layer)
                </div>
                <div class="services-grid">
                    <div class="service-box">
                        <div class="service-name">信号生成集群</div>
                        <div class="service-details">
                            • 10个实例<br>
                            • 统计套利引擎<br>
                            • 并行计算
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">风控服务</div>
                        <div class="service-details">
                            • 5个实例<br>
                            • 实时风险计算<br>
                            • 限额管理
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">订单路由</div>
                        <div class="service-details">
                            • 8个实例<br>
                            • 智能路由<br>
                            • 延迟优化
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">账户管理</div>
                        <div class="service-details">
                            • 分组管理<br>
                            • 动态分配<br>
                            • 状态同步
                        </div>
                    </div>
                </div>
                <div class="connection-line" style="bottom: -40px;"></div>
            </div>
            
            <!-- 执行层 -->
            <div class="layer">
                <div class="layer-title">
                    <span class="active-indicator"></span>
                    执行层 (Execution Layer) - 地理分布式部署
                </div>
                <div class="geographic-distribution">
                    <div class="region-box">
                        <div class="region-name">亚太区域</div>
                        <div class="region-details">
                            MT5执行器: 20个<br>
                            覆盖账户: 400+<br>
                            延迟: <10ms
                        </div>
                    </div>
                    <div class="region-box">
                        <div class="region-name">欧洲区域</div>
                        <div class="region-details">
                            MT5执行器: 15个<br>
                            覆盖账户: 300+<br>
                            延迟: <15ms
                        </div>
                    </div>
                    <div class="region-box">
                        <div class="region-name">美洲区域</div>
                        <div class="region-details">
                            MT5执行器: 15个<br>
                            覆盖账户: 300+<br>
                            延迟: <20ms
                        </div>
                    </div>
                </div>
                <div class="connection-line" style="bottom: -40px;"></div>
            </div>
            
            <!-- 消息总线层 -->
            <div class="layer">
                <div class="layer-title">
                    <span class="active-indicator"></span>
                    消息总线层 (Message Bus Layer)
                </div>
                <div class="services-grid">
                    <div class="service-box">
                        <div class="service-name">Kafka集群</div>
                        <div class="service-details">
                            • 5节点集群<br>
                            • 百万级TPS<br>
                            • 持久化存储
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">Redis Streams</div>
                        <div class="service-details">
                            • 实时流处理<br>
                            • 低延迟传输<br>
                            • 消息去重
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">gRPC通道</div>
                        <div class="service-details">
                            • 服务间RPC<br>
                            • 双向流<br>
                            • 高性能序列化
                        </div>
                    </div>
                </div>
                <div class="connection-line" style="bottom: -40px;"></div>
            </div>
            
            <!-- 数据层 -->
            <div class="layer">
                <div class="layer-title">
                    <span class="active-indicator"></span>
                    数据层 (Data Layer)
                </div>
                <div class="services-grid">
                    <div class="service-box">
                        <div class="service-name">Redis集群</div>
                        <div class="service-details">
                            • 6节点主从<br>
                            • 热数据缓存<br>
                            • 毫秒级响应
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">InfluxDB</div>
                        <div class="service-details">
                            • 时序数据<br>
                            • 高压缩比<br>
                            • 快速聚合
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">PostgreSQL</div>
                        <div class="service-details">
                            • 业务数据<br>
                            • ACID保证<br>
                            • 主从复制
                        </div>
                    </div>
                    <div class="service-box">
                        <div class="service-name">S3存储</div>
                        <div class="service-details">
                            • 历史数据<br>
                            • 日志归档<br>
                            • 备份恢复
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 数据流程图 -->
        <div class="flow-diagram">
            <h2>信号处理流程</h2>
            <div style="text-align: center; padding: 20px;">
                <span class="flow-step">市场数据</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">信号生成</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">风险检查</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">账户分配</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">并行执行</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">结果确认</span>
            </div>
        </div>
        
        <!-- 性能指标 -->
        <div class="metrics-section">
            <div class="metric-card">
                <div class="metric-label">信号延迟</div>
                <div class="metric-value">&lt;50ms</div>
                <div class="metric-label">从数据到信号生成</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">执行延迟</div>
                <div class="metric-value">&lt;100ms</div>
                <div class="metric-label">从信号到订单执行</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">并发账户</div>
                <div class="metric-value">1000+</div>
                <div class="metric-label">同时管理账户数</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">系统可用性</div>
                <div class="metric-value">99.99%</div>
                <div class="metric-label">年度正常运行时间</div>
            </div>
        </div>
        
        <!-- 架构优势 -->
        <h2>分布式架构优势</h2>
        <div class="advantages-grid">
            <div class="advantage-card">
                <div class="advantage-title">高性能</div>
                <div class="advantage-content">
                    • 水平扩展能力：可根据负载动态增加节点<br>
                    • 并行处理：多节点同时处理不同任务<br>
                    • 缓存优化：多级缓存减少计算延迟<br>
                    • 异步架构：非阻塞IO提升吞吐量
                </div>
            </div>
            <div class="advantage-card">
                <div class="advantage-title">高可用</div>
                <div class="advantage-content">
                    • 故障隔离：单点故障不影响整体<br>
                    • 自动故障转移：检测到故障自动切换<br>
                    • 多副本部署：关键服务多副本冗余<br>
                    • 滚动更新：无停机升级
                </div>
            </div>
            <div class="advantage-card">
                <div class="advantage-title">低延迟</div>
                <div class="advantage-content">
                    • 地理分布：就近部署降低网络延迟<br>
                    • 连接池：复用连接减少握手时间<br>
                    • 批量处理：减少网络往返次数<br>
                    • 智能路由：选择最优执行路径
                </div>
            </div>
            <div class="advantage-card">
                <div class="advantage-title">易维护</div>
                <div class="advantage-content">
                    • 微服务架构：服务独立部署和升级<br>
                    • 容器化：标准化部署流程<br>
                    • 监控完善：全链路追踪和监控<br>
                    • 自动化运维：减少人工干预
                </div>
            </div>
        </div>
        
        <!-- 技术栈 -->
        <div class="architecture-diagram">
            <h2>核心技术栈</h2>
            <div class="services-grid">
                <div class="service-box">
                    <div class="service-name">编程语言</div>
                    <div class="service-details">
                        • Python 3.9+ (异步)<br>
                        • Go (高性能服务)<br>
                        • Rust (低延迟组件)
                    </div>
                </div>
                <div class="service-box">
                    <div class="service-name">消息队列</div>
                    <div class="service-details">
                        • Kafka (事件流)<br>
                        • Redis Streams<br>
                        • RabbitMQ (任务队列)
                    </div>
                </div>
                <div class="service-box">
                    <div class="service-name">存储系统</div>
                    <div class="service-details">
                        • Redis Cluster<br>
                        • InfluxDB<br>
                        • PostgreSQL
                    </div>
                </div>
                <div class="service-box">
                    <div class="service-name">容器编排</div>
                    <div class="service-details">
                        • Kubernetes<br>
                        • Docker<br>
                        • Helm Charts
                    </div>
                </div>
                <div class="service-box">
                    <div class="service-name">服务网格</div>
                    <div class="service-details">
                        • Istio<br>
                        • Consul<br>
                        • Linkerd
                    </div>
                </div>
                <div class="service-box">
                    <div class="service-name">监控系统</div>
                    <div class="service-details">
                        • Prometheus<br>
                        • Grafana<br>
                        • Jaeger
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>