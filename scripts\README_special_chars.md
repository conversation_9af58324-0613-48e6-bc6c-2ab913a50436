# 特殊字符清理工具使用说明

本目录包含两个用于清理文件中特殊字符的Python脚本。

## 脚本说明

### 1. `remove_special_chars.py` - 通用特殊字符清理工具

功能强大的通用工具，支持多种文件格式和自定义配置。

**特性:**
- 支持多种文件格式 (.py, .txt, .md, .json, .yaml, .js, .html, .css 等)
- 智能编码检测
- 可配置的字符过滤规则
- 自动备份功能
- 预览模式 (dry-run)
- 排除模式支持

**使用方法:**
```bash
# 基本用法 - 处理当前目录
python scripts/remove_special_chars.py .

# 处理指定目录
python scripts/remove_special_chars.py /path/to/directory

# 预览模式 - 不实际修改文件
python scripts/remove_special_chars.py . --dry-run

# 不创建备份文件
python scripts/remove_special_chars.py . --no-backup

# 不递归处理子目录
python scripts/remove_special_chars.py . --no-recursive

# 排除特定文件模式
python scripts/remove_special_chars.py . --exclude "*.log" "temp*" "__pycache__"

# 详细输出
python scripts/remove_special_chars.py . --verbose
```

### 2. `clean_project_files.py` - 项目专用清理工具

专门为MT5 Python项目设计的简化版本。

**特性:**
- 专注于Python项目文件 (.py, .md, .txt, .yaml, .json, .log)
- 自动跳过常见的临时目录 (__pycache__, .git, .venv 等)
- 保留中文字符支持
- 简单易用的交互式界面
- 自动备份为 .bak 文件

**使用方法:**
```bash
# 清理当前目录
python scripts/clean_project_files.py

# 清理指定目录
python scripts/clean_project_files.py /path/to/project
```

## 清理规则

### 保留的字符类型:
- **ASCII字符** (0-127): 基本英文字母、数字、标点符号
- **基本拉丁扩展** (128-255): 带重音的字母等
- **中文字符**: CJK统一汉字、全角字符
- **基本标点**: 空格、换行符、制表符等

### 移除的字符类型:
- **Emoji表情**: 🚀 😀 🎉 ✅ ❌ 等
- **特殊Unicode符号**: ★ ♦ ◆ ▲ ● 等
- **装饰性字符**: 各种花式符号和图标
- **不常用Unicode字符**: 超出基本范围的特殊字符

## 安全特性

### 备份机制:
- `remove_special_chars.py`: 创建 `.backup` 文件
- `clean_project_files.py`: 创建 `.bak` 文件
- 如果备份文件已存在，会自动添加数字后缀

### 编码处理:
- 自动检测文件编码
- 支持UTF-8、GBK、GB2312等常见编码
- 错误字符替换而非失败

### 安全检查:
- 只处理支持的文件类型
- 自动跳过二进制文件
- 跳过系统和临时目录

## 使用示例

### 示例1: 清理Python项目
```bash
cd /path/to/mt5-python
python scripts/clean_project_files.py
```

### 示例2: 预览清理效果
```bash
python scripts/remove_special_chars.py . --dry-run --verbose
```

### 示例3: 清理特定文件类型
```bash
# 只处理Python文件
find . -name "*.py" -exec python scripts/remove_special_chars.py {} \;
```

## 常见问题

### Q: 会不会误删重要字符？
A: 脚本采用保守策略，只移除明确的emoji和装饰性字符，保留所有功能性字符。

### Q: 如何恢复原文件？
A: 所有修改的文件都有备份：
- 通用工具: `.backup` 文件
- 项目工具: `.bak` 文件

### Q: 支持哪些编程语言？
A: 支持所有基于文本的文件格式，特别优化了Python项目。

### Q: 会影响代码功能吗？
A: 不会。脚本只移除注释和字符串中的装饰性字符，不影响代码逻辑。

## 注意事项

1. **运行前备份**: 虽然脚本会自动备份，建议先用Git提交当前状态
2. **测试环境**: 建议先在测试环境运行，确认效果后再用于生产环境
3. **编码问题**: 如果遇到编码问题，可以先转换文件为UTF-8编码
4. **大型项目**: 对于大型项目，建议使用 `--dry-run` 先预览效果

## 技术细节

### 字符检测算法:
1. 使用正则表达式快速识别emoji
2. 基于Unicode分类判断字符类型
3. 白名单机制确保安全字符不被误删

### 性能优化:
- 批量处理文件
- 智能跳过不需要处理的目录
- 内存友好的流式处理

### 错误处理:
- 优雅处理编码错误
- 详细的错误日志
- 失败时不影响其他文件处理
