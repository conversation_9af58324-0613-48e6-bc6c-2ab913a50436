#!/usr/bin/env python3
"""
MT5 分布式交易系统统一启动入口
支持生产环境部署的标准化启动器

使用方法:
  python main.py --config /app/config/system.yaml --host-id uk-lon-node-01
  python main.py --host-id uk-lon-node-02
  python main.py --distributed --host-id uk-lon-node-03
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 使用实际存在的协调器
from src.core.main_coordinator import MainCoordinator
from src.core.mt5_coordinator import MT5Coordinator
from src.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="MT5 分布式交易系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 启动增强模式（推荐）
  python main.py --config config/core/system.yaml --host-id uk-001
  
  # 启动分布式模式
  python main.py --distributed --host-id uk-lon-node-02
  
  # Docker 部署模式（如部署文档所示）
  python main.py --config /app/config/core/system.yaml --host-id uk-lon-node-01
        """
    )
    
    # 核心参数
    parser.add_argument(
        "--config", 
        default="config/core/system.yaml",
        help="配置文件路径 (默认: config/core/system.yaml)"
    )
    parser.add_argument(
        "--host-id", 
        required=True,
        help="主机ID，用于分布式部署时指定当前主机身份 (必需)"
    )
    
    # 模式选择
    parser.add_argument(
        "--distributed", 
        action="store_true",
        help="使用分布式启动器模式（可选，默认使用增强模式）"
    )
    
    # 可选参数
    parser.add_argument(
        "--no-coordinator", 
        action="store_true",
        help="禁用系统协调器"
    )
    parser.add_argument(
        "--masters", 
        nargs="*",
        help="指定主账户列表"
    )
    parser.add_argument(
        "--slaves", 
        nargs="*",
        help="指定从账户列表"
    )
    parser.add_argument(
        "--log-level", 
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="日志级别 (默认: INFO)"
    )
    
    return parser.parse_args()


async def run_enhanced_mode(args):
    """运行分布式MT5跟单交易系统"""
    try:
        logger.info(f"启动MT5跟单交易系统 - 主机: {args.host_id}")

        logger.info(f"🚀 使用主协调器架构启动系统")
        logger.info(f"已加载系统配置: {args.config}")
        logger.info(f"账户配置将由主协调器自动发现并加载")

        # 创建主协调器
        config_dir = str(Path(args.config).parent)
        coordinator = MainCoordinator(
            host_id=args.host_id,
            config_path=config_dir
        )

        # 设置信号处理
        import signal
        def signal_handler(signum, frame):
            logger.info("收到停止信号")
            asyncio.create_task(coordinator.stop())

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # 初始化协调器
        success = await coordinator.initialize(allow_partial_failure=True)
        if not success:
            logger.error("❌ 协调器初始化失败")
            return 1

        # 启动协调器
        await coordinator.start()
        logger.info("✅ 主协调器启动成功")
        logger.info("✅ 系统启动成功，等待停止信号...")

        # 等待停止信号
        try:
            while coordinator.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到键盘中断信号")
        finally:
            # 优雅关闭
            logger.info("🛑 开始优雅关闭系统...")
            await coordinator.stop()
            logger.info("🛑 系统已停止")
            
            await coordinator.stop()
            logger.info("🛑 统一协调器已停止")

        return 0

    except Exception as e:
        logger.error(f"ERROR: 启动失败: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return 1


async def run_distributed_mode(args):
    """运行分布式模式"""
    # 重新组装 sys.argv 以传递给 distributed_main
    original_argv = sys.argv.copy()
    
    try:
        # 构建新的命令行参数
        new_argv = ["distributed_launcher.py"]
        new_argv.extend(["--host-id", args.host_id])
        new_argv.extend(["--log-level", args.log_level])
        
        if args.config != "config/core/system.yaml":
            new_argv.extend(["--config", args.config])
        
        sys.argv = new_argv
        logger.info(f"STARTING: 启动分布式模式 - 主机: {args.host_id}")
        
        # 调用分布式启动器（同步调用）
        import subprocess
        result = subprocess.call([
            sys.executable, 
            "scripts/launchers/distributed_launcher.py",
            "--host-id", args.host_id
        ])
        return result
        
    finally:
        sys.argv = original_argv


async def main():
    """主函数"""
    try:
        args = parse_arguments()
        
        # 设置日志
        setup_logging({'level': args.log_level, 'format': 'json'})
        
        # 显示启动信息
        logger.info("=" * 60)
        logger.info("MT5 分布式交易系统")
        logger.info("=" * 60)
        logger.info(f"主机ID: {args.host_id}")
        logger.info(f"配置文件: {args.config}")
        logger.info(f"运行模式: {'分布式' if args.distributed else '增强模式'}")
        logger.info(f"日志级别: {args.log_level}")
        logger.info("=" * 60)
        
        # 验证配置文件存在
        config_path = Path(args.config)
        if not config_path.exists():
            logger.error(f"ERROR: 配置文件不存在: {config_path}")
            logger.info("TIP: 请确保配置文件路径正确")
            return 1
        
        # 设置环境变量（保持向后兼容）
        os.environ['MT5_HOST_ID'] = args.host_id
        
        # 根据模式启动
        if args.distributed:
            return await run_distributed_mode(args)
        else:
            await run_enhanced_mode(args)
            return 0
            
    except KeyboardInterrupt:
        logger.info("INTERRUPT: 收到中断信号，正在停止...")
        return 0
    except Exception as e:
        logger.error(f"ERROR: 启动失败: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
