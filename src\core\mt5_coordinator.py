#!/usr/bin/env python3
"""
MT5协调器 - 硬迁移兼容层
为了保持向后兼容性，此文件将旧的DistributedMT5Coordinator映射到新的UnifiedMT5Coordinator
硬迁移：零向后兼容，强制统一，SSOT

注意：此文件仅为迁移过渡期使用，最终将完全移除
"""
import asyncio
import logging
from typing import Dict, Any

from .main_coordinator import MainCoordinator

logger = logging.getLogger(__name__)

class DistributedMT5Coordinator(MainCoordinator):
    """
    分布式MT5协调器 - 迁移兼容层
    
    警告：此类已被弃用，仅用于向后兼容
    建议直接使用 MainCoordinator
    """
    
    def __init__(self, host_id: str, config_path: str):
        logger.warning("🚨 使用已弃用的DistributedMT5Coordinator，建议迁移到UnifiedMT5Coordinator")
        super().__init__(host_id, config_path)
    
    

# 向后兼容的导入别名
MT5Coordinator = DistributedMT5Coordinator

# 迁移提示函数
def create_unified_coordinator(host_id: str, config_path: str) -> MainCoordinator:
    """
    创建统一协调器的推荐方法
    
    使用此函数代替直接实例化DistributedMT5Coordinator
    """
    logger.info("✨ 创建统一协调器（推荐方式）")
    return MainCoordinator(host_id, config_path)

def get_migration_guide() -> str:
    """获取迁移指南"""
    return """
    🔄 MT5协调器迁移指南
    
    旧代码:
        from src.core.mt5_coordinator import DistributedMT5Coordinator
        coordinator = DistributedMT5Coordinator(host_id, config_path)
    
    新代码:
        from src.core.main_coordinator import MainCoordinator
        coordinator = MainCoordinator(host_id, config_path)
    
    或使用便利函数:
        from src.core.mt5_coordinator import create_main_coordinator
        coordinator = create_unified_coordinator(host_id, config_path)
    
    主要改进:
    ✅ 依赖注入容器管理组件生命周期
    ✅ 异步初始化模式，允许部分失败
    ✅ 健康检查和服务发现机制
    ✅ 支持组件热插拔和动态重启
    ✅ 零拷贝消息传递和直接信号路由
    ✅ 统一内存池管理
    """

if __name__ == "__main__":
    print(get_migration_guide())