#!/usr/bin/env python3
"""
真实组件集成测试
专注于验证真实组件的创建、初始化和基本功能
"""

import asyncio
import json
import os
import sys
import tempfile
import time
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入真实组件
try:
    from src.core.mt5_account_monitor import MT5AccountMonitor
    from src.core.mt5_account_executor import MT5AccountExecutor
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.core.mt5_request_handler import MT5RequestHandler
    from src.messaging.jetstream_client import JetStreamClient
    from src.messaging.nats_manager import NATSManager
    from src.core.config_manager import get_stream_config_manager

    REAL_COMPONENTS_AVAILABLE = True
    print("真实组件集成测试导入成功")

except ImportError as e:
    print(f"导入组件失败: {e}")
    sys.exit(1)


class RealComponentIntegrationTest:
    """真实组件集成测试类"""
    
    def __init__(self):
        self.test_data_dir = Path(tempfile.mkdtemp(prefix="real_integration_test_"))
        self.jetstream_clients = []
        self.components = []
        print(f"测试数据目录: {self.test_data_dir}")
    
    def cleanup(self):
        """清理测试资源"""
        import shutil
        try:
            # 停止所有组件
            for component in self.components:
                if hasattr(component, 'running'):
                    component.running = False
            
            # 断开所有JetStream连接
            for client in self.jetstream_clients:
                if hasattr(client, 'disconnect'):
                    asyncio.create_task(client.disconnect())
            
            shutil.rmtree(self.test_data_dir)
            print("🧹 真实组件集成测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理警告: {e}")
    
    async def test_jetstream_infrastructure(self):
        """测试JetStream基础设施"""
        print("\n🏗️ 测试JetStream基础设施...")
        
        try:
            # 创建JetStream客户端
            config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'INTEGRATION_TEST_STREAM',
                'subjects': ['INTEGRATION.TEST.>']
            }
            
            client = JetStreamClient(config)
            connected = await client.connect()
            
            if not connected:
                print("  ❌ JetStream连接失败")
                return False
            
            print("  ✅ JetStream连接成功")
            self.jetstream_clients.append(client)
            
            # 测试流配置管理器
            try:
                stream_config_manager = get_stream_config_manager()
                local_config = stream_config_manager.get_stream_config('local')
                print(f"  ✅ 流配置管理器工作正常: {local_config.get('name', 'N/A')}")
            except Exception as e:
                print(f"  ⚠️ 流配置管理器警告: {e}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ JetStream基础设施测试失败: {e}")
            return False
    
    async def test_monitor_component_lifecycle(self):
        """测试监控器组件生命周期"""
        print("\n👁️ 测试监控器组件生命周期...")
        
        try:
            # 创建JetStream客户端
            config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'MONITOR_TEST_STREAM'
            }
            
            jetstream_client = JetStreamClient(config)
            await jetstream_client.connect()
            self.jetstream_clients.append(jetstream_client)
            
            # 创建RPC客户端
            rpc_client = MT5RPCClient(jetstream_client)
            
            # 创建监控器
            monitor_config = {
                'login': '60001',
                'server': 'IntegrationTest-Server',
                'password': 'integration_test_password'
            }
            
            monitor = MT5AccountMonitor(
                account_id='INTEGRATION_MONITOR_001',
                account_config=monitor_config,
                event_publisher=jetstream_client,
                rpc_client=rpc_client,
                host_id='integration_test_host'
            )
            
            print("  ✅ 监控器创建成功")
            self.components.append(monitor)
            
            # 测试监控器属性
            assert monitor.account_id == 'INTEGRATION_MONITOR_001', "账户ID应该正确"
            assert not monitor.running, "初始状态应该是未运行"
            assert hasattr(monitor, 'start_monitoring'), "应该有start_monitoring方法"
            assert hasattr(monitor, 'stop_monitoring'), "应该有stop_monitoring方法"
            
            print("  ✅ 监控器属性验证通过")
            
            # 测试监控器状态变化
            monitor.running = True
            assert monitor.running, "运行状态应该可以设置"
            
            monitor.running = False
            assert not monitor.running, "运行状态应该可以重置"
            
            print("  ✅ 监控器状态管理正常")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 监控器组件生命周期测试失败: {e}")
            return False
    
    async def test_executor_component_lifecycle(self):
        """测试执行器组件生命周期"""
        print("\n⚡ 测试执行器组件生命周期...")
        
        try:
            # 创建JetStream客户端
            config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'EXECUTOR_TEST_STREAM'
            }
            
            jetstream_client = JetStreamClient(config)
            await jetstream_client.connect()
            self.jetstream_clients.append(jetstream_client)
            
            # 创建RPC客户端
            rpc_client = MT5RPCClient(jetstream_client)
            
            # 创建执行器
            executor_config = {
                'login': '60002',
                'server': 'IntegrationTest-Server',
                'password': 'integration_exec_password'
            }
            
            executor = MT5AccountExecutor(
                account_id='INTEGRATION_EXECUTOR_001',
                account_config=executor_config,
                command_subscriber=jetstream_client,
                result_publisher=jetstream_client,
                rpc_client=rpc_client
            )
            
            print("  ✅ 执行器创建成功")
            self.components.append(executor)
            
            # 测试执行器属性
            assert executor.account_id == 'INTEGRATION_EXECUTOR_001', "账户ID应该正确"
            assert not executor.running, "初始状态应该是未运行"
            assert hasattr(executor, 'start_executor'), "应该有start_executor方法"
            assert hasattr(executor, 'stop_executor'), "应该有stop_executor方法"
            
            print("  ✅ 执行器属性验证通过")
            
            # 测试执行器状态变化
            executor.running = True
            assert executor.running, "运行状态应该可以设置"
            
            executor.running = False
            assert not executor.running, "运行状态应该可以重置"
            
            print("  ✅ 执行器状态管理正常")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 执行器组件生命周期测试失败: {e}")
            return False
    
    async def test_rpc_component_lifecycle(self):
        """测试RPC组件生命周期"""
        print("\n📞 测试RPC组件生命周期...")
        
        try:
            # 创建JetStream客户端
            config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'RPC_LIFECYCLE_TEST_STREAM'
            }
            
            jetstream_client = JetStreamClient(config)
            await jetstream_client.connect()
            self.jetstream_clients.append(jetstream_client)
            
            # 创建RPC客户端
            rpc_client = MT5RPCClient(jetstream_client)
            
            print("  ✅ RPC客户端创建成功")
            self.components.append(rpc_client)
            
            # 测试RPC客户端属性
            assert hasattr(rpc_client, 'call_rpc'), "应该有call_rpc方法"
            assert hasattr(rpc_client, 'call_rpc_with_retry'), "应该有call_rpc_with_retry方法"
            assert hasattr(rpc_client, 'get_performance_metrics'),  "应该有get_performance_metrics方法"
            
            print("  ✅ RPC客户端属性验证通过")
            
            # 测试RPC客户端指标
            metrics = rpc_client.get_performance_metrics()
            assert 'overview' in metrics, "指标应该包含overview"
            assert 'total_calls' in metrics['overview'], "指标应该包含total_calls"
            assert 'successful_calls' in metrics['overview'], "指标应该包含successful_calls"
            assert 'failed_calls' in metrics['overview'], "指标应该包含failed_calls"
            
            print("  ✅ RPC客户端指标系统正常")
            
            # 创建模拟的MT5ProcessManager
            class MockMT5ProcessManager:
                def __init__(self):
                    self.running = True
                
                async def handle_request(self, request_data):
                    return {
                        "status": "success",
                        "data": "mock_response",
                        "timestamp": time.time()
                    }
            
            mock_manager = MockMT5ProcessManager()
            
            # 创建RPC请求处理器
            rpc_handler = MT5RequestHandler(jetstream_client, mock_manager)
            
            print("  ✅ RPC请求处理器创建成功")
            self.components.append(rpc_handler)
            
            # 测试RPC处理器属性
            assert hasattr(rpc_handler, 'start'), "应该有start方法"
            assert hasattr(rpc_handler, 'stop'), "应该有stop方法"
            assert not rpc_handler.running, "初始状态应该是未运行"
            
            print("  ✅ RPC请求处理器属性验证通过")
            
            return True
            
        except Exception as e:
            print(f"  ❌ RPC组件生命周期测试失败: {e}")
            return False
    
    async def test_component_integration(self):
        """测试组件集成"""
        print("\n🔗 测试组件集成...")
        
        try:
            # 创建共享的JetStream客户端
            config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'COMPONENT_INTEGRATION_STREAM'
            }
            
            jetstream_client = JetStreamClient(config)
            await jetstream_client.connect()
            self.jetstream_clients.append(jetstream_client)
            
            # 创建RPC客户端
            rpc_client = MT5RPCClient(jetstream_client)
            
            # 创建监控器
            monitor_config = {
                'login': '70001',
                'server': 'Integration-Server',
                'password': 'integration_password'
            }
            
            monitor = MT5AccountMonitor(
                account_id='INTEGRATION_MONITOR',
                account_config=monitor_config,
                event_publisher=jetstream_client,
                rpc_client=rpc_client,
                host_id='integration_host'
            )
            
            # 创建执行器
            executor_config = {
                'login': '70002',
                'server': 'Integration-Server',
                'password': 'integration_password'
            }
            
            executor = MT5AccountExecutor(
                account_id='INTEGRATION_EXECUTOR',
                account_config=executor_config,
                command_subscriber=jetstream_client,
                result_publisher=jetstream_client,
                rpc_client=rpc_client
            )
            
            print("  ✅ 监控器和执行器创建成功")
            self.components.extend([monitor, executor])
            
            # 测试组件间的依赖关系
            assert monitor.rpc_client is not None, "监控器应该有RPC客户端"
            assert executor.rpc_client is not None, "执行器应该有RPC客户端"
            assert monitor.event_publisher is not None, "监控器应该有事件发布器"
            assert executor.result_publisher is not None, "执行器应该有结果发布器"
            
            print("  ✅ 组件依赖关系验证通过")
            
            # 测试组件配置
            assert monitor.account_id != executor.account_id, "监控器和执行器应该有不同的账户ID"
            assert monitor.account_config['login'] != executor.account_config['login'], "应该有不同的登录账户"
            
            print("  ✅ 组件配置验证通过")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 组件集成测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有真实组件集成测试"""
        print("🚀 开始真实组件集成测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['jetstream_infrastructure'] = await self.test_jetstream_infrastructure()
        test_results['monitor_lifecycle'] = await self.test_monitor_component_lifecycle()
        test_results['executor_lifecycle'] = await self.test_executor_component_lifecycle()
        test_results['rpc_lifecycle'] = await self.test_rpc_component_lifecycle()
        test_results['component_integration'] = await self.test_component_integration()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 真实组件集成测试结果:")
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        return success_rate >= 80  # 80%以上算成功


async def main():
    """主函数"""
    if not REAL_COMPONENTS_AVAILABLE:
        print("❌ 真实组件不可用，无法运行测试")
        return False
    
    test_suite = RealComponentIntegrationTest()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n🎉 真实组件集成测试成功!")
        else:
            print("\n⚠️ 真实组件集成测试部分失败")
        
        return success
        
    finally:
        test_suite.cleanup()


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
