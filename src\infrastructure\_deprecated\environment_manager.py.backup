"""
环境管理器
根据环境自动选择合适的客户端实现（测试/生产）
"""
import os
import logging
from typing import Optional, Dict, Any
import yaml

logger = logging.getLogger(__name__)


class EnvironmentManager:
    """环境管理器"""
    
    def __init__(self):
        self.environment = os.getenv('ENVIRONMENT', 'development')
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载环境配置"""
        config_file = f"config/{self.environment}.yaml"
        
        # 如果环境配置文件不存在，使用默认配置
        if not os.path.exists(config_file):
            logger.warning(f"配置文件不存在: {config_file}，使用默认配置")
            return self._get_default_config()
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"加载配置文件: {config_file}")
                return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'nats': {
                'servers': ['nats://localhost:4222'],
                'connection': {
                    'connect_timeout': 10.0,
                    'max_reconnect_attempts': 60,
                    'reconnect_time_wait': 2.0
                }
            },
            'logging': {
                'level': 'INFO'
            }
        }
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == 'production'
    
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment in ['test', 'testing', 'development']
    
    def get_jetstream_client(self):
        """获取JetStream客户端"""
        if self.is_testing():
            return self._create_mock_client()
        else:
            return self._create_production_client()
    
    def _create_mock_client(self):
        """创建模拟客户端"""
        try:
            from tests.mock_jetstream import create_mock_jetstream
            from src.messaging.jetstream_client import JetStreamClient as JetStreamClientAdapter
            
            mock_client = create_mock_jetstream()
            return JetStreamClientAdapter(mock_client)
            
        except ImportError as e:
            logger.error(f"无法导入模拟客户端: {e}")
            raise
    
    def _create_production_client(self):
        """创建生产客户端"""
        try:
            from src.messaging.jetstream_client import JetStreamClient
            from src.messaging.nats_client import NATSConfig
            
            nats_config = self.config.get('nats', {})
            
            # 创建NATS配置
            config = NATSConfig(
                servers=nats_config.get('servers', ['nats://localhost:4222']),
                user=nats_config.get('auth', {}).get('user'),
                password=os.getenv('NATS_PASSWORD') or nats_config.get('auth', {}).get('password'),
                token=os.getenv('NATS_TOKEN') or nats_config.get('auth', {}).get('token'),
                **nats_config.get('connection', {})
            )
            
            # 使用统一的JetStreamClient
            return JetStreamClient(nats_config)
            
        except ImportError as e:
            logger.error(f"无法导入生产客户端: {e}")
            raise
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.config.get('database', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.config.get('logging', {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.config.get('monitoring', {})
    
    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return self.config.get('security', {})


# 全局环境管理器实例
_env_manager: Optional[EnvironmentManager] = None


def get_environment_manager() -> EnvironmentManager:
    """获取环境管理器单例"""
    global _env_manager
    if _env_manager is None:
        _env_manager = EnvironmentManager()
    return _env_manager


def get_jetstream_client():
    """便捷函数：获取JetStream客户端"""
    return get_environment_manager().get_jetstream_client()


def is_production() -> bool:
    """便捷函数：是否为生产环境"""
    return get_environment_manager().is_production()


def is_testing() -> bool:
    """便捷函数：是否为测试环境"""
    return get_environment_manager().is_testing()


# 环境配置装饰器
def environment_aware(func):
    """环境感知装饰器"""
    def wrapper(*args, **kwargs):
        env_manager = get_environment_manager()
        
        # 在测试环境中可能需要特殊处理
        if env_manager.is_testing():
            logger.debug(f"在测试环境中执行: {func.__name__}")
        
        return func(*args, **kwargs)
    
    return wrapper


class EnvironmentConfig:
    """环境配置类"""
    
    def __init__(self, env_manager: EnvironmentManager = None):
        self.env_manager = env_manager or get_environment_manager()
    
    @property
    def nats_servers(self) -> list:
        """NATS服务器列表"""
        return self.env_manager.config.get('nats', {}).get('servers', ['nats://localhost:4222'])
    
    @property
    def log_level(self) -> str:
        """日志级别"""
        return self.env_manager.config.get('logging', {}).get('level', 'INFO')
    
    @property
    def metrics_enabled(self) -> bool:
        """是否启用指标收集"""
        return self.env_manager.config.get('monitoring', {}).get('prometheus', {}).get('enabled', False)
    
    @property
    def health_check_enabled(self) -> bool:
        """是否启用健康检查"""
        return self.env_manager.config.get('monitoring', {}).get('health', {}).get('enabled', False)
    
    @property
    def security_enabled(self) -> bool:
        """是否启用安全功能"""
        return self.env_manager.config.get('security', {}).get('api', {}).get('enabled', False)
    
    def get_stream_config(self, stream_name: str) -> Dict[str, Any]:
        """获取流配置"""
        streams = self.env_manager.config.get('streams', [])
        for stream in streams:
            if stream.get('name') == stream_name:
                return stream
        return {}
    
    def get_signal_router_config(self) -> Dict[str, Any]:
        """获取Signal Router配置"""
        return self.env_manager.config.get('signal_router', {})
    
    def get_copy_trading_config(self) -> Dict[str, Any]:
        """获取跟单交易配置"""
        return self.env_manager.config.get('copy_trading', {})
    
    def get_trade_matching_config(self) -> Dict[str, Any]:
        """获取交易匹配配置"""
        return self.env_manager.config.get('trade_matching', {})


# 便捷函数
def get_config() -> EnvironmentConfig:
    """获取环境配置"""
    return EnvironmentConfig()


def setup_logging():
    """设置日志"""
    config = get_config()
    
    # 设置日志级别
    log_level = getattr(logging, config.log_level.upper(), logging.INFO)
    logging.getLogger().setLevel(log_level)
    
    # 在生产环境中可能需要更复杂的日志配置
    if is_production():
        # 这里可以添加文件日志、结构化日志等
        pass
    
    logger.info(f"日志配置完成，级别: {config.log_level}")


def validate_environment():
    """验证环境配置"""
    env_manager = get_environment_manager()
    
    logger.info(f"当前环境: {env_manager.environment}")
    logger.info(f"生产环境: {env_manager.is_production()}")
    logger.info(f"测试环境: {env_manager.is_testing()}")
    
    # 验证必需的配置
    config = get_config()
    
    if not config.nats_servers:
        raise ValueError("NATS服务器配置不能为空")
    
    logger.info("✅ 环境配置验证通过")


if __name__ == "__main__":
    # 测试环境管理器
    setup_logging()
    validate_environment()
    
    # 测试客户端创建
    try:
        client = get_jetstream_client()
        logger.info(f"✅ JetStream客户端创建成功: {type(client)}")
    except Exception as e:
        logger.error(f"❌ JetStream客户端创建失败: {e}")
