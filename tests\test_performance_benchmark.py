#!/usr/bin/env python3
"""
性能基准测试
建立性能基准、监控内存使用、测试吞吐量限制
"""

import asyncio
import json
import os
import sys
import tempfile
import time
import psutil
import statistics
import threading
from pathlib import Path
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass
from collections import defaultdict

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入真实组件
try:
    from src.core.mt5_account_monitor import MT5AccountMonitor
    from src.core.mt5_account_executor import MT5AccountExecutor
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.core.mt5_request_handler import MT5RequestHandler
    from src.messaging.jetstream_client import JetStreamClient
    from src.messaging.message_types import MessageEnvelope, TradeSignal, SignalType, PositionSignalData
    from src.messaging.message_codec import MessageCodec
    
    REAL_COMPONENTS_AVAILABLE = True
    print("✅ 性能基准测试组件导入成功")
    
except ImportError as e:
    print(f"❌ 导入组件失败: {e}")
    sys.exit(1)


@dataclass
class PerformanceBenchmark:
    """性能基准"""
    test_name: str
    throughput_msg_per_sec: float
    avg_latency_ms: float
    p95_latency_ms: float
    p99_latency_ms: float
    max_latency_ms: float
    min_latency_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    error_rate_percent: float
    timestamp: float


@dataclass
class MemorySnapshot:
    """内存快照"""
    timestamp: float
    rss_mb: float  # 物理内存
    vms_mb: float  # 虚拟内存
    percent: float  # 内存使用百分比
    available_mb: float  # 可用内存


class MemoryProfiler:
    """内存分析器"""
    
    def __init__(self):
        self.snapshots = []
        self.monitoring = False
        self.monitor_thread = None
    
    def start_profiling(self, interval: float = 0.5):
        """开始内存分析"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._profile_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print("  📊 内存分析已启动")
    
    def stop_profiling(self):
        """停止内存分析"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        print("  📊 内存分析已停止")
    
    def _profile_loop(self, interval: float):
        """分析循环"""
        process = psutil.Process()
        
        while self.monitoring:
            try:
                memory_info = process.memory_info()
                memory_percent = process.memory_percent()
                virtual_memory = psutil.virtual_memory()
                
                snapshot = MemorySnapshot(
                    timestamp=time.time(),
                    rss_mb=memory_info.rss / 1024 / 1024,
                    vms_mb=memory_info.vms / 1024 / 1024,
                    percent=memory_percent,
                    available_mb=virtual_memory.available / 1024 / 1024
                )
                
                self.snapshots.append(snapshot)
                
                # 保持最近2000个快照
                if len(self.snapshots) > 2000:
                    self.snapshots = self.snapshots[-2000:]
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"    ⚠️ 内存分析错误: {e}")
                time.sleep(interval)
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """获取内存使用总结"""
        if not self.snapshots:
            return {}
        
        rss_values = [s.rss_mb for s in self.snapshots]
        vms_values = [s.vms_mb for s in self.snapshots]
        percent_values = [s.percent for s in self.snapshots]
        
        return {
            'duration_seconds': self.snapshots[-1].timestamp - self.snapshots[0].timestamp,
            'rss_memory': {
                'avg_mb': statistics.mean(rss_values),
                'max_mb': max(rss_values),
                'min_mb': min(rss_values),
                'p95_mb': statistics.quantiles(rss_values, n=20)[18] if len(rss_values) > 20 else max(rss_values)
            },
            'virtual_memory': {
                'avg_mb': statistics.mean(vms_values),
                'max_mb': max(vms_values),
                'min_mb': min(vms_values)
            },
            'memory_percent': {
                'avg': statistics.mean(percent_values),
                'max': max(percent_values),
                'min': min(percent_values)
            },
            'sample_count': len(self.snapshots),
            'memory_growth_mb': rss_values[-1] - rss_values[0] if len(rss_values) > 1 else 0
        }
    
    def detect_memory_leaks(self) -> Dict[str, Any]:
        """检测内存泄漏"""
        if len(self.snapshots) < 10:
            return {'status': 'insufficient_data'}
        
        # 计算内存增长趋势
        rss_values = [s.rss_mb for s in self.snapshots]
        time_values = [s.timestamp for s in self.snapshots]
        
        # 简单线性回归计算增长率
        n = len(rss_values)
        sum_x = sum(time_values)
        sum_y = sum(rss_values)
        sum_xy = sum(x * y for x, y in zip(time_values, rss_values))
        sum_x2 = sum(x * x for x in time_values)
        
        # 斜率 = (n*sum_xy - sum_x*sum_y) / (n*sum_x2 - sum_x^2)
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            growth_rate = 0
        else:
            growth_rate = (n * sum_xy - sum_x * sum_y) / denominator
        
        # 转换为MB/分钟
        growth_rate_mb_per_min = growth_rate * 60
        
        # 判断是否有内存泄漏
        leak_threshold = 1.0  # 1MB/分钟
        has_leak = growth_rate_mb_per_min > leak_threshold
        
        return {
            'status': 'analyzed',
            'growth_rate_mb_per_min': growth_rate_mb_per_min,
            'has_potential_leak': has_leak,
            'leak_severity': 'high' if growth_rate_mb_per_min > 5 else 'medium' if growth_rate_mb_per_min > 2 else 'low',
            'total_growth_mb': rss_values[-1] - rss_values[0],
            'analysis_duration_min': (time_values[-1] - time_values[0]) / 60
        }


class ThroughputTester:
    """吞吐量测试器"""
    
    def __init__(self):
        self.test_results = []
    
    async def test_message_throughput(self, jetstream_client: JetStreamClient, 
                                    message_count: int, concurrent_publishers: int = 1) -> Dict[str, Any]:
        """测试消息吞吐量"""
        print(f"    📈 测试消息吞吐量: {message_count} 消息, {concurrent_publishers} 并发发布者")
        
        # 生成测试消息
        test_messages = []
        for i in range(message_count):
            signal_data = PositionSignalData(
                symbol='EURUSD',
                volume=0.01,
                action='BUY',
                price=1.1234,
                sl=1.1200,
                tp=1.1300,
                magic=12345 + i,
                account_id='THROUGHPUT_MASTER',
                copy_magic_number=12345 + i
            )

            signal = TradeSignal(
                type=SignalType.POSITION_OPEN,
                master_id='THROUGHPUT_MASTER',
                ticket=1000000 + i,
                data=signal_data,
                timestamp=time.time()
            )
            
            envelope = MessageEnvelope.create_trade_signal(
                signal=signal,
                subject=f'THROUGHPUT.TEST.{i % 10}'  # 分散到10个主题
            )
            test_messages.append(envelope)
        
        # 分割消息给不同的发布者
        messages_per_publisher = message_count // concurrent_publishers
        publisher_message_groups = [
            test_messages[i * messages_per_publisher:(i + 1) * messages_per_publisher]
            for i in range(concurrent_publishers)
        ]
        
        # 如果有剩余消息，分配给最后一个发布者
        if message_count % concurrent_publishers:
            publisher_message_groups[-1].extend(
                test_messages[concurrent_publishers * messages_per_publisher:]
            )
        
        # 并发发布任务
        async def publisher_task(publisher_id: int, messages: List[MessageEnvelope]) -> Dict[str, Any]:
            start_time = time.time()
            successful_publishes = 0
            failed_publishes = 0
            latencies = []
            
            for message in messages:
                publish_start = time.time()
                try:
                    success = await jetstream_client.publish_message(message)
                    if success:
                        successful_publishes += 1
                        latency = (time.time() - publish_start) * 1000
                        latencies.append(latency)
                    else:
                        failed_publishes += 1
                except Exception as e:
                    failed_publishes += 1
            
            end_time = time.time()
            duration = end_time - start_time
            
            return {
                'publisher_id': publisher_id,
                'duration': duration,
                'successful_publishes': successful_publishes,
                'failed_publishes': failed_publishes,
                'throughput_msg_per_sec': successful_publishes / duration if duration > 0 else 0,
                'avg_latency_ms': statistics.mean(latencies) if latencies else 0,
                'max_latency_ms': max(latencies) if latencies else 0,
                'min_latency_ms': min(latencies) if latencies else 0
            }
        
        # 启动所有发布者任务
        start_time = time.time()
        publisher_tasks = [
            publisher_task(i, messages)
            for i, messages in enumerate(publisher_message_groups)
        ]
        
        results = await asyncio.gather(*publisher_tasks)
        end_time = time.time()
        
        # 汇总结果
        total_successful = sum(r['successful_publishes'] for r in results)
        total_failed = sum(r['failed_publishes'] for r in results)
        total_duration = end_time - start_time
        
        all_latencies = []
        for r in results:
            if r['avg_latency_ms'] > 0:
                # 近似重建延迟数据用于百分位计算
                count = r['successful_publishes']
                avg = r['avg_latency_ms']
                # 简化假设：延迟正态分布
                all_latencies.extend([avg] * count)
        
        if all_latencies:
            p95_latency = statistics.quantiles(all_latencies, n=20)[18] if len(all_latencies) > 20 else max(all_latencies)
            p99_latency = statistics.quantiles(all_latencies, n=100)[98] if len(all_latencies) > 100 else max(all_latencies)
        else:
            p95_latency = p99_latency = 0
        
        throughput_result = {
            'message_count': message_count,
            'concurrent_publishers': concurrent_publishers,
            'total_duration': total_duration,
            'successful_publishes': total_successful,
            'failed_publishes': total_failed,
            'success_rate': (total_successful / message_count * 100) if message_count > 0 else 0,
            'overall_throughput_msg_per_sec': total_successful / total_duration if total_duration > 0 else 0,
            'avg_latency_ms': statistics.mean(all_latencies) if all_latencies else 0,
            'p95_latency_ms': p95_latency,
            'p99_latency_ms': p99_latency,
            'max_latency_ms': max(all_latencies) if all_latencies else 0,
            'min_latency_ms': min(all_latencies) if all_latencies else 0,
            'publisher_results': results
        }
        
        self.test_results.append(throughput_result)
        return throughput_result
    
    async def find_throughput_limit(self, jetstream_client: JetStreamClient) -> Dict[str, Any]:
        """寻找吞吐量限制"""
        print(f"    🔍 寻找吞吐量限制...")
        
        # 逐步增加负载直到找到限制
        test_configs = [
            (100, 1),   # 100消息, 1发布者
            (500, 1),   # 500消息, 1发布者
            (1000, 1),  # 1000消息, 1发布者
            (1000, 2),  # 1000消息, 2发布者
            (2000, 2),  # 2000消息, 2发布者
            (2000, 4),  # 2000消息, 4发布者
        ]
        
        limit_results = []
        
        for message_count, publishers in test_configs:
            print(f"      测试配置: {message_count} 消息, {publishers} 发布者")
            
            result = await self.test_message_throughput(jetstream_client, message_count, publishers)
            
            limit_results.append({
                'config': f'{message_count}msg_{publishers}pub',
                'throughput': result['overall_throughput_msg_per_sec'],
                'success_rate': result['success_rate'],
                'avg_latency': result['avg_latency_ms']
            })
            
            print(f"        吞吐量: {result['overall_throughput_msg_per_sec']:.0f} msg/s")
            print(f"        成功率: {result['success_rate']:.1f}%")
            print(f"        平均延迟: {result['avg_latency_ms']:.2f}ms")
            
            # 如果成功率低于95%，认为接近限制
            if result['success_rate'] < 95:
                print(f"        ⚠️ 成功率下降，可能接近吞吐量限制")
                break
            
            # 短暂休息避免系统过载
            await asyncio.sleep(1)
        
        # 找到最大吞吐量
        max_throughput = max(r['throughput'] for r in limit_results)
        max_config = next(r for r in limit_results if r['throughput'] == max_throughput)
        
        return {
            'max_throughput_msg_per_sec': max_throughput,
            'optimal_config': max_config['config'],
            'test_results': limit_results,
            'throughput_limit_reached': any(r['success_rate'] < 95 for r in limit_results)
        }


class PerformanceBenchmarkTest:
    """性能基准测试类"""
    
    def __init__(self):
        self.test_data_dir = Path(tempfile.mkdtemp(prefix="performance_benchmark_test_"))
        self.jetstream_clients = []
        self.components = []
        self.memory_profiler = MemoryProfiler()
        self.throughput_tester = ThroughputTester()
        self.benchmarks = []
        print(f"测试数据目录: {self.test_data_dir}")
    
    def cleanup(self):
        """清理测试资源"""
        import shutil
        try:
            # 停止内存分析
            self.memory_profiler.stop_profiling()
            
            # 停止所有组件
            for component in self.components:
                if hasattr(component, 'running'):
                    component.running = False
            
            # 断开所有JetStream连接
            for client in self.jetstream_clients:
                if hasattr(client, 'disconnect'):
                    asyncio.create_task(client.disconnect())
            
            shutil.rmtree(self.test_data_dir)
            print("🧹 性能基准测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理警告: {e}")
    
    async def setup_benchmark_infrastructure(self):
        """设置性能基准测试基础设施"""
        print("\n🏗️ 设置性能基准测试基础设施...")
        
        try:
            # 创建主JetStream客户端
            main_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'PERFORMANCE_BENCHMARK_MAIN',
                'subjects': ['BENCHMARK.>']
            }
            
            main_client = JetStreamClient(main_config)
            connected = await main_client.connect()
            
            if not connected:
                print("  ❌ 无法连接到NATS服务器")
                return None
            
            print("  ✅ 性能基准测试基础设施设置成功")
            self.jetstream_clients.append(main_client)
            
            return main_client
            
        except Exception as e:
            print(f"  ❌ 性能基准测试基础设施设置失败: {e}")
            return None
    
    async def test_baseline_performance(self):
        """测试基线性能"""
        print("\n📊 测试基线性能...")
        
        # 设置基础设施
        main_client = await self.setup_benchmark_infrastructure()
        if not main_client:
            return False
        
        try:
            # 启动内存分析
            self.memory_profiler.start_profiling()
            
            # 基线性能测试
            print("  🎯 执行基线性能测试...")
            
            baseline_start = time.time()
            
            # 测试消息编解码性能
            codec = MessageCodec()
            encode_times = []
            decode_times = []
            
            test_data = {
                'signal_id': 'BASELINE_001',
                'account_id': 'BASELINE_ACCOUNT',
                'symbol': 'EURUSD',
                'action': 'BUY',
                'volume': 0.1,
                'price': 1.1234,
                'timestamp': time.time()
            }
            
            for _ in range(1000):
                # 编码测试
                encode_start = time.time()
                encoded = codec.encode(test_data)
                encode_times.append((time.time() - encode_start) * 1000)
                
                # 解码测试
                decode_start = time.time()
                decoded = codec.decode(encoded)
                decode_times.append((time.time() - decode_start) * 1000)
            
            # 测试消息发布性能
            throughput_result = await self.throughput_tester.test_message_throughput(
                main_client, 1000, 1
            )
            
            baseline_end = time.time()
            
            # 停止内存分析
            self.memory_profiler.stop_profiling()
            
            # 获取内存使用情况
            memory_summary = self.memory_profiler.get_memory_summary()
            
            # 创建基线基准
            baseline_benchmark = PerformanceBenchmark(
                test_name='baseline_performance',
                throughput_msg_per_sec=throughput_result['overall_throughput_msg_per_sec'],
                avg_latency_ms=throughput_result['avg_latency_ms'],
                p95_latency_ms=throughput_result['p95_latency_ms'],
                p99_latency_ms=throughput_result['p99_latency_ms'],
                max_latency_ms=throughput_result['max_latency_ms'],
                min_latency_ms=throughput_result['min_latency_ms'],
                memory_usage_mb=memory_summary.get('rss_memory', {}).get('avg_mb', 0),
                cpu_usage_percent=0,  # 需要额外监控
                error_rate_percent=100 - throughput_result['success_rate'],
                timestamp=time.time()
            )
            
            self.benchmarks.append(baseline_benchmark)
            
            print(f"  📊 基线性能结果:")
            print(f"    吞吐量: {baseline_benchmark.throughput_msg_per_sec:.0f} msg/s")
            print(f"    平均延迟: {baseline_benchmark.avg_latency_ms:.2f}ms")
            print(f"    P95延迟: {baseline_benchmark.p95_latency_ms:.2f}ms")
            print(f"    内存使用: {baseline_benchmark.memory_usage_mb:.1f}MB")
            print(f"    错误率: {baseline_benchmark.error_rate_percent:.2f}%")
            print(f"    编码平均时间: {statistics.mean(encode_times):.3f}ms")
            print(f"    解码平均时间: {statistics.mean(decode_times):.3f}ms")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 基线性能测试失败: {e}")
            return False
    
    async def test_memory_usage_patterns(self):
        """测试内存使用模式"""
        print("\n💾 测试内存使用模式...")
        
        # 设置基础设施
        main_client = await self.setup_benchmark_infrastructure()
        if not main_client:
            return False
        
        try:
            # 启动内存分析
            self.memory_profiler.start_profiling()
            
            print("  📈 执行内存压力测试...")
            
            # 创建大量消息对象
            messages = []
            for i in range(10000):
                signal_data = PositionSignalData(
                    symbol='EURUSD',
                    volume=0.01,
                    action='BUY',
                    price=1.1234 + i * 0.0001,
                    sl=1.1200 + i * 0.0001,
                    tp=1.1300 + i * 0.0001,
                    magic=12345 + i,
                    account_id='MEMORY_MASTER',
                    copy_magic_number=12345 + i
                )

                signal = TradeSignal(
                    type=SignalType.POSITION_OPEN,
                    master_id='MEMORY_MASTER',
                    ticket=1000000 + i,
                    data=signal_data,
                    timestamp=time.time()
                )
                messages.append(signal)
                
                # 每1000个消息检查一次内存
                if i % 1000 == 0:
                    await asyncio.sleep(0.1)
            
            print(f"    ✅ 创建了 {len(messages)} 个消息对象")
            
            # 执行消息处理
            processed_count = 0
            for message in messages[:5000]:  # 处理前5000个
                try:
                    envelope = MessageEnvelope.create_trade_signal(
                        signal=message,
                        subject='MEMORY.TEST.PROCESSING'
                    )
                    
                    # 模拟消息处理
                    await asyncio.sleep(0.001)  # 1ms处理时间
                    processed_count += 1
                    
                    if processed_count % 1000 == 0:
                        print(f"    📊 已处理 {processed_count} 个消息")
                
                except Exception as e:
                    print(f"    ⚠️ 消息处理错误: {e}")
            
            # 清理消息对象
            del messages
            
            # 等待垃圾回收
            import gc
            gc.collect()
            await asyncio.sleep(2)
            
            # 停止内存分析
            self.memory_profiler.stop_profiling()
            
            # 分析内存使用
            memory_summary = self.memory_profiler.get_memory_summary()
            leak_analysis = self.memory_profiler.detect_memory_leaks()
            
            print(f"  📊 内存使用分析:")
            if memory_summary:
                print(f"    平均内存: {memory_summary['rss_memory']['avg_mb']:.1f}MB")
                print(f"    峰值内存: {memory_summary['rss_memory']['max_mb']:.1f}MB")
                print(f"    内存增长: {memory_summary['memory_growth_mb']:.1f}MB")
            
            if leak_analysis.get('status') == 'analyzed':
                print(f"    内存泄漏检测: {'⚠️ 可能有泄漏' if leak_analysis['has_potential_leak'] else '✅ 无明显泄漏'}")
                print(f"    增长率: {leak_analysis['growth_rate_mb_per_min']:.2f}MB/分钟")
            
            # 成功条件：内存使用合理，无明显泄漏
            memory_reasonable = (
                memory_summary.get('rss_memory', {}).get('max_mb', 1000) < 500 and
                not leak_analysis.get('has_potential_leak', True)
            )
            
            return memory_reasonable
            
        except Exception as e:
            print(f"  ❌ 内存使用模式测试失败: {e}")
            return False
    
    async def test_throughput_limits(self):
        """测试吞吐量限制"""
        print("\n🚀 测试吞吐量限制...")
        
        # 设置基础设施
        main_client = await self.setup_benchmark_infrastructure()
        if not main_client:
            return False
        
        try:
            # 寻找吞吐量限制
            limit_result = await self.throughput_tester.find_throughput_limit(main_client)
            
            print(f"  📊 吞吐量限制测试结果:")
            print(f"    最大吞吐量: {limit_result['max_throughput_msg_per_sec']:.0f} msg/s")
            print(f"    最优配置: {limit_result['optimal_config']}")
            print(f"    是否达到限制: {'是' if limit_result['throughput_limit_reached'] else '否'}")
            
            # 创建吞吐量基准
            throughput_benchmark = PerformanceBenchmark(
                test_name='throughput_limit',
                throughput_msg_per_sec=limit_result['max_throughput_msg_per_sec'],
                avg_latency_ms=0,  # 从详细结果中获取
                p95_latency_ms=0,
                p99_latency_ms=0,
                max_latency_ms=0,
                min_latency_ms=0,
                memory_usage_mb=0,
                cpu_usage_percent=0,
                error_rate_percent=0,
                timestamp=time.time()
            )
            
            self.benchmarks.append(throughput_benchmark)
            
            # 成功条件：找到合理的吞吐量限制
            return limit_result['max_throughput_msg_per_sec'] >= 100  # 至少100 msg/s
            
        except Exception as e:
            print(f"  ❌ 吞吐量限制测试失败: {e}")
            return False
    
    def generate_benchmark_report(self) -> Dict[str, Any]:
        """生成基准测试报告"""
        if not self.benchmarks:
            return {}
        
        report = {
            'test_timestamp': time.time(),
            'total_benchmarks': len(self.benchmarks),
            'benchmarks': [
                {
                    'test_name': b.test_name,
                    'throughput_msg_per_sec': b.throughput_msg_per_sec,
                    'avg_latency_ms': b.avg_latency_ms,
                    'p95_latency_ms': b.p95_latency_ms,
                    'memory_usage_mb': b.memory_usage_mb,
                    'error_rate_percent': b.error_rate_percent
                }
                for b in self.benchmarks
            ],
            'summary': {
                'max_throughput': max(b.throughput_msg_per_sec for b in self.benchmarks),
                'min_latency': min(b.avg_latency_ms for b in self.benchmarks if b.avg_latency_ms > 0),
                'avg_memory_usage': statistics.mean(b.memory_usage_mb for b in self.benchmarks if b.memory_usage_mb > 0),
                'max_error_rate': max(b.error_rate_percent for b in self.benchmarks)
            }
        }
        
        return report
    
    async def run_all_tests(self):
        """运行所有性能基准测试"""
        print("🚀 开始性能基准测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['baseline_performance'] = await self.test_baseline_performance()
        test_results['memory_usage_patterns'] = await self.test_memory_usage_patterns()
        test_results['throughput_limits'] = await self.test_throughput_limits()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 性能基准测试结果:")
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        # 生成基准报告
        benchmark_report = self.generate_benchmark_report()
        if benchmark_report:
            print(f"\n📈 性能基准报告:")
            summary = benchmark_report['summary']
            print(f"  最大吞吐量: {summary['max_throughput']:.0f} msg/s")
            if summary['min_latency'] > 0:
                print(f"  最低延迟: {summary['min_latency']:.2f}ms")
            if summary['avg_memory_usage'] > 0:
                print(f"  平均内存使用: {summary['avg_memory_usage']:.1f}MB")
            print(f"  最大错误率: {summary['max_error_rate']:.2f}%")
        
        return success_rate >= 66  # 66%以上算成功


async def main():
    """主函数"""
    if not REAL_COMPONENTS_AVAILABLE:
        print("❌ 性能基准测试组件不可用，无法运行测试")
        return False
    
    test_suite = PerformanceBenchmarkTest()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n🎉 性能基准测试成功!")
        else:
            print("\n⚠️ 性能基准测试部分失败")
        
        return success
        
    finally:
        test_suite.cleanup()


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
