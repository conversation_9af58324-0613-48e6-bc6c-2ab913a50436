# src/core/trade_result_evaluator.py
"""
统一的交易结果评估器
解决不同模块对TradeResult处理方式不统一的问题
"""
import logging
from typing import Any, Tuple, Optional
from .mt5_client import TradeResult

logger = logging.getLogger(__name__)


class TradeResultEvaluator:
    """统一的交易结果评估器"""
    
    # MT5返回码常量
    TRADE_RETCODE_DONE = 10009          # 交易成功
    TRADE_RETCODE_DONE_PARTIAL = 10010  # 部分成功
    TRADE_RETCODE_ERROR = 10011         # 一般错误
    TRADE_RETCODE_TIMEOUT = 10012       # 超时
    TRADE_RETCODE_INVALID_PRICE = 10013 # 无效价格
    TRADE_RETCODE_INVALID_STOPS = 10014 # 无效止损/止盈
    TRADE_RETCODE_INVALID_VOLUME = 10015 # 无效数量
    TRADE_RETCODE_MARKET_CLOSED = 10016 # 市场关闭
    TRADE_RETCODE_NO_MONEY = 10017      # 资金不足
    TRADE_RETCODE_CONNECTION = 10018    # 连接错误
    TRADE_RETCODE_ONLY_REAL = 10019     # 仅实盘
    TRADE_RETCODE_LIMIT_ORDERS = 10020  # 挂单数量限制
    TRADE_RETCODE_LIMIT_VOLUME = 10021  # 数量限制
    TRADE_RETCODE_INVALID_ORDER = 10022 # 无效订单
    TRADE_RETCODE_POSITION_CLOSED = 10023 # 持仓已关闭
    
    @staticmethod
    def evaluate_result(result: Any, operation: str = "交易") -> Tuple[bool, str]:
        """统一的交易结果评估"""
        try:
            if result is None:
                error_msg = f"{operation}失败: 返回结果为空"
                logger.error(error_msg)
                return False, error_msg
            
            if hasattr(result, 'retcode'):
                return TradeResultEvaluator._evaluate_retcode_result(result, operation)
            
            elif isinstance(result, bool):
                return TradeResultEvaluator._evaluate_bool_result(result, operation)
            
            elif isinstance(result, dict):
                return TradeResultEvaluator._evaluate_dict_result(result, operation)
            
            else:
                warning_msg = f"{operation}: 未知的结果格式 {type(result)}"
                logger.warning(warning_msg)
                logger.warning(f"结果内容: {str(result)[:100]}...")
                error_msg = f"{operation}状态未知，视为失败"
                logger.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            error_msg = f"评估{operation}结果时出错: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def _evaluate_retcode_result(result, operation: str) -> Tuple[bool, str]:
        """评估带retcode的结果"""
        retcode = result.retcode
        
        if retcode == TradeResultEvaluator.TRADE_RETCODE_DONE:
            success_msg = f"{operation}成功"
            if hasattr(result, 'order') and result.order:
                success_msg += f" (订单号: {result.order})"
            if hasattr(result, 'deal') and result.deal:
                success_msg += f" (成交号: {result.deal})"
            logger.info(success_msg)
            return True, success_msg
        
        elif retcode == TradeResultEvaluator.TRADE_RETCODE_DONE_PARTIAL:
            warning_msg = f"{operation}部分成功 (返回码: {retcode})"
            logger.warning(warning_msg)
            return True, warning_msg
        
        else:
            error_msg = TradeResultEvaluator._get_error_message(retcode, operation)
            comment = getattr(result, 'comment', '')
            if comment:
                error_msg += f" - {comment}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def _evaluate_bool_result(result: bool, operation: str) -> Tuple[bool, str]:
        """评估布尔值结果"""
        if result:
            success_msg = f"{operation}成功"
            logger.info(success_msg)
            return True, success_msg
        else:
            error_msg = f"{operation}失败"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def _evaluate_dict_result(result: dict, operation: str) -> Tuple[bool, str]:
        """评估字典格式结果"""
        success = result.get('success', False)
        if success:
            success_msg = f"{operation}成功"
            logger.info(success_msg)
            return True, success_msg
        else:
            error = result.get('error', '未知错误')
            error_msg = f"{operation}失败: {error}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def _get_error_message(retcode: int, operation: str) -> str:
        """根据返回码获取错误信息"""
        error_messages = {
            TradeResultEvaluator.TRADE_RETCODE_ERROR: "一般错误",
            TradeResultEvaluator.TRADE_RETCODE_TIMEOUT: "请求超时",
            TradeResultEvaluator.TRADE_RETCODE_INVALID_PRICE: "无效价格",
            TradeResultEvaluator.TRADE_RETCODE_INVALID_STOPS: "无效止损/止盈",
            TradeResultEvaluator.TRADE_RETCODE_INVALID_VOLUME: "无效交易数量",
            TradeResultEvaluator.TRADE_RETCODE_MARKET_CLOSED: "市场关闭",
            TradeResultEvaluator.TRADE_RETCODE_NO_MONEY: "资金不足",
            TradeResultEvaluator.TRADE_RETCODE_CONNECTION: "连接错误",
            TradeResultEvaluator.TRADE_RETCODE_ONLY_REAL: "仅支持实盘账户",
            TradeResultEvaluator.TRADE_RETCODE_LIMIT_ORDERS: "挂单数量超限",
            TradeResultEvaluator.TRADE_RETCODE_LIMIT_VOLUME: "交易数量超限",
            TradeResultEvaluator.TRADE_RETCODE_INVALID_ORDER: "无效订单",
            TradeResultEvaluator.TRADE_RETCODE_POSITION_CLOSED: "持仓已关闭"
        }
        
        error_desc = error_messages.get(retcode, f"未知错误码: {retcode}")
        return f"{operation}失败: {error_desc} (返回码: {retcode})"
    
    @staticmethod
    def is_success(result: Any) -> bool:
        """简单的成功判断"""
        success, _ = TradeResultEvaluator.evaluate_result(result)
        return success
    
    @staticmethod
    def get_error_info(result: Any) -> Optional[str]:
        """获取错误信息"""
        success, message = TradeResultEvaluator.evaluate_result(result)
        return None if success else message


# 便捷函数
def evaluate_trade_result(result: Any, operation: str = "交易") -> Tuple[bool, str]:
    """便捷的交易结果评估函数"""
    return TradeResultEvaluator.evaluate_result(result, operation)


def is_trade_success(result: Any) -> bool:
    """便捷的成功判断函数"""
    return TradeResultEvaluator.is_success(result)


def get_trade_error(result: Any) -> Optional[str]:
    """便捷的错误信息获取函数"""
    return TradeResultEvaluator.get_error_info(result)


def auto_evaluate_trade_result(operation: str = "交易"):
    """装饰器：自动评估交易结果并记录日志"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                success, message = evaluate_trade_result(result, operation)
                return result  # 返回原始结果，让调用者决定如何处理
            except Exception as e:
                error_msg = f"{operation}执行异常: {e}"
                logger.error(error_msg)
                raise
        return wrapper
    return decorator


 
