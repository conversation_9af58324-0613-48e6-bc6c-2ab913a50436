#!/usr/bin/env python3
# tests/integration_test_process_isolation.py
"""
进程隔离集成测试
验证完整的进程隔离系统工作正常
"""

import asyncio
import time
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.mt5_process_manager import MT5ProcessManager
from src.core.mt5_client import create_mt5_client
from src.messaging.message_types import ProcessState

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProcessIsolationIntegrationTest:
    """进程隔离集成测试类"""
    
    def __init__(self):
        self.process_manager = None
        self.test_accounts = [
            {
                'account_id': 'TEST_ACC001',
                'login': 12345,
                'password': 'test_password_1',
                'server': 'TestServer-1',
                'terminal_path': None
            },
            {
                'account_id': 'TEST_ACC002', 
                'login': 67890,
                'password': 'test_password_2',
                'server': 'TestServer-2',
                'terminal_path': None
            },
            {
                'account_id': 'TEST_ACC003',
                'login': 11111,
                'password': 'test_password_3', 
                'server': 'TestServer-3',
                'terminal_path': None
            }
        ]
    
    async def setup(self):
        """设置测试环境"""
        logger.info("🚀 开始进程隔离集成测试")
        
        # 创建进程管理器
        self.process_manager = MT5ProcessManager()
        self.process_manager.start_manager()
        
        logger.info("✅ 进程管理器已启动")
        
        # 等待管理器完全启动
        await asyncio.sleep(1)
    
    async def test_multiple_account_isolation(self):
        """测试多账户隔离"""
        logger.info("📋 测试多账户进程隔离...")
        
        clients = []
        
        # 创建多个客户端
        for account in self.test_accounts:
            try:
                client = create_mt5_client(
                    account_id=account['account_id'],
                    login=account['login'],
                    password=account['password'],
                    server=account['server'],
                    terminal_path=account['terminal_path']
                )
                clients.append(client)
                logger.info(f"✅ 创建客户端: {account['account_id']}")
                
            except Exception as e:
                logger.error(f"❌ 创建客户端失败 {account['account_id']}: {e}")
                return False
        
        # 验证客户端隔离
        account_ids = [client.account_id for client in clients]
        if len(set(account_ids)) != len(account_ids):
            logger.error("❌ 账户ID重复，隔离失败")
            return False
        
        logger.info(f"✅ 成功创建 {len(clients)} 个隔离的客户端")
        return True
    
    async def test_process_manager_health(self):
        """测试进程管理器健康状态"""
        logger.info("🏥 测试进程管理器健康状态...")
        
        try:
            # 获取系统健康状态
            health = self.process_manager.get_system_health()
            
            logger.info(f"📊 系统健康状态:")
            logger.info(f"   总进程数: {health.total_processes}")
            logger.info(f"   运行中进程: {health.running_processes}")
            logger.info(f"   已连接进程: {health.connected_processes}")
            logger.info(f"   错误进程: {health.error_processes}")
            logger.info(f"   总内存使用: {health.total_memory_mb:.1f}MB")
            logger.info(f"   平均CPU使用: {health.average_cpu_percent:.1f}%")
            logger.info(f"   运行时间: {health.uptime_seconds:.1f}秒")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 获取健康状态失败: {e}")
            return False
    
    async def test_process_status_tracking(self):
        """测试进程状态跟踪"""
        logger.info("📈 测试进程状态跟踪...")
        
        try:
            # 获取所有进程状态
            all_status = self.process_manager.get_all_process_status()
            
            logger.info(f"📋 进程状态列表 ({len(all_status)} 个进程):")
            for account_name, status in all_status.items():
                logger.info(f"   {account_name}:")
                logger.info(f"     PID: {status.pid}")
                logger.info(f"     状态: {status.status}")
                logger.info(f"     存活: {status.is_alive}")
                logger.info(f"     连接: {status.connected}")
                logger.info(f"     运行时间: {status.uptime_seconds:.1f}秒")
                logger.info(f"     错误次数: {status.error_count}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 获取进程状态失败: {e}")
            return False
    
    async def test_restart_mechanism(self):
        """测试重启机制"""
        logger.info("🔄 测试自动重启机制...")
        
        try:
            # 为测试账户启用自动重启
            test_account = 'TEST_ACC001'
            self.process_manager.enable_auto_restart(test_account, max_restarts=3)
            
            # 检查重启策略
            policy = self.process_manager.restart_policy.get(test_account)
            if policy and policy.get('enabled'):
                logger.info(f"✅ 自动重启已启用: {test_account}")
                logger.info(f"   最大重启次数: {policy.get('max_restarts')}")
                logger.info(f"   时间窗口: {policy.get('time_window')}秒")
                return True
            else:
                logger.error(f"❌ 自动重启启用失败: {test_account}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 测试重启机制失败: {e}")
            return False
    
    async def test_concurrent_operations(self):
        """测试并发操作"""
        logger.info("⚡ 测试并发操作...")
        
        try:
            # 模拟并发获取账户信息
            tasks = []
            for account in self.test_accounts:
                account_id = account['account_id']
                if account_id in self.process_manager.processes:
                    # 创建模拟任务
                    task = self._simulate_account_operation(account_id)
                    tasks.append(task)
            
            if tasks:
                # 并发执行
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                success_count = sum(1 for r in results if r is True)
                logger.info(f"✅ 并发操作完成: {success_count}/{len(tasks)} 成功")
                
                return success_count > 0
            else:
                logger.warning("⚠️ 没有可用的进程进行并发测试")
                return True
                
        except Exception as e:
            logger.error(f"❌ 并发操作测试失败: {e}")
            return False
    
    async def _simulate_account_operation(self, account_id: str):
        """模拟账户操作"""
        try:
            # 模拟一些延迟
            await asyncio.sleep(0.1)
            
            # 检查进程状态
            status = self.process_manager.get_process_status(account_id)
            if status:
                logger.info(f"   {account_id}: 操作完成 (PID: {status.pid})")
                return True
            else:
                logger.warning(f"   {account_id}: 进程状态不可用")
                return False
                
        except Exception as e:
            logger.error(f"   {account_id}: 操作失败 - {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 清理测试环境...")
        
        if self.process_manager:
            try:
                self.process_manager.shutdown()
                logger.info("✅ 进程管理器已关闭")
            except Exception as e:
                logger.error(f"❌ 关闭进程管理器失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        test_results = []
        
        try:
            # 设置测试环境
            await self.setup()
            
            # 运行测试
            tests = [
                ("多账户隔离", self.test_multiple_account_isolation),
                ("进程管理器健康", self.test_process_manager_health),
                ("进程状态跟踪", self.test_process_status_tracking),
                ("重启机制", self.test_restart_mechanism),
                ("并发操作", self.test_concurrent_operations),
            ]
            
            for test_name, test_func in tests:
                logger.info(f"\n{'='*50}")
                logger.info(f"🧪 运行测试: {test_name}")
                logger.info(f"{'='*50}")
                
                try:
                    result = await test_func()
                    test_results.append((test_name, result))
                    
                    if result:
                        logger.info(f"✅ {test_name} - 通过")
                    else:
                        logger.error(f"❌ {test_name} - 失败")
                        
                except Exception as e:
                    logger.error(f"❌ {test_name} - 异常: {e}")
                    test_results.append((test_name, False))
                
                # 测试间隔
                await asyncio.sleep(1)
            
            # 总结结果
            logger.info(f"\n{'='*50}")
            logger.info("📊 测试结果总结")
            logger.info(f"{'='*50}")
            
            passed = sum(1 for _, result in test_results if result)
            total = len(test_results)
            
            for test_name, result in test_results:
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"   {test_name}: {status}")
            
            logger.info(f"\n🎯 总体结果: {passed}/{total} 测试通过")
            
            if passed == total:
                logger.info("🎉 所有测试通过！进程隔离系统工作正常")
                return True
            else:
                logger.error("⚠️ 部分测试失败，请检查系统配置")
                return False
                
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    test_runner = ProcessIsolationIntegrationTest()
    
    try:
        success = await test_runner.run_all_tests()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ 测试被用户中断")
        await test_runner.cleanup()
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"💥 测试运行异常: {e}")
        await test_runner.cleanup()
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
