# Redis从节点配置 - MT5分布式交易系统
# 作为主节点的只读副本，提供读取负载分担

# 基本配置
port 6379
bind 0.0.0.0
protected-mode no

# 从节点配置
replicaof redis-master 6379
replica-serve-stale-data yes
replica-read-only yes
replica-priority 100

# 主从复制配置
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# 数据持久化 (从节点可以关闭持久化以提高性能)
save ""
appendonly no

# 内存管理
maxmemory 1gb
maxmemory-policy allkeys-lru

# 网络配置
tcp-keepalive 300
timeout 0

# 日志配置
loglevel notice
logfile "/data/redis-replica.log"

# 数据库数量
databases 16
dir /data

# 客户端连接
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 键空间通知
notify-keyspace-events "Ex"

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""

# 性能优化
tcp-backlog 511
hz 10

# 内存碎片整理
activedefrag yes
