#!/usr/bin/env python3
"""
文件特殊字符清理工具
遍历指定目录下的所有文件，移除emoji、unicode等特殊符号
"""

import os
import re
import argparse
import logging
from pathlib import Path
from typing import List, Set, Dict, Optional
import unicodedata
import chardet

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SpecialCharRemover:
    """特殊字符移除器"""
    
    def __init__(self, backup: bool = True, dry_run: bool = False):
        self.backup = backup
        self.dry_run = dry_run
        self.stats = {
            'files_processed': 0,
            'files_modified': 0,
            'chars_removed': 0,
            'errors': 0
        }
        
        # 定义要保留的字符类别
        self.allowed_categories = {
            'Lu',  # Letter, uppercase
            'Ll',  # Letter, lowercase
            'Lt',  # Letter, titlecase
            'Lm',  # Letter, modifier
            'Lo',  # Letter, other
            'Nd',  # Number, decimal digit
            'Nl',  # Number, letter
            'No',  # Number, other
            'Pc',  # Punctuation, connector
            'Pd',  # Punctuation, dash
            'Ps',  # Punctuation, open
            'Pe',  # Punctuation, close
            'Pi',  # Punctuation, initial quote
            'Pf',  # Punctuation, final quote
            'Po',  # Punctuation, other
            'Sm',  # Symbol, math
            'Sc',  # Symbol, currency
            'Sk',  # Symbol, modifier
            'Zs',  # Separator, space
            'Zl',  # Separator, line
            'Zp',  # Separator, paragraph
            'Cc',  # Other, control (包括换行符等)
        }
        
        # 额外保留的特殊字符
        self.extra_allowed_chars = {
            '\n', '\r', '\t',  # 换行符和制表符
            ' ',               # 空格
        }
        
        # 支持的文件扩展名
        self.supported_extensions = {
            '.py', '.txt', '.md', '.rst', '.json', '.yaml', '.yml',
            '.js', '.ts', '.html', '.css', '.xml', '.csv', '.log',
            '.ini', '.cfg', '.conf', '.sh', '.bat', '.sql'
        }
    
    def is_char_allowed(self, char: str) -> bool:
        """检查字符是否允许保留"""
        if char in self.extra_allowed_chars:
            return True
        
        category = unicodedata.category(char)
        return category in self.allowed_categories
    
    def remove_special_chars(self, text: str) -> tuple[str, int]:
        """移除文本中的特殊字符"""
        cleaned_chars = []
        removed_count = 0
        
        for char in text:
            if self.is_char_allowed(char):
                cleaned_chars.append(char)
            else:
                removed_count += 1
                # 记录被移除的字符（用于调试）
                if removed_count <= 10:  # 只记录前10个
                    logger.debug(f"移除字符: '{char}' (U+{ord(char):04X}) - {unicodedata.name(char, 'UNKNOWN')}")
        
        return ''.join(cleaned_chars), removed_count
    
    def detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB用于检测
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
                confidence = result.get('confidence', 0)
                
                if confidence < 0.7:
                    logger.warning(f"文件 {file_path} 编码检测置信度较低: {confidence:.2f}")
                    encoding = 'utf-8'  # 默认使用UTF-8
                
                return encoding
        except Exception as e:
            logger.warning(f"无法检测文件 {file_path} 的编码: {e}")
            return 'utf-8'
    
    def backup_file(self, file_path: Path) -> Optional[Path]:
        """备份文件"""
        if not self.backup:
            return None
        
        backup_path = file_path.with_suffix(file_path.suffix + '.backup')
        counter = 1
        
        # 如果备份文件已存在，添加数字后缀
        while backup_path.exists():
            backup_path = file_path.with_suffix(f'{file_path.suffix}.backup.{counter}')
            counter += 1
        
        try:
            import shutil
            shutil.copy2(file_path, backup_path)
            logger.debug(f"已备份: {file_path} -> {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"备份文件失败 {file_path}: {e}")
            return None
    
    def process_file(self, file_path: Path) -> bool:
        """处理单个文件"""
        try:
            # 检查文件扩展名
            if file_path.suffix.lower() not in self.supported_extensions:
                logger.debug(f"跳过不支持的文件类型: {file_path}")
                return False
            
            # 检测编码
            encoding = self.detect_encoding(file_path)
            
            # 读取文件内容
            try:
                with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                    original_content = f.read()
            except UnicodeDecodeError:
                # 如果UTF-8失败，尝试其他编码
                for fallback_encoding in ['gbk', 'gb2312', 'latin1']:
                    try:
                        with open(file_path, 'r', encoding=fallback_encoding, errors='replace') as f:
                            original_content = f.read()
                        encoding = fallback_encoding
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    logger.error(f"无法读取文件 {file_path}")
                    self.stats['errors'] += 1
                    return False
            
            # 移除特殊字符
            cleaned_content, removed_count = self.remove_special_chars(original_content)
            
            self.stats['files_processed'] += 1
            
            if removed_count > 0:
                logger.info(f"文件 {file_path}: 移除了 {removed_count} 个特殊字符")
                
                if not self.dry_run:
                    # 备份原文件
                    backup_path = self.backup_file(file_path)
                    
                    # 写入清理后的内容
                    with open(file_path, 'w', encoding='utf-8', newline='') as f:
                        f.write(cleaned_content)
                    
                    logger.info(f"已更新文件: {file_path}")
                    if backup_path:
                        logger.info(f"备份文件: {backup_path}")
                
                self.stats['files_modified'] += 1
                self.stats['chars_removed'] += removed_count
            else:
                logger.debug(f"文件 {file_path}: 无需修改")
            
            return True
            
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            self.stats['errors'] += 1
            return False
    
    def process_directory(self, directory: Path, recursive: bool = True, 
                         exclude_patterns: List[str] = None) -> None:
        """处理目录中的所有文件"""
        if exclude_patterns is None:
            exclude_patterns = [
                '*.backup*', '__pycache__', '.git', '.svn', 
                'node_modules', '.venv', 'venv', '*.pyc'
            ]
        
        # 编译排除模式
        exclude_regexes = [re.compile(pattern.replace('*', '.*')) for pattern in exclude_patterns]
        
        def should_exclude(path: Path) -> bool:
            path_str = str(path)
            return any(regex.search(path_str) for regex in exclude_regexes)
        
        if recursive:
            pattern = '**/*'
        else:
            pattern = '*'
        
        for file_path in directory.glob(pattern):
            if file_path.is_file() and not should_exclude(file_path):
                self.process_file(file_path)
    
    def print_stats(self) -> None:
        """打印统计信息"""
        print("\n" + "="*60)
        print("特殊字符清理统计")
        print("="*60)
        print(f"处理文件数: {self.stats['files_processed']}")
        print(f"修改文件数: {self.stats['files_modified']}")
        print(f"移除字符数: {self.stats['chars_removed']}")
        print(f"错误文件数: {self.stats['errors']}")
        print("="*60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="移除文件中的emoji、unicode等特殊符号",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python remove_special_chars.py /path/to/directory
  python remove_special_chars.py /path/to/directory --no-backup --dry-run
  python remove_special_chars.py /path/to/directory --exclude "*.log" "temp*"
        """
    )
    
    parser.add_argument('path', help='要处理的文件或目录路径')
    parser.add_argument('--no-backup', action='store_true', help='不创建备份文件')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际修改文件')
    parser.add_argument('--no-recursive', action='store_true', help='不递归处理子目录')
    parser.add_argument('--exclude', nargs='*', default=[], help='排除的文件模式')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 验证路径
    target_path = Path(args.path)
    if not target_path.exists():
        logger.error(f"路径不存在: {target_path}")
        return 1
    
    # 创建清理器
    remover = SpecialCharRemover(
        backup=not args.no_backup,
        dry_run=args.dry_run
    )
    
    if args.dry_run:
        print("🔍 预览模式 - 不会实际修改文件")
    
    # 处理文件或目录
    if target_path.is_file():
        remover.process_file(target_path)
    else:
        remover.process_directory(
            target_path,
            recursive=not args.no_recursive,
            exclude_patterns=args.exclude
        )
    
    # 打印统计信息
    remover.print_stats()
    
    return 0

if __name__ == '__main__':
    exit(main())
