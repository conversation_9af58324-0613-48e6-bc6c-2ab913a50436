#!/usr/bin/env python3
"""
StateManager性能优化验证测试
测试优化后的三个核心问题是否得到解决
"""
import asyncio
import time
import sys
import os
from typing import Dict, Any, List

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

# Mock必要的依赖
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARN: {msg}")  
    def error(self, msg): print(f"ERROR: {msg}")
    def debug(self, msg): pass
    def critical(self, msg): print(f"CRITICAL: {msg}")

class MockMetrics:
    def record(self, *args, **kwargs): pass
    def increment(self, *args, **kwargs): pass

# 创建mock模块
import types
utils_module = types.ModuleType('utils')
utils_module.logger = types.ModuleType('logger')
utils_module.logger.get_logger = lambda name: MockLogger()
utils_module.metrics = types.ModuleType('metrics')
utils_module.metrics.get_metrics_collector = lambda: MockMetrics()
utils_module.recovery = types.ModuleType('recovery')

# Mock ExponentialBackoff
class MockExponentialBackoff:
    def __init__(self, max_retries=3, base_delay=1.0, max_delay=30.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
    
    async def execute(self, func):
        return await func()

utils_module.recovery.ExponentialBackoff = MockExponentialBackoff

sys.modules['utils'] = utils_module
sys.modules['utils.logger'] = utils_module.logger
sys.modules['utils.metrics'] = utils_module.metrics
sys.modules['utils.recovery'] = utils_module.recovery

# 现在可以导入StateManager
from src.distributed.state import StateManager, StateScope


class StateOptimizationTest:
    """StateManager优化测试类"""
    
    def __init__(self):
        self.test_results = {}
    
    async def test_set_method_performance(self):
        """测试set()方法的异步写入性能"""
        print("\n🧪 测试set()方法异步写入性能...")
        
        try:
            config = {
                'host_id': 'test-node',
                'cluster_id': 'test-cluster',
                'test_mode': True,  # 测试模式
                'l2_batch_size': 10,
                'l2_batch_timeout': 50,
                'l2_queue_size': 100
            }
            
            state_manager = StateManager(config)
            await state_manager.start()
            
            # 测试大量set操作的性能
            test_data = []
            for i in range(50):
                test_data.append({
                    'key': f'test_key_{i}',
                    'value': {'data': f'value_{i}', 'timestamp': time.time()},
                    'scope': StateScope.LOCAL
                })
            
            # 性能测试
            start_time = time.perf_counter()
            
            tasks = []
            for data in test_data:
                task = state_manager.set(
                    key=data['key'],
                    value=data['value'],
                    scope=data['scope']
                )
                tasks.append(task)
            
            # 等待所有set操作完成
            results = await asyncio.gather(*tasks)
            
            # 等待L2批量处理完成
            await asyncio.sleep(0.2)
            
            total_time = (time.perf_counter() - start_time) * 1000
            
            success_count = sum(1 for r in results if r)
            
            result = {
                'total_operations': len(test_data),
                'successful_operations': success_count,
                'total_time_ms': total_time,
                'avg_time_per_operation_ms': total_time / len(test_data),
                'performance_improved': total_time < 100,  # 期望<100ms
                'l1_cache_size': len(state_manager.l1_cache),
                'l2_queue_size': state_manager._l2_write_queue.qsize()
            }
            
            await state_manager.stop()
            
            print(f"  总操作数: {result['total_operations']}")
            print(f"  成功操作数: {result['successful_operations']}")
            print(f"  总时间: {result['total_time_ms']:.2f}ms")
            print(f"  平均每操作: {result['avg_time_per_operation_ms']:.4f}ms")
            print(f"  L1缓存大小: {result['l1_cache_size']}")
            print(f"  性能提升: {'✅' if result['performance_improved'] else '❌'}")
            
            self.test_results['set_performance'] = result
            return result
            
        except Exception as e:
            print(f"❌ set()方法性能测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_increment_optimization(self):
        """测试increment()方法的本地缓存优化"""
        print("\n🧪 测试increment()方法本地缓存优化...")
        
        try:
            config = {
                'host_id': 'test-node',
                'cluster_id': 'test-cluster',
                'test_mode': True,
                'counter_sync_threshold': 5
            }
            
            state_manager = StateManager(config)
            await state_manager.start()
            
            # 测试increment操作性能
            test_counters = ['counter_1', 'counter_2', 'counter_3']
            increment_count = 20
            
            start_time = time.perf_counter()
            
            # 执行多次increment操作
            for counter in test_counters:
                for i in range(increment_count):
                    await state_manager.increment(counter, 1, StateScope.LOCAL)
            
            total_time = (time.perf_counter() - start_time) * 1000
            
            # 验证本地缓存
            local_counter_count = len(state_manager._local_counters)
            local_dirty_count = len(state_manager._local_dirty_flags)
            
            # 等待可能的同步操作
            await asyncio.sleep(0.1)
            
            result = {
                'counter_names': test_counters,
                'increments_per_counter': increment_count,
                'total_increments': len(test_counters) * increment_count,
                'total_time_ms': total_time,
                'avg_time_per_increment_ms': total_time / (len(test_counters) * increment_count),
                'local_counters_created': local_counter_count,
                'local_dirty_flags': local_dirty_count,
                'performance_good': total_time < 50,  # 期望<50ms
                'local_optimization_working': local_counter_count > 0
            }
            
            await state_manager.stop()
            
            print(f"  计数器数量: {len(test_counters)}")
            print(f"  总递增次数: {result['total_increments']}")
            print(f"  总时间: {result['total_time_ms']:.2f}ms")
            print(f"  平均每次递增: {result['avg_time_per_increment_ms']:.4f}ms")
            print(f"  本地缓存计数器: {result['local_counters_created']}")
            print(f"  本地缓存优化: {'✅' if result['local_optimization_working'] else '❌'}")
            print(f"  性能良好: {'✅' if result['performance_good'] else '❌'}")
            
            self.test_results['increment_optimization'] = result
            return result
            
        except Exception as e:
            print(f"❌ increment()优化测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_non_blocking_sync(self):
        """测试非阻塞同步循环优化"""
        print("\n🧪 测试非阻塞同步循环优化...")
        
        try:
            config = {
                'host_id': 'test-node',
                'cluster_id': 'test-cluster',
                'test_mode': True,
                'sync_enabled': True,
                'sync_interval': 0.1,  # 100ms间隔
                'cleanup_concurrency': 2
            }
            
            state_manager = StateManager(config)
            await state_manager.start()
            
            # 添加一些需要清理的L1缓存数据
            for i in range(10):
                await state_manager.set(f'temp_key_{i}', f'temp_value_{i}', ttl=1)  # 1ms TTL
            
            # 等待一段时间让同步循环运行
            start_time = time.perf_counter()
            
            # 在同步循环运行时执行其他操作，测试是否被阻塞
            operation_results = []
            for i in range(20):
                start_op = time.perf_counter()
                success = await state_manager.set(f'concurrent_key_{i}', f'value_{i}')
                op_time = (time.perf_counter() - start_op) * 1000
                operation_results.append({'success': success, 'time_ms': op_time})
                
                # 短暂等待
                await asyncio.sleep(0.01)
            
            total_test_time = (time.perf_counter() - start_time) * 1000
            
            # 检查清理工作者是否运行
            cleanup_workers_count = len(state_manager._cleanup_workers)
            cleanup_queue_size = state_manager._cleanup_queue.qsize()
            
            await state_manager.stop()
            
            # 分析结果
            successful_ops = sum(1 for r in operation_results if r['success'])
            max_op_time = max(r['time_ms'] for r in operation_results)
            avg_op_time = sum(r['time_ms'] for r in operation_results) / len(operation_results)
            
            result = {
                'total_concurrent_operations': len(operation_results),
                'successful_operations': successful_ops,
                'total_test_time_ms': total_test_time,
                'max_operation_time_ms': max_op_time,
                'avg_operation_time_ms': avg_op_time,
                'cleanup_workers_started': cleanup_workers_count,
                'non_blocking_performance': max_op_time < 20,  # 期望单个op<20ms
                'cleanup_optimization_working': cleanup_workers_count > 0
            }
            
            print(f"  并发操作数: {result['total_concurrent_operations']}")
            print(f"  成功操作数: {result['successful_operations']}")
            print(f"  总测试时间: {result['total_test_time_ms']:.2f}ms")
            print(f"  最大操作时间: {result['max_operation_time_ms']:.4f}ms")
            print(f"  平均操作时间: {result['avg_operation_time_ms']:.4f}ms")
            print(f"  清理工作者数: {result['cleanup_workers_started']}")
            print(f"  非阻塞性能: {'✅' if result['non_blocking_performance'] else '❌'}")
            print(f"  清理优化工作: {'✅' if result['cleanup_optimization_working'] else '❌'}")
            
            self.test_results['non_blocking_sync'] = result
            return result
            
        except Exception as e:
            print(f"❌ 非阻塞同步测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def run_all_tests(self):
        """运行所有优化测试"""
        print("🚀 开始StateManager性能优化验证测试")
        print("=" * 60)
        
        await self.test_set_method_performance()
        await self.test_increment_optimization()
        await self.test_non_blocking_sync()
        
        return self.test_results
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 StateManager性能优化效果总结")
        print("=" * 60)
        
        optimizations_passed = 0
        total_optimizations = 0
        
        # set()方法优化
        if 'set_performance' in self.test_results:
            total_optimizations += 1
            set_result = self.test_results['set_performance']
            if set_result['performance_improved']:
                print("✅ set()方法优化: 异步写入性能提升")
                optimizations_passed += 1
            else:
                print("❌ set()方法优化: 性能未达到预期")
        
        # increment()优化
        if 'increment_optimization' in self.test_results:
            total_optimizations += 1
            inc_result = self.test_results['increment_optimization']
            if inc_result['local_optimization_working'] and inc_result['performance_good']:
                print("✅ increment()优化: 本地缓存和批量同步工作正常")
                optimizations_passed += 1
            else:
                print("❌ increment()优化: 本地缓存或性能需要改进")
        
        # 非阻塞同步
        if 'non_blocking_sync' in self.test_results:
            total_optimizations += 1
            sync_result = self.test_results['non_blocking_sync']
            if sync_result['non_blocking_performance'] and sync_result['cleanup_optimization_working']:
                print("✅ 非阻塞同步: 清理工作者和并发性能良好")
                optimizations_passed += 1
            else:
                print("❌ 非阻塞同步: 性能或清理机制需要改进")
        
        print(f"\n🎯 优化成功率: {optimizations_passed}/{total_optimizations} ({optimizations_passed/max(total_optimizations, 1)*100:.0f}%)")
        
        print("\n📈 核心性能指标:")
        
        if 'set_performance' in self.test_results:
            set_perf = self.test_results['set_performance']
            print(f"  • set()平均时间: {set_perf['avg_time_per_operation_ms']:.4f}ms")
        
        if 'increment_optimization' in self.test_results:
            inc_perf = self.test_results['increment_optimization']
            print(f"  • increment()平均时间: {inc_perf['avg_time_per_increment_ms']:.4f}ms")
            print(f"  • 本地缓存计数器: {inc_perf['local_counters_created']}个")
        
        if 'non_blocking_sync' in self.test_results:
            sync_perf = self.test_results['non_blocking_sync']
            print(f"  • 并发操作最大时间: {sync_perf['max_operation_time_ms']:.4f}ms")
            print(f"  • 清理工作者数量: {sync_perf['cleanup_workers_started']}个")
        
        return optimizations_passed >= total_optimizations * 0.8  # 80%成功率为通过


async def main():
    """主测试函数"""
    tester = StateOptimizationTest()
    results = await tester.run_all_tests()
    success = tester.print_summary()
    
    if success:
        print("\n🎉 StateManager性能优化测试通过！")
        print("🚀 三个关键性能问题已得到有效解决：")
        print("   1. ✅ set()方法异步写入 - 消除同步阻塞")
        print("   2. ✅ increment()本地缓存 - 减少网络调用") 
        print("   3. ✅ 非阻塞清理任务 - 并发性能提升")
        return 0
    else:
        print("\n⚠️  StateManager性能优化测试部分失败，需要进一步调整")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)