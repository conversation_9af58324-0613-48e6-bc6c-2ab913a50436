#!/usr/bin/env python3
"""
核心功能简单测试
直接测试交易信号和基础功能，避免复杂导入
"""
import asyncio
import time
import sys
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class SignalType(Enum):
    """信号类型"""
    OPEN_POSITION = "open_position"
    CLOSE_POSITION = "close_position"
    MODIFY_POSITION = "modify_position"


@dataclass
class TradeSignal:
    """交易信号"""
    signal_id: str
    account_id: str
    signal_type: SignalType
    symbol: str
    action: str
    volume: float
    price: float
    timestamp: float
    metadata: Optional[Dict] = None


class MockJetStreamClient:
    """模拟JetStream客户端"""
    
    def __init__(self):
        self.published_messages = []
        self.connected = True
    
    async def publish(self, subject: str, data: Any) -> bool:
        """模拟发布消息"""
        self.published_messages.append({
            'subject': subject,
            'data': data,
            'timestamp': time.time()
        })
        return True
    
    async def subscribe(self, subject: str, callback):
        """模拟订阅"""
        pass


class CoreTradingSystem:
    """核心交易系统"""
    
    def __init__(self, account_id: str, host_id: str):
        self.account_id = account_id
        self.host_id = host_id
        self.jetstream = MockJetStreamClient()
        self.signal_buffer = []
        self.batch_size = 10
        self.batch_timeout = 0.1
        self.stats = {
            'signals_processed': 0,
            'batches_sent': 0,
            'errors': 0,
            'start_time': time.time()
        }
        self.running = False
    
    async def start(self):
        """启动系统"""
        self.running = True
        self.stats['start_time'] = time.time()
        print(f"✅ 交易系统已启动 - 账户: {self.account_id}, 主机: {self.host_id}")
    
    async def stop(self):
        """停止系统"""
        self.running = False
        if self.signal_buffer:
            await self._flush_signal_buffer()
        print(f"🛑 交易系统已停止 - 账户: {self.account_id}")
    
    async def process_signal(self, signal: TradeSignal):
        """处理交易信号"""
        try:
            # 验证信号
            if not self._validate_signal(signal):
                raise ValueError(f"无效信号: {signal.signal_id}")
            
            # 添加到缓冲区
            self.signal_buffer.append(signal)
            self.stats['signals_processed'] += 1
            
            # 检查是否需要刷新缓冲区
            if len(self.signal_buffer) >= self.batch_size:
                await self._flush_signal_buffer()
            
            return True
            
        except Exception as e:
            self.stats['errors'] += 1
            print(f"❌ 处理信号失败: {e}")
            return False
    
    def _validate_signal(self, signal: TradeSignal) -> bool:
        """验证信号"""
        if not signal.signal_id:
            return False
        if not signal.account_id:
            return False
        if signal.volume <= 0:
            return False
        if signal.price <= 0:
            return False
        return True
    
    async def _flush_signal_buffer(self):
        """刷新信号缓冲区"""
        if not self.signal_buffer:
            return
        
        try:
            # 准备批量数据
            batch_data = {
                'account_id': self.account_id,
                'host_id': self.host_id,
                'signals': [self._signal_to_dict(s) for s in self.signal_buffer],
                'batch_id': f"batch_{int(time.time() * 1000)}",
                'timestamp': time.time()
            }
            
            # 发布到JetStream
            subject = f"MT5.TRADES.{self.account_id}"
            await self.jetstream.publish(subject, batch_data)
            
            self.stats['batches_sent'] += 1
            signal_count = len(self.signal_buffer)
            self.signal_buffer.clear()
            
            print(f"📤 发送批次: {signal_count} 个信号")
            
        except Exception as e:
            self.stats['errors'] += 1
            print(f"❌ 发送批次失败: {e}")
    
    def _signal_to_dict(self, signal: TradeSignal) -> Dict:
        """将信号转换为字典"""
        return {
            'signal_id': signal.signal_id,
            'account_id': signal.account_id,
            'signal_type': signal.signal_type.value,
            'symbol': signal.symbol,
            'action': signal.action,
            'volume': signal.volume,
            'price': signal.price,
            'timestamp': signal.timestamp,
            'metadata': signal.metadata or {}
        }
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        uptime = time.time() - self.stats['start_time']
        return {
            **self.stats,
            'uptime': uptime,
            'signals_per_second': self.stats['signals_processed'] / max(uptime, 1),
            'buffer_size': len(self.signal_buffer),
            'published_messages': len(self.jetstream.published_messages)
        }


class TestSuite:
    """测试套件"""
    
    def __init__(self):
        self.test_results = []
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始核心功能测试")
        print("=" * 50)
        
        # 基础功能测试
        await self._test_basic_functionality()
        
        # 性能测试
        await self._test_performance()
        
        # 压力测试
        await self._test_stress()
        
        # 故障测试
        await self._test_error_handling()
        
        # 生成报告
        self._generate_report()
    
    async def _test_basic_functionality(self):
        """基础功能测试"""
        print("\n📋 基础功能测试")
        print("-" * 25)
        
        # 测试1: 系统初始化
        try:
            system = CoreTradingSystem("TEST_MASTER_001", "test-host-001")
            await system.start()
            
            assert system.running is True
            assert system.account_id == "TEST_MASTER_001"
            assert system.host_id == "test-host-001"
            
            await system.stop()
            self._record_test("系统初始化", True, "")
            print("✅ 系统初始化测试通过")
            
        except Exception as e:
            self._record_test("系统初始化", False, str(e))
            print(f"❌ 系统初始化测试失败: {e}")
        
        # 测试2: 信号处理
        try:
            system = CoreTradingSystem("TEST_MASTER_002", "test-host-002")
            await system.start()
            
            signal = TradeSignal(
                signal_id="test_signal_001",
                account_id="TEST_MASTER_002",
                signal_type=SignalType.OPEN_POSITION,
                symbol="EURUSD",
                action="BUY",
                volume=0.1,
                price=1.1000,
                timestamp=time.time()
            )
            
            result = await system.process_signal(signal)
            assert result is True
            assert len(system.signal_buffer) == 1
            assert system.stats['signals_processed'] == 1
            
            await system.stop()
            self._record_test("信号处理", True, "")
            print("✅ 信号处理测试通过")
            
        except Exception as e:
            self._record_test("信号处理", False, str(e))
            print(f"❌ 信号处理测试失败: {e}")
        
        # 测试3: 批处理
        try:
            system = CoreTradingSystem("TEST_MASTER_003", "test-host-003")
            system.batch_size = 5  # 小批次用于测试
            await system.start()
            
            # 发送多个信号
            for i in range(12):  # 超过批次大小
                signal = TradeSignal(
                    signal_id=f"batch_signal_{i:03d}",
                    account_id="TEST_MASTER_003",
                    signal_type=SignalType.OPEN_POSITION,
                    symbol="EURUSD",
                    action="BUY",
                    volume=0.01,
                    price=1.1000 + i * 0.0001,
                    timestamp=time.time()
                )
                await system.process_signal(signal)
            
            # 验证批处理
            assert system.stats['batches_sent'] >= 2  # 至少发送了2个批次
            assert len(system.jetstream.published_messages) >= 2
            
            await system.stop()
            self._record_test("批处理", True, f"发送了{system.stats['batches_sent']}个批次")
            print(f"✅ 批处理测试通过 - 发送了{system.stats['batches_sent']}个批次")
            
        except Exception as e:
            self._record_test("批处理", False, str(e))
            print(f"❌ 批处理测试失败: {e}")
    
    async def _test_performance(self):
        """性能测试"""
        print("\n⚡ 性能测试")
        print("-" * 25)
        
        try:
            system = CoreTradingSystem("PERF_MASTER", "perf-host")
            await system.start()
            
            # 高频信号测试
            signal_count = 10000
            start_time = time.perf_counter()
            
            for i in range(signal_count):
                signal = TradeSignal(
                    signal_id=f"perf_signal_{i:05d}",
                    account_id="PERF_MASTER",
                    signal_type=SignalType.OPEN_POSITION,
                    symbol="EURUSD",
                    action="BUY",
                    volume=0.01,
                    price=1.1000,
                    timestamp=time.time()
                )
                await system.process_signal(signal)
            
            await system.stop()
            
            end_time = time.perf_counter()
            duration = end_time - start_time
            throughput = signal_count / duration
            
            # 验证性能
            assert throughput > 1000  # 至少1000信号/秒
            assert system.stats['errors'] == 0
            
            self._record_test("高频信号处理", True, f"{throughput:.0f}信号/秒")
            print(f"✅ 高频信号处理测试通过 - {throughput:.0f}信号/秒")
            
        except Exception as e:
            self._record_test("高频信号处理", False, str(e))
            print(f"❌ 高频信号处理测试失败: {e}")
    
    async def _test_stress(self):
        """压力测试"""
        print("\n💪 压力测试")
        print("-" * 25)
        
        try:
            # 多系统并发测试
            systems = []
            for i in range(5):
                system = CoreTradingSystem(f"STRESS_MASTER_{i:02d}", f"stress-host-{i}")
                systems.append(system)
                await system.start()
            
            async def process_signals_for_system(system, count):
                for i in range(count):
                    signal = TradeSignal(
                        signal_id=f"stress_{system.account_id}_{i:04d}",
                        account_id=system.account_id,
                        signal_type=SignalType.OPEN_POSITION,
                        symbol="EURUSD",
                        action="BUY",
                        volume=0.01,
                        price=1.1000,
                        timestamp=time.time()
                    )
                    await system.process_signal(signal)
            
            # 并发处理
            start_time = time.perf_counter()
            tasks = []
            for system in systems:
                task = asyncio.create_task(process_signals_for_system(system, 2000))
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            # 停止所有系统
            for system in systems:
                await system.stop()
            
            end_time = time.perf_counter()
            duration = end_time - start_time
            total_signals = len(systems) * 2000
            throughput = total_signals / duration
            
            # 验证压力测试
            assert throughput > 2000  # 至少2000信号/秒
            total_errors = sum(s.stats['errors'] for s in systems)
            assert total_errors == 0
            
            self._record_test("并发压力测试", True, f"{len(systems)}系统, {throughput:.0f}信号/秒")
            print(f"✅ 并发压力测试通过 - {len(systems)}个系统，{throughput:.0f}信号/秒")
            
        except Exception as e:
            self._record_test("并发压力测试", False, str(e))
            print(f"❌ 并发压力测试失败: {e}")
    
    async def _test_error_handling(self):
        """错误处理测试"""
        print("\n🛡️ 错误处理测试")
        print("-" * 25)
        
        try:
            system = CoreTradingSystem("ERROR_TEST", "error-host")
            await system.start()
            
            # 测试无效信号
            invalid_signals = [
                TradeSignal("", "ERROR_TEST", SignalType.OPEN_POSITION, "EURUSD", "BUY", 0.1, 1.1000, time.time()),  # 空ID
                TradeSignal("test", "", SignalType.OPEN_POSITION, "EURUSD", "BUY", 0.1, 1.1000, time.time()),  # 空账户
                TradeSignal("test", "ERROR_TEST", SignalType.OPEN_POSITION, "EURUSD", "BUY", 0, 1.1000, time.time()),  # 零量
                TradeSignal("test", "ERROR_TEST", SignalType.OPEN_POSITION, "EURUSD", "BUY", 0.1, 0, time.time()),  # 零价格
            ]
            
            error_count = 0
            for signal in invalid_signals:
                result = await system.process_signal(signal)
                if not result:
                    error_count += 1
            
            await system.stop()
            
            # 验证错误处理
            assert error_count == len(invalid_signals)  # 所有无效信号都应该被拒绝
            assert system.stats['errors'] == len(invalid_signals)
            
            self._record_test("错误处理", True, f"正确拒绝{error_count}个无效信号")
            print(f"✅ 错误处理测试通过 - 正确拒绝{error_count}个无效信号")
            
        except Exception as e:
            self._record_test("错误处理", False, str(e))
            print(f"❌ 错误处理测试失败: {e}")
    
    def _record_test(self, test_name: str, passed: bool, details: str):
        """记录测试结果"""
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'details': details
        })
    
    def _generate_report(self):
        """生成测试报告"""
        print("\n📊 测试报告")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['passed']])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result['passed'] else "❌"
            print(f"{status_icon} {result['name']}")
            if result['details']:
                print(f"   {result['details']}")
        
        if failed_tests == 0:
            print("\n🎉 所有测试通过！核心交易系统功能正常")
            return True
        else:
            print(f"\n⚠️ 有{failed_tests}个测试失败，需要检查")
            return False


async def main():
    """主函数"""
    test_suite = TestSuite()
    success = await test_suite.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
