# 分层并行初始化配置
# 支持开发、测试、生产环境的差异化调优

# 初始化超时配置
initialization:
  timeouts:
    # 核心服务初始化超时（秒）
    # 包括容器初始化和服务发现启动
    core: 30.0
    
    # 账户启动超时（秒）  
    # 所有本地账户并行启动的总超时时间
    accounts: 60.0
    
    # 最终化操作超时（秒）
    # 服务注册和健康监控启动
    finalization: 15.0

# 服务重试配置
services:
  queue_manager:
    # 是否启用重试机制
    retryable: true
    
    # 最大重试次数
    max_retries: 3
    
    # 重试延迟基数（秒），使用指数退避 delay * (2^attempt)
    retry_delay: 2.0
  
  message_router:
    retryable: true
    max_retries: 2
    retry_delay: 1.0
  
  # 其他服务的重试配置...

# MT5 API配置
mt5:
  api:
    # 最大并发连接数，避免API过载
    # 生产环境建议4-8，测试环境可以更高
    max_concurrent_connections: 4
    
    # 连接超时（秒）
    connection_timeout: 10.0
    
    # API调用速率限制（次/秒）
    rate_limit: 80

# 环境特定配置示例
environments:
  development:
    initialization:
      timeouts:
        core: 60.0      # 开发环境允许更长的超时
        accounts: 120.0
        finalization: 30.0
    mt5:
      api:
        max_concurrent_connections: 8  # 开发环境可以更高并发
  
  production:
    initialization:
      timeouts:
        core: 25.0      # 生产环境要求更快启动
        accounts: 45.0
        finalization: 10.0
    services:
      queue_manager:
        max_retries: 5  # 生产环境更多重试
        retry_delay: 1.5
    mt5:
      api:
        max_concurrent_connections: 4  # 生产环境保守设置