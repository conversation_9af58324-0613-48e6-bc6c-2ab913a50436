#!/usr/bin/env python3
"""
本地内存消息队列实现
作为NATS和Redis的降级模式，提供本地高性能消息传递
适用于单机部署或网络故障时的紧急模式
"""

import asyncio
import time
from collections import defaultdict, deque
from typing import Dict, Any, Optional, List, Callable, Set
import threading
from dataclasses import dataclass

from .message_queue_interface import MessageQueueInterface, QueueConfig, QueueStatus
from .message_types import MessageEnvelope
from .priority_queue import MessagePriority

from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class LocalSubscription:
    """本地订阅信息"""
    subject: str
    callback: Callable[[MessageEnvelope], None]
    queue_group: Optional[str] = None
    is_active: bool = True


class LocalMessageQueue(MessageQueueInterface):
    """
    本地内存消息队列实现
    
    特点：
    1. 零网络延迟 - 内存中直接传递
    2. 高性能 - 无序列化开销
    3. 降级模式 - 网络故障时使用
    4. 支持优先级 - 分层队列处理
    5. 线程安全 - 支持多线程访问
    """
    
    def __init__(self, config: QueueConfig):
        super().__init__(config)
        self._connected = False
        
        # 优先级队列 - 每个优先级一个队列
        self._priority_queues: Dict[MessagePriority, deque] = {
            priority: deque() for priority in MessagePriority
        }
        
        # 订阅管理
        self._subscriptions: Dict[str, List[LocalSubscription]] = defaultdict(list)
        self._queue_groups: Dict[str, Dict[str, List[LocalSubscription]]] = defaultdict(lambda: defaultdict(list))
        
        # 请求-响应映射
        self._pending_requests: Dict[str, asyncio.Future] = {}
        
        # 消费者任务
        self._consumer_tasks: List[asyncio.Task] = []
        self._stop_consuming = False
        
        # 线程安全锁
        self._queue_lock = threading.RLock()
        self._subscription_lock = threading.RLock()
        
        # 队列深度统计
        self._queue_depths: Dict[str, int] = defaultdict(int)
        
        # 消息TTL管理
        self._message_ttl: Dict[str, float] = {}  # message_id -> expire_time
        self._ttl_cleanup_task: Optional[asyncio.Task] = None
    
    async def connect(self) -> bool:
        """连接（本地队列立即可用）"""
        try:
            logger.info("初始化本地内存消息队列...")
            
            # 启动消费者工作器
            await self._start_consumer_workers()
            
            # 启动TTL清理任务
            self._ttl_cleanup_task = asyncio.create_task(self._ttl_cleanup_loop())
            
            self._connected = True
            self.status = QueueStatus.CONNECTED
            logger.info("本地MessageQueue初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"本地队列初始化失败: {e}")
            self.status = QueueStatus.FAILED
            return False
    
    async def disconnect(self) -> None:
        """断开连接"""
        try:
            self._stop_consuming = True
            
            # 停止消费者任务
            for task in self._consumer_tasks:
                task.cancel()
            
            if self._consumer_tasks:
                await asyncio.gather(*self._consumer_tasks, return_exceptions=True)
            
            self._consumer_tasks.clear()
            
            # 停止TTL清理任务
            if self._ttl_cleanup_task:
                self._ttl_cleanup_task.cancel()
                try:
                    await self._ttl_cleanup_task
                except asyncio.CancelledError:
                    pass
            
            # 清理数据
            with self._queue_lock:
                for queue in self._priority_queues.values():
                    queue.clear()
            
            with self._subscription_lock:
                self._subscriptions.clear()
                self._queue_groups.clear()
            
            self._pending_requests.clear()
            self._queue_depths.clear()
            self._message_ttl.clear()
            
            self._connected = False
            self.status = QueueStatus.DISCONNECTED
            logger.info("本地MessageQueue已断开连接")
            
        except Exception as e:
            logger.error(f"断开本地队列连接失败: {e}")
    
    async def publish(
        self, 
        subject: str, 
        message: MessageEnvelope,
        priority: MessagePriority = MessagePriority.REALTIME_QUERY
    ) -> bool:
        """发布消息到本地队列"""
        if not self._connected:
            logger.error("本地队列未连接")
            return False
        
        try:
            start_time = time.perf_counter()
            
            # 添加TTL管理
            if message.ttl:
                self._message_ttl[message.id] = time.time() + message.ttl
            
            # 入队到对应优先级队列
            queue_item = {
                'subject': subject,
                'message': message,
                'priority': priority,
                'enqueue_time': time.time()
            }
            
            with self._queue_lock:
                self._priority_queues[priority].append(queue_item)
                self._queue_depths[subject] += 1
            
            # 更新指标
            latency_ms = (time.perf_counter() - start_time) * 1000
            self._update_metrics("publish", True, latency_ms)
            
            logger.debug(f"本地消息发布成功: {subject} (优先级: {priority.name})")
            return True
            
        except Exception as e:
            logger.error(f"本地发布消息失败: {e}")
            self._update_metrics("publish", False)
            return False
    
    async def publish_batch(
        self, 
        messages: List[tuple[str, MessageEnvelope, MessagePriority]]
    ) -> int:
        """批量发布消息"""
        if not self._connected:
            logger.error("本地队列未连接")  
            return 0
        
        success_count = 0
        
        try:
            with self._queue_lock:
                for subject, message, priority in messages:
                    try:
                        if message.ttl:
                            self._message_ttl[message.id] = time.time() + message.ttl
                        
                        queue_item = {
                            'subject': subject,
                            'message': message,
                            'priority': priority,
                            'enqueue_time': time.time()
                        }
                        
                        self._priority_queues[priority].append(queue_item)
                        self._queue_depths[subject] += 1
                        success_count += 1
                        
                    except Exception as e:
                        logger.error(f"批量发布单条消息失败: {e}")
            
            logger.debug(f"本地批量发布完成: {success_count}/{len(messages)}")
            
        except Exception as e:
            logger.error(f"本地批量发布失败: {e}")
        
        return success_count
    
    async def subscribe(
        self, 
        subject: str, 
        callback: Callable[[MessageEnvelope], None],
        queue_group: Optional[str] = None
    ) -> bool:
        """订阅本地消息"""
        if not self._connected:
            logger.error("本地队列未连接")
            return False
        
        try:
            subscription = LocalSubscription(
                subject=subject,
                callback=callback,
                queue_group=queue_group,
                is_active=True
            )
            
            with self._subscription_lock:
                self._subscriptions[subject].append(subscription)
                
                if queue_group:
                    self._queue_groups[queue_group][subject].append(subscription)
            
            logger.debug(f"本地订阅成功: {subject} (队列组: {queue_group})")
            return True
            
        except Exception as e:
            logger.error(f"本地订阅失败: {e}")
            return False
    
    async def unsubscribe(self, subject: str) -> bool:
        """取消本地订阅"""
        try:
            with self._subscription_lock:
                if subject in self._subscriptions:
                    # 标记为非活跃状态
                    for subscription in self._subscriptions[subject]:
                        subscription.is_active = False
                    
                    # 从队列组中移除
                    for group_subjects in self._queue_groups.values():
                        if subject in group_subjects:
                            for subscription in group_subjects[subject]:
                                subscription.is_active = False
                    
                    del self._subscriptions[subject]
            
            logger.info(f"本地取消订阅成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"本地取消订阅失败: {e}")
            return False
    
    async def request(
        self, 
        subject: str, 
        message: MessageEnvelope,
        timeout_ms: int = 5000
    ) -> Optional[MessageEnvelope]:
        """本地请求-响应模式"""
        if not self._connected:
            logger.error("本地队列未连接")
            return None
        
        try:
            # 创建响应Future
            request_id = f"req_{int(time.time() * 1000)}_{id(message)}"
            response_future = asyncio.Future()
            self._pending_requests[request_id] = response_future
            
            # 在消息头中添加请求ID
            message.headers['request_id'] = request_id
            message.headers['reply_required'] = 'true'
            
            # 发布请求
            await self.publish(subject, message)
            
            # 等待响应
            try:
                response = await asyncio.wait_for(response_future, timeout=timeout_ms / 1000)
                return response
            except asyncio.TimeoutError:
                logger.warning(f"本地请求超时: {subject}")
                return None
            finally:
                # 清理
                self._pending_requests.pop(request_id, None)
                
        except Exception as e:
            logger.error(f"本地请求失败: {e}")
            return None
    
    async def get_queue_depth(self, subject: str) -> int:
        """获取队列深度"""
        with self._queue_lock:
            depth = self._queue_depths.get(subject, 0)
            self.metrics.queue_depth = sum(self._queue_depths.values())
            return depth
    
    async def purge_queue(self, subject: str) -> bool:
        """清空队列"""
        try:
            with self._queue_lock:
                # 从所有优先级队列中移除相关消息
                for priority_queue in self._priority_queues.values():
                    items_to_remove = []
                    for item in priority_queue:
                        if item['subject'] == subject:
                            items_to_remove.append(item)
                    
                    for item in items_to_remove:
                        priority_queue.remove(item)
                
                # 重置深度计数
                self._queue_depths[subject] = 0
            
            logger.info(f"本地队列清空成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"清空本地队列失败: {e}")
            return False
    
    async def _start_consumer_workers(self):
        """启动消费者工作器"""
        worker_count = max(1, self.config.connection_params.get('worker_count', 4))
        
        for i in range(worker_count):
            task = asyncio.create_task(self._consumer_worker(f"worker-{i}"))
            self._consumer_tasks.append(task)
        
        logger.info(f"启动了{worker_count}个本地消费者工作器")
    
    async def _consumer_worker(self, worker_name: str):
        """消费者工作器"""
        while not self._stop_consuming:
            try:
                # 按优先级顺序处理消息
                message_processed = False
                
                for priority in MessagePriority:
                    with self._queue_lock:
                        if self._priority_queues[priority]:
                            queue_item = self._priority_queues[priority].popleft()
                            message_processed = True
                    
                    if message_processed:
                        await self._process_message(queue_item)
                        break
                
                if not message_processed:
                    # 没有消息时短暂等待
                    await asyncio.sleep(0.001)  # 1ms
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"消费者工作器{worker_name}错误: {e}")
                await asyncio.sleep(0.1)
    
    async def _process_message(self, queue_item: Dict[str, Any]):
        """处理单个消息"""
        try:
            subject = queue_item['subject']
            message = queue_item['message']
            
            # 检查TTL
            if message.id in self._message_ttl:
                if time.time() > self._message_ttl[message.id]:
                    # 消息已过期
                    del self._message_ttl[message.id]
                    with self._queue_lock:
                        self._queue_depths[subject] = max(0, self._queue_depths[subject] - 1)
                    return
            
            # 更新队列深度
            with self._queue_lock:
                self._queue_depths[subject] = max(0, self._queue_depths[subject] - 1)
            
            # 处理请求-响应
            if message.headers.get('reply_required') == 'true':
                await self._handle_request_message(message)
                return
            
            # 查找订阅者
            subscribers = []
            
            with self._subscription_lock:
                # 直接主题匹配
                if subject in self._subscriptions:
                    subscribers.extend([s for s in self._subscriptions[subject] if s.is_active])
                
                # 通配符匹配 (简单实现)
                for sub_subject, sub_list in self._subscriptions.items():
                    if self._match_subject(subject, sub_subject):
                        subscribers.extend([s for s in sub_list if s.is_active])
            
            # 分发消息
            await self._distribute_message(message, subscribers)
            
            self._update_metrics("receive", True)
            
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            self._update_metrics("receive", False)
    
    async def _distribute_message(self, message: MessageEnvelope, subscribers: List[LocalSubscription]):
        """分发消息给订阅者"""
        if not subscribers:
            return
        
        # 按队列组分组
        group_subscribers = defaultdict(list)
        broadcast_subscribers = []
        
        for subscriber in subscribers:
            if subscriber.queue_group:
                group_subscribers[subscriber.queue_group].append(subscriber)
            else:
                broadcast_subscribers.append(subscriber)
        
        # 广播模式：所有订阅者都收到
        for subscriber in broadcast_subscribers:
            try:
                if asyncio.iscoroutinefunction(subscriber.callback):
                    await subscriber.callback(message)
                else:
                    subscriber.callback(message)
            except Exception as e:
                logger.error(f"回调执行失败: {e}")
        
        # 队列组模式：每组只有一个订阅者收到（负载均衡）
        for group, group_subs in group_subscribers.items():
            if group_subs:
                # 简单轮询选择
                subscriber = group_subs[hash(message.id) % len(group_subs)]
                try:
                    if asyncio.iscoroutinefunction(subscriber.callback):
                        await subscriber.callback(message)
                    else:
                        subscriber.callback(message)
                except Exception as e:
                    logger.error(f"队列组回调执行失败: {e}")
    
    async def _handle_request_message(self, message: MessageEnvelope):
        """处理请求消息（生成响应）"""
        request_id = message.headers.get('request_id')
        if request_id and request_id in self._pending_requests:
            # 这是对之前请求的响应
            future = self._pending_requests[request_id]
            if not future.done():
                future.set_result(message)
    
    def _match_subject(self, message_subject: str, subscription_subject: str) -> bool:
        """主题匹配（支持简单通配符）"""
        if subscription_subject == message_subject:
            return True
        
        # 支持 * 通配符
        if '*' in subscription_subject:
            pattern = subscription_subject.replace('*', '.*')
            import re
            return re.match(f"^{pattern}$", message_subject) is not None
        
        return False
    
    async def _ttl_cleanup_loop(self):
        """TTL清理循环"""
        while not self._stop_consuming:
            try:
                current_time = time.time()
                expired_messages = []
                
                # 查找过期消息
                for message_id, expire_time in self._message_ttl.items():
                    if current_time > expire_time:
                        expired_messages.append(message_id)
                
                # 清理过期消息
                for message_id in expired_messages:
                    del self._message_ttl[message_id]
                
                if expired_messages:
                    logger.debug(f"清理了{len(expired_messages)}条过期消息")
                
                # 每10秒清理一次
                await asyncio.sleep(10)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"TTL清理失败: {e}")
                await asyncio.sleep(5)
    
    async def health_check(self) -> bool:
        """本地队列健康检查"""
        return self._connected and self.status == QueueStatus.CONNECTED