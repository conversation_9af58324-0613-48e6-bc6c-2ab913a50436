"""
多MT5终端管理模块
支持单个主机运行多个MT5终端，动态切换主从角色
模块化设计，避免重复代码

架构层次:
1. 基础层: models.py, base_service.py, terminal_pool.py
2. 功能层: clean_role_manager.py, enterprise_terminal_manager.py
3. 兼容层: terminal_manager.py (兼容性包装器)
"""

# 共享模型
from .models import (
    Terminal, TerminalRole, TerminalStatus,
    RoleChangeEvent, RoleConfiguration, RoleSwitchEvent, SystemSummary
)

# 基础服务
from .base_service import BaseTerminalService

# 连接池
from .terminal_pool import TerminalPool, PoolConfig

# 企业级管理器
from .terminal_manager import EnterpriseTerminalManager, EnterpriseTerminalConfig

# 兼容性管理器
from .terminal_manager import TerminalManager, TerminalConfig, MultiTerminalManager

__all__ = [
    # 模型
    'Terminal',
    'TerminalRole',
    'TerminalStatus',
    'RoleChangeEvent',
    'RoleConfiguration',
    'RoleSwitchEvent',
    'SystemSummary',

    # 基础服务
    'BaseTerminalService',

    # 连接池
    'TerminalPool',
    'PoolConfig',

    # 企业级管理器
    'EnterpriseTerminalManager',
    'EnterpriseTerminalConfig',

    # 兼容性管理器
    'TerminalManager',
    'TerminalConfig',
    'MultiTerminalManager'
]
