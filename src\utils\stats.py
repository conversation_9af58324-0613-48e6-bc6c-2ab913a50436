"""
线程安全的统计管理器
提供高性能的并发统计数据更新和查询
"""
import asyncio
import time
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from collections import defaultdict
import threading

from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class StatValue:
    """统计值包装器"""
    value: Union[int, float] = 0
    last_updated: float = field(default_factory=time.time)
    update_count: int = 0


class ThreadSafeStats:
    """线程安全的统计管理器"""
    
    def __init__(self, instance_id: str = "default"):
        self.instance_id = instance_id
        self._stats: Dict[str, StatValue] = defaultdict(StatValue)
        self._lock = asyncio.Lock()
        self._sync_lock = threading.RLock()  # 用于同步方法
        
        # 性能统计
        self._start_time = time.time()
        self._operation_count = 0
        
        logger.info(f"线程安全统计管理器初始化: {instance_id}")
    
    async def increment(self, key: str, value: Union[int, float] = 1) -> Union[int, float]:
        """异步增加统计值"""
        async with self._lock:
            stat = self._stats[key]
            stat.value += value
            stat.last_updated = time.time()
            stat.update_count += 1
            self._operation_count += 1
            return stat.value
    
    async def set_value(self, key: str, value: Union[int, float]) -> Union[int, float]:
        """异步设置统计值"""
        async with self._lock:
            stat = self._stats[key]
            stat.value = value
            stat.last_updated = time.time()
            stat.update_count += 1
            self._operation_count += 1
            return stat.value
    
    async def update_multiple(self, updates: Dict[str, Union[int, float]]):
        """批量更新多个统计值"""
        async with self._lock:
            current_time = time.time()
            for key, value in updates.items():
                if key.endswith('_increment'):
                    # 支持增量更新
                    real_key = key[:-10]  # 移除 '_increment' 后缀
                    stat = self._stats[real_key]
                    stat.value += value
                else:
                    # 直接设置值
                    stat = self._stats[key]
                    stat.value = value
                
                stat.last_updated = current_time
                stat.update_count += 1
                self._operation_count += 1
    
    async def get_value(self, key: str) -> Union[int, float]:
        """异步获取统计值"""
        async with self._lock:
            return self._stats[key].value
    
    async def get_stat_info(self, key: str) -> Dict[str, Any]:
        """获取完整的统计信息"""
        async with self._lock:
            stat = self._stats[key]
            return {
                'value': stat.value,
                'last_updated': stat.last_updated,
                'update_count': stat.update_count,
                'age_seconds': time.time() - stat.last_updated
            }
    
    async def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有统计信息"""
        async with self._lock:
            result = {}
            current_time = time.time()
            
            for key, stat in self._stats.items():
                result[key] = {
                    'value': stat.value,
                    'last_updated': stat.last_updated,
                    'update_count': stat.update_count,
                    'age_seconds': current_time - stat.last_updated
                }
            
            # 添加管理器统计
            result['_manager_stats'] = {
                'instance_id': self.instance_id,
                'operation_count': self._operation_count,
                'uptime_seconds': current_time - self._start_time,
                'total_stats': len(self._stats)
            }
            
            return result
    
    def increment_sync(self, key: str, value: Union[int, float] = 1) -> Union[int, float]:
        """同步增加统计值（用于非异步上下文）"""
        with self._sync_lock:
            stat = self._stats[key]
            stat.value += value
            stat.last_updated = time.time()
            stat.update_count += 1
            self._operation_count += 1
            return stat.value
    
    def get_value_sync(self, key: str) -> Union[int, float]:
        """同步获取统计值"""
        with self._sync_lock:
            return self._stats[key].value
    
    def set_value_sync(self, key: str, value: Union[int, float]) -> Union[int, float]:
        """同步设置统计值"""
        with self._sync_lock:
            stat = self._stats[key]
            stat.value = value
            stat.last_updated = time.time()
            stat.update_count += 1
            self._operation_count += 1
            return stat.value
    
    async def reset_stats(self, prefix: Optional[str] = None):
        """重置统计（可选择性重置指定前缀的统计）"""
        async with self._lock:
            if prefix:
                keys_to_reset = [k for k in self._stats.keys() if k.startswith(prefix)]
                for key in keys_to_reset:
                    self._stats[key] = StatValue()
                logger.info(f"重置统计（前缀: {prefix}）: {len(keys_to_reset)} 项")
            else:
                self._stats.clear()
                logger.info("重置所有统计")
            
            self._operation_count += 1
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        async with self._lock:
            current_time = time.time()
            uptime = current_time - self._start_time
            
            return {
                'instance_id': self.instance_id,
                'uptime_seconds': uptime,
                'total_operations': self._operation_count,
                'operations_per_second': self._operation_count / uptime if uptime > 0 else 0,
                'total_stats_tracked': len(self._stats),
                'average_stat_updates': sum(s.update_count for s in self._stats.values()) / len(self._stats) if self._stats else 0,
                'most_active_stat': max(self._stats.items(), key=lambda x: x[1].update_count)[0] if self._stats else None
            }
    
    def __repr__(self):
        return f"ThreadSafeStats(id={self.instance_id}, stats={len(self._stats)}, ops={self._operation_count})"


class StatsManager:
    """全局统计管理器"""
    
    _instances: Dict[str, ThreadSafeStats] = {}
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls, instance_id: str = "default") -> ThreadSafeStats:
        """获取或创建统计实例"""
        with cls._lock:
            if instance_id not in cls._instances:
                cls._instances[instance_id] = ThreadSafeStats(instance_id)
                logger.info(f"创建新的统计实例: {instance_id}")
            
            return cls._instances[instance_id]
    
    @classmethod
    def remove_instance(cls, instance_id: str):
        """移除统计实例"""
        with cls._lock:
            if instance_id in cls._instances:
                del cls._instances[instance_id]
                logger.info(f"移除统计实例: {instance_id}")
    
    @classmethod
    def get_all_instances(cls) -> Dict[str, ThreadSafeStats]:
        """获取所有统计实例"""
        with cls._lock:
            return cls._instances.copy()


# 便捷函数
def get_stats(instance_id: str = "default") -> ThreadSafeStats:
    """获取线程安全统计实例的便捷函数"""
    return StatsManager.get_instance(instance_id)


# 装饰器支持
def track_calls(stat_key: str, instance_id: str = "default"):
    """装饰器：跟踪函数调用次数"""
    def decorator(func):
        stats = get_stats(instance_id)
        
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                await stats.increment(f"{stat_key}_calls")
                try:
                    result = await func(*args, **kwargs)
                    await stats.increment(f"{stat_key}_success")
                    return result
                except Exception as e:
                    await stats.increment(f"{stat_key}_errors")
                    raise
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                stats.increment_sync(f"{stat_key}_calls")
                try:
                    result = func(*args, **kwargs)
                    stats.increment_sync(f"{stat_key}_success")
                    return result
                except Exception as e:
                    stats.increment_sync(f"{stat_key}_errors")
                    raise
            return sync_wrapper
    
    return decorator


def track_timing(stat_key: str, instance_id: str = "default"):
    """装饰器：跟踪函数执行时间"""
    def decorator(func):
        stats = get_stats(instance_id)
        
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    await stats.set_value(f"{stat_key}_last_duration", execution_time)
                    await stats.increment(f"{stat_key}_total_time", execution_time)
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    await stats.set_value(f"{stat_key}_last_error_duration", execution_time)
                    raise
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    stats.set_value_sync(f"{stat_key}_last_duration", execution_time)
                    stats.increment_sync(f"{stat_key}_total_time", execution_time)
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    stats.set_value_sync(f"{stat_key}_last_error_duration", execution_time)
                    raise
            return sync_wrapper
    
    return decorator