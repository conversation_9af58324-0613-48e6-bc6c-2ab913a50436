"""
Message Types Definition - Unified Data Models
Integrates MT5 process isolation related data models, avoiding duplicate definitions
All data models use Pydantic for consistency and validation
"""
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel, Field
from datetime import datetime


class SignalType(str, Enum):
    """信号类型枚举"""
    POSITION_OPEN = "POSITION_OPEN"
    POSITION_CLOSE = "POSITION_CLOSE"
    POSITION_MODIFY = "POSITION_MODIFY"
    ORDER_PLACE = "ORDER_PLACE"
    ORDER_CANCEL = "ORDER_CANCEL"
    ORDER_MODIFY = "ORDER_MODIFY"
    HEARTBEAT = "HEARTBEAT"
    STATUS_UPDATE = "STATUS_UPDATE"
    ERROR = "ERROR"


class TradeType(int, Enum):
    """交易类型枚举"""
    BUY = 0
    SELL = 1
    BUY_LIMIT = 2
    SELL_LIMIT = 3
    BUY_STOP = 4
    SELL_STOP = 5
    BUY_STOP_LIMIT = 6
    SELL_STOP_LIMIT = 7


class PositionSignalData(BaseModel):
    """持仓信号数据"""
    symbol: str
    volume: float
    action: str  # "BUY" or "SELL"
    price: float
    sl: float = 0.0
    tp: float = 0.0
    comment: Optional[str] = None
    magic: Optional[int] = None
    account_id: str
    copy_magic_number: int  # 从主账户配对配置中传递的魔术号


class OrderSignalData(BaseModel):
    """订单信号数据"""
    symbol: str
    volume: float
    action: str  # "BUY" or "SELL"
    price: float
    order_type: int  # MT5订单类型
    sl: float = 0.0
    tp: float = 0.0
    comment: Optional[str] = None
    magic: Optional[int] = None
    account_id: str
    copy_magic_number: int  # 从主账户配对配置中传递的魔术号


class TradeSignal(BaseModel):
    """交易信号数据结构"""
    type: SignalType
    master_id: str
    slave_ids: Optional[List[str]] = None
    ticket: int
    data: Union[PositionSignalData, OrderSignalData, Dict[str, Any]]
    timestamp: float
    capture_latency_ms: Optional[float] = None
    processing_latency_ms: Optional[float] = None
    
    class Config:
        use_enum_values = True


class PositionData(BaseModel):
    """持仓数据结构"""
    symbol: str
    volume: float
    type: TradeType
    price: float
    sl: float = 0.0
    tp: float = 0.0
    time: Optional[int] = None
    profit: Optional[float] = None
    swap: Optional[float] = None
    magic: Optional[int] = None
    comment: Optional[str] = None


class OrderData(BaseModel):
    """订单数据结构"""
    symbol: str
    volume: float
    type: TradeType
    price: float
    sl: float = 0.0
    tp: float = 0.0
    expiration: Optional[int] = None
    magic: Optional[int] = None
    comment: Optional[str] = None


class HeartbeatData(BaseModel):
    """心跳数据结构"""
    node_id: str
    node_type: str  # "master" or "slave"
    status: str
    timestamp: float
    uptime: float
    last_activity: float
    metrics: Dict[str, Any] = Field(default_factory=dict)


class StatusUpdateData(BaseModel):
    """状态更新数据结构"""
    node_id: str
    status: str
    message: Optional[str] = None
    timestamp: float
    details: Dict[str, Any] = Field(default_factory=dict)


class ErrorData(BaseModel):
    """错误数据结构"""
    error_type: str
    error_code: Optional[str] = None
    message: str
    timestamp: float
    node_id: str
    context: Dict[str, Any] = Field(default_factory=dict)


class MessageEnvelope(BaseModel):
    """消息信封"""
    id: str
    subject: str
    payload: Dict[str, Any]
    headers: Dict[str, str] = Field(default_factory=dict)
    timestamp: float
    ttl: Optional[int] = None  # 消息生存时间（秒）
    
    @classmethod
    def create_trade_signal(cls, signal: TradeSignal, subject: str = None) -> 'MessageEnvelope':
        """创建交易信号消息"""
        import uuid
        
        if subject is None:
            subject = f"trades.{signal.master_id}"
        
        return cls(
            id=str(uuid.uuid4()),
            subject=subject,
            payload=signal.model_dump(),
            timestamp=signal.timestamp,
            ttl=60  # 1分钟TTL
        )
    
    @classmethod
    def create_heartbeat(cls, heartbeat: HeartbeatData, subject: str = None) -> 'MessageEnvelope':
        """创建心跳消息"""
        import uuid
        
        if subject is None:
            subject = f"heartbeat.{heartbeat.node_id}"
        
        return cls(
            id=str(uuid.uuid4()),
            subject=subject,
            payload=heartbeat.model_dump(),
            timestamp=heartbeat.timestamp,
            ttl=30  # 30秒TTL
        )
    
    @classmethod
    def create_status_update(cls, status: StatusUpdateData, subject: str = None) -> 'MessageEnvelope':
        """创建状态更新消息"""
        import uuid
        
        if subject is None:
            subject = f"status.{status.node_id}"
        
        return cls(
            id=str(uuid.uuid4()),
            subject=subject,
            payload=status.model_dump(),
            timestamp=status.timestamp,
            ttl=300  # 5分钟TTL
        )
    
    @classmethod
    def create_error(cls, error: ErrorData, subject: str = None) -> 'MessageEnvelope':
        """创建错误消息"""
        import uuid
        
        if subject is None:
            subject = f"errors.{error.node_id}"
        
        return cls(
            id=str(uuid.uuid4()),
            subject=subject,
            payload=error.model_dump(),
            timestamp=error.timestamp,
            ttl=3600  # 1小时TTL
        )


class MessageBatch(BaseModel):
    """消息批次"""
    messages: List[MessageEnvelope]
    batch_id: str
    timestamp: float
    
    @classmethod
    def create(cls, messages: List[MessageEnvelope]) -> 'MessageBatch':
        """创建消息批次"""
        import uuid
        
        return cls(
            messages=messages,
            batch_id=str(uuid.uuid4()),
            timestamp=datetime.now().timestamp()
        )


class SubscriptionConfig(BaseModel):
    """订阅配置"""
    subject: str
    queue_group: Optional[str] = None
    durable_name: Optional[str] = None
    deliver_policy: str = "new"  # "new", "all", "last", "by_start_sequence", "by_start_time"
    ack_policy: str = "explicit"  # "none", "all", "explicit"
    max_deliver: int = 3
    ack_wait: int = 30000  # 30秒


class PublishConfig(BaseModel):
    """发布配置"""
    subject: str
    headers: Dict[str, str] = Field(default_factory=dict)
    expect_one_response: bool = False
    timeout: Optional[float] = None


# 预定义的主题模式
class Subjects:
    """主题模式"""
    
    # 交易信号
    TRADES = "trades"
    TRADES_MASTER = "trades.{master_id}"
    TRADES_SLAVE = "trades.{slave_id}"

    # 分布式交易信号 - 更新为新的分层流主题
    TRADES_LOCAL = "MT5.LOCAL.{host_id}.{account_id}"
    SIGNALS_PRIORITY = "MT5.SIGNALS.{priority}.{account_id}"
    TRADES_REMOTE = "MT5.RPC.{method}.{account_id}"

    # 跨主机通信
    CROSS_HOST = "MT5.CROSS.{host_id}.{account_id}"
    HOST_DISCOVERY = "MT5.DISCOVERY.{host_id}"
    HOST_HEARTBEAT = "MT5.HEARTBEAT.{host_id}"
    
    # 心跳
    HEARTBEAT = "heartbeat"
    HEARTBEAT_NODE = "heartbeat.{node_id}"
    
    # 状态更新
    STATUS = "status"
    STATUS_NODE = "status.{node_id}"
    
    # 错误
    ERRORS = "errors"
    ERRORS_NODE = "errors.{node_id}"
    
    # 系统控制
    CONTROL = "control"
    CONTROL_START = "control.start"
    CONTROL_STOP = "control.stop"
    CONTROL_RESTART = "control.restart"
    
    # 配置更新
    CONFIG = "config"
    CONFIG_UPDATE = "config.update"
    CONFIG_RELOAD = "config.reload"
    
    # 监控
    METRICS = "metrics"
    METRICS_NODE = "metrics.{node_id}"
    
    @classmethod
    def get_trades_subject(cls, master_id: str) -> str:
        """获取交易主题"""
        return cls.TRADES_MASTER.format(master_id=master_id)
    
    @classmethod
    def get_heartbeat_subject(cls, node_id: str) -> str:
        """获取心跳主题"""
        return cls.HEARTBEAT_NODE.format(node_id=node_id)
    
    @classmethod
    def get_status_subject(cls, node_id: str) -> str:
        """获取状态主题"""
        return cls.STATUS_NODE.format(node_id=node_id)
    
    @classmethod
    def get_errors_subject(cls, node_id: str) -> str:
        """获取错误主题"""
        return cls.ERRORS_NODE.format(node_id=node_id)
    
    @classmethod
    def get_metrics_subject(cls, node_id: str) -> str:
        """获取指标主题"""
        return cls.METRICS_NODE.format(node_id=node_id)

    # 分布式主题辅助方法
    @classmethod
    def get_local_trades_subject(cls, host_id: str, account_id: str) -> str:
        """获取本地交易主题"""
        return cls.TRADES_LOCAL.format(host_id=host_id, account_id=account_id)

    @classmethod
    def get_signals_priority_subject(cls, priority: str, account_id: str) -> str:
        """获取优先级信号主题"""
        return cls.SIGNALS_PRIORITY.format(priority=priority, account_id=account_id)

    @classmethod
    def get_rpc_subject(cls, method: str, account_id: str) -> str:
        """获取RPC主题"""
        return cls.TRADES_REMOTE.format(method=method, account_id=account_id)

    @classmethod
    def get_cross_host_subject(cls, host_id: str, account_id: str) -> str:
        """获取跨主机主题"""
        return cls.CROSS_HOST.format(host_id=host_id, account_id=account_id)

    @classmethod
    def get_host_discovery_subject(cls, host_id: str) -> str:
        """获取主机发现主题"""
        return cls.HOST_DISCOVERY.format(host_id=host_id)

    @classmethod
    def get_host_heartbeat_subject(cls, host_id: str) -> str:
        """获取主机心跳主题"""
        return cls.HOST_HEARTBEAT.format(host_id=host_id)


# ==================== MT5标准枚举 ====================

class PositionType(int, Enum):
    """持仓类型 (符合MT5标准)"""
    BUY = 0
    SELL = 1


class TradeMode(int, Enum):
    """账户交易模式 (符合MT5标准)"""
    DISABLED = 0
    LONG_ONLY = 1
    SHORT_ONLY = 2
    LONG_SHORT = 3


class MarginMode(int, Enum):
    """账户保证金模式 (符合MT5标准)"""
    NETTING = 0
    HEDGED = 1
    RETAIL_NETTING = 2
    RETAIL_HEDGING = 3


class TradeAction(str, Enum):
    """交易动作枚举"""
    DEAL = "DEAL"
    PENDING = "PENDING"
    SLTP = "SLTP"
    MODIFY = "MODIFY"
    REMOVE = "REMOVE"


class OrderTypeEnum(str, Enum):
    """订单类型枚举"""
    # 市价单
    BUY = "BUY"
    SELL = "SELL"
    # 限价单
    BUY_LIMIT = "BUY_LIMIT"
    SELL_LIMIT = "SELL_LIMIT"
    # 止损单
    BUY_STOP = "BUY_STOP"
    SELL_STOP = "SELL_STOP"
    # 止损限价单
    BUY_STOP_LIMIT = "BUY_STOP_LIMIT"
    SELL_STOP_LIMIT = "SELL_STOP_LIMIT"


class OrderFillingType(int, Enum):
    """订单执行类型 (符合MT5标准)"""
    FOK = 0  # Fill Or Kill
    IOC = 1  # Immediate Or Cancel
    RETURN = 2  # Return


class OrderTimeType(int, Enum):
    """订单有效期类型 (符合MT5标准)"""
    GTC = 0  # Good Till Cancelled
    DAY = 1  # Good Till Day
    SPECIFIED = 2  # Good Till Specified Time
    SPECIFIED_DAY = 3  # Good Till Specified Day


class OrderType(str, Enum):
    """订单类型枚举"""
    MARKET = "market"
    PENDING = "pending"
    POSITION = "position"


class CommandType(str, Enum):
    """命令类型枚举"""
    GET_ACCOUNT_INFO = "get_account_info"
    GET_POSITIONS = "get_positions"
    GET_ORDERS = "get_orders"
    SEND_ORDER = "send_order"
    CLOSE_POSITION = "close_position"
    MODIFY_POSITION = "modify_position"
    GET_HISTORY = "get_history"
    GET_SYMBOLS = "get_symbols"
    SHUTDOWN = "shutdown"
    HEARTBEAT = "heartbeat"


class ProcessState(str, Enum):
    """进程状态枚举"""
    NOT_STARTED = "not_started"
    CONNECTING = "connecting"
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"
    CRASHED = "crashed"


class CommandRequest(BaseModel):
    """命令请求模型 - 集成process.py优秀设计"""
    type: CommandType = Field(..., description="命令类型")
    params: Dict[str, Any] = Field(default_factory=dict, description="命令参数")
    request_id: str = Field(..., description="请求ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="请求时间")
    timeout: int = Field(default=30, description="超时时间（秒）")

    class Config:
        use_enum_values = True


class CommandResponse(BaseModel):
    """命令响应模型 - 集成process.py优秀设计"""
    type: str = Field(..., description="响应类型")
    account: str = Field(..., description="账户名称")
    status: str = Field(..., description="状态: success/error")
    data: Optional[Dict[str, Any]] = Field(default=None, description="响应数据")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    request_id: str = Field(..., description="对应的请求ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    execution_time_ms: Optional[float] = Field(default=None, description="执行时间（毫秒）")

    class Config:
        use_enum_values = True


class ProcessStatus(BaseModel):
    """进程状态信息 - 集成process.py优秀设计"""
    account_name: str = Field(..., description="账户名称")
    pid: int = Field(..., description="进程ID")
    status: ProcessState = Field(..., description="进程状态")
    is_alive: bool = Field(..., description="进程是否存活")
    uptime_seconds: float = Field(..., description="运行时间（秒）")
    last_heartbeat: datetime = Field(..., description="最后心跳时间")
    restart_count: int = Field(default=0, description="重启次数")
    error_count: int = Field(default=0, description="错误次数")
    connected: bool = Field(default=False, description="MT5连接状态")
    account_info: Optional[Dict[str, Any]] = Field(default=None, description="账户信息")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    memory_usage_mb: Optional[float] = Field(default=None, description="内存使用量（MB）")
    cpu_percent: Optional[float] = Field(default=None, description="CPU使用率（%）")

    class Config:
        use_enum_values = True


class AccountInfoModel(BaseModel):
    """账户信息模型 - 完整版本，集成process.py优秀设计"""
    login: int = Field(..., description="登录号")
    trade_mode: TradeMode = Field(..., description="交易模式")
    leverage: int = Field(..., description="杠杆")
    limit_orders: int = Field(..., description="限价单数量")
    margin_so_mode: int = Field(..., description="强制平仓模式")
    trade_allowed: bool = Field(..., description="是否允许交易")
    trade_expert: bool = Field(..., description="是否允许专家顾问")
    margin_mode: MarginMode = Field(..., description="保证金模式")
    currency_digits: int = Field(..., description="货币小数位数")
    fifo_close: bool = Field(..., description="FIFO平仓")
    
    # 余额相关
    balance: float = Field(..., description="余额")
    credit: float = Field(..., description="信贷")
    profit: float = Field(..., description="浮动盈亏")
    equity: float = Field(..., description="净值")
    margin: float = Field(..., description="已用保证金")
    margin_free: float = Field(..., description="可用保证金")
    margin_level: float = Field(..., description="保证金水平")
    margin_so_call: float = Field(..., description="追加保证金水平")
    margin_so_so: float = Field(..., description="强制平仓水平")
    margin_initial: float = Field(..., description="初始保证金")
    margin_maintenance: float = Field(..., description="维持保证金")
    assets: float = Field(..., description="资产")
    liabilities: float = Field(..., description="负债")
    commission_blocked: float = Field(..., description="被冻结的手续费")
    
    # 账户信息
    name: str = Field(default="", description="账户名称")
    server: str = Field(default="", description="服务器")
    currency: str = Field(default="", description="货币")
    company: str = Field(default="", description="公司名称")
    
    class Config:
        from_attributes = True
        use_enum_values = True


# 保持向后兼容性
AccountInfo = AccountInfoModel


class PositionModel(BaseModel):
    """持仓信息模型 - 完整版本，集成process.py优秀设计"""
    ticket: int = Field(..., description="持仓票据")
    time: datetime = Field(..., description="开仓时间")
    time_msc: int = Field(..., description="开仓时间（毫秒）")
    time_update: datetime = Field(..., description="更新时间")
    time_update_msc: int = Field(..., description="更新时间（毫秒）")
    type: PositionType = Field(..., description="持仓类型")  # 0=BUY, 1=SELL
    magic: int = Field(..., description="魔术数字")
    identifier: int = Field(..., description="持仓标识符")
    reason: int = Field(..., description="开仓原因")
    volume: float = Field(..., description="持仓量")
    price_open: float = Field(..., description="开仓价格")
    sl: float = Field(..., description="止损价格")
    tp: float = Field(..., description="止盈价格")
    price_current: float = Field(..., description="当前价格")
    swap: float = Field(..., description="库存费")
    profit: float = Field(..., description="盈亏")
    symbol: str = Field(..., description="交易品种")
    comment: str = Field(default="", description="注释")
    external_id: str = Field(default="", description="外部标识符")
    
    class Config:
        from_attributes = True
        use_enum_values = True


# 保持向后兼容性
Position = PositionModel


class TradeRequestModel(BaseModel):
    """
    交易请求模型 - 完整版本，集成process.py优秀设计
    该模型设计为可以完整描述MT5终端所支持的所有交易请求类型。
    """
    action: TradeAction = Field(..., description="交易动作")
    symbol: str = Field(..., description="交易品种")
    volume: float = Field(..., description="交易量")
    type: OrderTypeEnum = Field(..., description="订单类型")
    price: float = Field(default=0.0, description="价格")
    stoplimit: Optional[float] = Field(None, description="用于止损限价订单 (BUY_STOP_LIMIT, SELL_STOP_LIMIT)")
    sl: float = Field(default=0.0, description="止损价格")
    tp: float = Field(default=0.0, description="止盈价格")
    deviation: int = Field(default=20, description="价格偏差")
    magic: int = Field(default=0, description="魔术数字")
    comment: str = Field(default="TradeCopier", description="注释")
    type_time: OrderTimeType = Field(OrderTimeType.GTC, description="订单有效期类型")
    type_filling: OrderFillingType = Field(OrderFillingType.IOC, description="订单执行类型")
    expiration: Optional[datetime] = Field(None, description="用于有指定到期时间的订单")
    position: Optional[int] = Field(None, description="用于修改或平仓的持仓单号")
    position_by: Optional[int] = Field(None, description="用于平仓另一个持仓的持仓单号 (对冲)")
    order: Optional[int] = Field(None, description="用于修改或取消的挂单号")
    
    class Config:
        use_enum_values = True


# 保持向后兼容性
TradeRequest = TradeRequestModel


class TradeResultModel(BaseModel):
    """交易执行结果模型 - 完整版本，集成process.py优秀设计"""
    retcode: int = Field(..., description="返回代码")
    deal: int = Field(default=0, description="成交票据")
    order: int = Field(default=0, description="订单票据")
    volume: float = Field(default=0.0, description="成交量")
    price: float = Field(default=0.0, description="成交价格")
    bid: float = Field(default=0.0, description="买价")
    ask: float = Field(default=0.0, description="卖价")
    comment: str = Field(default="", description="注释")
    request_id: Optional[int] = Field(default=None, description="请求ID")
    retcode_external: Optional[int] = Field(default=None, description="外部返回代码")
    
    class Config:
        from_attributes = True


# 保持向后兼容性
TradeResult = TradeResultModel


class RestartPolicy(BaseModel):
    """重启策略模型"""
    enabled: bool = Field(default=True, description="是否启用自动重启")
    max_restarts: int = Field(default=5, description="最大重启次数")
    time_window: int = Field(default=3600, description="时间窗口（秒）")
    restart_delay: int = Field(default=30, description="重启延迟（秒）")
    config: Dict[str, Any] = Field(..., description="账户配置")
    count: int = Field(default=0, description="当前重启次数")
    last_time: float = Field(default=0, description="最后重启时间")


class SystemHealth(BaseModel):
    """系统健康状态模型"""
    total_processes: int = Field(..., description="总进程数")
    running_processes: int = Field(..., description="运行中进程数")
    connected_processes: int = Field(..., description="已连接进程数")
    error_processes: int = Field(..., description="错误进程数")
    total_memory_mb: float = Field(..., description="总内存使用量（MB）")
    average_cpu_percent: float = Field(..., description="平均CPU使用率（%）")
    uptime_seconds: float = Field(..., description="系统运行时间（秒）")
    last_check: datetime = Field(default_factory=datetime.now, description="最后检查时间")


class SymbolInfoModel(BaseModel):
    """交易品种信息模型 - 完整版本，集成process.py优秀设计"""
    name: str = Field(..., description="品种名称")
    basis: str = Field(default="", description="基础")
    currency_base: str = Field(default="", description="基础货币")
    currency_profit: str = Field(default="", description="利润货币")
    currency_margin: str = Field(default="", description="保证金货币")
    bank: str = Field(default="", description="银行")
    description: str = Field(default="", description="描述")
    path: str = Field(default="", description="路径")
    
    # 价格相关
    bid: float = Field(..., description="买价")
    ask: float = Field(..., description="卖价")
    point: float = Field(..., description="最小价格变动")
    digits: int = Field(..., description="小数位数")
    spread: int = Field(..., description="点差")
    
    # 交易限制
    volume_min: float = Field(..., description="最小交易量")
    volume_max: float = Field(..., description="最大交易量")
    volume_step: float = Field(..., description="交易量步长")
    trade_contract_size: float = Field(..., description="合约大小")
    trade_tick_value: float = Field(..., description="跳动价值")
    trade_tick_size: float = Field(..., description="跳动大小")
    
    # 其他属性
    trade_stops_level: int = Field(..., description="止损止盈水平")
    trade_freeze_level: int = Field(..., description="冻结水平")
    
    class Config:
        from_attributes = True


class MT5ConnectionConfig(BaseModel):
    """MT5 Connection Configuration Model"""
    account_id: str = Field(..., description="Account ID")
    login: int = Field(..., description="Login number")
    password: str = Field(..., description="Password")
    server: str = Field(..., description="Server")
    terminal_path: Optional[str] = Field(default=None, description="Terminal path")
    timeout: int = Field(default=30000, description="Timeout in milliseconds")
    portable: bool = Field(default=True, description="Portable mode")
    retry_attempts: int = Field(default=3, description="Retry attempts")


# ==================== Unified System Models ====================
# These models replace dataclass definitions throughout the system

class AccountInfo(BaseModel):
    """Account Information Model - replaces dataclass definitions"""
    account_id: str = Field(..., description="Account ID")
    host_id: str = Field(..., description="Host ID")
    role: str = Field(..., description="Account role: master, slave, auto")
    template: str = Field(..., description="Configuration template")
    enabled: bool = Field(default=True, description="Account enabled status")
    connection: Dict[str, Any] = Field(default_factory=dict, description="Connection config")
    properties: Dict[str, Any] = Field(default_factory=dict, description="Account properties")
    deployment: Dict[str, Any] = Field(default_factory=dict, description="Deployment config")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    config_path: str = Field(..., description="Configuration file path")
    last_updated: datetime = Field(default_factory=datetime.now, description="Last update time")


class PairingRule(BaseModel):
    """Pairing Rule Model - replaces dataclass definitions"""
    master_id: str = Field(..., description="Master account ID")
    slave_ids: List[str] = Field(..., description="Slave account IDs")
    strategy: str = Field(default="regular", description="Copy strategy: regular, reverse")
    enabled: bool = Field(default=True, description="Rule enabled status")
    copy_ratio: float = Field(default=1.0, description="Copy ratio")
    conditions: Dict[str, Any] = Field(default_factory=dict, description="Additional conditions")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation time")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class RouteInfo(BaseModel):
    """Route Information Model - replaces dataclass definitions"""
    source_account: str = Field(..., description="Source account ID")
    source_host: str = Field(..., description="Source host ID")
    target_accounts: List[str] = Field(..., description="Target account IDs")
    target_hosts: List[str] = Field(..., description="Target host IDs")
    strategy: str = Field(..., description="Routing strategy")
    priority: int = Field(default=1, description="Route priority")
    enabled: bool = Field(default=True, description="Route enabled status")


class TradeMapping(BaseModel):
    """Trade Mapping Model - replaces dataclass definitions"""
    mapping_id: str = Field(..., description="Mapping ID")
    master_account: str = Field(..., description="Master account ID")
    master_ticket: int = Field(..., description="Master ticket number")
    slave_account: str = Field(..., description="Slave account ID")
    slave_ticket: Optional[int] = Field(default=None, description="Slave ticket number")
    symbol: str = Field(default="", description="Trading symbol")
    volume: float = Field(default=0.0, description="Trade volume")
    copy_ratio: float = Field(default=1.0, description="Copy ratio")
    signal_uuid: str = Field(default="", description="Unique signal identifier")
    open_time: datetime = Field(default_factory=datetime.now, description="Open time")
    close_time: Optional[datetime] = Field(default=None, description="Close time")
    status: str = Field(default="active", description="Mapping status")


class Terminal(BaseModel):
    """Terminal Instance Model - replaces dataclass definitions"""
    terminal_id: str = Field(..., description="Terminal ID")
    account_id: str = Field(..., description="Account ID")
    login: int = Field(..., description="Login number")
    password: str = Field(..., description="Password")
    server: str = Field(..., description="Server")
    terminal_path: str = Field(..., description="Terminal path")
    data_path: str = Field(..., description="Data path")
    port: int = Field(default=0, description="Port number")
    pid: Optional[int] = Field(default=None, description="Process ID")
    status: str = Field(default="STOPPED", description="Terminal status")
    role: str = Field(default="IDLE", description="Terminal role")
    last_heartbeat: float = Field(default=0.0, description="Last heartbeat timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    start_count: int = Field(default=0, description="Start count")
    error_count: int = Field(default=0, description="Error count")
    uptime: float = Field(default=0.0, description="Uptime in seconds")


class MatchResult(BaseModel):
    """Match Result Model - replaces dataclass definitions"""
    confidence: float = Field(..., description="Match confidence score")
    matched_positions: List[Dict[str, Any]] = Field(..., description="Matched positions")
    execution_plan: List[Dict[str, Any]] = Field(..., description="Execution plan")
    warnings: List[str] = Field(default_factory=list, description="Warning messages")


class RoleChangeEvent(BaseModel):
    """Role Change Event Model - replaces dataclass definitions"""
    event_id: str = Field(..., description="Event ID")
    terminal_id: str = Field(..., description="Terminal ID")
    old_role: str = Field(..., description="Old role")
    new_role: str = Field(..., description="New role")
    timestamp: float = Field(..., description="Event timestamp")
    success: bool = Field(default=True, description="Success status")
    error_message: Optional[str] = Field(default=None, description="Error message")


class RoleConfiguration(BaseModel):
    """Role Configuration Model - replaces dataclass definitions"""
    config_id: str = Field(..., description="Configuration ID")
    name: str = Field(..., description="Configuration name")
    description: str = Field(..., description="Configuration description")
    role_type: str = Field(..., description="Role type")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Role parameters")
    constraints: Dict[str, Any] = Field(default_factory=dict, description="Role constraints")
    created_at: float = Field(..., description="Creation timestamp")
    last_used: float = Field(default=0.0, description="Last used timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class RoleSwitchEvent(BaseModel):
    """Role Switch Event Model - replaces dataclass definitions"""
    event_id: str = Field(..., description="Event ID")
    timestamp: float = Field(..., description="Event timestamp")
    old_config: str = Field(..., description="Old configuration")
    new_config: str = Field(..., description="New configuration")
    affected_terminals: List[str] = Field(..., description="Affected terminal IDs")
    initiated_by: str = Field(..., description="Initiated by user/system")
    reason: str = Field(..., description="Switch reason")
    status: str = Field(default="pending", description="Switch status")
    error_message: Optional[str] = Field(default=None, description="Error message")


class SystemSummary(BaseModel):
    """System Summary Model - replaces dataclass definitions"""
    total_terminals: int = Field(..., description="Total terminals")
    running_terminals: int = Field(..., description="Running terminals")
    master_terminals: int = Field(..., description="Master terminals")
    slave_terminals: int = Field(..., description="Slave terminals")
    idle_terminals: int = Field(..., description="Idle terminals")
    error_terminals: int = Field(..., description="Error terminals")
    system_uptime: float = Field(..., description="System uptime")
    last_update: float = Field(..., description="Last update timestamp")
    retry_delay: int = Field(default=5, description="重试延迟（秒）")