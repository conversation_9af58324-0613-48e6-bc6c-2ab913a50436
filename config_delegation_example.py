#!/usr/bin/env python3
"""
配置管理器委托模式示例
展示ConfigManager如何智能委托给专门的配置管理器
"""

from src.core.config_manager import get_config_manager

def demonstrate_delegation_pattern():
    """展示委托模式的使用方法"""
    print("🔧 ConfigManager 委托模式示例")
    print("=" * 50)
    
    # 获取配置管理器实例
    config_manager = get_config_manager()
    
    print("\n📋 配置获取示例：")
    
    # 1. 系统配置 - 由ConfigManager直接处理
    print("\n1. 系统配置（ConfigManager直接处理）:")
    try:
        log_level = config_manager.get('system.log_level', 'INFO')
        host_id = config_manager.get('system.host_id', 'localhost')
        print(f"   ✅ system.log_level: {log_level}")
        print(f"   ✅ system.host_id: {host_id}")
    except Exception as e:
        print(f"   ❌ 系统配置获取失败: {e}")
    
    # 2. 基础设施配置 - 由ConfigManager直接处理
    print("\n2. 基础设施配置（ConfigManager直接处理）:")
    try:
        nats_servers = config_manager.get('infrastructure.nats.servers', ['nats://localhost:4222'])
        redis_url = config_manager.get('infrastructure.redis.url', 'redis://localhost:6379')
        print(f"   ✅ infrastructure.nats.servers: {nats_servers}")
        print(f"   ✅ infrastructure.redis.url: {redis_url}")
    except Exception as e:
        print(f"   ❌ 基础设施配置获取失败: {e}")
    
    # 3. 账户配置 - 委托给AccountConfigManager
    print("\n3. 账户配置（委托给AccountConfigManager）:")
    try:
        # 假设存在一个测试账户
        account_login = config_manager.get('accounts.ACC001.login', None)
        account_server = config_manager.get('accounts.ACC001.server', None)
        print(f"   ✅ accounts.ACC001.login: {account_login}")
        print(f"   ✅ accounts.ACC001.server: {account_server}")
        
        # 获取账户能力
        max_lot_size = config_manager.get('accounts.ACC001.capabilities.max_lot_size', None)
        print(f"   ✅ accounts.ACC001.capabilities.max_lot_size: {max_lot_size}")
    except Exception as e:
        print(f"   ❌ 账户配置获取失败: {e}")
    
    # 4. 流配置 - 委托给StreamConfigManager
    print("\n4. 流配置（委托给StreamConfigManager）:")
    try:
        # 获取流配置
        local_stream = config_manager.get('streams.stream_config.local', None)
        print(f"   ✅ streams.stream_config.local: {local_stream}")
        
        # 获取消费者配置
        consumer_config = config_manager.get('streams.consumer_config.signal_processor', None)
        print(f"   ✅ streams.consumer_config.signal_processor: {consumer_config}")
    except Exception as e:
        print(f"   ❌ 流配置获取失败: {e}")
    
    print("\n🎯 委托模式的优势：")
    print("   • 系统配置：ConfigManager直接处理，高效快速")
    print("   • 账户配置：委托给AccountConfigManager，专业验证")
    print("   • 流配置：委托给StreamConfigManager，专业管理")
    print("   • 统一接口：所有配置通过一个get()方法获取")
    print("   • 职责分离：每个管理器专注于自己的领域")
    print("   • 易于扩展：可以轻松添加新的专门管理器")
    
    print("\n📊 配置摘要：")
    try:
        # 获取账户配置摘要
        account_summary = config_manager.get_account_config_summary()
        print(f"   • 总账户数: {account_summary.get('total_accounts', 0)}")
        print(f"   • 启用账户数: {account_summary.get('enabled_accounts', 0)}")
        print(f"   • 配置错误数: {len(account_summary.get('validation_errors', {}))}")
    except Exception as e:
        print(f"   ❌ 获取配置摘要失败: {e}")

if __name__ == "__main__":
    demonstrate_delegation_pattern()