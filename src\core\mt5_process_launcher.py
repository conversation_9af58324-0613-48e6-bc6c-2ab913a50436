#!/usr/bin/env python3
"""
MT5进程启动器 - 在每个MT5独立进程中运行
负责初始化进程内的监听器和执行器，并集成零拷贝通信
"""
import asyncio
import logging
import sys
import os
from pathlib import Path
from typing import Dict, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.service_container import UnifiedDependencyContainer
from src.core.service_discovery import UnifiedServiceDiscovery, ServiceEndpoint, ServiceType
from src.core.account_monitor import AccountMonitor
from src.core.trade_executor import TradeExecutor
from src.messaging.zerocopy import get_zero_copy_message_bus
from src.utils.memory_pool import get_memory_pool
from src.messaging.queue_manager import QueueManager
from src.utils.logger import setup_logger

# 设置日志
logger = setup_logger(f"mt5_process_{os.getpid()}")

class MT5ProcessLauncher:
    """MT5进程启动器"""
    
    def __init__(self, account_id: str, config: Dict):
        self.account_id = account_id
        self.config = config
        self.host_id = config.get('host_id', 'default')
        
        # 进程内的依赖注入容器
        self.container = UnifiedDependencyContainer()
        self.service_discovery = None
        
        # 组件实例
        self.monitor = None
        self.executor = None
        self.zero_copy_bus = None
        self.memory_pool = None
        
        # 端口分配（基于进程ID）
        self.monitor_port = 20000 + (os.getpid() % 1000)
        self.executor_port = 21000 + (os.getpid() % 1000)
        
        logger.info(f"🚀 MT5进程启动器初始化: {account_id} (PID: {os.getpid()})")
    
    async def initialize(self):
        """初始化进程内所有组件"""
        try:
            # 1. 初始化共享组件
            await self._initialize_shared_components()
            
            # 2. 注册进程内服务
            await self._register_services()
            
            # 3. 初始化容器
            results = await self.container.initialize_all(allow_partial_failure=False)
            
            # 4. 获取服务实例
            self.monitor = await self.container.get(AccountMonitor)
            self.executor = await self.container.get(TradeExecutor)
            
            # 5. 注册到服务发现
            await self._register_to_service_discovery()
            
            logger.info(f"✅ MT5进程初始化完成: {self.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ MT5进程初始化失败: {self.account_id} - {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return False
    
    async def _initialize_shared_components(self):
        """初始化共享组件（内存池、消息总线）"""
        # 获取全局内存池实例
        self.memory_pool = get_unified_memory_pool()
        await self.memory_pool.start()
        
        # 获取零拷贝消息总线
        self.zero_copy_bus = get_zero_copy_message_bus(f"{self.host_id}_bus")
        await self.zero_copy_bus.start()
        
        # 创建服务发现客户端
        self.service_discovery = UnifiedServiceDiscovery()
        await self.service_discovery.start()
        
        logger.info("✅ 共享组件初始化完成")
    
    async def _register_services(self):
        """注册进程内服务到容器"""
        # 注册监听器
        self.container.register_singleton(
            AccountMonitor,
            factory=lambda: self._create_monitor(),
            health_check=self._check_monitor_health,
            required=True,
            initialization_order=1
        )
        
        # 注册执行器
        self.container.register_singleton(
            TradeExecutor,
            factory=lambda: self._create_executor(),
            health_check=self._check_executor_health,
            required=True,
            initialization_order=2
        )
        
        logger.info("✅ 服务注册完成")
    
    def _create_monitor(self) -> AccountMonitor:
        """创建监听器实例"""
        # 创建增强的监听器配置
        monitor_config = {
            **self.config,
            'zero_copy_enabled': True,
            'memory_pool': self.memory_pool,
            'zero_copy_bus': self.zero_copy_bus,
            'process_id': os.getpid()
        }
        
        return AccountMonitor(
            account_name=self.account_id,
            config=monitor_config
        )
    
    def _create_executor(self) -> TradeExecutor:
        """创建执行器实例"""
        # 创建增强的执行器配置
        executor_config = {
            **self.config,
            'zero_copy_enabled': True,
            'memory_pool': self.memory_pool,
            'zero_copy_bus': self.zero_copy_bus,
            'process_id': os.getpid()
        }
        
        return TradeExecutor(
            account_name=self.account_id,
            config=executor_config
        )
    
    async def _register_to_service_discovery(self):
        """注册到服务发现"""
        # 注册监听器服务
        monitor_service_id = await self.service_discovery.register_service(
            service_name=f"mt5-monitor-{self.account_id}",
            endpoint=ServiceEndpoint(
                host=self.host_id,
                port=self.monitor_port,
                protocol="tcp"
            ),
            service_type=ServiceType.CORE,
            metadata={
                'account_id': self.account_id,
                'process_pid': os.getpid(),
                'component_type': 'monitor',
                'terminal_path': self.config.get('terminal_path')
            },
            tags={'mt5', 'monitor', self.account_id},
            health_checker=self._check_monitor_health
        )
        
        # 注册执行器服务
        executor_service_id = await self.service_discovery.register_service(
            service_name=f"mt5-executor-{self.account_id}",
            endpoint=ServiceEndpoint(
                host=self.host_id,
                port=self.executor_port,
                protocol="tcp"
            ),
            service_type=ServiceType.CORE,
            metadata={
                'account_id': self.account_id,
                'process_pid': os.getpid(),
                'component_type': 'executor'
            },
            tags={'mt5', 'executor', self.account_id},
            health_checker=self._check_executor_health
        )
        
        logger.info(f"✅ 服务已注册到服务发现: Monitor={monitor_service_id}, Executor={executor_service_id}")
    
    async def _check_monitor_health(self, _=None) -> bool:
        """监听器健康检查"""
        if self.monitor:
            return await self.monitor.health_check()
        return False
    
    async def _check_executor_health(self, _=None) -> bool:
        """执行器健康检查"""
        if self.executor:
            return await self.executor.health_check()
        return False
    
    async def start(self):
        """启动MT5进程"""
        try:
            # 初始化
            if not await self.initialize():
                raise RuntimeError("进程初始化失败")
            
            # 启动监听器和执行器
            monitor_task = asyncio.create_task(self.monitor.start())
            executor_task = asyncio.create_task(self.executor.start())
            
            logger.info(f"✅ MT5进程已启动: {self.account_id}")
            
            # 等待任务完成（永远运行直到被停止）
            await asyncio.gather(monitor_task, executor_task)
            
        except Exception as e:
            logger.error(f"❌ MT5进程运行异常: {self.account_id} - {e}")
            raise
    
    async def stop(self):
        """停止MT5进程"""
        logger.info(f"🛑 停止MT5进程: {self.account_id}")
        
        try:
            # 停止监听器和执行器
            if self.monitor:
                await self.monitor.stop()
            
            if self.executor:
                await self.executor.stop()
            
            # 从服务发现注销
            if self.service_discovery:
                # 注销所有服务
                await self.service_discovery.stop()
            
            # 停止共享组件
            if self.zero_copy_bus:
                await self.zero_copy_bus.stop()
            
            if self.memory_pool:
                await self.memory_pool.stop()
            
            # 关闭容器
            await self.container.shutdown_all()
            
            logger.info(f"✅ MT5进程已停止: {self.account_id}")
            
        except Exception as e:
            logger.error(f"❌ MT5进程停止异常: {self.account_id} - {e}")


async def main():
    """主函数 - 从命令行参数读取配置并启动进程"""
    if len(sys.argv) < 3:
        print("用法: python mt5_process_launcher.py <account_id> <config_json>")
        sys.exit(1)
    
    account_id = sys.argv[1]
    
    # 解析配置
    import json
    try:
        config = json.loads(sys.argv[2])
    except Exception as e:
        logger.error(f"配置解析失败: {e}")
        sys.exit(1)
    
    # 创建并启动启动器
    launcher = MT5ProcessLauncher(account_id, config)
    
    try:
        await launcher.start()
    except KeyboardInterrupt:
        logger.info("⚠️ 收到中断信号")
    except Exception as e:
        logger.error(f"❌ 进程异常: {e}")
        sys.exit(1)
    finally:
        await launcher.stop()


if __name__ == "__main__":
    asyncio.run(main())