# src/distributed/failover_manager.py
# 🚨 AUTO-MIGRATED: Old messaging components replaced with QueueManager
# See MIGRATION_GUIDE.md for details

"""
故障转移管理器
处理主机和账户故障的自动恢复
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

from ..messaging.queue_manager import QueueManager
from ..utils.logger import get_logger
from ..core.config_manager import ConfigManager
from .accounts import DistributedAccountRegistry, AccountType, AccountStatus

logger = get_logger(__name__)


class FailoverStrategy(Enum):
    """故障转移策略"""
    IMMEDIATE = "immediate"      # 立即转移
    DELAYED = "delayed"          # 延迟转移
    MANUAL = "manual"            # 手动转移


@dataclass
class FailoverRule:
    """故障转移规则"""
    source_host: str
    target_host: str
    account_types: Set[AccountType]
    strategy: FailoverStrategy
    delay_seconds: int = 0
    conditions: Dict = None
    
    def __post_init__(self):
        if self.conditions is None:
            self.conditions = {}


@dataclass
class FailoverEvent:
    """故障转移事件"""
    event_id: str
    source_host: str
    target_host: str
    affected_accounts: List[str]
    strategy: FailoverStrategy
    status: str  # pending, in_progress, completed, failed
    created_at: float
    completed_at: Optional[float] = None
    error_message: Optional[str] = None


class DistributedFailoverManager:
    """分布式故障转移管理器"""
    
    def __init__(self, queue_manager: JetStreamClient,
                 config_manager: ConfigManager,
                 account_registry: DistributedAccountRegistry,
                 local_host_id: str):
        self.jetstream = queue_manager
        self.config = config_manager
        self.account_registry = account_registry
        self.local_host_id = local_host_id
        
        # 故障转移规则和事件
        self.failover_rules: Dict[str, List[FailoverRule]] = {}
        self.active_events: Dict[str, FailoverEvent] = {}
        
        # 主机状态监控
        self.host_status: Dict[str, Dict] = {}
        
        # 运行状态
        self.running = False
        
        # 配置
        self.check_interval = config_manager.get('failover.check_interval', 30)
        self.host_timeout = config_manager.get('failover.host_timeout', 120)
        
        # 统计
        self.stats = {
            'total_events': 0,
            'successful_failovers': 0,
            'failed_failovers': 0,
            'active_events': 0
        }
        
        logger.info(f"故障转移管理器初始化完成 - 主机: {local_host_id}")
    
    async def start(self):
        """启动故障转移管理器"""
        if self.running:
            return
        
        self.running = True
        
        # 确保流存在
        await self._ensure_streams()
        
        # 设置订阅
        await self._setup_subscriptions()
        
        # 加载故障转移规则
        await self._load_failover_rules()
        
        # 启动监控任务
        asyncio.create_task(self._monitoring_task())
        
        logger.info("故障转移管理器已启动")
    
    async def stop(self):
        """停止故障转移管理器"""
        self.running = False
        logger.info("故障转移管理器已停止")
    
    async def _ensure_streams(self):
        """确保JetStream流存在"""
        streams = [
            {
                'name': 'MT5_FAILOVER',
                'subjects': ['MT5.FAILOVER.*'],
                'retention': 'limits',
                'max_age': 7200,  # 2小时
                'max_msgs': 10000
            }
        ]
        
        for stream_config in streams:
            try:
                await self.jetstream.add_stream(**stream_config)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.error(f"创建流失败: {stream_config['name']} - {e}")
    
    async def _setup_subscriptions(self):
        """设置订阅"""
        # 订阅故障转移事件
        await self.jetstream.subscribe(
            "MT5.FAILOVER.EVENT",
            self._handle_failover_event
        )
        
        # 订阅主机状态更新
        await self.jetstream.subscribe(
            "MT5.HOST.STATUS.*",
            self._handle_host_status
        )
    
    async def _load_failover_rules(self):
        """加载故障转移规则"""
        try:
            failover_config = self.config.get('failover', {})
            rules_config = failover_config.get('rules', [])
            
            for rule_config in rules_config:
                source_host = rule_config.get('source_host')
                target_host = rule_config.get('target_host')
                
                if source_host and target_host:
                    rule = FailoverRule(
                        source_host=source_host,
                        target_host=target_host,
                        account_types={AccountType(t) for t in rule_config.get('account_types', ['master', 'slave'])},
                        strategy=FailoverStrategy(rule_config.get('strategy', 'delayed')),
                        delay_seconds=rule_config.get('delay_seconds', 60),
                        conditions=rule_config.get('conditions', {})
                    )
                    
                    if source_host not in self.failover_rules:
                        self.failover_rules[source_host] = []
                    
                    self.failover_rules[source_host].append(rule)
            
            logger.info(f"加载故障转移规则: {sum(len(rules) for rules in self.failover_rules.values())} 个")
            
        except Exception as e:
            logger.error(f"加载故障转移规则失败: {e}")
    
    async def trigger_failover(self, failed_host: str, reason: str = "") -> List[str]:
        """触发故障转移"""
        try:
            # 获取失败主机上的账户
            failed_accounts = self.account_registry.get_accounts_by_host(failed_host)
            
            if not failed_accounts:
                logger.info(f"主机 {failed_host} 上没有账户需要故障转移")
                return []
            
            # 查找适用的故障转移规则
            applicable_rules = self.failover_rules.get(failed_host, [])
            
            if not applicable_rules:
                logger.warning(f"主机 {failed_host} 没有配置故障转移规则")
                return []
            
            event_ids = []
            
            for rule in applicable_rules:
                # 筛选符合规则的账户
                affected_accounts = [
                    acc.account_id for acc in failed_accounts
                    if acc.account_type in rule.account_types
                ]
                
                if affected_accounts:
                    event_id = await self._create_failover_event(
                        failed_host, rule.target_host, affected_accounts, rule, reason
                    )
                    event_ids.append(event_id)
            
            return event_ids
            
        except Exception as e:
            logger.error(f"触发故障转移失败: {failed_host} - {e}")
            return []
    
    async def _create_failover_event(self, source_host: str, target_host: str,
                                   affected_accounts: List[str], rule: FailoverRule,
                                   reason: str = "") -> str:
        """创建故障转移事件"""
        event_id = f"failover_{int(time.time() * 1000)}_{source_host}_{target_host}"
        
        event = FailoverEvent(
            event_id=event_id,
            source_host=source_host,
            target_host=target_host,
            affected_accounts=affected_accounts,
            strategy=rule.strategy,
            status="pending",
            created_at=time.time()
        )
        
        self.active_events[event_id] = event
        self.stats['total_events'] += 1
        self.stats['active_events'] += 1
        
        # 广播故障转移事件
        await self._broadcast_failover_event(event, reason)
        
        # 根据策略执行故障转移
        if rule.strategy == FailoverStrategy.IMMEDIATE:
            asyncio.create_task(self._execute_failover(event_id))
        elif rule.strategy == FailoverStrategy.DELAYED:
            asyncio.create_task(self._delayed_failover(event_id, rule.delay_seconds))
        
        logger.info(f"创建故障转移事件: {event_id} ({len(affected_accounts)} 个账户)")
        
        return event_id
    
    async def _broadcast_failover_event(self, event: FailoverEvent, reason: str = ""):
        """广播故障转移事件"""
        payload = {
            'event_id': event.event_id,
            'source_host': event.source_host,
            'target_host': event.target_host,
            'affected_accounts': event.affected_accounts,
            'strategy': event.strategy.value,
            'status': event.status,
            'reason': reason,
            'timestamp': time.time()
        }
        
        await self.jetstream.publish(
            "MT5.FAILOVER.EVENT",
            json.dumps(payload).encode('utf-8')
        )
    
    async def _delayed_failover(self, event_id: str, delay_seconds: int):
        """延迟故障转移"""
        try:
            logger.info(f"延迟故障转移: {event_id} (延迟 {delay_seconds} 秒)")
            await asyncio.sleep(delay_seconds)
            
            # 检查事件是否仍然有效
            if event_id in self.active_events:
                await self._execute_failover(event_id)
            
        except Exception as e:
            logger.error(f"延迟故障转移失败: {event_id} - {e}")
    
    async def _execute_failover(self, event_id: str):
        """执行故障转移"""
        try:
            if event_id not in self.active_events:
                logger.warning(f"故障转移事件不存在: {event_id}")
                return
            
            event = self.active_events[event_id]
            event.status = "in_progress"
            
            logger.info(f"执行故障转移: {event_id}")
            
            # 这里实现实际的故障转移逻辑
            # 1. 在目标主机上重新启动账户
            # 2. 更新路由表
            # 3. 通知相关组件
            
            success = await self._perform_account_migration(event)
            
            if success:
                event.status = "completed"
                event.completed_at = time.time()
                self.stats['successful_failovers'] += 1
                logger.info(f"故障转移完成: {event_id}")
            else:
                event.status = "failed"
                event.error_message = "账户迁移失败"
                self.stats['failed_failovers'] += 1
                logger.error(f"故障转移失败: {event_id}")
            
            # 广播状态更新
            await self._broadcast_failover_event(event)
            
            # 清理完成的事件
            if event.status in ["completed", "failed"]:
                del self.active_events[event_id]
                self.stats['active_events'] -= 1
            
        except Exception as e:
            logger.error(f"执行故障转移失败: {event_id} - {e}")
            
            if event_id in self.active_events:
                self.active_events[event_id].status = "failed"
                self.active_events[event_id].error_message = str(e)
                self.stats['failed_failovers'] += 1
    
    async def _perform_account_migration(self, event: FailoverEvent) -> bool:
        """执行账户迁移"""
        try:
            # 这里应该实现实际的账户迁移逻辑
            # 由于这是一个复杂的过程，这里只是模拟
            
            logger.info(f"迁移账户: {event.affected_accounts} "
                       f"从 {event.source_host} 到 {event.target_host}")
            
            # 模拟迁移过程
            await asyncio.sleep(2)
            
            # 在实际实现中，这里需要:
            # 1. 通知目标主机启动账户
            # 2. 更新账户注册表
            # 3. 更新路由表
            # 4. 验证迁移成功
            
            return True
            
        except Exception as e:
            logger.error(f"账户迁移失败: {e}")
            return False
    
    async def _monitoring_task(self):
        """监控任务"""
        while self.running:
            try:
                # 检查主机状态
                await self._check_host_health()
                
                # 清理过期事件
                await self._cleanup_expired_events()
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"监控任务错误: {e}")
                await asyncio.sleep(10)
    
    async def _check_host_health(self):
        """检查主机健康状态"""
        current_time = time.time()
        
        # 检查已知主机的心跳
        for host_id, status in self.host_status.items():
            last_heartbeat = status.get('last_heartbeat', 0)
            
            if (current_time - last_heartbeat) > self.host_timeout:
                # 主机可能离线
                if status.get('status') != 'offline':
                    logger.warning(f"检测到主机可能离线: {host_id}")
                    
                    # 触发故障转移
                    await self.trigger_failover(host_id, "心跳超时")
                    
                    # 更新主机状态
                    status['status'] = 'offline'
    
    async def _cleanup_expired_events(self):
        """清理过期事件"""
        current_time = time.time()
        expired_events = []
        
        for event_id, event in self.active_events.items():
            # 清理超过1小时的事件
            if (current_time - event.created_at) > 3600:
                expired_events.append(event_id)
        
        for event_id in expired_events:
            del self.active_events[event_id]
            self.stats['active_events'] -= 1
            logger.info(f"清理过期故障转移事件: {event_id}")
    
    async def _handle_failover_event(self, msg):
        """处理故障转移事件"""
        try:
            data = json.loads(msg.data.decode('utf-8'))
            event_id = data.get('event_id')
            
            # 处理来自其他主机的故障转移事件
            logger.debug(f"收到故障转移事件: {event_id}")
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理故障转移事件失败: {e}")
            await msg.nak()
    
    async def _handle_host_status(self, msg):
        """处理主机状态更新"""
        try:
            data = json.loads(msg.data.decode('utf-8'))
            host_id = data.get('host_id')
            
            if host_id:
                self.host_status[host_id] = {
                    'status': data.get('status', 'unknown'),
                    'last_heartbeat': time.time(),
                    'metadata': data.get('metadata', {})
                }
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理主机状态失败: {e}")
            await msg.nak()
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        return {
            **self.stats,
            'running': self.running,
            'known_hosts': len(self.host_status),
            'failover_rules': sum(len(rules) for rules in self.failover_rules.values())
        }