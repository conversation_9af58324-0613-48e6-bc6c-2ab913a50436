#!/usr/bin/env python3
"""
MT5 分布式跟单系统 - 自动化部署脚本
基于正确架构设计原则的一键部署工具
"""

import os
import sys
import subprocess
import yaml
import time
import argparse
from pathlib import Path
from typing import Dict, List

class MT5Deployer:
    """MT5 自动化部署器"""
    
    def __init__(self, config_path: str = "docker/deployment-config.yaml"):
        self.config_path = config_path
        self.config = self.load_config()
        self.compose_file = "docker/docker-compose.yml"
        
    def load_config(self) -> Dict:
        """加载部署配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            sys.exit(1)
    
    def check_prerequisites(self) -> bool:
        """检查部署前提条件"""
        print("🔍 检查部署前提条件...")
        
        # 检查 Docker
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Docker 未安装或不可用")
                return False
            print(f"✅ Docker: {result.stdout.strip()}")
        except FileNotFoundError:
            print("❌ Docker 未安装")
            return False
        
        # 检查 Docker Compose
        try:
            result = subprocess.run(['docker', 'compose', 'version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Docker Compose 未安装或不可用")
                return False
            print(f"✅ Docker Compose: {result.stdout.strip()}")
        except FileNotFoundError:
            print("❌ Docker Compose 未安装")
            return False
        
        # 检查配置文件
        required_files = [
            "config/optimized_system.yaml",
            "config/copy_relationships.yaml",
            "docker/docker-compose.yml",
            "docker/deployment-config.yaml"
        ]
        
        for file_path in required_files:
            if not Path(file_path).exists():
                print(f"❌ 配置文件不存在: {file_path}")
                return False
            print(f"✅ 配置文件: {file_path}")
        
        # 检查环境变量文件
        if not Path(".env").exists():
            print("⚠️  .env 文件不存在，将使用默认配置")
        else:
            print("✅ 环境变量文件: .env")
        
        return True
    
    def validate_config(self) -> bool:
        """验证配置文件"""
        print("🔍 验证配置文件...")
        
        try:
            # 验证系统配置
            with open("config/optimized_system.yaml", 'r', encoding='utf-8') as f:
                system_config = yaml.safe_load(f)
            
            # 验证跟单关系配置
            with open("config/copy_relationships.yaml", 'r', encoding='utf-8') as f:
                copy_config = yaml.safe_load(f)
            
            # 检查账户配置
            accounts = self.config.get('accounts', {})
            copy_relationships = copy_config.get('relationships', [])
            
            print(f"✅ 配置的账户数量: {len(accounts)}")
            print(f"✅ 跟单关系数量: {len(copy_relationships)}")
            
            # 验证跟单关系
            for rel in copy_relationships:
                master = rel.get('master_account')
                slave = rel.get('slave_account')
                if master not in accounts or slave not in accounts:
                    print(f"⚠️  跟单关系中的账户未在部署配置中定义: {master} -> {slave}")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置验证失败: {e}")
            return False
    
    def build_images(self) -> bool:
        """构建 Docker 镜像"""
        print("🔨 构建 Docker 镜像...")
        
        try:
            cmd = ['docker', 'compose', '-f', self.compose_file, 'build']
            result = subprocess.run(cmd, check=True)
            print("✅ Docker 镜像构建成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Docker 镜像构建失败: {e}")
            return False
    
    def deploy_infrastructure(self) -> bool:
        """部署基础设施服务"""
        print("🚀 部署基础设施服务...")
        
        infrastructure_services = [
            'redis', 'nats', 'prometheus', 'pushgateway', 'grafana'
        ]
        
        try:
            for service in infrastructure_services:
                print(f"   启动 {service}...")
                cmd = ['docker', 'compose', '-f', self.compose_file, 
                       'up', '-d', service]
                subprocess.run(cmd, check=True)
                time.sleep(2)  # 等待服务启动
            
            print("✅ 基础设施服务部署成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 基础设施服务部署失败: {e}")
            return False
    
    def deploy_trading_services(self) -> bool:
        """部署交易服务"""
        print("🎯 部署交易服务...")
        
        # 按照启动顺序部署
        startup_order = self.config.get('startup_order', {})
        
        try:
            for step, services in startup_order.items():
                if step == 1:  # 跳过基础设施，已经部署
                    continue
                    
                print(f"   步骤 {step}: {', '.join(services)}")
                for service in services:
                    if service.startswith('mt5-'):
                        print(f"     启动 {service}...")
                        cmd = ['docker', 'compose', '-f', self.compose_file, 
                               'up', '-d', service]
                        subprocess.run(cmd, check=True)
                        time.sleep(5)  # 等待服务启动
            
            print("✅ 交易服务部署成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 交易服务部署失败: {e}")
            return False
    
    def check_health(self) -> bool:
        """检查服务健康状态"""
        print("🏥 检查服务健康状态...")
        
        try:
            cmd = ['docker', 'compose', '-f', self.compose_file, 'ps']
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            print("📊 服务状态:")
            print(result.stdout)
            
            # 检查是否有失败的服务
            if 'Exit' in result.stdout or 'unhealthy' in result.stdout:
                print("⚠️  发现不健康的服务")
                return False
            
            print("✅ 所有服务运行正常")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 健康检查失败: {e}")
            return False
    
    def show_status(self):
        """显示部署状态"""
        print("\n" + "="*60)
        print("🎉 MT5 分布式跟单系统部署完成!")
        print("="*60)
        
        services = {
            "API 网关": "http://localhost:8000",
            "Grafana 监控": "http://localhost:3000",
            "Prometheus": "http://localhost:9090",
            "Redis Commander": "http://localhost:8081"
        }
        
        print("📡 服务访问地址:")
        for name, url in services.items():
            print(f"   {name}: {url}")
        
        print("\n📋 管理命令:")
        print(f"   查看日志: docker compose -f {self.compose_file} logs -f")
        print(f"   停止服务: docker compose -f {self.compose_file} down")
        print(f"   重启服务: docker compose -f {self.compose_file} restart")
        
        print("\n🔍 账户配置:")
        accounts = self.config.get('accounts', {})
        for account_id, config in accounts.items():
            print(f"   {account_id}: {config['description']}")
    
    def deploy(self, skip_build: bool = False) -> bool:
        """执行完整部署"""
        print("🚀 开始 MT5 分布式跟单系统部署")
        print("="*60)
        
        # 检查前提条件
        if not self.check_prerequisites():
            return False
        
        # 验证配置
        if not self.validate_config():
            return False
        
        # 构建镜像
        if not skip_build and not self.build_images():
            return False
        
        # 部署基础设施
        if not self.deploy_infrastructure():
            return False
        
        # 等待基础设施启动
        print("⏳ 等待基础设施服务启动...")
        time.sleep(10)
        
        # 部署交易服务
        if not self.deploy_trading_services():
            return False
        
        # 等待服务启动
        print("⏳ 等待交易服务启动...")
        time.sleep(15)
        
        # 健康检查
        if not self.check_health():
            print("⚠️  部分服务可能未正常启动，请检查日志")
        
        # 显示状态
        self.show_status()
        
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MT5 分布式跟单系统自动化部署")
    parser.add_argument('--skip-build', action='store_true', 
                       help='跳过 Docker 镜像构建')
    parser.add_argument('--config', default='docker/deployment-config.yaml',
                       help='部署配置文件路径')
    
    args = parser.parse_args()
    
    deployer = MT5Deployer(args.config)
    
    try:
        success = deployer.deploy(skip_build=args.skip_build)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 部署被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 部署失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
