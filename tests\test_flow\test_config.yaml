# MT5分离架构集成测试配置文件
# 用于配置各种测试参数和场景

# 基础测试配置
basic_tests:
  monitor_startup_timeout: 10  # 监控器启动超时时间(秒)
  executor_startup_timeout: 10  # 执行器启动超时时间(秒)
  process_cleanup_timeout: 5   # 进程清理超时时间(秒)

# 压力测试配置
stress_tests:
  # 高频消息处理测试
  high_frequency:
    message_count: 10000      # 消息数量
    concurrent_workers: 10    # 并发工作者数量
    duration_seconds: 5       # 测试持续时间
    
  # 内存压力测试
  memory_stress:
    target_memory_mb: 100     # 目标内存增长(MB)
    object_batch_size: 1000   # 对象批次大小
    max_objects: 10000        # 最大对象数量
    
  # 并发处理测试
  concurrency:
    concurrent_tasks: 20      # 并发任务数量
    operations_per_task: 100  # 每个任务的操作数
    task_duration: 3          # 任务持续时间(秒)

# 网络故障模拟配置
network_failure:
  # 连接超时测试
  timeout_simulation:
    max_retries: 5           # 最大重试次数
    retry_delay_ms: 100      # 重试延迟(毫秒)
    timeout_duration_ms: 100 # 超时时长(毫秒)
    
  # 间歇性故障测试
  intermittent_failure:
    failure_rate: 0.3        # 故障率(0.0-1.0)
    test_messages: 100       # 测试消息数量
    retry_delay_ms: 10       # 重试延迟(毫秒)
    
  # 网络分区测试
  network_partition:
    partition_duration: 2    # 分区持续时间(秒)
    messages_during_partition: 20  # 分区期间的消息数量
    message_interval_ms: 100 # 消息间隔(毫秒)

# 边缘情况测试配置
edge_cases:
  # 极端数据测试
  extreme_data:
    max_account_id_length: 1000    # 最大账户ID长度
    max_slave_accounts: 10000      # 最大从账户数量
    extreme_volume_ratios:         # 极端交易比例
      - 0.0
      - -1.0
      - 999999.99
      
  # 特殊字符测试
  special_characters:
    test_account_ids:
      - "账户_中文_测试"
      - "Account-With-Dashes"
      - "Account.With.Dots"
      - "Account@With@Symbols"
      - "Account With Spaces"
      - "🚀💰📈_Emoji_Account"
      - "Ñiño_Español_Açcount"
      - "Русский_Аккаунт_тест"
      
  # 快速启停测试
  rapid_start_stop:
    cycle_count: 50          # 循环次数
    cycle_delay_ms: 10       # 循环间隔(毫秒)

# 数据一致性测试配置
data_consistency:
  # 消息顺序测试
  message_ordering:
    message_count: 1000      # 消息数量
    min_consistency_rate: 0.95  # 最小一致性率
    
  # 数据完整性测试
  data_integrity:
    test_data_types: 4       # 测试数据类型数量
    messages_per_type: 250   # 每种类型的消息数量
    checksum_algorithm: "md5" # 校验和算法
    
  # 并发访问测试
  concurrent_access:
    worker_count: 10         # 工作者数量
    operations_per_worker: 50 # 每个工作者的操作数
    operation_delay_ms: 1    # 操作延迟(毫秒)

# 综合压力场景配置
comprehensive_stress:
  # 多组件压力测试
  multi_component:
    monitor_components: 5    # 监控器组件数量
    executor_components: 5   # 执行器组件数量
    router_components: 3     # 路由器组件数量
    test_duration: 3         # 测试持续时间(秒)
    
    # 各组件的操作间隔(毫秒)
    monitor_interval_ms: 1
    executor_interval_ms: 2
    router_interval_ms: 0.5
    
  # 资源耗尽测试
  resource_exhaustion:
    memory_limit_mb: 100     # 内存限制(MB)
    cpu_limit_percent: 80    # CPU限制(百分比)
    max_test_objects: 10000  # 最大测试对象数量

# 错误处理测试配置
error_handling:
  # 进程崩溃测试
  process_crash:
    crash_delay_seconds: 1   # 崩溃延迟时间
    recovery_timeout: 5      # 恢复超时时间
    
  # 队列满载测试
  queue_overload:
    routing_rules_count: 1000 # 路由规则数量
    
  # 配置文件损坏测试
  config_corruption:
    test_invalid_yaml: true  # 测试无效YAML
    test_missing_fields: true # 测试缺失字段

# 性能基准配置
performance_benchmarks:
  # 吞吐量基准
  throughput:
    min_ops_per_second: 1000  # 最小操作/秒
    target_ops_per_second: 5000 # 目标操作/秒
    
  # 延迟基准
  latency:
    max_avg_latency_ms: 10   # 最大平均延迟(毫秒)
    max_p99_latency_ms: 50   # 最大P99延迟(毫秒)
    
  # 内存使用基准
  memory:
    max_memory_growth_mb: 50 # 最大内存增长(MB)
    max_memory_leak_mb: 10   # 最大内存泄漏(MB)

# 测试环境配置
test_environment:
  temp_dir_prefix: "mt5_test_" # 临时目录前缀
  cleanup_on_exit: true       # 退出时清理
  log_level: "INFO"           # 日志级别
  enable_profiling: false     # 启用性能分析
  
# 报告配置
reporting:
  generate_html_report: false # 生成HTML报告
  generate_json_report: true  # 生成JSON报告
  include_performance_charts: false # 包含性能图表
  report_output_dir: "test_reports" # 报告输出目录
