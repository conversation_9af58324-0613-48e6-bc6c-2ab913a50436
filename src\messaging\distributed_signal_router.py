#!/usr/bin/env python3
"""
分布式信号路由器 - 智能跨主机信号路由
根据目标主机位置自动选择最优路由路径：本地零拷贝 vs 跨主机NATS
"""
import asyncio
import time
import logging
from typing import Dict, Any, Optional, Callable, List, Set
from dataclasses import dataclass
from enum import Enum
import hashlib

from .zerocopy import ZeroCopyMessageBus, MessageType as ZeroCopyMessageType
from .queue_manager import QueueManager
from .message_types import TradeSignal, MessageEnvelope
from ..utils.logger import get_logger

logger = get_logger(__name__)

class RoutingStrategy(Enum):
    """路由策略"""
    LOCAL_ONLY = "local_only"          # 仅本地路由
    REMOTE_ONLY = "remote_only"        # 仅远程路由
    HYBRID_AUTO = "hybrid_auto"        # 自动选择
    BROADCAST = "broadcast"            # 广播到所有

@dataclass
class RoutingTarget:
    """路由目标"""
    account_id: str
    host_id: str
    is_local: bool
    priority: int = 0
    endpoint: Optional[str] = None

@dataclass
class RoutingDecision:
    """路由决策"""
    use_zero_copy: bool
    use_nats: bool
    target_channel: str
    estimated_latency_ms: float
    reason: str

class DistributedSignalRouter:
    """
    分布式信号路由器
    
    智能路由功能：
    1. 本地信号使用零拷贝消息总线（微秒级延迟）
    2. 跨主机信号使用NATS/Redis（毫秒级延迟）
    3. 自动故障转移和负载均衡
    4. 路由性能监控和优化
    """
    
    def __init__(self, host_id: str, config: Dict[str, Any]):
        self.host_id = host_id
        self.config = config
        self.running = False
        
        # 路由组件
        self.zero_copy_bus: Optional[ZeroCopyMessageBus] = None
        self.queue_manager: Optional[QueueManager] = None
        
        # 账户-主机映射缓存
        self.account_host_mapping: Dict[str, str] = {}
        self.local_accounts: Set[str] = set()
        self.remote_accounts: Dict[str, str] = {}  # account_id -> host_id
        
        # 路由统计
        self.routing_stats = {
            'total_signals': 0,
            'local_signals': 0,
            'remote_signals': 0,
            'routing_errors': 0,
            'avg_routing_latency_ms': 0.0,
            'last_update_time': time.time()
        }
        
        # 路由决策缓存
        self.routing_cache: Dict[str, RoutingDecision] = {}
        self.cache_ttl = config.get('routing_cache_ttl', 300)  # 5分钟
        
        logger.info(f"✅ 分布式信号路由器初始化: {host_id}")
    
    async def start(self):
        """启动路由器"""
        if self.running:
            return
        
        try:
            # 启动路由组件发现
            await self._discover_routing_components()
            
            # 启动账户映射发现
            await self._discover_account_mappings()
            
            # 启动路由监控
            asyncio.create_task(self._routing_monitor_loop())
            
            # 启动缓存清理
            asyncio.create_task(self._cache_cleanup_loop())
            
            self.running = True
            logger.info(f"✅ 分布式信号路由器已启动: {self.host_id}")
            
        except Exception as e:
            logger.error(f"❌ 启动分布式信号路由器失败: {e}")
            raise
    
    async def stop(self):
        """停止路由器"""
        if not self.running:
            return
        
        self.running = False
        logger.info(f"🛑 分布式信号路由器已停止: {self.host_id}")
    
    async def route_signal(self, signal: TradeSignal, targets: List[str]) -> Dict[str, bool]:
        """
        路由信号到目标账户
        
        Args:
            signal: 交易信号
            targets: 目标账户列表
            
        Returns:
            字典：{account_id: success}
        """
        start_time = time.time()
        results = {}
        
        try:
            # 更新统计
            self.routing_stats['total_signals'] += 1
            
            # 按主机分组目标
            local_targets, remote_targets = self._group_targets_by_host(targets)
            
            # 并行路由
            tasks = []
            
            # 本地路由（零拷贝）
            if local_targets:
                tasks.append(self._route_local_signals(signal, local_targets))
            
            # 远程路由（NATS/Redis）
            if remote_targets:
                tasks.append(self._route_remote_signals(signal, remote_targets))
            
            # 等待所有路由完成
            if tasks:
                routing_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 合并结果
                for result in routing_results:
                    if isinstance(result, dict):
                        results.update(result)
                    elif isinstance(result, Exception):
                        logger.error(f"路由异常: {result}")
                        self.routing_stats['routing_errors'] += 1
            
            # 更新延迟统计
            routing_time_ms = (time.time() - start_time) * 1000
            self._update_latency_stats(routing_time_ms)
            
            logger.debug(f"信号路由完成: {signal.signal_id} -> {len(results)} 个目标, 用时: {routing_time_ms:.2f}ms")
            
            return results
            
        except Exception as e:
            logger.error(f"信号路由失败: {signal.signal_id} - {e}")
            self.routing_stats['routing_errors'] += 1
            return {target: False for target in targets}
    
    def _group_targets_by_host(self, targets: List[str]) -> tuple[List[str], Dict[str, List[str]]]:
        """按主机分组目标账户"""
        local_targets = []
        remote_targets = {}  # host_id -> [account_ids]
        
        for account_id in targets:
            if account_id in self.local_accounts:
                local_targets.append(account_id)
            elif account_id in self.remote_accounts:
                host_id = self.remote_accounts[account_id]
                if host_id not in remote_targets:
                    remote_targets[host_id] = []
                remote_targets[host_id].append(account_id)
            else:
                # 未知账户，尝试发现
                logger.warning(f"未知账户位置: {account_id}，尝试远程路由")
                if 'unknown' not in remote_targets:
                    remote_targets['unknown'] = []
                remote_targets['unknown'].append(account_id)
        
        return local_targets, remote_targets
    
    async def _route_local_signals(self, signal: TradeSignal, targets: List[str]) -> Dict[str, bool]:
        """本地信号路由（零拷贝）"""
        results = {}
        
        if not self.zero_copy_bus:
            logger.warning("零拷贝消息总线不可用，跳过本地路由")
            return {target: False for target in targets}
        
        try:
            self.routing_stats['local_signals'] += len(targets)
            
            for account_id in targets:
                try:
                    # 使用零拷贝发送
                    await self.zero_copy_bus.send_message(
                        message_type=ZeroCopyMessageType.TRADE_SIGNAL,
                        data={
                            'signal_id': signal.signal_id,
                            'account_id': signal.account_id,
                            'target_account': account_id,
                            'symbol': signal.symbol,
                            'action': signal.action.value,
                            'volume': signal.volume,
                            'price': signal.price,
                            'sl': signal.sl,
                            'tp': signal.tp,
                            'timestamp': signal.timestamp,
                            'metadata': signal.metadata
                        },
                        target_id=hash(account_id),
                        priority=self._get_signal_priority(signal)
                    )
                    
                    results[account_id] = True
                    logger.debug(f"本地信号发送成功: {signal.signal_id} -> {account_id}")
                    
                except Exception as e:
                    logger.error(f"本地信号发送失败: {signal.signal_id} -> {account_id}: {e}")
                    results[account_id] = False
            
            return results
            
        except Exception as e:
            logger.error(f"本地信号路由异常: {e}")
            return {target: False for target in targets}
    
    async def _route_remote_signals(self, signal: TradeSignal, remote_targets: Dict[str, List[str]]) -> Dict[str, bool]:
        """远程信号路由（NATS/Redis）"""
        results = {}
        
        if not self.queue_manager:
            logger.warning("队列管理器不可用，跳过远程路由")
            return {target: False for host_targets in remote_targets.values() for target in host_targets}
        
        try:
            for host_id, account_ids in remote_targets.items():
                self.routing_stats['remote_signals'] += len(account_ids)
                
                # 创建消息信封
                message = MessageEnvelope(
                    message_id=f"{signal.signal_id}_{int(time.time())}",
                    source_host=self.host_id,
                    target_host=host_id,
                    message_type='trade_signal',
                    priority=self._get_signal_priority(signal),
                    payload={
                        'signal_id': signal.signal_id,
                        'source_account': signal.account_id,
                        'target_accounts': account_ids,
                        'symbol': signal.symbol,
                        'action': signal.action.value,
                        'volume': signal.volume,
                        'price': signal.price,
                        'sl': signal.sl,
                        'tp': signal.tp,
                        'timestamp': signal.timestamp,
                        'metadata': signal.metadata
                    }
                )
                
                # 发送到目标主机
                try:
                    subject = f"MT5.SIGNALS.{host_id}.TRADE_SIGNAL"
                    success = await self.queue_manager.publish(subject, message)
                    
                    for account_id in account_ids:
                        results[account_id] = success
                        if success:
                            logger.debug(f"远程信号发送成功: {signal.signal_id} -> {host_id}:{account_id}")
                        else:
                            logger.error(f"远程信号发送失败: {signal.signal_id} -> {host_id}:{account_id}")
                
                except Exception as e:
                    logger.error(f"远程信号路由异常: {host_id} - {e}")
                    for account_id in account_ids:
                        results[account_id] = False
            
            return results
            
        except Exception as e:
            logger.error(f"远程信号路由异常: {e}")
            return {target: False for host_targets in remote_targets.values() for target in host_targets}
    
    def _get_signal_priority(self, signal: TradeSignal) -> int:
        """获取信号优先级"""
        # 简化优先级映射
        if hasattr(signal, 'priority'):
            return signal.priority
        
        # 基于信号类型和风险级别确定优先级
        if signal.action.value in ['CLOSE', 'MODIFY']:
            return 1  # 高优先级
        elif signal.sl and signal.sl > 0:
            return 2  # 有止损的信号中等优先级
        else:
            return 3  # 普通优先级
    
    async def _discover_routing_components(self):
        """发现路由组件"""
        try:
            # 从配置或依赖注入容器获取组件
            if 'zero_copy_bus' in self.config:
                self.zero_copy_bus = self.config['zero_copy_bus']
            
            if 'queue_manager' in self.config:
                self.queue_manager = self.config['queue_manager']
            
            logger.info(f"路由组件发现完成: 零拷贝={self.zero_copy_bus is not None}, 队列管理器={self.queue_manager is not None}")
            
        except Exception as e:
            logger.error(f"路由组件发现失败: {e}")
    
    async def _discover_account_mappings(self):
        """发现账户-主机映射"""
        try:
            # 从配置加载本地账户
            local_accounts_config = self.config.get('local_accounts', [])
            self.local_accounts.update(local_accounts_config)
            
            # 从配置加载远程账户映射
            remote_mappings = self.config.get('remote_account_mappings', {})
            self.remote_accounts.update(remote_mappings)
            
            logger.info(f"账户映射发现完成: 本地={len(self.local_accounts)}, 远程={len(self.remote_accounts)}")
            
        except Exception as e:
            logger.error(f"账户映射发现失败: {e}")
    
    def _update_latency_stats(self, routing_time_ms: float):
        """更新延迟统计"""
        current_avg = self.routing_stats['avg_routing_latency_ms']
        total_signals = self.routing_stats['total_signals']
        
        # 指数移动平均
        if total_signals == 1:
            self.routing_stats['avg_routing_latency_ms'] = routing_time_ms
        else:
            alpha = 0.1  # 平滑因子
            self.routing_stats['avg_routing_latency_ms'] = alpha * routing_time_ms + (1 - alpha) * current_avg
    
    async def _routing_monitor_loop(self):
        """路由监控循环"""
        while self.running:
            try:
                # 每60秒输出统计信息
                if self.routing_stats['total_signals'] > 0:
                    local_ratio = self.routing_stats['local_signals'] / self.routing_stats['total_signals']
                    remote_ratio = self.routing_stats['remote_signals'] / self.routing_stats['total_signals']
                    error_ratio = self.routing_stats['routing_errors'] / self.routing_stats['total_signals']
                    
                    logger.info(f"📊 路由统计: 总信号={self.routing_stats['total_signals']}, "
                              f"本地={local_ratio:.1%}, 远程={remote_ratio:.1%}, "
                              f"错误={error_ratio:.1%}, 平均延迟={self.routing_stats['avg_routing_latency_ms']:.2f}ms")
                
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"路由监控异常: {e}")
                await asyncio.sleep(10)
    
    async def _cache_cleanup_loop(self):
        """缓存清理循环"""
        while self.running:
            try:
                current_time = time.time()
                expired_keys = [
                    key for key, decision in self.routing_cache.items()
                    if current_time - getattr(decision, 'created_time', 0) > self.cache_ttl
                ]
                
                for key in expired_keys:
                    del self.routing_cache[key]
                
                if expired_keys:
                    logger.debug(f"清理过期路由缓存: {len(expired_keys)} 项")
                
                await asyncio.sleep(60)  # 每分钟清理一次
                
            except Exception as e:
                logger.error(f"路由缓存清理异常: {e}")
                await asyncio.sleep(30)
    
    def update_account_mapping(self, account_id: str, host_id: str):
        """更新账户映射"""
        if host_id == self.host_id:
            self.local_accounts.add(account_id)
            self.remote_accounts.pop(account_id, None)
        else:
            self.remote_accounts[account_id] = host_id
            self.local_accounts.discard(account_id)
        
        # 清理相关缓存
        cache_keys_to_clear = [key for key in self.routing_cache.keys() if account_id in key]
        for key in cache_keys_to_clear:
            self.routing_cache.pop(key, None)
        
        logger.debug(f"更新账户映射: {account_id} -> {host_id}")
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """获取路由统计"""
        return {
            **self.routing_stats,
            'host_id': self.host_id,
            'local_accounts_count': len(self.local_accounts),
            'remote_accounts_count': len(self.remote_accounts),
            'cache_size': len(self.routing_cache),
            'components_status': {
                'zero_copy_bus': self.zero_copy_bus is not None,
                'queue_manager': self.queue_manager is not None
            }
        }


# 全局实例管理
_distributed_router_instance: Optional[DistributedSignalRouter] = None

def get_distributed_signal_router(host_id: str = None, config: Dict[str, Any] = None) -> DistributedSignalRouter:
    """获取分布式信号路由器实例（单例）"""
    global _distributed_router_instance
    
    if _distributed_router_instance is None:
        if not host_id or not config:
            raise ValueError("首次调用需要提供 host_id 和 config")
        _distributed_router_instance = DistributedSignalRouter(host_id, config)
    
    return _distributed_router_instance