#!/usr/bin/env python3
"""
多账户并发测试
测试多个监控器和执行器的并发运行
"""

import asyncio
import json
import os
import sys
import tempfile
import time
import multiprocessing as mp
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入真实组件
try:
    from src.core.mt5_account_monitor import MT5AccountMonitor
    from src.core.mt5_account_executor import MT5AccountExecutor
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.messaging.jetstream_client import JetStreamClient
    from src.messaging.nats_manager import NATSManager
    
    REAL_COMPONENTS_AVAILABLE = True
    print("✅ 多账户测试组件导入成功")
    
except ImportError as e:
    print(f"❌ 导入组件失败: {e}")
    sys.exit(1)


class MultiAccountConcurrentTest:
    """多账户并发测试类"""
    
    def __init__(self):
        self.test_data_dir = Path(tempfile.mkdtemp(prefix="multi_account_test_"))
        self.jetstream_clients = []
        self.monitors = []
        self.executors = []
        self.processes = []
        print(f"测试数据目录: {self.test_data_dir}")
    
    def cleanup(self):
        """清理测试资源"""
        import shutil
        try:
            # 停止所有监控器
            for monitor in self.monitors:
                if hasattr(monitor, 'running'):
                    monitor.running = False
            
            # 停止所有执行器
            for executor in self.executors:
                if hasattr(executor, 'running'):
                    executor.running = False
            
            # 终止所有进程
            for process in self.processes:
                if process.is_alive():
                    process.terminate()
                    process.join(timeout=3)
            
            # 断开所有JetStream连接
            for client in self.jetstream_clients:
                if hasattr(client, 'disconnect'):
                    asyncio.create_task(client.disconnect())
            
            shutil.rmtree(self.test_data_dir)
            print("🧹 多账户测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理警告: {e}")
    
    async def setup_multi_account_infrastructure(self, account_count: int = 5):
        """设置多账户基础设施"""
        print(f"\n🏗️ 设置 {account_count} 个账户的基础设施...")
        
        try:
            # 为每个账户创建专用的JetStream客户端
            for i in range(account_count):
                account_id = f"MULTI_ACC_{i+1:03d}"
                
                # 监控器客户端
                monitor_config = {
                    'servers': ['nats://localhost:4222'],
                    'stream_name': f'MULTI_MONITOR_{account_id}',
                    'subjects': [f'MULTI.MONITOR.{account_id}.>']
                }
                
                monitor_client = JetStreamClient(monitor_config)
                connected = await monitor_client.connect()
                
                if not connected:
                    print(f"  ❌ 账户 {account_id} 监控器连接失败")
                    return False
                
                self.jetstream_clients.append(monitor_client)
                
                # 执行器客户端
                executor_config = {
                    'servers': ['nats://localhost:4222'],
                    'stream_name': f'MULTI_EXECUTOR_{account_id}',
                    'subjects': [f'MULTI.EXECUTOR.{account_id}.>']
                }
                
                executor_client = JetStreamClient(executor_config)
                await executor_client.connect()
                self.jetstream_clients.append(executor_client)
                
                print(f"  ✅ 账户 {account_id} 基础设施设置完成")
            
            print(f"  ✅ {account_count} 个账户基础设施全部设置完成")
            return True
            
        except Exception as e:
            print(f"  ❌ 多账户基础设施设置失败: {e}")
            return False
    
    async def test_concurrent_monitors(self, monitor_count: int = 5):
        """测试并发监控器"""
        print(f"\n👁️ 测试 {monitor_count} 个并发监控器...")
        
        # 设置基础设施
        if not await self.setup_multi_account_infrastructure(monitor_count):
            return False
        
        try:
            # 创建RPC客户端
            rpc_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'MULTI_RPC_CLIENT'
            }
            rpc_jetstream = JetStreamClient(rpc_config)
            await rpc_jetstream.connect()
            self.jetstream_clients.append(rpc_jetstream)
            
            rpc_client = MT5RPCClient(rpc_jetstream)
            
            # 创建多个监控器
            for i in range(monitor_count):
                account_id = f"MULTI_MONITOR_{i+1:03d}"
                
                account_config = {
                    'login': f'{10000 + i}',
                    'server': 'MultiTest-Server',
                    'password': f'test_password_{i}'
                }
                
                monitor_client = self.jetstream_clients[i * 2]  # 每个账户有2个客户端
                
                monitor = MT5AccountMonitor(
                    account_id=account_id,
                    account_config=account_config,
                    event_publisher=monitor_client,
                    rpc_client=rpc_client,
                    host_id=f'multi_test_host_{i}'
                )
                
                self.monitors.append(monitor)
                print(f"  ✅ 监控器 {account_id} 创建成功")
            
            # 并发监控任务
            async def monitor_task(monitor: MT5AccountMonitor, duration: int = 3):
                """单个监控器任务"""
                try:
                    # 模拟监控活动
                    operations = 0
                    start_time = time.time()
                    
                    while time.time() - start_time < duration:
                        # 模拟账户数据采集
                        account_data = {
                            'account_id': monitor.account_id,
                            'balance': 10000.0 + operations * 0.1,
                            'equity': 10000.0 + operations * 0.1,
                            'timestamp': time.time(),
                            'operation_count': operations
                        }
                        
                        # 发布监控数据
                        await monitor.event_publisher.publish_message(
                            subject=f'MULTI.MONITOR.{monitor.account_id}.DATA',
                            data=account_data
                        )
                        
                        operations += 1
                        await asyncio.sleep(0.1)  # 100ms间隔
                    
                    return {
                        'account_id': monitor.account_id,
                        'operations': operations,
                        'duration': time.time() - start_time
                    }
                    
                except Exception as e:
                    return {
                        'account_id': monitor.account_id,
                        'error': str(e),
                        'operations': 0
                    }
            
            # 启动所有监控器任务
            print(f"  🚀 启动 {len(self.monitors)} 个并发监控器任务...")
            
            monitor_tasks = [
                monitor_task(monitor, duration=3)
                for monitor in self.monitors
            ]
            
            start_time = time.time()
            results = await asyncio.gather(*monitor_tasks)
            end_time = time.time()
            
            # 分析结果
            successful_monitors = [r for r in results if 'error' not in r]
            total_operations = sum(r['operations'] for r in successful_monitors)
            avg_operations = total_operations / len(successful_monitors) if successful_monitors else 0
            
            print(f"  📊 并发监控器测试结果:")
            print(f"    成功监控器: {len(successful_monitors)}/{len(self.monitors)}")
            print(f"    总操作数: {total_operations}")
            print(f"    平均操作数: {avg_operations:.0f}")
            print(f"    总耗时: {end_time - start_time:.2f}秒")
            
            return len(successful_monitors) >= monitor_count * 0.8  # 80%成功率
            
        except Exception as e:
            print(f"  ❌ 并发监控器测试失败: {e}")
            return False
    
    async def test_concurrent_executors(self, executor_count: int = 5):
        """测试并发执行器"""
        print(f"\n⚡ 测试 {executor_count} 个并发执行器...")
        
        # 设置基础设施
        if not await self.setup_multi_account_infrastructure(executor_count):
            return False
        
        try:
            # 创建RPC客户端
            rpc_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'MULTI_EXECUTOR_RPC'
            }
            rpc_jetstream = JetStreamClient(rpc_config)
            await rpc_jetstream.connect()
            self.jetstream_clients.append(rpc_jetstream)
            
            rpc_client = MT5RPCClient(rpc_jetstream)
            
            # 创建多个执行器
            for i in range(executor_count):
                account_id = f"MULTI_EXECUTOR_{i+1:03d}"
                
                account_config = {
                    'login': f'{20000 + i}',
                    'server': 'MultiTest-Server',
                    'password': f'exec_password_{i}'
                }
                
                executor_client = self.jetstream_clients[i * 2 + 1]  # 执行器客户端
                
                executor = MT5AccountExecutor(
                    account_id=account_id,
                    account_config=account_config,
                    command_subscriber=executor_client,
                    result_publisher=executor_client,
                    rpc_client=rpc_client
                )
                
                self.executors.append(executor)
                print(f"  ✅ 执行器 {account_id} 创建成功")
            
            # 并发执行任务
            async def executor_task(executor: MT5AccountExecutor, duration: int = 3):
                """单个执行器任务"""
                try:
                    operations = 0
                    start_time = time.time()
                    
                    while time.time() - start_time < duration:
                        # 模拟交易执行
                        trade_command = {
                            'account_id': executor.account_id,
                            'symbol': 'EURUSD',
                            'action': 'buy' if operations % 2 == 0 else 'sell',
                            'volume': 0.01 * (operations % 10 + 1),
                            'timestamp': time.time(),
                            'command_id': f'CMD_{operations}'
                        }
                        
                        # 发布执行结果
                        result = {
                            'command_id': trade_command['command_id'],
                            'status': 'executed',
                            'execution_time': time.time(),
                            'account_id': executor.account_id
                        }
                        
                        await executor.result_publisher.publish_message(
                            subject=f'MULTI.EXECUTOR.{executor.account_id}.RESULT',
                            data=result
                        )
                        
                        operations += 1
                        await asyncio.sleep(0.05)  # 50ms间隔
                    
                    return {
                        'account_id': executor.account_id,
                        'operations': operations,
                        'duration': time.time() - start_time
                    }
                    
                except Exception as e:
                    return {
                        'account_id': executor.account_id,
                        'error': str(e),
                        'operations': 0
                    }
            
            # 启动所有执行器任务
            print(f"  🚀 启动 {len(self.executors)} 个并发执行器任务...")
            
            executor_tasks = [
                executor_task(executor, duration=3)
                for executor in self.executors
            ]
            
            start_time = time.time()
            results = await asyncio.gather(*executor_tasks)
            end_time = time.time()
            
            # 分析结果
            successful_executors = [r for r in results if 'error' not in r]
            total_operations = sum(r['operations'] for r in successful_executors)
            avg_operations = total_operations / len(successful_executors) if successful_executors else 0
            
            print(f"  📊 并发执行器测试结果:")
            print(f"    成功执行器: {len(successful_executors)}/{len(self.executors)}")
            print(f"    总操作数: {total_operations}")
            print(f"    平均操作数: {avg_operations:.0f}")
            print(f"    总耗时: {end_time - start_time:.2f}秒")
            
            return len(successful_executors) >= executor_count * 0.8  # 80%成功率
            
        except Exception as e:
            print(f"  ❌ 并发执行器测试失败: {e}")
            return False
    
    async def test_mixed_concurrent_operations(self):
        """测试混合并发操作"""
        print("\n🔄 测试混合并发操作（监控器+执行器）...")
        
        # 设置基础设施
        if not await self.setup_multi_account_infrastructure(6):  # 3个监控器 + 3个执行器
            return False
        
        try:
            # 创建RPC客户端
            rpc_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'MIXED_RPC_CLIENT'
            }
            rpc_jetstream = JetStreamClient(rpc_config)
            await rpc_jetstream.connect()
            self.jetstream_clients.append(rpc_jetstream)
            
            rpc_client = MT5RPCClient(rpc_jetstream)
            
            # 创建3个监控器
            for i in range(3):
                account_id = f"MIXED_MONITOR_{i+1:03d}"
                account_config = {
                    'login': f'{30000 + i}',
                    'server': 'MixedTest-Server',
                    'password': f'mixed_password_{i}'
                }
                
                monitor_client = self.jetstream_clients[i * 2]
                monitor = MT5AccountMonitor(
                    account_id=account_id,
                    account_config=account_config,
                    event_publisher=monitor_client,
                    rpc_client=rpc_client,
                    host_id=f'mixed_host_{i}'
                )
                self.monitors.append(monitor)
            
            # 创建3个执行器
            for i in range(3):
                account_id = f"MIXED_EXECUTOR_{i+1:03d}"
                account_config = {
                    'login': f'{40000 + i}',
                    'server': 'MixedTest-Server',
                    'password': f'mixed_exec_password_{i}'
                }
                
                executor_client = self.jetstream_clients[(i + 3) * 2]
                executor = MT5AccountExecutor(
                    account_id=account_id,
                    account_config=account_config,
                    command_subscriber=executor_client,
                    result_publisher=executor_client,
                    rpc_client=rpc_client
                )
                self.executors.append(executor)
            
            print(f"  ✅ 创建了 {len(self.monitors)} 个监控器和 {len(self.executors)} 个执行器")
            
            # 混合并发任务
            async def mixed_operation_task(duration: int = 5):
                """混合操作任务"""
                operations = {'monitor': 0, 'executor': 0}
                start_time = time.time()
                
                while time.time() - start_time < duration:
                    # 监控器操作
                    for monitor in self.monitors:
                        try:
                            data = {
                                'account_id': monitor.account_id,
                                'timestamp': time.time(),
                                'operation': 'monitor_data'
                            }
                            await monitor.event_publisher.publish_message(
                                subject=f'MIXED.MONITOR.{monitor.account_id}',
                                data=data
                            )
                            operations['monitor'] += 1
                        except Exception:
                            pass
                    
                    # 执行器操作
                    for executor in self.executors:
                        try:
                            result = {
                                'account_id': executor.account_id,
                                'timestamp': time.time(),
                                'operation': 'executor_result'
                            }
                            await executor.result_publisher.publish_message(
                                subject=f'MIXED.EXECUTOR.{executor.account_id}',
                                data=result
                            )
                            operations['executor'] += 1
                        except Exception:
                            pass
                    
                    await asyncio.sleep(0.1)
                
                return operations
            
            # 运行混合操作
            print("  🚀 启动混合并发操作...")
            
            start_time = time.time()
            operations = await mixed_operation_task(duration=4)
            end_time = time.time()
            
            total_operations = operations['monitor'] + operations['executor']
            duration = end_time - start_time
            throughput = total_operations / duration
            
            print(f"  📊 混合并发操作结果:")
            print(f"    监控器操作: {operations['monitor']}")
            print(f"    执行器操作: {operations['executor']}")
            print(f"    总操作数: {total_operations}")
            print(f"    耗时: {duration:.2f}秒")
            print(f"    吞吐量: {throughput:.0f} 操作/秒")
            
            return total_operations >= 100 and throughput >= 20  # 至少100操作且20操作/秒
            
        except Exception as e:
            print(f"  ❌ 混合并发操作测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有多账户并发测试"""
        print("🚀 开始多账户并发测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['concurrent_monitors'] = await self.test_concurrent_monitors(3)
        test_results['concurrent_executors'] = await self.test_concurrent_executors(3)
        test_results['mixed_operations'] = await self.test_mixed_concurrent_operations()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 多账户并发测试结果:")
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        return success_rate >= 66  # 66%以上算成功


async def main():
    """主函数"""
    if not REAL_COMPONENTS_AVAILABLE:
        print("❌ 多账户测试组件不可用，无法运行测试")
        return False
    
    test_suite = MultiAccountConcurrentTest()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n🎉 多账户并发测试成功!")
        else:
            print("\n⚠️ 多账户并发测试部分失败")
        
        return success
        
    finally:
        test_suite.cleanup()


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
