#!/usr/bin/env python3
"""
Redis Hash管理器
使用Redis Hash数据结构优化对象存储和操作
避免"读取-修改-写回"整个JSON的开销
"""
import json
import time
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
import redis
from redis.sentinel import Sentinel


@dataclass
class HostRegistration:
    """主机注册信息"""
    host_id: str
    host_name: str
    ip_address: str
    status: str  # online, offline, maintenance
    last_heartbeat: float
    capabilities: List[str]
    load_factor: float
    mt5_terminals: List[str]
    created_at: float
    updated_at: float


@dataclass
class AccountInfo:
    """账户信息"""
    account_id: str
    account_type: str  # master, slave
    host_id: str
    terminal_id: str
    balance: float
    equity: float
    margin: float
    free_margin: float
    status: str  # active, inactive, error
    last_trade_time: float
    trade_count: int
    created_at: float
    updated_at: float


@dataclass
class PairingInfo:
    """配对信息"""
    pairing_id: str
    master_account: str
    slave_accounts: List[str]
    copy_ratio: float
    risk_level: str
    status: str  # active, paused, stopped
    created_at: float
    updated_at: float
    last_signal_time: float
    total_signals: int


class RedisHashManager:
    """Redis Hash管理器"""
    
    def __init__(self, 
                 redis_client: Optional[redis.Redis] = None,
                 sentinel_hosts: Optional[List[tuple]] = None,
                 master_name: str = "mt5-redis"):
        """
        初始化Redis Hash管理器
        
        Args:
            redis_client: 直接的Redis客户端
            sentinel_hosts: Sentinel主机列表 [(host, port), ...]
            master_name: Sentinel监控的主节点名称
        """
        if redis_client:
            self.redis = redis_client
        elif sentinel_hosts:
            # 使用Sentinel模式
            sentinel = Sentinel(sentinel_hosts)
            self.redis = sentinel.master_for(master_name, decode_responses=True)
        else:
            # 默认单机模式
            self.redis = redis.Redis(host='localhost', port=6379, decode_responses=True)
    
    # ==================== 主机管理 ====================
    
    def register_host(self, host_info: HostRegistration) -> bool:
        """注册主机信息"""
        try:
            host_key = f"host:{host_info.host_id}"
            host_data = asdict(host_info)

            # 转换复杂数据类型为JSON字符串
            host_data['capabilities'] = json.dumps(host_data['capabilities'])
            host_data['mt5_terminals'] = json.dumps(host_data['mt5_terminals'])

            # 确保所有值都是字符串
            for key, value in host_data.items():
                host_data[key] = str(value)

            # 使用HSET存储主机信息
            for key, value in host_data.items():
                self.redis.hset(host_key, key, value)

            # 添加到主机列表
            self.redis.sadd("hosts:all", host_info.host_id)

            # 按状态分类
            self.redis.sadd(f"hosts:status:{host_info.status}", host_info.host_id)

            return True
        except Exception as e:
            print(f"注册主机失败: {e}")
            return False
    
    def update_host_status(self, host_id: str, status: str) -> bool:
        """更新主机状态"""
        try:
            host_key = f"host:{host_id}"
            
            # 获取旧状态
            old_status = self.redis.hget(host_key, "status")
            
            # 原子更新状态和时间戳
            pipe = self.redis.pipeline()
            pipe.hset(host_key, "status", status)
            pipe.hset(host_key, "updated_at", time.time())
            
            # 更新状态分类
            if old_status:
                pipe.srem(f"hosts:status:{old_status}", host_id)
            pipe.sadd(f"hosts:status:{status}", host_id)
            
            pipe.execute()
            return True
        except Exception as e:
            print(f"更新主机状态失败: {e}")
            return False
    
    def update_host_heartbeat(self, host_id: str) -> bool:
        """更新主机心跳"""
        try:
            host_key = f"host:{host_id}"
            current_time = time.time()
            
            # 原子更新心跳时间
            self.redis.hset(host_key, "last_heartbeat", current_time)
            self.redis.hset(host_key, "updated_at", current_time)
            
            return True
        except Exception as e:
            print(f"更新主机心跳失败: {e}")
            return False
    
    def get_host_info(self, host_id: str) -> Optional[HostRegistration]:
        """获取主机信息"""
        try:
            host_key = f"host:{host_id}"
            host_data = self.redis.hgetall(host_key)
            
            if not host_data:
                return None
            
            # 转换数据类型
            host_data['last_heartbeat'] = float(host_data['last_heartbeat'])
            host_data['load_factor'] = float(host_data['load_factor'])
            host_data['created_at'] = float(host_data['created_at'])
            host_data['updated_at'] = float(host_data['updated_at'])
            host_data['capabilities'] = json.loads(host_data['capabilities'])
            host_data['mt5_terminals'] = json.loads(host_data['mt5_terminals'])
            
            return HostRegistration(**host_data)
        except Exception as e:
            print(f"获取主机信息失败: {e}")
            return None
    
    def get_hosts_by_status(self, status: str) -> List[str]:
        """根据状态获取主机列表"""
        try:
            return list(self.redis.smembers(f"hosts:status:{status}"))
        except Exception as e:
            print(f"获取主机列表失败: {e}")
            return []
    
    # ==================== 账户管理 ====================
    
    def register_account(self, account_info: AccountInfo) -> bool:
        """注册账户信息"""
        try:
            account_key = f"account:{account_info.account_id}"
            account_data = asdict(account_info)

            # 确保所有值都是字符串
            for key, value in account_data.items():
                account_data[key] = str(value)

            # 使用HSET存储账户信息
            for key, value in account_data.items():
                self.redis.hset(account_key, key, value)

            # 添加到账户列表
            self.redis.sadd("accounts:all", account_info.account_id)

            # 按类型分类
            self.redis.sadd(f"accounts:type:{account_info.account_type}", account_info.account_id)

            # 按主机分类
            self.redis.sadd(f"accounts:host:{account_info.host_id}", account_info.account_id)

            return True
        except Exception as e:
            print(f"注册账户失败: {e}")
            return False
    
    def update_account_balance(self, account_id: str, balance: float, equity: float, 
                             margin: float, free_margin: float) -> bool:
        """更新账户余额信息"""
        try:
            account_key = f"account:{account_id}"
            current_time = time.time()
            
            # 原子更新余额信息
            pipe = self.redis.pipeline()
            pipe.hset(account_key, "balance", balance)
            pipe.hset(account_key, "equity", equity)
            pipe.hset(account_key, "margin", margin)
            pipe.hset(account_key, "free_margin", free_margin)
            pipe.hset(account_key, "updated_at", current_time)
            pipe.execute()
            
            return True
        except Exception as e:
            print(f"更新账户余额失败: {e}")
            return False
    
    def update_account_trade_stats(self, account_id: str, last_trade_time: float, 
                                 increment_count: bool = True) -> bool:
        """更新账户交易统计"""
        try:
            account_key = f"account:{account_id}"
            current_time = time.time()
            
            pipe = self.redis.pipeline()
            pipe.hset(account_key, "last_trade_time", last_trade_time)
            pipe.hset(account_key, "updated_at", current_time)
            
            if increment_count:
                pipe.hincrby(account_key, "trade_count", 1)
            
            pipe.execute()
            return True
        except Exception as e:
            print(f"更新账户交易统计失败: {e}")
            return False
    
    def get_account_info(self, account_id: str) -> Optional[AccountInfo]:
        """获取账户信息"""
        try:
            account_key = f"account:{account_id}"
            account_data = self.redis.hgetall(account_key)
            
            if not account_data:
                return None
            
            # 转换数据类型
            for field in ['balance', 'equity', 'margin', 'free_margin', 
                         'last_trade_time', 'created_at', 'updated_at']:
                account_data[field] = float(account_data[field])
            
            account_data['trade_count'] = int(account_data['trade_count'])
            
            return AccountInfo(**account_data)
        except Exception as e:
            print(f"获取账户信息失败: {e}")
            return None
    
    def get_accounts_by_type(self, account_type: str) -> List[str]:
        """根据类型获取账户列表"""
        try:
            return list(self.redis.smembers(f"accounts:type:{account_type}"))
        except Exception as e:
            print(f"获取账户列表失败: {e}")
            return []
    
    def get_accounts_by_host(self, host_id: str) -> List[str]:
        """根据主机获取账户列表"""
        try:
            return list(self.redis.smembers(f"accounts:host:{host_id}"))
        except Exception as e:
            print(f"获取主机账户列表失败: {e}")
            return []
    
    # ==================== 配对管理 ====================
    
    def create_pairing(self, pairing_info: PairingInfo) -> bool:
        """创建配对信息"""
        try:
            pairing_key = f"pairing:{pairing_info.pairing_id}"
            pairing_data = asdict(pairing_info)

            # 将列表转换为JSON字符串
            pairing_data['slave_accounts'] = json.dumps(pairing_info.slave_accounts)

            # 确保所有值都是字符串
            for key, value in pairing_data.items():
                pairing_data[key] = str(value)

            # 使用HSET存储配对信息
            for key, value in pairing_data.items():
                self.redis.hset(pairing_key, key, value)

            # 添加到配对列表
            self.redis.sadd("pairings:all", pairing_info.pairing_id)

            # 按状态分类
            self.redis.sadd(f"pairings:status:{pairing_info.status}", pairing_info.pairing_id)

            # 主账户映射
            self.redis.set(f"pairing:master:{pairing_info.master_account}", pairing_info.pairing_id)

            # 从账户映射
            for slave_account in pairing_info.slave_accounts:
                self.redis.set(f"pairing:slave:{slave_account}", pairing_info.pairing_id)

            return True
        except Exception as e:
            print(f"创建配对失败: {e}")
            return False
    
    def update_pairing_signal_stats(self, pairing_id: str, signal_time: float) -> bool:
        """更新配对信号统计"""
        try:
            pairing_key = f"pairing:{pairing_id}"
            current_time = time.time()
            
            pipe = self.redis.pipeline()
            pipe.hset(pairing_key, "last_signal_time", signal_time)
            pipe.hset(pairing_key, "updated_at", current_time)
            pipe.hincrby(pairing_key, "total_signals", 1)
            pipe.execute()
            
            return True
        except Exception as e:
            print(f"更新配对信号统计失败: {e}")
            return False
    
    def get_pairing_info(self, pairing_id: str) -> Optional[PairingInfo]:
        """获取配对信息"""
        try:
            pairing_key = f"pairing:{pairing_id}"
            pairing_data = self.redis.hgetall(pairing_key)
            
            if not pairing_data:
                return None
            
            # 转换数据类型
            pairing_data['copy_ratio'] = float(pairing_data['copy_ratio'])
            pairing_data['created_at'] = float(pairing_data['created_at'])
            pairing_data['updated_at'] = float(pairing_data['updated_at'])
            pairing_data['last_signal_time'] = float(pairing_data['last_signal_time'])
            pairing_data['total_signals'] = int(pairing_data['total_signals'])
            pairing_data['slave_accounts'] = json.loads(pairing_data['slave_accounts'])
            
            return PairingInfo(**pairing_data)
        except Exception as e:
            print(f"获取配对信息失败: {e}")
            return None
    
    def get_pairing_by_master(self, master_account: str) -> Optional[str]:
        """根据主账户获取配对ID"""
        try:
            return self.redis.get(f"pairing:master:{master_account}")
        except Exception as e:
            print(f"获取主账户配对失败: {e}")
            return None
    
    def get_pairing_by_slave(self, slave_account: str) -> Optional[str]:
        """根据从账户获取配对ID"""
        try:
            return self.redis.get(f"pairing:slave:{slave_account}")
        except Exception as e:
            print(f"获取从账户配对失败: {e}")
            return None
    
    # ==================== 通用方法 ====================
    
    def cleanup_expired_data(self, expiry_hours: int = 24) -> int:
        """清理过期数据"""
        try:
            current_time = time.time()
            expiry_threshold = current_time - (expiry_hours * 3600)
            cleaned_count = 0
            
            # 清理过期主机
            for host_id in self.redis.smembers("hosts:all"):
                host_key = f"host:{host_id}"
                last_heartbeat = self.redis.hget(host_key, "last_heartbeat")
                
                if last_heartbeat and float(last_heartbeat) < expiry_threshold:
                    # 删除主机相关数据
                    status = self.redis.hget(host_key, "status")
                    if status:
                        self.redis.srem(f"hosts:status:{status}", host_id)
                    
                    self.redis.srem("hosts:all", host_id)
                    self.redis.delete(host_key)
                    cleaned_count += 1
            
            return cleaned_count
        except Exception as e:
            print(f"清理过期数据失败: {e}")
            return 0
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            stats = {
                'hosts': {
                    'total': self.redis.scard("hosts:all"),
                    'online': self.redis.scard("hosts:status:online"),
                    'offline': self.redis.scard("hosts:status:offline"),
                    'maintenance': self.redis.scard("hosts:status:maintenance")
                },
                'accounts': {
                    'total': self.redis.scard("accounts:all"),
                    'master': self.redis.scard("accounts:type:master"),
                    'slave': self.redis.scard("accounts:type:slave")
                },
                'pairings': {
                    'total': self.redis.scard("pairings:all"),
                    'active': self.redis.scard("pairings:status:active"),
                    'paused': self.redis.scard("pairings:status:paused"),
                    'stopped': self.redis.scard("pairings:status:stopped")
                },
                'redis_info': {
                    'memory_usage': self.redis.info('memory')['used_memory_human'],
                    'connected_clients': self.redis.info('clients')['connected_clients'],
                    'total_commands': self.redis.info('stats')['total_commands_processed']
                }
            }
            
            return stats
        except Exception as e:
            print(f"获取系统统计失败: {e}")
            return {}
