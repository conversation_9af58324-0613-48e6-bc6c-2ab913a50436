#!/usr/bin/env python3
"""
网络流量监控工具
监控分布式账户注册和心跳的网络流量
验证批量消息优化效果
"""
import asyncio
import time
import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path
import sys
from collections import defaultdict
import statistics

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class NetworkMessage:
    """网络消息记录"""
    timestamp: float
    subject: str
    size_bytes: int
    message_type: str  # 'registration', 'heartbeat', 'batch_registration', 'batch_heartbeat'
    host_id: str
    account_count: int = 1
    is_batch: bool = False

@dataclass
class TrafficStats:
    """流量统计"""
    total_messages: int = 0
    total_bytes: int = 0
    message_types: Dict[str, int] = field(default_factory=dict)
    bytes_by_type: Dict[str, int] = field(default_factory=dict)
    avg_message_size: float = 0.0
    peak_rate_msgs_per_sec: float = 0.0
    peak_rate_bytes_per_sec: float = 0.0

class NetworkTrafficMonitor:
    """网络流量监控器"""
    
    def __init__(self):
        self.messages: List[NetworkMessage] = []
        self.monitoring = False
        self.start_time = 0.0
        self.end_time = 0.0
        
        # 模拟的NATS消息大小（字节）
        self.message_size_estimates = {
            'single_registration': 450,  # JSON序列化的账户注册消息
            'batch_registration': 200,   # 批量消息中的单个账户（减少重复字段）
            'single_heartbeat': 180,     # 单个心跳消息
            'batch_heartbeat': 80,       # 批量心跳中的单个账户
            'overhead_per_message': 50   # NATS协议开销
        }
    
    async def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.start_time = time.time()
        self.messages.clear()
        logger.info("🔍 开始网络流量监控")
    
    async def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.end_time = time.time()
        logger.info("🛑 停止网络流量监控")
    
    def record_message(self, subject: str, message_type: str, host_id: str, 
                      account_count: int = 1, is_batch: bool = False):
        """记录消息"""
        if not self.monitoring:
            return
        
        # 计算消息大小
        if is_batch:
            if message_type == 'registration':
                size = (self.message_size_estimates['batch_registration'] * account_count + 
                       self.message_size_estimates['overhead_per_message'])
            else:  # heartbeat
                size = (self.message_size_estimates['batch_heartbeat'] * account_count + 
                       self.message_size_estimates['overhead_per_message'])
        else:
            if message_type == 'registration':
                size = self.message_size_estimates['single_registration']
            else:  # heartbeat
                size = self.message_size_estimates['single_heartbeat']
            size += self.message_size_estimates['overhead_per_message']
        
        message = NetworkMessage(
            timestamp=time.time(),
            subject=subject,
            size_bytes=size,
            message_type=message_type,
            host_id=host_id,
            account_count=account_count,
            is_batch=is_batch
        )
        
        self.messages.append(message)
        
        logger.debug(f"记录消息: {subject} ({size} bytes, {account_count} accounts)")
    
    def calculate_stats(self) -> TrafficStats:
        """计算流量统计"""
        if not self.messages:
            return TrafficStats()
        
        stats = TrafficStats()
        stats.total_messages = len(self.messages)
        stats.total_bytes = sum(msg.size_bytes for msg in self.messages)
        
        # 按类型统计
        for msg in self.messages:
            msg_type_key = f"{'batch_' if msg.is_batch else ''}{msg.message_type}"
            stats.message_types[msg_type_key] = stats.message_types.get(msg_type_key, 0) + 1
            stats.bytes_by_type[msg_type_key] = stats.bytes_by_type.get(msg_type_key, 0) + msg.size_bytes
        
        stats.avg_message_size = stats.total_bytes / stats.total_messages if stats.total_messages > 0 else 0
        
        # 计算峰值速率（每秒窗口）
        duration = self.end_time - self.start_time if self.end_time > self.start_time else 1.0
        if duration > 0:
            window_size = 1.0  # 1秒窗口
            
            # 按时间窗口计算速率
            window_stats = defaultdict(lambda: {'messages': 0, 'bytes': 0})
            
            for msg in self.messages:
                window = int((msg.timestamp - self.start_time) / window_size)
                window_stats[window]['messages'] += 1
                window_stats[window]['bytes'] += msg.size_bytes
            
            if window_stats:
                stats.peak_rate_msgs_per_sec = max(w['messages'] for w in window_stats.values())
                stats.peak_rate_bytes_per_sec = max(w['bytes'] for w in window_stats.values())
        
        return stats
    
    async def simulate_old_behavior(self, num_accounts: int, num_hosts: int = 3) -> TrafficStats:
        """模拟旧的单独消息行为"""
        logger.info(f"🔄 模拟旧行为: {num_accounts} 账户, {num_hosts} 主机")
        
        await self.start_monitoring()
        
        # 模拟账户注册阶段
        for host_idx in range(num_hosts):
            host_id = f"host-{host_idx:02d}"
            accounts_per_host = num_accounts // num_hosts
            
            for acc_idx in range(accounts_per_host):
                # 每个账户单独注册
                self.record_message(
                    subject="MT5.ACCOUNT.REGISTER",
                    message_type="registration",
                    host_id=host_id,
                    account_count=1,
                    is_batch=False
                )
                
                # 模拟注册间隔
                await asyncio.sleep(0.01)
        
        # 模拟心跳阶段（30秒周期）
        heartbeat_cycles = 3  # 模拟3个心跳周期
        
        for cycle in range(heartbeat_cycles):
            for host_idx in range(num_hosts):
                host_id = f"host-{host_idx:02d}"
                accounts_per_host = num_accounts // num_hosts
                
                for acc_idx in range(accounts_per_host):
                    account_id = f"ACC_{host_idx:02d}_{acc_idx:03d}"
                    
                    # 每个账户单独心跳
                    self.record_message(
                        subject=f"MT5.ACCOUNT.HEARTBEAT.{account_id}",
                        message_type="heartbeat",
                        host_id=host_id,
                        account_count=1,
                        is_batch=False
                    )
                    
                    # 模拟心跳间隔
                    await asyncio.sleep(0.001)
            
            # 心跳周期间隔
            await asyncio.sleep(0.1)
        
        await self.stop_monitoring()
        return self.calculate_stats()
    
    async def simulate_new_behavior(self, num_accounts: int, num_hosts: int = 3) -> TrafficStats:
        """模拟新的批量消息行为"""
        logger.info(f"🚀 模拟新行为: {num_accounts} 账户, {num_hosts} 主机")
        
        await self.start_monitoring()
        
        # 模拟批量账户注册阶段
        batch_size = 5
        
        for host_idx in range(num_hosts):
            host_id = f"host-{host_idx:02d}"
            accounts_per_host = num_accounts // num_hosts
            
            # 批量注册
            for batch_start in range(0, accounts_per_host, batch_size):
                batch_count = min(batch_size, accounts_per_host - batch_start)
                
                self.record_message(
                    subject="MT5.ACCOUNT.BATCH_REGISTER",
                    message_type="registration",
                    host_id=host_id,
                    account_count=batch_count,
                    is_batch=True
                )
                
                # 批量注册间隔（2秒延迟收集）
                await asyncio.sleep(0.02)
        
        # 模拟聚合心跳阶段
        heartbeat_cycles = 3
        
        for cycle in range(heartbeat_cycles):
            for host_idx in range(num_hosts):
                host_id = f"host-{host_idx:02d}"
                accounts_per_host = num_accounts // num_hosts
                
                # 聚合心跳（所有账户在一个消息中）
                self.record_message(
                    subject="MT5.ACCOUNT.BATCH_HEARTBEAT",
                    message_type="heartbeat",
                    host_id=host_id,
                    account_count=accounts_per_host,
                    is_batch=True
                )
                
                # 主账户独立心跳（向后兼容）
                if accounts_per_host > 0:
                    primary_account = f"ACC_{host_idx:02d}_000"
                    self.record_message(
                        subject=f"MT5.ACCOUNT.HEARTBEAT.{primary_account}",
                        message_type="heartbeat",
                        host_id=host_id,
                        account_count=1,
                        is_batch=False
                    )
            
            # 心跳周期间隔
            await asyncio.sleep(0.1)
        
        await self.stop_monitoring()
        return self.calculate_stats()
    
    def compare_behaviors(self, old_stats: TrafficStats, new_stats: TrafficStats) -> Dict[str, Any]:
        """比较新旧行为的网络效率"""
        comparison = {
            'old_behavior': {
                'total_messages': old_stats.total_messages,
                'total_bytes': old_stats.total_bytes,
                'avg_message_size': old_stats.avg_message_size,
                'peak_rate_msgs_per_sec': old_stats.peak_rate_msgs_per_sec,
                'peak_rate_bytes_per_sec': old_stats.peak_rate_bytes_per_sec
            },
            'new_behavior': {
                'total_messages': new_stats.total_messages,
                'total_bytes': new_stats.total_bytes,
                'avg_message_size': new_stats.avg_message_size,
                'peak_rate_msgs_per_sec': new_stats.peak_rate_msgs_per_sec,
                'peak_rate_bytes_per_sec': new_stats.peak_rate_bytes_per_sec
            },
            'improvements': {}
        }
        
        # 计算改进幅度
        if old_stats.total_messages > 0:
            comparison['improvements']['message_reduction'] = (
                (old_stats.total_messages - new_stats.total_messages) / old_stats.total_messages * 100
            )
        
        if old_stats.total_bytes > 0:
            comparison['improvements']['bandwidth_reduction'] = (
                (old_stats.total_bytes - new_stats.total_bytes) / old_stats.total_bytes * 100
            )
        
        if old_stats.peak_rate_msgs_per_sec > 0:
            comparison['improvements']['peak_rate_reduction'] = (
                (old_stats.peak_rate_msgs_per_sec - new_stats.peak_rate_msgs_per_sec) / 
                old_stats.peak_rate_msgs_per_sec * 100
            )
        
        comparison['improvements']['efficiency_gain'] = new_stats.avg_message_size / old_stats.avg_message_size if old_stats.avg_message_size > 0 else 0
        
        return comparison
    
    async def run_traffic_analysis(self, scenarios: List[Dict[str, int]]) -> Dict[str, Any]:
        """运行流量分析"""
        logger.info("🔍 开始网络流量分析")
        
        results = {
            'analysis_timestamp': time.time(),
            'scenarios': []
        }
        
        for scenario in scenarios:
            num_accounts = scenario['accounts']
            num_hosts = scenario['hosts']
            
            logger.info(f"分析场景: {num_accounts} 账户, {num_hosts} 主机")
            
            # 测试旧行为
            old_stats = await self.simulate_old_behavior(num_accounts, num_hosts)
            await asyncio.sleep(0.1)  # 清理间隔
            
            # 测试新行为
            new_stats = await self.simulate_new_behavior(num_accounts, num_hosts)
            await asyncio.sleep(0.1)  # 清理间隔
            
            # 比较结果
            comparison = self.compare_behaviors(old_stats, new_stats)
            
            scenario_result = {
                'scenario': scenario,
                'comparison': comparison,
                'old_stats_detail': old_stats.__dict__,
                'new_stats_detail': new_stats.__dict__
            }
            
            results['scenarios'].append(scenario_result)
        
        # 生成报告
        await self._generate_traffic_report(results)
        
        return results
    
    async def _generate_traffic_report(self, results: Dict[str, Any]):
        """生成流量分析报告"""
        report_file = f"reports/network_traffic_analysis_{int(time.time())}.json"
        
        # 确保报告目录存在
        Path("reports").mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 流量分析报告已生成: {report_file}")
        
        # 打印摘要
        self._print_traffic_summary(results)
    
    def _print_traffic_summary(self, results: Dict[str, Any]):
        """打印流量分析摘要"""
        print("\n" + "="*70)
        print("📊 网络流量优化效果分析")
        print("="*70)
        
        for scenario_result in results['scenarios']:
            scenario = scenario_result['scenario']
            comparison = scenario_result['comparison']
            improvements = comparison['improvements']
            
            print(f"\n📈 场景: {scenario['accounts']} 账户, {scenario['hosts']} 主机")
            print("-" * 50)
            
            print(f"消息数量:")
            print(f"  旧方案: {comparison['old_behavior']['total_messages']:,} 条")
            print(f"  新方案: {comparison['new_behavior']['total_messages']:,} 条")
            print(f"  减少: {improvements.get('message_reduction', 0):.1f}%")
            
            print(f"网络带宽:")
            print(f"  旧方案: {comparison['old_behavior']['total_bytes']:,} 字节")
            print(f"  新方案: {comparison['new_behavior']['total_bytes']:,} 字节")
            print(f"  节省: {improvements.get('bandwidth_reduction', 0):.1f}%")
            
            print(f"峰值消息速率:")
            print(f"  旧方案: {comparison['old_behavior']['peak_rate_msgs_per_sec']:.1f} msg/s")
            print(f"  新方案: {comparison['new_behavior']['peak_rate_msgs_per_sec']:.1f} msg/s")
            print(f"  降低: {improvements.get('peak_rate_reduction', 0):.1f}%")
            
            print(f"平均消息大小:")
            print(f"  旧方案: {comparison['old_behavior']['avg_message_size']:.1f} 字节")
            print(f"  新方案: {comparison['new_behavior']['avg_message_size']:.1f} 字节")
            print(f"  效率提升: {improvements.get('efficiency_gain', 0):.2f}x")
        
        print("\n" + "="*70)
        print("✅ 网络优化显著减少了消息数量和带宽使用")
        print("="*70)

async def main():
    """主函数"""
    monitor = NetworkTrafficMonitor()
    
    # 定义测试场景
    scenarios = [
        {'accounts': 20, 'hosts': 2},   # 小规模
        {'accounts': 50, 'hosts': 3},   # 中规模
        {'accounts': 100, 'hosts': 5},  # 大规模
        {'accounts': 200, 'hosts': 8}   # 超大规模
    ]
    
    results = await monitor.run_traffic_analysis(scenarios)
    
    # 检查优化效果
    significant_improvement = all(
        scenario['comparison']['improvements'].get('message_reduction', 0) > 50
        for scenario in results['scenarios']
    )
    
    if significant_improvement:
        logger.info("🎉 网络优化效果显著!")
        return 0
    else:
        logger.warning("⚠️ 网络优化效果有限")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)