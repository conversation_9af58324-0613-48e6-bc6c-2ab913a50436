#!/usr/bin/env python3
"""
项目文件清理工具 - 专门为MT5项目设计
移除Python文件中的emoji和特殊unicode字符，保持代码的兼容性
"""

import os
import re
import logging
from pathlib import Path
from typing import Dict, Set
import unicodedata

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProjectCleaner:
    """项目文件清理器"""

    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.stats = {
            'files_scanned': 0,
            'files_cleaned': 0,
            'chars_removed': 0,
            'backup_created': 0
        }
        
        # 要处理的文件类型
        self.target_extensions = {'.py', '.md', '.txt', '.yaml', '.yml', '.json', '.log'}
        
        # 要跳过的目录
        self.skip_dirs = {
            '__pycache__', '.git', '.svn', 'node_modules', 
            '.venv', 'venv', '.pytest_cache', 'build', 'dist'
        }
        
        # 常见的emoji和特殊字符模式
        self.emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"  # dingbats
            "\U000024C2-\U0001F251"
            "\U0001F900-\U0001F9FF"  # supplemental symbols
            "\U0001FA70-\U0001FAFF"  # symbols and pictographs extended-a
            "]+", 
            flags=re.UNICODE
        )
    
    def is_safe_char(self, char: str) -> bool:
        """检查字符是否安全（ASCII + 基本拉丁字符 + 常用标点）"""
        code = ord(char)
        
        # ASCII字符 (0-127)
        if code <= 127:
            return True
        
        # 基本拉丁扩展 (128-255) - 包含常用的重音字符
        if 128 <= code <= 255:
            return True
        
        # 中文字符范围（如果项目需要支持中文）
        if 0x4E00 <= code <= 0x9FFF:  # CJK统一汉字
            return True
        if 0x3400 <= code <= 0x4DBF:  # CJK扩展A
            return True
        if 0xFF00 <= code <= 0xFFEF:  # 全角字符
            return True
        
        # 其他常用字符
        category = unicodedata.category(char)
        if category in ['Zs', 'Zl', 'Zp']:  # 空格类字符
            return True
        
        return False
    
    def clean_text(self, text: str) -> tuple[str, int]:
        """清理文本，移除emoji和特殊字符"""
        # 首先移除emoji
        text_no_emoji = self.emoji_pattern.sub('', text)
        
        # 然后移除其他不安全字符
        cleaned_chars = []
        removed_count = 0
        
        for char in text_no_emoji:
            if self.is_safe_char(char):
                cleaned_chars.append(char)
            else:
                removed_count += 1
                # 记录被移除的字符（调试用）
                if removed_count <= 5:
                    char_name = unicodedata.name(char, f'U+{ord(char):04X}')
                    logger.debug(f"移除字符: '{char}' ({char_name})")
        
        cleaned_text = ''.join(cleaned_chars)
        
        # 计算总移除数（包括emoji）
        total_removed = len(text) - len(cleaned_text)
        
        return cleaned_text, total_removed
    
    def backup_file(self, file_path: Path) -> bool:
        """创建文件备份"""
        backup_path = file_path.with_suffix(file_path.suffix + '.bak')
        
        try:
            import shutil
            shutil.copy2(file_path, backup_path)
            self.stats['backup_created'] += 1
            logger.debug(f"已备份: {backup_path}")
            return True
        except Exception as e:
            logger.error(f"备份失败 {file_path}: {e}")
            return False
    
    def process_file(self, file_path: Path) -> bool:
        """处理单个文件"""
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                original_content = f.read()
            
            # 清理内容
            cleaned_content, removed_count = self.clean_text(original_content)
            
            self.stats['files_scanned'] += 1
            
            if removed_count > 0:
                if self.dry_run:
                    logger.info(f"[预览] 文件 {file_path} 将移除 {removed_count} 个字符")
                else:
                    logger.info(f"清理文件: {file_path} (移除 {removed_count} 个字符)")

                    # 创建备份
                    if self.backup_file(file_path):
                        # 写入清理后的内容
                        with open(file_path, 'w', encoding='utf-8', newline='') as f:
                            f.write(cleaned_content)

                        self.stats['files_cleaned'] += 1
                        self.stats['chars_removed'] += removed_count
                        return True
                    else:
                        logger.error(f"跳过文件 {file_path} (备份失败)")
                        return False

                # 在dry-run模式下也计算统计
                if self.dry_run:
                    self.stats['files_cleaned'] += 1
                    self.stats['chars_removed'] += removed_count
            else:
                logger.debug(f"文件无需清理: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            return False
    
    def should_skip_dir(self, dir_path: Path) -> bool:
        """检查是否应该跳过目录"""
        return dir_path.name in self.skip_dirs
    
    def scan_directory(self, root_dir: Path) -> None:
        """扫描目录中的所有文件"""
        logger.info(f"开始扫描目录: {root_dir}")
        
        for root, dirs, files in os.walk(root_dir):
            root_path = Path(root)
            
            # 移除要跳过的目录
            dirs[:] = [d for d in dirs if not self.should_skip_dir(root_path / d)]
            
            for file in files:
                file_path = root_path / file
                
                # 检查文件扩展名
                if file_path.suffix.lower() in self.target_extensions:
                    self.process_file(file_path)
    
    def print_summary(self) -> None:
        """打印清理摘要"""
        print("\n" + "="*50)
        print("🧹 项目文件清理完成")
        print("="*50)
        print(f"扫描文件数: {self.stats['files_scanned']}")
        print(f"清理文件数: {self.stats['files_cleaned']}")
        print(f"移除字符数: {self.stats['chars_removed']}")
        print(f"备份文件数: {self.stats['backup_created']}")
        print("="*50)
        
        if self.stats['files_cleaned'] > 0:
            print("✅ 清理完成！原文件已备份为 .bak 文件")
        else:
            print("✅ 所有文件都很干净，无需清理")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="清理项目文件中的emoji和特殊字符",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python clean_project_files.py                    # 清理当前目录
  python clean_project_files.py /path/to/project   # 清理指定目录
  python clean_project_files.py --dry-run          # 预览模式
        """
    )

    parser.add_argument('directory', nargs='?', default='.',
                       help='要清理的项目目录 (默认: 当前目录)')
    parser.add_argument('--dry-run', action='store_true',
                       help='预览模式，不实际修改文件')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    project_dir = Path(args.directory)

    if not project_dir.exists():
        print(f"❌ 目录不存在: {project_dir}")
        return 1
    
    print(f"🚀 开始清理项目: {project_dir}")
    print("📋 将处理以下文件类型: .py, .md, .txt, .yaml, .yml, .json, .log")

    if args.dry_run:
        print("🔍 预览模式 - 不会实际修改文件")
    else:
        print("⚠️  原文件将备份为 .bak 文件")

        # 确认操作
        response = input("\n是否继续? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ 操作已取消")
            return 0

    # 执行清理
    cleaner = ProjectCleaner(dry_run=args.dry_run)
    cleaner.scan_directory(project_dir)
    cleaner.print_summary()
    
    return 0

if __name__ == '__main__':
    exit(main())
