#!/usr/bin/env python3
# 🚨 AUTO-MIGRATED: Old messaging components replaced with QueueManager
# See MIGRATION_GUIDE.md for details

"""
MT5账户执行器 - 严格职责分离架构
仅负责执行交易命令，不进行任何监控活动
接收消息系统的执行指令，将结果反馈给消息系统
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime


# 处理导入问题 - 支持相对导入和绝对导入
from ..messaging.message_types import TradeSignal, OrderTypeEnum, TradeAction
from ..utils.logger import get_logger
from ..messaging.zerocopy import ZeroCopyMessageBus, MessageType as ZeroCopyMessageType
from ..utils.memory_pool import UnifiedMemoryPool, get_unified_memory_pool
from ..messaging.priority_queue import PriorityMessageQueue, MessagePriority, PriorityAnalyzer
from ..utils.batch_processor import get_trade_batch_processor
from ..performance.processor import get_processor, PerformanceProcessor
 
from .mt5_rpc_client import MT5RPCClient


logger = get_logger(__name__)


@dataclass
class ExecutionResult:
    """执行结果"""
    command_id: str
    account_id: str
    success: bool
    result_data: Dict[str, Any]
    timestamp: float
    error_message: Optional[str] = None


@dataclass
class ExecutionCommand:
    """执行命令"""
    command_id: str
    command_type: str  # open, close, modify
    account_id: str
    symbol: str
    volume: float
    order_type: OrderTypeEnum
    price: Optional[float] = None
    sl: Optional[float] = None
    tp: Optional[float] = None
    position_id: Optional[int] = None
    metadata: Dict[str, Any] = None


class TradeExecutor:
    """
    交易执行器 - 严格职责分离实现
    
    职责：
    执行开仓命令
    执行平仓命令  
    执行修改命令
    反馈执行结果
    
    职责边界：
    不监控任何数据
    不主动生成信号
    不处理跟单逻辑
    只响应执行命令
    """
    
    def __init__(self, account_name: str, config: Dict[str, Any] = None):
        # 基础属性
        self.account_name = account_name
        self.account_id = account_name  # 向后兼容
        self.config = config or {}
        self.account_config = self.config  # 向后兼容
        self.running = False
        self.start_time = None
        self.host_id = self.config.get('host_id', 'unknown')
        
        # 零拷贝通信组件
        self.zero_copy_bus = self.config.get('zero_copy_bus')
        self.message_bus = self.zero_copy_bus  # 向后兼容
        self.memory_pool = self.config.get('memory_pool') or get_unified_memory_pool()
        
        # RPC客户端（延迟初始化）
        self.rpc_client = self.config.get('rpc_client')
        if not self.rpc_client:
            # 延迟导入和初始化RPC客户端
            try:
                from .mt5_rpc_client import MT5RPCClient
                from ..messaging.queue_manager import QueueManager
                
                # 如果有队列管理器，使用它创建RPC客户端
                queue_manager = self.config.get('queue_manager')
                if queue_manager:
                    self.rpc_client = MT5RPCClient(queue_manager)
                else:
                    logger.warning(f"队列管理器不可用，RPC客户端初始化跳过: {self.account_id}")
                    self.rpc_client = None
            except ImportError:
                logger.warning(f"RPC客户端不可用: {self.account_id}")
                self.rpc_client = None
        
        # 交易验证器
        try:
            from .trade_validator import TradeValidator
            self.trade_validator = TradeValidator()
        except ImportError:
            # 如果相对导入失败，尝试绝对导入或创建Mock
            try:
                import sys
                import os
                sys.path.append(os.path.dirname(__file__))
                from trade_validator import TradeValidator
                self.trade_validator = TradeValidator()
            except ImportError:
                from unittest.mock import Mock
                self.trade_validator = Mock()

        # 如果启用了零拷贝
        if self.config.get('zero_copy_enabled', False):
            logger.info(f"执行器启用零拷贝通信: {self.account_id}")
        else:
            logger.info(f"执行器使用传统RPC架构: {self.account_id}")

        # 执行状态
        self.pending_commands: Dict[str, ExecutionCommand] = {}
        
        # 🚀 多线程执行器配置
        self.worker_count = self.config.get('executor_worker_count', 4)  # 默认4个工作者
        self.max_worker_count = self.config.get('max_executor_workers', 8)  # 最大8个工作者
        self.min_worker_count = self.config.get('min_executor_workers', 2)  # 最小2个工作者
        self.worker_scaling_threshold = self.config.get('worker_scaling_threshold', 0.8)  # 80%负载时扩展
        self.worker_scale_down_threshold = self.config.get('worker_scale_down_threshold', 0.3)  # 30%负载时缩减
        
        # 优先级队列配置 - 使用各优先级的队列大小（支持5级优先级）
        queue_sizes = {
            MessagePriority.CRITICAL: self.config.get('critical_queue_size', 100),
            MessagePriority.HIGH: self.config.get('high_queue_size', 200),
            MessagePriority.NORMAL: self.config.get('normal_queue_size', 300),
            MessagePriority.REALTIME_QUERY: self.config.get('realtime_query_queue_size', 250),
            MessagePriority.LOW: self.config.get('low_queue_size', 400)
        }
        
        # 创建优先级队列替换简单队列
        self.command_queue = PriorityMessageQueue(max_sizes=queue_sizes)
        self.queue_size = sum(queue_sizes.values())  # 总队列大小
        
        # 🚀 工作者线程池管理
        self.worker_tasks: List[asyncio.Task] = []
        self.worker_stats: Dict[str, Dict[str, Any]] = {}
        self.load_balancer_task: Optional[asyncio.Task] = None
        self.last_scaling_time = 0
        self.scaling_cooldown = 30  # 30秒缩放冷却时间
        
        # 初始化交易批处理器
        self.trade_batch_processor = get_trade_batch_processor()
        self.trade_batch_processor.set_trade_executor(self)
        
        # 增强性能处理器（延迟初始化）
        self.enhanced_processor = None
        
        # 执行统计
        self.execution_stats = {
            'commands_received': 0,
            'commands_executed': 0,
            'commands_failed': 0,
            'execution_errors': 0,
            'last_execution_time': 0,
            'queue_full_count': 0,
            'queue_max_size': 0,
            'queue_current_size': 0
        }
        
        # 信号主题 - 使用配置驱动的主题模式
        try:
            from .config_manager import get_stream_config_manager
            stream_config_manager = get_stream_config_manager()
            
            # 使用配置的主题模式
            self.signal_topic_pattern = stream_config_manager.get_subject_pattern(
                'global_signals', priority='*', master_account=self.account_id
            )
            self.result_topic = stream_config_manager.get_subject_pattern(
                'control', host_id='*', command_type=f'RESULT.{self.account_id}'
            )
            
            # 如果配置获取失败，使用默认模式
            if not self.signal_topic_pattern:
                self.signal_topic_pattern = f"MT5.SIGNALS.*.{self.account_id}"
            if not self.result_topic:
                self.result_topic = f"MT5.CONTROL.RESULT.{self.account_id}"
                
            logger.debug(f"执行器主题配置: 信号={self.signal_topic_pattern}, 结果={self.result_topic}")
            
        except Exception as e:
            logger.warning(f"获取主题配置失败，使用默认: {e}")
            self.signal_topic_pattern = f"MT5.SIGNALS.*.{self.account_id}"
            self.result_topic = f"MT5.CONTROL.RESULT.{self.account_id}"

        # 从统一监控配置获取执行器配置
        try:
            from .config_manager import get_config_manager
            config_mgr = get_config_manager()

            # 获取执行器相关配置
            self.execution_timeout = config_mgr.get('monitoring.execution_timeout', 30.0)
            self.retry_attempts = config_mgr.get('monitoring.retry_attempts', 3)
            self.sleep_interval = config_mgr.get('monitoring.sleep_interval', 0.2)

            logger.info(f"执行器配置加载: {self.account_id} - 超时:{self.execution_timeout}s, 重试:{self.retry_attempts}次, 睡眠:{self.sleep_interval}s")

        except Exception as e:
            logger.warning(f"执行器配置加载失败，使用默认值: {e}")
            self.execution_timeout = 30.0
            self.retry_attempts = 3
            self.sleep_interval = 0.2

        logger.info(f"MT5账户执行器初始化: {self.account_id} - 纯执行模式 (优先级队列 + 批处理)")
    
    def _analyze_command_priority(self, command) -> MessagePriority:
        """分析命令优先级 - 统一使用PriorityAnalyzer"""
        try:
            # 构建参数字典供PriorityAnalyzer使用
            params = {
                'action': command.command_type,
                'signal_type': command.command_type,
                'type': getattr(command, 'order_type', None),
                'sl': getattr(command, 'sl', 0),
                'tp': getattr(command, 'tp', 0),
                'symbol': getattr(command, 'symbol', ''),
                'volume': getattr(command, 'volume', 0),
                'metadata': getattr(command, 'metadata', {}),
                'urgent': getattr(command, 'urgent', False),
                'command_id': getattr(command, 'command_id', '')
            }
            
            # 使用统一的优先级分析器（现在支持消息级别的逻辑）
            priority = PriorityAnalyzer.get_command_priority('send_order', params)
            
            logger.debug(f"命令优先级分析: {command.command_type} -> {priority.name}")
            return priority
            
        except Exception as e:
            logger.warning(f"优先级分析失败，使用NORMAL优先级: {e}")
            return MessagePriority.NORMAL
    
    async def execute_trade(self, trade_command) -> Dict[str, Any]:
        """执行交易 - 批处理接口实现"""
        try:
            result = await self._execute_command(trade_command)
            return {
                'success': True,
                'command_id': trade_command.command_id,
                'result': result
            }
        except Exception as e:
            logger.error(f"批处理执行交易失败: {e}")
            return {
                'success': False,
                'command_id': getattr(trade_command, 'command_id', 'unknown'),
                'error': str(e)
            }

    def _parse_order_type(self, order_type_value):
        """解析订单类型"""
        try:
            # 如果已经是OrderTypeEnum实例，直接返回
            if isinstance(order_type_value, OrderTypeEnum):
                return order_type_value

            # 如果是字符串，尝试转换
            if isinstance(order_type_value, str):
                # 标准化字符串格式
                order_type_str = order_type_value.upper().strip()

                # 映射常见的订单类型
                type_mapping = {
                    'BUY': OrderTypeEnum.BUY,
                    'SELL': OrderTypeEnum.SELL,
                    'BUY_LIMIT': OrderTypeEnum.BUY_LIMIT,
                    'SELL_LIMIT': OrderTypeEnum.SELL_LIMIT,
                    'BUY_STOP': OrderTypeEnum.BUY_STOP,
                    'SELL_STOP': OrderTypeEnum.SELL_STOP,
                    'BUY_STOP_LIMIT': OrderTypeEnum.BUY_STOP_LIMIT,
                    'SELL_STOP_LIMIT': OrderTypeEnum.SELL_STOP_LIMIT,
                    # 兼容数字类型
                    '0': OrderTypeEnum.BUY,
                    '1': OrderTypeEnum.SELL,
                }

                if order_type_str in type_mapping:
                    return type_mapping[order_type_str]
                else:
                    return OrderTypeEnum(order_type_str)

            if isinstance(order_type_value, (int, float)):
                if order_type_value == 0:
                    return OrderTypeEnum.BUY
                elif order_type_value == 1:
                    return OrderTypeEnum.SELL
                else:
                    logger.warning(f"未知的数字订单类型: {order_type_value}, 默认使用BUY")
                    return OrderTypeEnum.BUY

            logger.warning(f"无法解析订单类型: {order_type_value}, 默认使用BUY")
            return OrderTypeEnum.BUY

        except Exception as e:
            logger.error(f"解析订单类型失败: {order_type_value}, 错误: {e}")
            return OrderTypeEnum.BUY

    async def start(self):
        """启动执行器 - 实现启动接口"""
        return await self.start_executor()
    
    async def _ensure_execution_ready(self) -> bool:
        """确保执行器就绪状态 - 只使用RPC架构"""
        # 使用RPC验证连接健康
        try:
            health_result = await self.rpc_client.health_check(self.account_id)
            
            if health_result.get('status') == 'success':
                logger.info(f"执行器RPC连接验证成功: {self.account_id}")
                return True
            else:
                logger.warning(f"RPC连接状态异常，但允许启动: {self.account_id}")
                return True  # 允许启动，在循环中重试
        except Exception as e:
            logger.warning(f"RPC连接验证异常，但允许启动: {self.account_id} - {e}")
            return True  # 允许启动，在循环中重试
    
    async def start_executor(self) -> bool:
        """启动执行器 - 仅执行，不监控"""
        if self.running:
            logger.warning(f"执行器已在运行: {self.account_id}")
            return False
        
        try:
            if not await self._ensure_execution_ready():
                logger.error(f"执行器未就绪: {self.account_id}")
                return False
            
            self.running = True
            self.start_time = time.time()
            
            # 如果启用了零拷贝，订阅零拷贝消息总线
            if self.zero_copy_bus:
                await self.zero_copy_bus.subscribe(
                    ZeroCopyMessageType.TRADE_SIGNAL,
                    self._handle_trade_signal
                )
            
            # 初始化增强性能处理器
            try:
                enhanced_config = PerformanceProcessor(
                    enable_trade_matching=True,
                    enable_trade_validation=True,
                    use_unified_memory=True,
                    use_priority_queue=True,
                    batch_enabled=True
                )
                self.enhanced_processor = await get_processor(enhanced_config)
                logger.info(f"增强性能处理器已集成: {self.account_id}")
            except Exception as e:
                logger.warning(f"增强性能处理器初始化失败，继续使用基础架构: {e}")
                self.enhanced_processor = None
            
            # 🚀 启动多工作者执行器池
            await self._start_worker_pool()
            
            # 启动负载均衡器
            self.load_balancer_task = asyncio.create_task(self._load_balancer_loop())
            
            # 启动队列监控
            asyncio.create_task(self._queue_monitor_loop())
            
            # 发布启动事件（如果有结果发布器）
            if hasattr(self, 'result_publisher') and self.result_publisher:
                await self._publish_execution_result("executor_started", self.account_id, True, {
                    "account_id": self.account_id,
                    "start_time": self.start_time
                })
            
            logger.info(f"MT5账户执行器已启动: {self.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动执行器失败: {e}")
            self.running = False
            return False
    
    async def stop_executor(self):
        """🚀 停止执行器 - 优雅关闭所有工作者"""
        if not self.running:
            return
        
        logger.info(f"开始停止执行器: {self.account_id}")
        self.running = False
        
        # 🚀 停止负载均衡器
        if self.load_balancer_task:
            self.load_balancer_task.cancel()
            try:
                await self.load_balancer_task
            except asyncio.CancelledError:
                pass
        
        # 🚀 等待所有工作者完成当前任务
        logger.info(f"等待 {len(self.worker_tasks)} 个工作者完成当前任务...")
        if self.worker_tasks:
            # 给工作者一些时间完成当前任务
            await asyncio.sleep(2)
            
            # 强制取消所有工作者任务
            for task in self.worker_tasks:
                if not task.done():
                    task.cancel()
            
            # 等待所有任务完成
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
            self.worker_tasks.clear()
        
        # 🚀 处理剩余队列中的关键命令
        critical_commands_processed = 0
        while not self.command_queue.is_empty() and critical_commands_processed < 50:
            try:
                priority_msg = await self.command_queue.dequeue(timeout=0.5)
                if priority_msg:
                    # 只处理关键和高优先级命令
                    if priority_msg.priority in [MessagePriority.CRITICAL, MessagePriority.HIGH]:
                        await self._execute_command(priority_msg.data)
                        critical_commands_processed += 1
                    else:
                        logger.debug(f"跳过低优先级命令: {priority_msg.priority.name}")
                        break
            except asyncio.TimeoutError:
                break
        
        if critical_commands_processed > 0:
            logger.info(f"处理了 {critical_commands_processed} 个关键命令")
        
        # 🚀 输出最终统计信息
        total_processed = sum(stats['commands_processed'] for stats in self.worker_stats.values())
        total_failed = sum(stats['commands_failed'] for stats in self.worker_stats.values())
        
        logger.info(f"执行器停止统计: 总处理 {total_processed} 个命令, 失败 {total_failed} 个")
        
        # 发布停止事件（如果有结果发布器）
        if hasattr(self, 'result_publisher') and self.result_publisher:
            await self._publish_execution_result("executor_stopped", self.account_id, True, {
                "account_id": self.account_id,
                "stop_time": time.time(),
                "stats": self.execution_stats,
                "worker_stats": self.worker_stats,
                "final_queue_size": self.command_queue.get_stats()['heap_size']
            })
        
        logger.info(f"🎯 MT5账户执行器已停止: {self.account_id}")
    
    async def _handle_priority_signal(self, msg):
        """处理优先级信号消息 - 现代架构入口"""
        try:
            # 从主题中提取优先级
            subject = getattr(msg, 'subject', None)
            priority = MessagePriority.NORMAL  # 默认优先级
            
            if subject:
                # 从配置的信号主题模式中提取优先级
                parts = subject.split('.')
                if len(parts) >= 3:
                    priority_str = parts[2]  # CRITICAL, HIGH, NORMAL, LOW
                    try:
                        priority = MessagePriority[priority_str]
                        logger.debug(f"提取优先级: {priority_str} -> {priority}")
                    except KeyError:
                        logger.warning(f"未知优先级: {priority_str}，使用默认NORMAL")
            
            # 委托给原有的执行命令处理器，但加入优先级信息
            await self._handle_execution_command(msg, priority)
            
        except Exception as e:
            logger.error(f"处理优先级信号失败: {e}")
            await msg.nak()
    
    async def _handle_trade_signal(self, header: Dict[str, Any], payload: Dict[str, Any]):
        """处理交易信号消息"""
        try:
            # 如果有增强处理器，优先使用它处理信号
            if self.enhanced_processor:
                signal_dict = {
                    'signal_id': payload.get('signal_id', ''),
                    'action': payload.get('action', '').lower(),
                    'account_id': self.account_id,
                    'symbol': payload.get('symbol', ''),
                    'volume': float(payload.get('volume', 0)),
                    'price': payload.get('price'),
                    'sl': payload.get('sl'),
                    'tp': payload.get('tp'),
                    'ticket': payload.get('ticket'),
                    'metadata': payload.get('metadata', {}),
                    'signal_type': payload.get('action', '').upper()
                }
                
                priority = MessagePriority(header.get('priority', 3))
                
                logger.info(f"增强处理器处理交易信号: {signal_dict['action']} ({priority.name}) -> {self.account_id}")
                
                result = await self.enhanced_processor.process_trade_signal(
                    signal_dict, priority, enable_matching=True, enable_validation=True
                )
                
                if result.get('status') in ['queued', 'batched', 'success']:
                    self.execution_stats['commands_received'] += 1
                else:
                    self.execution_stats['commands_failed'] += 1
                
                return

            # 传统处理方式（备用）
            command = ExecutionCommand(
                command_id=payload.get('signal_id', ''),
                command_type=payload.get('action', '').lower(),
                account_id=self.account_id,
                symbol=payload.get('symbol', ''),
                volume=float(payload.get('volume', 0)),
                order_type=self._parse_order_type(payload.get('action', 'BUY')),
                price=payload.get('price'),
                sl=payload.get('sl'),
                tp=payload.get('tp'),
                position_id=payload.get('ticket'),
                metadata=payload.get('metadata', {})
            )

            priority = MessagePriority(header.get('priority', 3))

            logger.info(f"传统处理交易信号: {command.command_type} ({priority.name}) -> {command.account_id}")

            success = await self.command_queue.enqueue(
                priority=priority,
                data=command,
                message_id=command.command_id,
                callback=None
            )

            if success:
                self.execution_stats['commands_received'] += 1
            else:
                logger.error(f"命令队列已满，拒绝信号: {command.command_id}")
                self.execution_stats['commands_failed'] += 1

        except Exception as e:
            logger.error(f"处理交易信号失败: {e}")
    
    async def _start_worker_pool(self):
        """🚀 启动多工作者执行器池"""
        logger.info(f"启动执行器工作者池: {self.worker_count}个工作者 - {self.account_id}")
        
        for i in range(self.worker_count):
            worker_id = f"worker-{i}"
            task = asyncio.create_task(self._execution_worker(worker_id))
            self.worker_tasks.append(task)
            
            # 初始化工作者统计
            self.worker_stats[worker_id] = {
                'commands_processed': 0,
                'commands_failed': 0,
                'avg_processing_time_ms': 0.0,
                'last_activity_time': time.time(),
                'is_busy': False,
                'current_command': None
            }
            
        logger.info(f"工作者池启动完成: {len(self.worker_tasks)}个工作者")
    
    async def _execution_worker(self, worker_id: str):
        """🚀 执行工作者 - 多线程并行处理"""
        logger.debug(f"执行工作者启动: {worker_id} - {self.account_id}")
        
        while self.running:
            try:
                # 从优先级队列获取最高优先级的命令
                priority_msg = await self.command_queue.dequeue(timeout=1.0)
                
                if priority_msg is None:
                    continue  # 队列为空，继续等待
                
                command = priority_msg.data
                start_time = time.time()
                
                # 更新工作者状态
                self.worker_stats[worker_id]['is_busy'] = True
                self.worker_stats[worker_id]['current_command'] = command.command_id
                self.worker_stats[worker_id]['last_activity_time'] = start_time
                
                logger.debug(f"工作者 {worker_id} 处理命令: {command.command_type} (优先级: {priority_msg.priority.name})")
                
                try:
                    # 🚀 智能处理策略选择
                    if self.enhanced_processor:
                        # 使用增强处理器
                        await self._process_with_enhanced_processor(command, priority_msg.priority)
                    else:
                        # 使用批处理器处理命令
                        await self.trade_batch_processor.process_item(command)
                    
                    self.worker_stats[worker_id]['commands_processed'] += 1
                    
                except Exception as cmd_error:
                    logger.error(f"工作者 {worker_id} 处理命令失败: {cmd_error}")
                    self.worker_stats[worker_id]['commands_failed'] += 1
                    self.execution_stats['execution_errors'] += 1
                
                finally:
                    # 更新工作者统计
                    processing_time = (time.time() - start_time) * 1000
                    old_avg = self.worker_stats[worker_id]['avg_processing_time_ms']
                    processed_count = self.worker_stats[worker_id]['commands_processed']
                    
                    if processed_count > 0:
                        self.worker_stats[worker_id]['avg_processing_time_ms'] = (
                            (old_avg * (processed_count - 1) + processing_time) / processed_count
                        )
                    
                    self.worker_stats[worker_id]['is_busy'] = False
                    self.worker_stats[worker_id]['current_command'] = None
                
            except Exception as e:
                logger.error(f"执行工作者 {worker_id} 异常: {e}")
                self.execution_stats['execution_errors'] += 1
                await asyncio.sleep(1)
        
        logger.debug(f"执行工作者停止: {worker_id}")
    
    async def _process_with_enhanced_processor(self, command: ExecutionCommand, priority: MessagePriority):
        """使用增强处理器处理命令"""
        signal_dict = {
            'signal_id': command.command_id,
            'action': command.command_type,
            'account_id': command.account_id,
            'symbol': command.symbol,
            'volume': command.volume,
            'price': command.price,
            'sl': command.sl,
            'tp': command.tp,
            'position_id': command.position_id,
            'metadata': command.metadata or {},
            'signal_type': command.command_type.upper()
        }
        
        result = await self.enhanced_processor.process_trade_signal(
            signal_dict, priority, enable_matching=True, enable_validation=True
        )
        
        # 更新统计
        if result.get('status') in ['success', 'queued', 'batched']:
            self.execution_stats['commands_executed'] += 1
        else:
            self.execution_stats['commands_failed'] += 1

    async def _load_balancer_loop(self):
        """🚀 负载均衡器 - 动态调整工作者数量"""
        logger.info(f"负载均衡器启动: {self.account_id}")
        
        while self.running:
            try:
                await asyncio.sleep(10)  # 每10秒检查一次
                
                # 获取队列统计
                queue_stats = self.command_queue.get_stats()
                current_queue_size = queue_stats['heap_size']
                queue_usage_rate = current_queue_size / self.queue_size if self.queue_size > 0 else 0
                
                # 计算工作者繁忙率
                active_workers = len(self.worker_tasks)
                busy_workers = sum(1 for stats in self.worker_stats.values() if stats['is_busy'])
                worker_busy_rate = busy_workers / active_workers if active_workers > 0 else 0
                
                current_time = time.time()
                should_scale = (current_time - self.last_scaling_time) > self.scaling_cooldown
                
                # 🚀 动态扩展逻辑
                if should_scale:
                    if (queue_usage_rate > self.worker_scaling_threshold or 
                        worker_busy_rate > 0.9) and active_workers < self.max_worker_count:
                        
                        # 扩展工作者
                        new_worker_count = min(active_workers + 2, self.max_worker_count)
                        await self._scale_workers(new_worker_count)
                        self.last_scaling_time = current_time
                        
                        logger.info(f"🔥 扩展工作者: {active_workers} → {new_worker_count} "
                                  f"(队列使用率: {queue_usage_rate:.1%}, 工作者繁忙率: {worker_busy_rate:.1%})")
                    
                    elif (queue_usage_rate < self.worker_scale_down_threshold and 
                          worker_busy_rate < 0.3) and active_workers > self.min_worker_count:
                        
                        # 缩减工作者
                        new_worker_count = max(active_workers - 1, self.min_worker_count)
                        await self._scale_workers(new_worker_count)
                        self.last_scaling_time = current_time
                        
                        logger.info(f"📉 缩减工作者: {active_workers} → {new_worker_count} "
                                  f"(队列使用率: {queue_usage_rate:.1%}, 工作者繁忙率: {worker_busy_rate:.1%})")
                
                # 🚀 队列背压处理
                if queue_usage_rate > 0.95:
                    logger.warning(f"🚨 队列接近满载 ({queue_usage_rate:.1%})，启动背压处理")
                    await self._handle_queue_backpressure(queue_stats)
                
            except Exception as e:
                logger.error(f"负载均衡器异常: {e}")
                await asyncio.sleep(5)
        
        logger.info("负载均衡器停止")
    
    async def _scale_workers(self, target_count: int):
        """🚀 动态调整工作者数量"""
        current_count = len(self.worker_tasks)
        
        if target_count > current_count:
            # 扩展工作者
            for i in range(current_count, target_count):
                worker_id = f"worker-{i}"
                task = asyncio.create_task(self._execution_worker(worker_id))
                self.worker_tasks.append(task)
                
                # 初始化工作者统计
                self.worker_stats[worker_id] = {
                    'commands_processed': 0,
                    'commands_failed': 0,
                    'avg_processing_time_ms': 0.0,
                    'last_activity_time': time.time(),
                    'is_busy': False,
                    'current_command': None
                }
        
        elif target_count < current_count:
            # 缩减工作者
            workers_to_remove = current_count - target_count
            for _ in range(workers_to_remove):
                if self.worker_tasks:
                    task = self.worker_tasks.pop()
                    task.cancel()
                    
                    # 清理统计信息
                    worker_id = f"worker-{len(self.worker_tasks)}"
                    if worker_id in self.worker_stats:
                        del self.worker_stats[worker_id]
        
        logger.debug(f"工作者数量调整完成: {current_count} → {target_count}")
    
    async def _handle_queue_backpressure(self, queue_stats: Dict[str, Any]):
        """🚀 队列背压处理"""
        # 优先处理高优先级队列
        high_priority_queues = ['CRITICAL', 'HIGH']
        
        for priority_name in high_priority_queues:
            priority_size = queue_stats['queue_sizes'].get(priority_name, 0)
            if priority_size > 0:
                logger.info(f"🎯 优先处理 {priority_name} 队列: {priority_size} 个命令")
                # 这里可以实现特殊的批量处理逻辑
                break
        
        # 统计背压事件
        self.execution_stats['queue_full_count'] = self.execution_stats.get('queue_full_count', 0) + 1

    async def _queue_monitor_loop(self):
        """🚀 增强队列监控循环 - 包含工作者统计"""
        logger.info(f"队列监控启动: {self.account_id}")

        while self.running:
            try:
                # 获取优先级队列统计信息
                queue_stats = self.command_queue.get_stats()
                current_size = queue_stats['heap_size']
                
                self.execution_stats['queue_current_size'] = current_size
                self.execution_stats['queue_max_size'] = max(
                    self.execution_stats.get('queue_max_size', 0),
                    current_size
                )
                
                # 记录各优先级队列的大小
                self.execution_stats['priority_queue_sizes'] = queue_stats['queue_sizes']
                
                # 🚀 工作者统计信息
                active_workers = len(self.worker_tasks)
                busy_workers = sum(1 for stats in self.worker_stats.values() if stats['is_busy'])
                total_processed = sum(stats['commands_processed'] for stats in self.worker_stats.values())
                total_failed = sum(stats['commands_failed'] for stats in self.worker_stats.values())
                
                # 更新执行统计
                self.execution_stats['active_workers'] = active_workers
                self.execution_stats['busy_workers'] = busy_workers
                self.execution_stats['worker_busy_rate'] = busy_workers / active_workers if active_workers > 0 else 0
                self.execution_stats['total_worker_processed'] = total_processed
                self.execution_stats['total_worker_failed'] = total_failed

                # 队列使用率警告
                usage_rate = current_size / self.queue_size if self.queue_size > 0 else 0
                worker_busy_rate = self.execution_stats['worker_busy_rate']
                
                if usage_rate > 0.95:
                    logger.warning(f"🚨 队列严重积压: {current_size}/{self.queue_size} ({usage_rate:.1%}), "
                                 f"工作者繁忙率: {worker_busy_rate:.1%}")
                elif usage_rate > 0.8:
                    logger.warning(f"⚠️  队列使用率高: {current_size}/{self.queue_size} ({usage_rate:.1%}), "
                                 f"活跃工作者: {busy_workers}/{active_workers}")
                    logger.debug(f"各优先级队列详情: {queue_stats['queue_sizes']}")
                elif usage_rate > 0.5:
                    logger.info(f"📊 队列使用率: {current_size}/{self.queue_size} ({usage_rate:.1%}), "
                              f"工作者: {busy_workers}/{active_workers}")
                
                # 🚀 性能分析日志
                if active_workers > 0:
                    avg_worker_processing_time = sum(
                        stats['avg_processing_time_ms'] for stats in self.worker_stats.values()
                    ) / active_workers
                    
                    logger.debug(f"📈 工作者性能: 平均处理时间 {avg_worker_processing_time:.2f}ms, "
                               f"总处理量: {total_processed}, 失败量: {total_failed}")

                await asyncio.sleep(10)  # 每10秒检查一次

            except Exception as e:
                logger.error(f"队列监控异常: {e}")
                await asyncio.sleep(5)

    async def _execute_command(self, command: ExecutionCommand):
        """执行单个命令"""
        start_time = time.time()
        
        try:
            logger.info(f"执行命令: {command.command_type} {command.symbol} {command.volume}")
            
            if command.command_type == "open":
                result = await self._execute_open_position(command)
            elif command.command_type == "close":
                result = await self._execute_close_position(command)
            elif command.command_type == "modify":
                result = await self._execute_modify_position(command)
            else:
                result = ExecutionResult(
                    command_id=command.command_id,
                    account_id=command.account_id,
                    success=False,
                    result_data={},
                    timestamp=time.time(),
                    error_message=f"未知命令类型: {command.command_type}"
                )
            
            await self._publish_execution_result(
                command.command_type, 
                command.command_id, 
                result.success, 
                result.result_data,
                result.error_message
            )
            
            if result.success:
                self.execution_stats['commands_executed'] += 1
            else:
                self.execution_stats['commands_failed'] += 1
            
            self.execution_stats['last_execution_time'] = time.time()
            
            execution_time = time.time() - start_time
            logger.debug(f"命令执行完成: {command.command_id} - 耗时: {execution_time:.3f}s")
            
        except Exception as e:
            logger.error(f"执行命令异常: {e}")
            self.execution_stats['commands_failed'] += 1
            
            await self._publish_execution_result(
                command.command_type,
                command.command_id,
                False,
                {},
                str(e)
            )
    
    async def _execute_open_position(self, command: ExecutionCommand) -> ExecutionResult:
        """执行开仓"""
        try:
            # 1. 预验证交易请求
            validation_result = await self.trade_validator.validate_trade_request({
                'symbol': command.symbol,
                'volume': command.volume,
                'order_type': command.order_type.value,
                'sl': command.sl,
                'tp': command.tp,
                'account_id': self.account_id
            })

            if not validation_result.is_valid:
                logger.warning(f"开仓验证失败: {validation_result.error_message}")
                return ExecutionResult(
                    command_id=command.command_id,
                    success=False,
                    result_data={'validation_error': validation_result.error_message},
                    timestamp=time.time(),
                    error_message=f"验证失败: {validation_result.error_message}"
                )

            execution_volume = await self._calculate_execution_volume(
                command.volume,
                command.symbol
            )

            execution_price = await self._get_execution_price(
                command.symbol,
                command.order_type.value
            )
            
            # 只使用RPC架构执行交易
            order_type = 0 if command.order_type.value == 'BUY' else 1  # MT5 order types

            # 增强的MT5 API调用，支持错误验证和重试
            trade_result_dict = await self._execute_mt5_operation_with_validation(
                'send_order',
                account_id=self.account_id,
                symbol=command.symbol,
                order_type=order_type,
                volume=execution_volume,
                price=execution_price,
                sl=command.sl or 0.0,
                tp=command.tp or 0.0,
                comment=f"Execute_{command.command_id}"
            )

            # 验证和解析结果
            validation_result = await self._validate_trade_result(trade_result_dict, 'open_position')
            
            if validation_result['is_valid']:
                trade_result = type('TradeResult', (), {
                    'retcode': trade_result_dict.get('retcode', 10009),
                    'order': trade_result_dict.get('order', 0),
                    'deal': trade_result_dict.get('deal', 0),
                    'price': trade_result_dict.get('price', execution_price),
                    'comment': trade_result_dict.get('comment', '')
                })()
                
                success = True
                error_message = None
            else:
                trade_result = type('TradeResult', (), {
                    'retcode': validation_result.get('error_code', 10020),
                    'order': 0,
                    'deal': 0,
                    'price': 0.0,
                    'comment': validation_result['error_message']
                })()
                
                success = False
                error_message = validation_result['error_message']
                
                # 记录错误到统计
                self.execution_stats['execution_errors'] += 1
            
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=success,
                result_data={
                    'ticket': trade_result.order,
                    'deal': trade_result.deal,
                    'volume': execution_volume,
                    'price': trade_result.price or execution_price,
                    'symbol': command.symbol,
                    'retcode': trade_result.retcode,
                    'validation_risk': validation_result.get('risk_level', 'unknown')
                },
                timestamp=time.time(),
                error_message=error_message
            )
            
        except Exception as e:
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=False,
                result_data={},
                timestamp=time.time(),
                error_message=str(e)
            )
    
    async def _execute_close_position(self, command: ExecutionCommand) -> ExecutionResult:
        """执行平仓 - 只使用RPC架构"""
        try:
            # 1. 预验证平仓请求
            validation_result = await self.trade_validator.validate_close_request({
                'position_id': command.position_id,
                'volume': command.volume,
                'account_id': self.account_id
            })

            if not validation_result.is_valid:
                logger.warning(f"平仓验证失败: {validation_result.error_message}")
                return ExecutionResult(
                    command_id=command.command_id,
                    success=False,
                    result_data={'validation_error': validation_result.error_message},
                    timestamp=time.time(),
                    error_message=f"验证失败: {validation_result.error_message}"
                )

            # 只使用RPC架构执行平仓
            close_result_dict = await self.rpc_client.close_position(
                account_id=self.account_id,
                position_id=command.position_id,
                volume=command.volume
            )

            # 2. 验证平仓结果
            validation_result = await self._validate_trade_result(close_result_dict, 'close_position')

            if validation_result['is_valid']:
                trade_result = type('TradeResult', (), {
                    'retcode': close_result_dict.get('retcode', 10009),
                    'deal': close_result_dict.get('deal', 0),
                    'comment': close_result_dict.get('comment', '')
                })()
                success = True
                error_message = None
            else:
                trade_result = type('TradeResult', (), {
                    'retcode': validation_result.get('error_code', 10020),
                    'deal': 0,
                    'comment': validation_result['error_message']
                })()
                success = False
                error_message = validation_result['error_message']

            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=success,
                result_data={
                    'position_id': command.position_id,
                    'volume': command.volume,
                    'deal': trade_result.deal,
                    'retcode': trade_result.retcode,
                    'validation_details': validation_result
                },
                timestamp=time.time(),
                error_message=error_message
            )
            
        except Exception as e:
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=False,
                result_data={},
                timestamp=time.time(),
                error_message=str(e)
            )
    
    async def _execute_modify_position(self, command: ExecutionCommand) -> ExecutionResult:
        """执行修改持仓 - 只使用RPC架构"""
        try:
            # 只使用RPC架构执行修改持仓
            modify_result_dict = await self.rpc_client.modify_position(
                account_id=self.account_id,
                position_id=command.position_id,
                sl=command.sl or 0.0,
                tp=command.tp or 0.0,
                comment=f"Modify_{command.command_id}"
            )

            if modify_result_dict.get("status") == "success":
                trade_result = type('TradeResult', (), {
                    'retcode': modify_result_dict.get('retcode', 10009),
                    'comment': modify_result_dict.get('comment', '')
                })()
            else:
                trade_result = type('TradeResult', (), {
                    'retcode': 10020,  # TRADE_RETCODE_ERROR
                    'comment': modify_result_dict.get('error', 'RPC modify failed')
                })()
            
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=trade_result.retcode == 10009,  # TRADE_RETCODE_DONE
                result_data={
                    'position_id': command.position_id,
                    'sl': command.sl,
                    'tp': command.tp,
                    'retcode': trade_result.retcode
                },
                timestamp=time.time(),
                error_message=trade_result.comment if trade_result.retcode != 10009 else None
            )
            
        except Exception as e:
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=False,
                result_data={},
                timestamp=time.time(),
                error_message=str(e)
            )
    
    async def _calculate_execution_volume(self, signal_volume: float, symbol: str) -> float:
        """计算执行手数 - 基于风控规则"""
        try:
            # 简单实现：直接使用信号手数
            # 实际生产中这里应该有复杂的风控逻辑
            return signal_volume
        except Exception as e:
            logger.error(f"计算执行手数失败: {e}")
            return 0.0
    
    async def _get_execution_price(self, symbol: str, order_type: str) -> Optional[float]:
        """获取执行价格 - 基于市价或配置，只使用RPC架构"""
        try:
            # 对于市价单，返回None让MT5自动处理
            if order_type.upper() in ['BUY', 'SELL']:
                return None
            
            # 对于限价单，通过RPC获取当前市价
            try:
                price_result = await self.rpc_client.get_symbol_price(self.account_id, symbol)
                if price_result.get("status") == "success":
                    price_data = price_result.get("price", {})
                    # 根据订单类型返回买价或卖价
                    if order_type.upper() in ['BUY_LIMIT', 'BUY_STOP']:
                        return price_data.get('bid', None)
                    elif order_type.upper() in ['SELL_LIMIT', 'SELL_STOP']:
                        return price_data.get('ask', None)
            except Exception as rpc_error:
                logger.warning(f"RPC获取价格失败: {rpc_error}")
            
            return None
        except Exception as e:
            logger.error(f"获取执行价格失败: {e}")
            return None
    
    async def _publish_execution_result(self, command_type: str, command_id: str, 
                                      success: bool, data: Dict[str, Any], 
                                      error_message: Optional[str] = None):
        """发布执行结果"""
        try:
            result = {
                'command_type': command_type,
                'command_id': command_id,
                'account_id': self.account_id,
                'success': success,
                'data': data,
                'timestamp': time.time(),
                'error_message': error_message
            }
            
            # 使用零拷贝消息总线或结果发布器
            if self.zero_copy_bus:
                await self.zero_copy_bus.send_message(
                    message_type=ZeroCopyMessageType.EXECUTION_RESULT,
                    data=result,
                    target_id=hash(self.account_id)
                )
            elif hasattr(self, 'result_publisher') and self.result_publisher:
                await self.result_publisher.publish(
                    subject=self.result_topic,
                    data=result
                )
            
            logger.debug(f"发布执行结果: {command_type} - {success}")
            
        except Exception as e:
            logger.error(f"发布执行结果失败: {e}")
    
    async def _execute_mt5_operation_with_validation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """执行MT5操作并进行验证，支持错误处理和重试"""
        try:
            # 执行RPC操作
            if hasattr(self.rpc_client, operation):
                rpc_method = getattr(self.rpc_client, operation)
                result = await rpc_method(**kwargs)
            else:
                result = await self.rpc_client.call(operation, **kwargs)
            
            # 返回原始结果供进一步验证
            return result
            
        except Exception as e:
            logger.error(f"MT5操作执行异常: {operation}, 错误: {e}")
            return {
                'status': 'error',
                'error_code': -1,
                'error': f"执行异常: {str(e)}",
                'operation': operation
            }
    
    async def _validate_trade_result(self, result: Dict[str, Any], operation: str) -> Dict[str, Any]:
        """验证交易结果，返回验证信息"""
        try:
            # 使用集成的交易验证器
            validation = await self.trade_validator.validate_trade_result(
                result=result,
                expected_signal={'operation': operation},
                mt5_client=self.rpc_client
            )

            # 返回验证结果
            return {
                'is_valid': validation.is_valid,
                'error_code': validation.error_code,
                'error_message': validation.error_message,
                'should_retry': validation.can_retry,
                'retry_delay': validation.retry_delay,
                'risk_level': 'medium'  # 默认风险级别
            }
            
        except Exception as e:
            logger.error(f"交易结果验证异常: {e}")
            return {
                'is_valid': False,
                'error_code': -1,
                'error_message': f"验证异常: {str(e)}",
                'should_retry': False,
                'retry_delay': 0,
                'risk_level': 'high'
            }
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """🚀 获取增强执行统计 - 包含工作者详情"""
        uptime = time.time() - (self.start_time or time.time())

        # 获取优先级队列统计信息
        queue_stats = self.command_queue.get_stats()
        
        # 🚀 计算工作者汇总统计
        active_workers = len(self.worker_tasks)
        busy_workers = sum(1 for stats in self.worker_stats.values() if stats['is_busy'])
        total_worker_processed = sum(stats['commands_processed'] for stats in self.worker_stats.values())
        total_worker_failed = sum(stats['commands_failed'] for stats in self.worker_stats.values())
        
        # 计算工作者平均性能指标
        worker_avg_processing_time = 0.0
        if active_workers > 0:
            worker_avg_processing_time = sum(
                stats['avg_processing_time_ms'] for stats in self.worker_stats.values()
            ) / active_workers
        
        # 获取验证器统计
        validator_stats = {}
        try:
            from .trade_validator import get_trade_validator
            validator = get_trade_validator()
            if hasattr(validator, 'get_validation_stats'):
                validator_stats = validator.get_validation_stats()
            else:
                validator_stats = {'stats_unavailable': True}
        except Exception as e:
            logger.debug(f"获取验证器统计失败: {e}")
            validator_stats = {'error': str(e)}
        
        return {
            # 基础统计
            **self.execution_stats,
            'account_id': self.account_id,
            'running': self.running,
            'uptime_seconds': uptime,
            'pending_commands': len(self.pending_commands),
            
            # 队列统计
            'queue_size': queue_stats['heap_size'],
            'queue_usage_rate': queue_stats['heap_size'] / self.queue_size if self.queue_size > 0 else 0,
            'priority_queue_stats': queue_stats,
            
            # 🚀 工作者统计
            'worker_pool': {
                'active_workers': active_workers,
                'busy_workers': busy_workers,
                'idle_workers': active_workers - busy_workers,
                'worker_busy_rate': busy_workers / active_workers if active_workers > 0 else 0,
                'total_worker_processed': total_worker_processed,
                'total_worker_failed': total_worker_failed,
                'worker_success_rate': (
                    total_worker_processed / (total_worker_processed + total_worker_failed)
                    if (total_worker_processed + total_worker_failed) > 0 else 1.0
                ),
                'avg_processing_time_ms': worker_avg_processing_time,
                'worker_scaling_config': {
                    'current_count': active_workers,
                    'min_workers': self.min_worker_count,
                    'max_workers': self.max_worker_count,
                    'scaling_threshold': self.worker_scaling_threshold,
                    'scale_down_threshold': self.worker_scale_down_threshold
                }
            },
            
            # 工作者详细统计
            'worker_details': dict(self.worker_stats),
            
            # 连接信息
            'signal_topic_pattern': self.signal_topic_pattern,
            'result_topic': self.result_topic,
            'rpc_enabled': self.rpc_client is not None,
            'enhanced_processor_enabled': self.enhanced_processor is not None,
            'validator_stats': validator_stats,
            
            # 性能指标
            'performance_metrics': {
                'commands_per_second': total_worker_processed / uptime if uptime > 0 else 0,
                'error_rate': total_worker_failed / (total_worker_processed + total_worker_failed) if (total_worker_processed + total_worker_failed) > 0 else 0,
                'queue_backpressure_events': self.execution_stats.get('queue_full_count', 0)
            }
        }

    async def execute_order_rpc(self, symbol: str, order_type: str, volume: float,
                               price: float = 0.0, sl: float = 0.0, tp: float = 0.0,
                               comment: str = "") -> Dict[str, Any]:
        """使用RPC直接执行订单"""
        if not self.rpc_client:
            return {"status": "failed", "error": "RPC client not available"}

        try:
            # 转换订单类型
            mt5_order_type = 0 if order_type.upper() == 'BUY' else 1

            result = await self.rpc_client.send_order(
                account_id=self.account_id,
                symbol=symbol,
                order_type=mt5_order_type,
                volume=volume,
                price=price,
                sl=sl,
                tp=tp,
                comment=comment
            )

            return result
        except Exception as e:
            logger.error(f"RPC订单执行失败: {self.account_id}, 错误: {e}")
            return {"status": "failed", "error": str(e)}
    
    # 重写父类方法，确保只执行
    async def execute_trade(self, signal: TradeSignal) -> bool:
        """执行交易 - 父类接口实现"""
        command = ExecutionCommand(
            command_id=f"trade_{signal.signal_id}",
            command_type=signal.action.value.lower(),
            account_id=self.account_id,
            symbol=signal.symbol,
            volume=signal.volume,
            order_type=signal.order_type,
            price=signal.price,
            sl=signal.sl,
            tp=signal.tp,
            position_id=signal.position_id
        )
        
        priority = self._analyze_command_priority(command)
        success = await self.command_queue.enqueue(
            priority=priority,
            data=command,
            message_id=command.command_id,
            callback=None
        )
        return success
    
    async def health_check(self) -> bool:
        """🚀 增强健康检查 - 包含工作者状态检查"""
        try:
            # 检查执行器运行状态
            if not self.running:
                logger.warning(f"执行器未运行: {self.account_id}")
                return False
            
            # 🚀 检查工作者健康状态
            active_workers = len(self.worker_tasks)
            if active_workers == 0:
                logger.error(f"没有活跃工作者: {self.account_id}")
                return False
            
            # 检查工作者是否响应
            current_time = time.time()
            stale_workers = []
            for worker_id, stats in self.worker_stats.items():
                last_activity = stats['last_activity_time']
                if current_time - last_activity > 60:  # 60秒无活动认为不健康
                    stale_workers.append(worker_id)
            
            if stale_workers:
                logger.warning(f"发现不健康工作者: {stale_workers}")
                # 可以选择是否重启这些工作者
            
            # 检查队列状态
            if self.command_queue:
                queue_stats = self.command_queue.get_stats()
                queue_usage = queue_stats['heap_size'] / self.queue_size if self.queue_size > 0 else 0
                
                if queue_usage > 0.95:  # 队列使用率超过95%
                    logger.warning(f"🚨 执行器队列满载: {queue_usage:.1%}")
                    # 队列满载但仍认为健康，因为有背压处理机制
                elif queue_usage > 0.8:
                    logger.info(f"⚠️  执行器队列使用率高: {queue_usage:.1%}")
            
            # 检查工作者繁忙率
            busy_workers = sum(1 for stats in self.worker_stats.values() if stats['is_busy'])
            worker_busy_rate = busy_workers / active_workers if active_workers > 0 else 0
            
            if worker_busy_rate > 0.95:
                logger.info(f"📊 工作者高负载运行: {worker_busy_rate:.1%}")
            
            # 检查零拷贝通信组件
            if self.zero_copy_bus and not await self.zero_copy_bus.is_healthy():
                logger.warning(f"零拷贝消息总线不健康: {self.account_id}")
            
            # 检查RPC连接
            if self.rpc_client:
                try:
                    health_result = await self.rpc_client.health_check(self.account_id)
                    if health_result.get('status') != 'success':
                        logger.warning(f"RPC健康检查失败: {self.account_id}")
                        # 但不立即返回False，允许其他组件正常工作
                except Exception as e:
                    logger.debug(f"RPC健康检查异常: {self.account_id} - {e}")
            
            # 检查负载均衡器状态
            if self.load_balancer_task and self.load_balancer_task.done():
                logger.warning(f"负载均衡器意外停止: {self.account_id}")
                # 尝试重启负载均衡器
                try:
                    self.load_balancer_task = asyncio.create_task(self._load_balancer_loop())
                    logger.info(f"负载均衡器已重启: {self.account_id}")
                except Exception as e:
                    logger.error(f"重启负载均衡器失败: {e}")
            
            # 🚀 记录健康检查统计
            health_stats = {
                'active_workers': active_workers,
                'stale_workers': len(stale_workers),
                'queue_usage_rate': queue_usage,
                'worker_busy_rate': worker_busy_rate,
                'check_time': current_time
            }
            
            logger.debug(f"健康检查完成: {health_stats}")
            return True
            
        except Exception as e:
            logger.error(f"执行器健康检查异常: {self.account_id} - {e}")
            return False
    
    async def stop(self):
        """停止执行器 - 进程启动器接口要求"""
        await self.stop_executor()