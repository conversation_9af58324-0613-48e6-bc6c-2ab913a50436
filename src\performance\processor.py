#!/usr/bin/env python3
"""
增强性能处理器 - 完整集成版本
合并Protocol Buffers、优先级队列、连接池、统一内存池、交易匹配和验证
"""
import asyncio
import time
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass

from src.utils.logger import get_logger
from src.utils.metrics import get_metrics_collector
from src.utils.memory_pool import UnifiedMemoryPool, get_unified_memory_pool
from src.messaging.message_types import TradeSignal
from src.messaging.priority_queue import PriorityMessageQueue, MessagePriority, get_priority_queue
from src.core.trade_matching import IndustrialTradeMatching, get_trade_matching
from src.core.trade_validator import TradeValidator, get_trade_validator

logger = get_logger(__name__)
metrics = get_metrics_collector()


@dataclass
class ProcessorConfig:
    """性能处理器配置"""
    # Protocol Buffers配置
    use_protobuf: bool = True
    compression_enabled: bool = True
    compression_threshold: int = 1024
    
    # 🚀 优先级队列配置 - 增强并发处理
    use_priority_queue: bool = True
    queue_worker_count: int = 8  # 增加工作线程适配多执行器
    enable_dynamic_scaling: bool = True  # 动态扩展工作者
    max_queue_workers: int = 12  # 最大工作者数量
    min_queue_workers: int = 4   # 最小工作者数量
    
    # 连接池配置（在分布式多进程架构中可选）
    use_connection_pool: bool = False  # 分布式架构下默认禁用
    max_connections: int = 15
    max_idle_time: int = 300
    
    # 🚀 批处理配置 - 工业级高并发处理
    batch_enabled: bool = True
    batch_size: int = 150  # 增大批次大小提高吞吐量
    batch_timeout_ms: int = 25  # 减少超时时间提升响应性
    concurrent_batch_processors: int = 3  # 并发批处理器数量
    enable_priority_batching: bool = True  # 优先级感知批处理
    adaptive_batch_sizing: bool = True  # 自适应批次大小
    
    # 内存池配置
    use_unified_memory: bool = True
    memory_pool_size: int = 64 * 1024 * 1024  # 64MB
    
    # 交易系统集成
    enable_trade_matching: bool = True
    enable_trade_validation: bool = True
    validation_timeout: float = 5.0
    
    # 缓存配置
    cache_enabled: bool = True
    cache_ttl: int = 300
    
    # 性能监控
    enable_performance_monitoring: bool = True
    stats_interval: float = 30.0


class PerformanceProcessor:
    """性能处理器 - 完整集成版本"""
    
    def __init__(self, config: ProcessorConfig = None):
        self.config = config or ProcessorConfig()
        self.running = False
        
        # 核心组件引用
        self.memory_pool: Optional[UnifiedMemoryPool] = None
        self.priority_queue: Optional[PriorityMessageQueue] = None
        self.trade_matcher: Optional[IndustrialTradeMatching] = None
        self.trade_validator: Optional[TradeValidator] = None
        self.retry_manager = None
        
        # Protocol Buffers编解码器
        self.message_envelope = None
        
        # 🚀 增强批处理缓冲区
        self._batch_buffer: List[Dict[str, Any]] = []
        self._batch_lock = asyncio.Lock()
        self._batch_tasks: List[asyncio.Task] = []
        
        # 🚀 自适应批处理配置
        self._current_batch_size = self.config.batch_size
        self._batch_performance_history: List[float] = []
        
        # 缓存系统
        self._cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, float] = {}
        self._cache_lock = asyncio.Lock()
        
        # 性能监控
        self._monitoring_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self.stats = {
            'messages_processed': 0,
            'batches_processed': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'trade_matches_found': 0,
            'trade_validations_passed': 0,
            'trade_retries_executed': 0,
            'memory_allocations': 0,
            'avg_processing_time_ms': 0.0,
            'total_processing_time_ms': 0.0,
            'compression_ratio': 0.0
        }
        
        logger.info("增强性能处理器初始化开始...")
    
    async def initialize(self, 
                        memory_pool: UnifiedMemoryPool = None,
                        priority_queue: PriorityMessageQueue = None, 
                        connection_pool: ConnectionPool = None,
                        state_manager = None):
        """初始化增强性能处理器"""
        logger.info("初始化增强性能处理器...")
        
        try:
            # 1. 初始化统一内存池
            if self.config.use_unified_memory:
                if memory_pool:
                    self.memory_pool = memory_pool
                else:
                    self.memory_pool = get_unified_memory_pool()
                    await self.memory_pool.start()
                logger.info("统一内存池已集成")
            
            # 2. 初始化Protocol Buffers编解码器
            if self.config.use_protobuf:
                from src.messaging.message_types import MessageEnvelope
                self.message_envelope = MessageEnvelope
                logger.info("Protocol Buffers编解码器已初始化")
            
            # 3. 初始化优先级队列
            if self.config.use_priority_queue:
                if priority_queue:
                    self.priority_queue = priority_queue
                else:
                    self.priority_queue = get_priority_queue()
                    await self.priority_queue.start_workers(self.config.queue_worker_count)
                
                # 注册处理器
                await self._register_priority_processors()
                logger.info("优先级队列已初始化")
            
            # 4. 初始化连接池（分布式架构中可选）
            if self.config.use_connection_pool:
                if connection_pool:
                    self.connection_pool = connection_pool
                else:
                    self.connection_pool = get_connection_pool()
                    await self.connection_pool.start()
                logger.info("连接池已初始化")
            else:
                logger.info("连接池已禁用（分布式多进程架构）")
            
            # 5. 初始化交易匹配系统
            if self.config.enable_trade_matching:
                self.trade_matcher = get_trade_matching(state_manager)
                logger.info("交易匹配系统已集成")
            
            # 6. 初始化交易验证系统
            if self.config.enable_trade_validation:
                self.trade_validator = get_trade_validator()
                if self.connection_pool:
                    # 这里需要MT5客户端，暂时使用None
                    # self.retry_manager = get_retry_manager(mt5_client)
                    pass
                logger.info("交易验证系统已集成")
            
            # 7. 🚀 启动多并发批处理器
            if self.config.batch_enabled:
                batch_count = self.config.concurrent_batch_processors
                for i in range(batch_count):
                    batch_task = asyncio.create_task(
                        self._batch_processor(f"batch-processor-{i}")
                    )
                    self._batch_tasks.append(batch_task)
                logger.info(f"🚀 {batch_count}个并发批处理器已启动")
            
            # 8. 启动性能监控
            if self.config.enable_performance_monitoring:
                self._monitoring_task = asyncio.create_task(self._performance_monitor())
                logger.info("性能监控已启动")
            
            self.running = True
            logger.info("增强性能处理器初始化完成")
            
        except Exception as e:
            logger.error(f"增强性能处理器初始化失败: {e}")
            raise
    
    async def _register_priority_processors(self):
        """注册优先级处理器"""
        self.priority_queue.register_processor(
            MessagePriority.SystemCritical, self._process_critical_signal
        )
        self.priority_queue.register_processor(
            MessagePriority.riskCommand, self._process_high_signal
        )
        self.priority_queue.register_processor(
            MessagePriority.NORMAL, self._process_normal_signal
        )
        self.priority_queue.register_processor(
            MessagePriority.REALTIME_QUERY, self._process_realtime_query
        )
        self.priority_queue.register_processor(
            MessagePriority.LOW, self._process_low_signal
        )
    
    async def shutdown(self):
        """关闭增强性能处理器"""
        logger.info("关闭增强性能处理器...")
        
        self.running = False
        
        try:
            # 🚀 停止所有批处理器
            if self._batch_tasks:
                for batch_task in self._batch_tasks:
                    batch_task.cancel()
                
                # 等待所有批处理器停止
                await asyncio.gather(*self._batch_tasks, return_exceptions=True)
                self._batch_tasks.clear()
            
            # 停止监控任务
            if self._monitoring_task:
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass
            
            # 处理剩余批次
            async with self._batch_lock:
                if self._batch_buffer:
                    await self._flush_batch()
            
            # 关闭各个组件
            if self.priority_queue:
                await self.priority_queue.stop_workers()
            
            if self.connection_pool:
                await self.connection_pool.stop()
            
            if self.memory_pool:
                await self.memory_pool.stop()
            
            logger.info("增强性能处理器已关闭")
            
        except Exception as e:
            logger.error(f"关闭增强性能处理器失败: {e}")
    
    async def process_trade_signal(self, 
                                 signal: Union[TradeSignal, Dict[str, Any]], 
                                 priority: MessagePriority = MessagePriority.SIGNAL_EXECUTION,
                                 enable_matching: bool = True,
                                 enable_validation: bool = True) -> Dict[str, Any]:
        """处理交易信号 - 增强版本"""
        start_time = time.time()
        signal_id = getattr(signal, 'signal_id', signal.get('signal_id', 'unknown')) if isinstance(signal, dict) else signal.signal_id
        
        try:
            # 1. 转换信号格式
            if isinstance(signal, TradeSignal):
                signal_dict = signal.__dict__
            else:
                signal_dict = signal
            
            # 2. 缓存检查
            cache_key = f"signal_{signal_id}_{signal_dict.get('account_id', '')}"
            cached_result = await self._get_from_cache(cache_key)
            if cached_result:
                self.stats['cache_hits'] += 1
                return cached_result
            
            self.stats['cache_misses'] += 1
            
            # 3. 内存分配（如果启用）
            memory_buffer = None
            if self.memory_pool and self.config.use_unified_memory:
                buffer_size = len(str(signal_dict).encode('utf-8'))
                memory_buffer = self.memory_pool.allocate_memory(buffer_size, f"signal_{signal_id}")
                if memory_buffer:
                    self.stats['memory_allocations'] += 1
            
            # 4. 确定优先级
            signal_priority = self._determine_priority(signal_dict)
            if priority != MessagePriority.NORMAL:
                signal_priority = priority
            
            # 5. 交易匹配（开仓时创建映射，平仓时查找匹配）
            matching_result = None
            if enable_matching and self.trade_matcher:
                matching_result = await self._handle_trade_matching(signal_dict)
                if matching_result:
                    self.stats['trade_matches_found'] += 1
            
            # 6. 批处理或直接处理
            result = None
            if self.config.batch_enabled:
                # 添加到批处理缓冲区
                await self._add_to_batch({
                    'signal': signal_dict,
                    'priority': signal_priority,
                    'timestamp': start_time,
                    'memory_buffer': memory_buffer,
                    'matching_result': matching_result
                })
                result = {'status': 'batched', 'signal_id': signal_id}
            
            elif self.priority_queue:
                # 直接入队
                success = await self.priority_queue.enqueue(
                    signal_priority, {
                        'signal_data': signal_dict,
                        'memory_buffer': memory_buffer,
                        'matching_result': matching_result
                    }, signal_id
                )
                result = {'status': 'queued' if success else 'queue_failed', 'signal_id': signal_id}
            
            else:
                # 直接处理
                result = await self._process_signal_direct(signal_dict, matching_result)
            
            # 7. 交易验证（如果是执行结果）
            if enable_validation and self.trade_validator and result.get('trade_result'):
                validation_result = await self.trade_validator.validate_trade_result(
                    result['trade_result'], signal_dict
                )
                result['validation'] = validation_result
                if validation_result.is_valid:
                    self.stats['trade_validations_passed'] += 1
            
            # 8. 更新统计
            processing_time = (time.time() - start_time) * 1000
            self._update_stats(processing_time)
            
            # 9. 缓存结果
            await self._set_cache(cache_key, result)
            
            # 10. 释放内存
            if memory_buffer and self.memory_pool:
                self.memory_pool.deallocate_memory(memory_buffer)
            
            return result
            
        except Exception as e:
            logger.error(f"处理交易信号失败 {signal_id}: {e}")
            metrics.increment_counter("enhanced_processor_error_total")
            return {'status': 'error', 'error': str(e), 'signal_id': signal_id}
    
    async def _handle_trade_matching(self, signal_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理交易匹配"""
        signal_type = signal_dict.get('signal_type', '').upper()
        
        if signal_type == 'POSITION_OPEN':
            # 开仓 - 创建映射
            signal_uuid = await self.trade_matcher.create_signal_mapping(
                master_account=signal_dict.get('master_account', ''),
                master_ticket=signal_dict.get('master_ticket', 0),
                slave_account=signal_dict.get('account_id', ''),
                signal=signal_dict
            )
            return {'action': 'create_mapping', 'signal_uuid': signal_uuid}
        
        elif signal_type == 'POSITION_CLOSE':
            # 平仓 - 查找匹配
            current_positions = signal_dict.get('current_positions', [])
            match_result = await self.trade_matcher.find_matching_positions(
                close_signal=signal_dict,
                slave_account=signal_dict.get('account_id', ''),
                current_positions=current_positions
            )
            return {'action': 'find_matches', 'match_result': match_result}
        
        return None
    
    async def _add_to_batch(self, item: Dict[str, Any]):
        """添加到批处理缓冲区"""
        async with self._batch_lock:
            self._batch_buffer.append(item)
            
            # 如果缓冲区满了，立即处理
            if len(self._batch_buffer) >= self.config.batch_size:
                await self._flush_batch()
    
    async def _batch_processor(self, processor_id: str = "default"):
        """🚀 增强批处理器任务 - 支持并发和优先级感知"""
        logger.info(f"🔄 启动增强批处理器: {processor_id}")
        
        local_batch_buffer = []  # 每个处理器的本地缓冲区
        batch_count = 0
        
        while self.running:
            try:
                await asyncio.sleep(self.config.batch_timeout_ms / 1000.0)
                
                # 🚀 优先级感知批处理 - 从共享缓冲区获取项目
                async with self._batch_lock:
                    if self._batch_buffer:
                        # 如果启用优先级批处理，按优先级排序
                        if self.config.enable_priority_batching:
                            self._batch_buffer.sort(
                                key=lambda x: x['priority'].value if hasattr(x['priority'], 'value') else 3
                            )
                        
                        # 分配给当前处理器
                        items_to_take = min(len(self._batch_buffer), self._current_batch_size)
                        local_batch_buffer = self._batch_buffer[:items_to_take]
                        self._batch_buffer = self._batch_buffer[items_to_take:]
                
                # 处理本地批次
                if local_batch_buffer:
                    start_time = time.time()
                    await self._flush_batch_items(local_batch_buffer, processor_id)
                    processing_time = (time.time() - start_time) * 1000
                    
                    # 🚀 自适应批次大小调整
                    if self.config.adaptive_batch_sizing:
                        await self._adjust_batch_size(processing_time, len(local_batch_buffer))
                    
                    batch_count += 1
                    local_batch_buffer.clear()
                    
            except asyncio.CancelledError:
                # 处理剩余批次
                if local_batch_buffer:
                    await self._flush_batch_items(local_batch_buffer, processor_id)
                break
            except Exception as e:
                logger.error(f"批处理器 {processor_id} 异常: {e}")
                await asyncio.sleep(1)
        
        logger.info(f"批处理器 {processor_id} 停止 (处理了 {batch_count} 个批次)")
    
    async def _flush_batch(self):
        """刷新批处理缓冲区"""
        if not self._batch_buffer:
            return
        
        batch_size = len(self._batch_buffer)
        logger.debug(f"处理增强批次: {batch_size} 个信号")
        
        start_time = time.time()
        
        try:
            # 按优先级分组
            priority_groups = {
                MessagePriority.CRITICAL: [],
                MessagePriority.HIGH: [],
                MessagePriority.NORMAL: [],
                MessagePriority.REALTIME_QUERY: [],
                MessagePriority.LOW: []
            }
            
            for item in self._batch_buffer:
                priority = item['priority']
                priority_groups[priority].append(item)
            
            # 按优先级顺序处理
            for priority in [MessagePriority.CRITICAL, MessagePriority.HIGH, 
                           MessagePriority.NORMAL, MessagePriority.REALTIME_QUERY, MessagePriority.LOW]:
                items = priority_groups[priority]
                if items:
                    if self.priority_queue:
                        # 批量入队
                        for item in items:
                            await self.priority_queue.enqueue(
                                priority, item, item['signal'].get('signal_id', 'batch')
                            )
                    else:
                        # 直接批量处理
                        await self._process_batch_items(items)
            
            # Protocol Buffers编码（如果启用）
            if self.message_envelope and self.config.use_protobuf:
                await self._encode_batch_with_protobuf(self._batch_buffer)
            
            # 更新统计
            processing_time = (time.time() - start_time) * 1000
            self.stats['batches_processed'] += 1
            self.stats['messages_processed'] += batch_size
            
            metrics.increment_counter("enhanced_processor_batch_processed_total")
            metrics.record_histogram("enhanced_processor_batch_size", batch_size)
            metrics.record_histogram("enhanced_processor_batch_time_ms", processing_time)
            
            logger.debug(f"增强批次处理完成: {batch_size} 个信号，耗时 {processing_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"批次处理失败: {e}")
        finally:
            # 清空缓冲区
            self._batch_buffer.clear()
    
    async def _flush_batch_items(self, batch_items: List[Dict[str, Any]], processor_id: str):
        """🚀 处理批次项目 - 增强版本"""
        if not batch_items:
            return
        
        batch_size = len(batch_items)
        start_time = time.time()
        
        try:
            # 按优先级分组处理
            if self.config.enable_priority_batching:
                priority_groups = self._group_by_priority(batch_items)
                
                # 按优先级顺序处理
                for priority in [MessagePriority.CRITICAL, MessagePriority.HIGH, 
                               MessagePriority.NORMAL, MessagePriority.REALTIME_QUERY, MessagePriority.LOW]:
                    items = priority_groups.get(priority, [])
                    if items:
                        await self._process_priority_group(items, priority, processor_id)
            else:
                # 直接批量处理
                await self._process_batch_items(batch_items)
            
            # Protocol Buffers编码（如果启用）
            if self.message_envelope and self.config.use_protobuf:
                await self._encode_batch_with_protobuf(batch_items)
            
            # 更新统计
            processing_time = (time.time() - start_time) * 1000
            self.stats['batches_processed'] += 1
            self.stats['messages_processed'] += batch_size
            
            metrics.increment_counter("enhanced_processor_batch_processed_total")
            metrics.record_histogram("enhanced_processor_batch_size", batch_size)
            metrics.record_histogram("enhanced_processor_batch_time_ms", processing_time)
            
            logger.debug(f"批处理器 {processor_id} 完成: {batch_size} 个信号，耗时 {processing_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"批处理器 {processor_id} 处理失败: {e}")
    
    def _group_by_priority(self, batch_items: List[Dict[str, Any]]) -> Dict[MessagePriority, List]:
        """按优先级分组批次项目"""
        priority_groups = {
            MessagePriority.CRITICAL: [],
            MessagePriority.HIGH: [],
            MessagePriority.NORMAL: [],
            MessagePriority.REALTIME_QUERY: [],
            MessagePriority.LOW: []
        }
        
        for item in batch_items:
            priority = item.get('priority', MessagePriority.NORMAL)
            if priority in priority_groups:
                priority_groups[priority].append(item)
            else:
                priority_groups[MessagePriority.NORMAL].append(item)
        
        return priority_groups
    
    async def _process_priority_group(self, items: List[Dict[str, Any]], 
                                    priority: MessagePriority, processor_id: str):
        """处理优先级组"""
        if not items:
            return
        
        logger.debug(f"批处理器 {processor_id} 处理 {priority.name} 组: {len(items)} 个项目")
        
        if self.priority_queue:
            # 批量入队
            for item in items:
                await self.priority_queue.enqueue(
                    priority, item, item['signal'].get('signal_id', f'batch_{processor_id}')
                )
        else:
            # 直接处理
            await self._process_batch_items(items)
    
    async def _adjust_batch_size(self, processing_time_ms: float, batch_size: int):
        """🚀 自适应批次大小调整"""
        # 记录性能历史
        self._batch_performance_history.append(processing_time_ms / batch_size)
        
        # 保持最近100次的历史记录
        if len(self._batch_performance_history) > 100:
            self._batch_performance_history.pop(0)
        
        # 每10次调整一次批次大小
        if len(self._batch_performance_history) >= 10:
            avg_time_per_item = sum(self._batch_performance_history[-10:]) / 10
            
            # 如果处理时间过长，减小批次大小
            if avg_time_per_item > 2.0:  # 2ms per item
                self._current_batch_size = max(
                    int(self._current_batch_size * 0.9), 
                    self.config.batch_size // 2
                )
                logger.debug(f"🔽 减小批次大小至: {self._current_batch_size}")
            
            # 如果处理时间很短，增加批次大小
            elif avg_time_per_item < 0.5:  # 0.5ms per item
                self._current_batch_size = min(
                    int(self._current_batch_size * 1.1), 
                    self._current_batch_size * 2
                )
                logger.debug(f"🔼 增加批次大小至: {self._current_batch_size}")

    async def _encode_batch_with_protobuf(self, batch_items: List[Dict[str, Any]]):
        """使用Protocol Buffers编码批次"""
        try:
            # 提取信号数据
            signals = [item['signal'] for item in batch_items]
            
            # 模拟编码过程
            original_size = len(str(signals).encode('utf-8'))
            
            if self.config.compression_enabled and original_size > self.config.compression_threshold:
                # 模拟压缩
                compressed_size = int(original_size * 0.7)  # 假设70%压缩率
                compression_ratio = compressed_size / original_size
                
                self.stats['compression_ratio'] = (
                    (self.stats['compression_ratio'] * (self.stats['batches_processed'] - 1) + compression_ratio) /
                    self.stats['batches_processed']
                )
                
                logger.debug(f"批次压缩: {original_size} -> {compressed_size} bytes (压缩率: {compression_ratio:.2f})")
            
        except Exception as e:
            logger.error(f"Protocol Buffers编码失败: {e}")
    
    async def _process_batch_items(self, items: List[Dict[str, Any]]):
        """处理批次项目"""
        for item in items:
            signal_dict = item['signal']
            matching_result = item.get('matching_result')
            await self._process_signal_direct(signal_dict, matching_result)
    
    # 优先级处理器方法
    async def _process_critical_signal(self, data: Dict[str, Any]):
        """处理关键信号"""
        signal_data = data.get('signal_data', {})
        logger.info(f"处理关键信号: {signal_data.get('signal_id')}")
        await self._process_signal_with_connection(signal_data, data.get('matching_result'))
    
    async def _process_high_signal(self, data: Dict[str, Any]):
        """处理高优先级信号"""
        signal_data = data.get('signal_data', {})
        logger.debug(f"处理高优先级信号: {signal_data.get('signal_id')}")
        await self._process_signal_with_connection(signal_data, data.get('matching_result'))
    
    async def _process_normal_signal(self, data: Dict[str, Any]):
        """处理普通信号"""
        signal_data = data.get('signal_data', {})
        logger.debug(f"处理普通信号: {signal_data.get('signal_id')}")
        await self._process_signal_with_connection(signal_data, data.get('matching_result'))
    
    async def _process_realtime_query(self, data: Dict[str, Any]):
        """处理实时查询"""
        signal_data = data.get('signal_data', {})
        logger.debug(f"处理实时查询: {signal_data.get('signal_id', signal_data.get('query_type'))}")
        
        # 实时查询通常不需要交易匹配
        await self._handle_realtime_query(signal_data)
    
    async def _process_low_signal(self, data: Dict[str, Any]):
        """处理低优先级信号"""
        signal_data = data.get('signal_data', {})
        logger.debug(f"处理低优先级信号: {signal_data.get('signal_id')}")
        await self._process_signal_with_connection(signal_data, data.get('matching_result'))
    
    async def _process_signal_with_connection(self, signal_dict: Dict[str, Any], matching_result: Optional[Dict] = None):
        """使用连接池处理信号"""
        account_id = signal_dict.get('account_id')
        
        if not account_id:
            logger.error("信号缺少账户ID")
            return
        
        if self.connection_pool:
            try:
                async with self.connection_pool.get_connection(account_id) as connection:
                    if connection:
                        # 执行信号并集成匹配结果
                        result = await self._execute_signal_with_matching(
                            connection.connection_object, signal_dict, matching_result
                        )
                        
                        # 如果启用验证且有执行结果
                        if self.trade_validator and result:
                            validation = await self.trade_validator.validate_trade_result(
                                result, signal_dict
                            )
                            if not validation.is_valid and validation.can_retry:
                                await self._handle_retry(signal_dict, result, validation)
                    else:
                        logger.error(f"无法获取连接 (账户: {account_id})")
            except Exception as e:
                logger.error(f"连接池处理失败: {e}")
                await self._process_signal_direct(signal_dict, matching_result)
        else:
            await self._process_signal_direct(signal_dict, matching_result)
    
    async def _execute_signal_with_matching(self, connection, signal_dict: Dict[str, Any], matching_result: Optional[Dict]) -> Optional[Dict]:
        """执行信号并考虑匹配结果"""
        logger.debug(f"执行信号: {signal_dict.get('signal_id')}")
        
        # 如果有匹配结果，使用匹配信息
        if matching_result and matching_result.get('action') == 'find_matches':
            match_result = matching_result.get('match_result')
            if match_result and match_result.matched_positions:
                # 使用匹配的持仓信息执行平仓
                execution_plan = match_result.execution_plan
                for plan_item in execution_plan:
                    logger.info(f"执行匹配的平仓计划: {plan_item}")
                    # 这里调用实际的MT5平仓API
                    await asyncio.sleep(0.005)  # 模拟执行时间
        else:
            # 正常执行信号
            await asyncio.sleep(0.005)  # 模拟执行时间
        
        # 返回模拟的执行结果
        return {
            'retcode': 10009,  # TRADE_RETCODE_DONE
            'order': 12345,
            'volume': signal_dict.get('volume', 0.0),
            'price': signal_dict.get('price', 0.0)
        }
    
    async def _process_signal_direct(self, signal_dict: Dict[str, Any], matching_result: Optional[Dict] = None):
        """直接处理信号"""
        logger.debug(f"直接处理信号: {signal_dict.get('signal_id')}")
        await asyncio.sleep(0.001)  # 模拟处理时间
    
    async def _handle_realtime_query(self, signal_dict: Dict[str, Any]):
        """处理实时查询"""
        query_type = signal_dict.get('query_type', signal_dict.get('action', ''))
        logger.debug(f"执行实时查询: {query_type}")
        await asyncio.sleep(0.002)  # 模拟查询时间
    
    async def _handle_retry(self, signal_dict: Dict[str, Any], failed_result: Dict, validation):
        """处理重试"""
        if self.retry_manager:
            self.stats['trade_retries_executed'] += 1
            logger.info(f"执行交易重试: {signal_dict.get('signal_id')}")
            # 这里可以调用重试管理器
    
    # 缓存系统
    async def _get_from_cache(self, key: str) -> Optional[Any]:
        """从缓存获取数据"""
        async with self._cache_lock:
            if key in self._cache:
                timestamp = self._cache_timestamps.get(key, 0)
                if time.time() - timestamp < self.config.cache_ttl:
                    return self._cache[key]
                else:
                    # 过期，删除
                    del self._cache[key]
                    del self._cache_timestamps[key]
        return None
    
    async def _set_cache(self, key: str, value: Any):
        """设置缓存"""
        async with self._cache_lock:
            self._cache[key] = value
            self._cache_timestamps[key] = time.time()
    
    # 性能监控
    async def _performance_monitor(self):
        """性能监控任务"""
        logger.info("📊 启动性能监控")
        
        while self.running:
            try:
                await asyncio.sleep(self.config.stats_interval)
                await self._log_performance_stats()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
        
        logger.info("性能监控停止")
    
    async def _log_performance_stats(self):
        """记录性能统计"""
        try:
            # 获取各组件统计
            stats = await self.get_comprehensive_stats()
            
            logger.info(f"📊 性能统计: 处理={stats['messages_processed']}, "
                       f"批次={stats['batches_processed']}, "
                       f"缓存命中率={stats.get('cache_hit_rate', 0):.1%}, "
                       f"平均处理时间={stats['avg_processing_time_ms']:.2f}ms")
            
            # 记录到监控系统
            for key, value in stats.items():
                if isinstance(value, (int, float)):
                    metrics.gauge(f"enhanced_processor_{key}", value)
                    
        except Exception as e:
            logger.error(f"记录性能统计失败: {e}")
    
    def _determine_priority(self, signal_dict: Dict[str, Any]) -> MessagePriority:
        """确定信号优先级"""
        action = signal_dict.get('action', '').upper()
        signal_type = signal_dict.get('signal_type', '').upper()
        
        # CRITICAL - 系统级风险
        if action in ['EMERGENCY_STOP', 'FORCE_CLOSE', 'MARGIN_CALL'] or \
           signal_type in ['EMERGENCY_CLOSE', 'STOP_LOSS_HIT', 'RISK_LIMIT_HIT']:
            return MessagePriority.CRITICAL
        
        # HIGH - 订单级风控
        elif action in ['CLOSE', 'CLOSE_ALL', 'CANCEL', 'MODIFY_SL', 'MODIFY_TP'] or \
             signal_type in ['POSITION_CLOSE', 'POSITION_MODIFY', 'ORDER_CANCEL']:
            return MessagePriority.HIGH
        
        # NORMAL - 策略信号执行
        elif action in ['BUY', 'SELL', 'BUY_LIMIT', 'SELL_LIMIT', 'OPEN'] or \
             signal_type == 'POSITION_OPEN':
            return MessagePriority.NORMAL
        
        # REALTIME_QUERY - 实时数据查询
        elif action in ['QUERY', 'GET_TICK', 'GET_ACCOUNT', 'GET_POSITIONS'] or \
             signal_dict.get('query_type') in ['tick_data', 'account_info', 'positions', 'orders']:
            return MessagePriority.REALTIME_QUERY
        
        # LOW - 后台任务
        else:
            return MessagePriority.LOW
    
    def _update_stats(self, processing_time_ms: float):
        """更新统计信息"""
        self.stats['messages_processed'] += 1
        self.stats['total_processing_time_ms'] += processing_time_ms
        
        # 计算移动平均
        total_processed = self.stats['messages_processed']
        if total_processed > 0:
            self.stats['avg_processing_time_ms'] = (
                self.stats['total_processing_time_ms'] / total_processed
            )
    
    async def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        stats = dict(self.stats)
        
        # 计算缓存命中率
        total_cache_requests = stats['cache_hits'] + stats['cache_misses']
        if total_cache_requests > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / total_cache_requests
        else:
            stats['cache_hit_rate'] = 0.0
        
        # 添加各组件统计
        if self.priority_queue:
            stats['priority_queue'] = self.priority_queue.get_stats()
        
        if self.connection_pool:
            stats['connection_pool'] = self.connection_pool.get_stats()
        
        if self.memory_pool:
            memory_stats = self.memory_pool.get_performance_stats()
            stats['memory_pool'] = memory_stats['global_stats']
        
        if self.trade_matcher:
            matching_stats = await self.trade_matcher.get_matching_stats()
            stats['trade_matching'] = matching_stats
        
        if self.trade_validator:
            validation_stats = await self.trade_validator.get_validation_stats()
            stats['trade_validation'] = validation_stats
        
        # 🚀 增强批处理统计
        stats['batch_processing'] = {
            'buffer_size': len(self._batch_buffer),
            'active_processors': len(self._batch_tasks),
            'current_batch_size': self._current_batch_size,
            'adaptive_sizing_enabled': self.config.adaptive_batch_sizing,
            'priority_batching_enabled': self.config.enable_priority_batching,
            'avg_processing_time_per_item': (
                sum(self._batch_performance_history[-10:]) / min(10, len(self._batch_performance_history))
                if self._batch_performance_history else 0
            )
        }
        
        stats['cache_size'] = len(self._cache)
        stats['running'] = self.running
        
        return stats


# 全局增强处理器实例
_enhanced_processor = None


async def get_enhanced_processor(config: ProcessorConfig = None) -> PerformanceProcessor:
    """获取全局增强处理器实例"""
    global _enhanced_processor
    
    if _enhanced_processor is None:
        _enhanced_processor = PerformanceProcessor(config)
        await _enhanced_processor.initialize()
    
    return _enhanced_processor


def get_enhanced_processor_sync() -> PerformanceProcessor:
    """获取全局性能处理器实例（同步版本，仅用于配置）"""
    global _enhanced_processor

    if _enhanced_processor is None:
        _enhanced_processor = PerformanceProcessor()

    return _enhanced_processor


def get_processor() -> PerformanceProcessor:
    """获取性能处理器实例（别名）"""
    return get_enhanced_processor_sync()


