#!/usr/bin/env python3
"""
统一依赖注入容器 - 架构唯一权威实现
消除复杂的初始化依赖链，实现异步初始化和优雅降级
硬迁移：零向后兼容，强制统一，SSOT
"""
import asyncio
import inspect
import logging
import time
from typing import Dict, Any, Optional, Callable, Set, List, Type, TypeVar, Union
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor
import threading
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

T = TypeVar('T')

class ServiceState(Enum):
    """服务状态"""
    UNINITIALIZED = "uninitialized"
    INITIALIZING = "initializing"
    INITIALIZED = "initialized"
    FAILED = "failed"
    STOPPED = "stopped"

class ServiceLifecycle(Enum):
    """服务生命周期"""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"

@dataclass
class ServiceDescriptor:
    """服务描述符"""
    service_type: Type
    implementation: Optional[Type] = None
    factory: Optional[Callable] = None
    instance: Optional[Any] = None
    lifecycle: ServiceLifecycle = ServiceLifecycle.SINGLETON
    dependencies: Set[Type] = field(default_factory=set)
    initialization_order: int = 0
    health_check: Optional[Callable] = None
    fallback_factory: Optional[Callable] = None
    required: bool = True
    timeout: float = 30.0
    state: ServiceState = field(default=ServiceState.UNINITIALIZED)
    initialization_time: float = 0.0
    error: Optional[Exception] = None

@dataclass
class InitializationResult:
    """初始化结果"""
    success: bool
    service_type: Type
    error: Optional[Exception] = None
    initialization_time: float = 0.0
    fallback_used: bool = False

class ServiceContainer:
    """
    服务容器 - 架构唯一权威实现
    消除复杂初始化依赖链的唯一解决方案
    """
    
    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._instances: Dict[Type, Any] = {}
        self._lock = asyncio.Lock()
        self._initialization_lock = asyncio.Lock()
        self._initialization_order: List[Type] = []
        
        # 健康监控
        self._health_checks: Dict[Type, Callable] = {}
        self._service_metrics: Dict[Type, Dict[str, Any]] = {}
        
        # 初始化状态
        self._initialization_started = False
        self._initialization_completed = False
        self._failed_services: Set[Type] = set()
        self._fallback_services: Set[Type] = set()
        
        # 异步初始化支持
        self._initialization_tasks: Dict[Type, asyncio.Task] = {}
        self._partial_failure_allowed = True
        
        # 线程池用于阻塞性初始化
        self._thread_pool = ThreadPoolExecutor(max_workers=4, thread_name_prefix="di-worker")
        
        logger.info("📦 服务容器已创建")
    
    def register_singleton(self, service_type: Type[T], 
                          implementation: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None,
                          dependencies: Set[Type] = None,
                          health_check: Optional[Callable] = None,
                          fallback_factory: Optional[Callable] = None,
                          required: bool = True,
                          timeout: float = 30.0,
                          initialization_order: int = 0) -> 'UnifiedDependencyContainer':
        """注册单例服务"""
        return self._register_service(
            service_type=service_type,
            implementation=implementation,
            factory=factory,
            lifecycle=ServiceLifecycle.SINGLETON,
            dependencies=dependencies or set(),
            health_check=health_check,
            fallback_factory=fallback_factory,
            required=required,
            timeout=timeout,
            initialization_order=initialization_order
        )
    
    def register_transient(self, service_type: Type[T],
                          implementation: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None) -> 'UnifiedDependencyContainer':
        """注册瞬态服务"""
        return self._register_service(
            service_type=service_type,
            implementation=implementation,
            factory=factory,
            lifecycle=ServiceLifecycle.TRANSIENT
        )
    
    def register_instance(self, service_type: Type[T], instance: T) -> 'UnifiedDependencyContainer':
        """注册实例"""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            instance=instance,
            lifecycle=ServiceLifecycle.SINGLETON,
            state=ServiceState.INITIALIZED
        )
        
        self._services[service_type] = descriptor
        self._instances[service_type] = instance
        
        logger.info(f"📝 注册实例: {service_type.__name__}")
        return self
    
    def _register_service(self, service_type: Type, **kwargs) -> 'UnifiedDependencyContainer':
        """内部注册服务方法"""
        descriptor = ServiceDescriptor(service_type=service_type, **kwargs)
        self._services[service_type] = descriptor
        
        # 自动分析依赖关系
        if not descriptor.dependencies and descriptor.implementation:
            descriptor.dependencies = self._analyze_dependencies(descriptor.implementation)
        
        logger.info(f"📝 注册服务: {service_type.__name__} ({descriptor.lifecycle.value})")
        return self
    
    def _analyze_dependencies(self, implementation: Type) -> Set[Type]:
        """自动分析依赖关系"""
        dependencies = set()
        
        try:
            # 分析构造函数参数
            if hasattr(implementation, '__init__'):
                sig = inspect.signature(implementation.__init__)
                for param_name, param in sig.parameters.items():
                    if param_name == 'self':
                        continue
                    
                    if param.annotation != inspect.Parameter.empty:
                        # 只包含已注册的服务类型
                        if param.annotation in self._services:
                            dependencies.add(param.annotation)
        
        except Exception as e:
            logger.warning(f"依赖分析失败 {implementation.__name__}: {e}")
        
        return dependencies
    
    async def initialize_all(self, allow_partial_failure: bool = True) -> Dict[Type, InitializationResult]:
        """
        异步初始化所有服务 - 支持部分失败
        """
        async with self._initialization_lock:
            if self._initialization_started:
                logger.warning("初始化已经开始，跳过重复初始化")
                return {}
            
            self._initialization_started = True
            self._partial_failure_allowed = allow_partial_failure
            
            logger.info("🚀 开始异步初始化所有服务")
            start_time = time.time()
            
            # 计算初始化顺序
            initialization_order = self._calculate_initialization_order()
            results = {}
            
            # 分批初始化（按依赖层级）
            dependency_layers = self._group_by_dependency_layers(initialization_order)
            
            for layer_index, layer_services in enumerate(dependency_layers):
                logger.info(f"📦 初始化第{layer_index + 1}层服务: {[s.__name__ for s in layer_services]}")
                
                # 并行初始化同层服务
                layer_tasks = []
                for service_type in layer_services:
                    task = asyncio.create_task(
                        self._initialize_service_with_timeout(service_type)
                    )
                    layer_tasks.append((service_type, task))
                
                # 等待同层所有服务完成
                for service_type, task in layer_tasks:
                    try:
                        result = await task
                        results[service_type] = result
                        
                        if not result.success and not allow_partial_failure:
                            # 不允许部分失败，停止初始化
                            logger.error(f"❌ 服务初始化失败，停止初始化: {service_type.__name__}")
                            return results
                            
                    except Exception as e:
                        error_result = InitializationResult(
                            success=False,
                            service_type=service_type,
                            error=e
                        )
                        results[service_type] = error_result
                        
                        if not allow_partial_failure:
                            return results
            
            # 统计结果
            successful_count = sum(1 for r in results.values() if r.success)
            failed_count = len(results) - successful_count
            fallback_count = sum(1 for r in results.values() if r.fallback_used)
            
            total_time = time.time() - start_time
            
            logger.info(f"✅ 服务初始化完成: 成功 {successful_count}, 失败 {failed_count}, 回退 {fallback_count}, 用时 {total_time:.2f}s")
            
            self._initialization_completed = True
            return results
    
    def _calculate_initialization_order(self) -> List[Type]:
        """计算初始化顺序（拓扑排序）"""
        # 使用拓扑排序确定初始化顺序
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(service_type: Type):
            if service_type in temp_visited:
                raise ValueError(f"检测到循环依赖: {service_type.__name__}")
            
            if service_type in visited:
                return
            
            temp_visited.add(service_type)
            
            descriptor = self._services.get(service_type)
            if descriptor:
                for dependency in descriptor.dependencies:
                    if dependency in self._services:
                        visit(dependency)
            
            temp_visited.remove(service_type)
            visited.add(service_type)
            result.append(service_type)
        
        # 按初始化顺序权重排序
        service_types = sorted(
            self._services.keys(),
            key=lambda t: self._services[t].initialization_order
        )
        
        for service_type in service_types:
            visit(service_type)
        
        return result
    
    def _group_by_dependency_layers(self, ordered_services: List[Type]) -> List[List[Type]]:
        """按依赖层级分组"""
        layers = []
        service_to_layer = {}
        
        for service_type in ordered_services:
            descriptor = self._services[service_type]
            
            # 计算依赖层级
            max_dep_layer = -1
            for dependency in descriptor.dependencies:
                if dependency in service_to_layer:
                    max_dep_layer = max(max_dep_layer, service_to_layer[dependency])
            
            layer_index = max_dep_layer + 1
            service_to_layer[service_type] = layer_index
            
            # 确保有足够的层
            while len(layers) <= layer_index:
                layers.append([])
            
            layers[layer_index].append(service_type)
        
        return layers
    
    async def _initialize_service_with_timeout(self, service_type: Type) -> InitializationResult:
        """带超时的服务初始化"""
        descriptor = self._services[service_type]
        start_time = time.time()
        
        try:
            # 设置超时
            result = await asyncio.wait_for(
                self._initialize_service(service_type),
                timeout=descriptor.timeout
            )
            
            result.initialization_time = time.time() - start_time
            return result
            
        except asyncio.TimeoutError:
            error = TimeoutError(f"服务初始化超时: {service_type.__name__}")
            descriptor.state = ServiceState.FAILED
            descriptor.error = error
            
            logger.error(f"⏰ 服务初始化超时: {service_type.__name__} ({descriptor.timeout}s)")
            
            # 尝试使用回退工厂
            if descriptor.fallback_factory:
                try:
                    fallback_result = await self._try_fallback_initialization(service_type)
                    if fallback_result.success:
                        fallback_result.initialization_time = time.time() - start_time
                        return fallback_result
                except Exception as fallback_error:
                    logger.error(f"回退初始化失败: {service_type.__name__} - {fallback_error}")
            
            return InitializationResult(
                success=False,
                service_type=service_type,
                error=error,
                initialization_time=time.time() - start_time
            )
        
        except Exception as e:
            descriptor.state = ServiceState.FAILED
            descriptor.error = e
            
            return InitializationResult(
                success=False,
                service_type=service_type,
                error=e,
                initialization_time=time.time() - start_time
            )
    
    async def _initialize_service(self, service_type: Type) -> InitializationResult:
        """初始化单个服务"""
        descriptor = self._services[service_type]
        
        if descriptor.state == ServiceState.INITIALIZED:
            return InitializationResult(success=True, service_type=service_type)
        
        if descriptor.state == ServiceState.INITIALIZING:
            # 等待其他初始化完成
            while descriptor.state == ServiceState.INITIALIZING:
                await asyncio.sleep(0.01)
            
            return InitializationResult(
                success=descriptor.state == ServiceState.INITIALIZED,
                service_type=service_type,
                error=descriptor.error
            )
        
        descriptor.state = ServiceState.INITIALIZING
        
        try:
            # 首先初始化所有依赖
            for dependency_type in descriptor.dependencies:
                if dependency_type not in self._instances:
                    dep_result = await self._initialize_service(dependency_type)
                    if not dep_result.success and self._services[dependency_type].required:
                        raise RuntimeError(f"必需依赖初始化失败: {dependency_type.__name__}")
            
            # 创建服务实例
            instance = await self._create_service_instance(descriptor)
            
            if instance is None:
                raise RuntimeError(f"服务实例创建失败: {service_type.__name__}")
            
            # 存储实例
            self._instances[service_type] = instance
            descriptor.instance = instance
            descriptor.state = ServiceState.INITIALIZED
            
            logger.info(f"✅ 服务初始化成功: {service_type.__name__}")
            
            return InitializationResult(success=True, service_type=service_type)
            
        except Exception as e:
            descriptor.state = ServiceState.FAILED
            descriptor.error = e
            
            logger.error(f"❌ 服务初始化失败: {service_type.__name__} - {e}")
            
            # 尝试回退初始化
            if descriptor.fallback_factory:
                try:
                    return await self._try_fallback_initialization(service_type)
                except Exception as fallback_error:
                    logger.error(f"回退初始化失败: {service_type.__name__} - {fallback_error}")
            
            return InitializationResult(
                success=False,
                service_type=service_type,
                error=e
            )
    
    async def _try_fallback_initialization(self, service_type: Type) -> InitializationResult:
        """尝试回退初始化"""
        descriptor = self._services[service_type]
        
        if not descriptor.fallback_factory:
            raise RuntimeError("没有回退工厂")
        
        logger.info(f"🔄 尝试回退初始化: {service_type.__name__}")
        
        try:
            # 使用回退工厂创建实例
            if asyncio.iscoroutinefunction(descriptor.fallback_factory):
                instance = await descriptor.fallback_factory()
            else:
                instance = await asyncio.get_event_loop().run_in_executor(
                    self._thread_pool,
                    descriptor.fallback_factory
                )
            
            if instance is None:
                raise RuntimeError("回退工厂返回空实例")
            
            # 存储实例
            self._instances[service_type] = instance
            descriptor.instance = instance
            descriptor.state = ServiceState.INITIALIZED
            self._fallback_services.add(service_type)
            
            logger.info(f"✅ 回退初始化成功: {service_type.__name__}")
            
            return InitializationResult(
                success=True,
                service_type=service_type,
                fallback_used=True
            )
            
        except Exception as e:
            descriptor.state = ServiceState.FAILED
            descriptor.error = e
            raise
    
    async def _create_service_instance(self, descriptor: ServiceDescriptor):
        """创建服务实例"""
        if descriptor.instance:
            return descriptor.instance
        
        if descriptor.factory:
            # 使用工厂函数
            if asyncio.iscoroutinefunction(descriptor.factory):
                return await descriptor.factory()
            else:
                return await asyncio.get_event_loop().run_in_executor(
                    self._thread_pool,
                    descriptor.factory
                )
        
        if descriptor.implementation:
            # 使用实现类
            # 解析构造函数依赖
            dependencies = {}
            
            if hasattr(descriptor.implementation, '__init__'):
                sig = inspect.signature(descriptor.implementation.__init__)
                for param_name, param in sig.parameters.items():
                    if param_name == 'self':
                        continue
                    
                    if param.annotation != inspect.Parameter.empty:
                        if param.annotation in self._instances:
                            dependencies[param_name] = self._instances[param.annotation]
            
            # 创建实例
            if asyncio.iscoroutinefunction(descriptor.implementation.__init__):
                instance = descriptor.implementation()
                if asyncio.iscoroutine(instance):
                    return await instance
                return instance
            else:
                return await asyncio.get_event_loop().run_in_executor(
                    self._thread_pool,
                    lambda: descriptor.implementation(**dependencies)
                )
        
        raise RuntimeError(f"无法创建服务实例: {descriptor.service_type.__name__}")
    
    async def get(self, service_type: Type[T]) -> Optional[T]:
        """获取服务实例"""
        if service_type not in self._services:
            logger.warning(f"未注册的服务类型: {service_type.__name__}")
            return None
        
        descriptor = self._services[service_type]
        
        # 瞬态服务每次创建新实例
        if descriptor.lifecycle == ServiceLifecycle.TRANSIENT:
            return await self._create_service_instance(descriptor)
        
        # 单例服务返回缓存实例
        if service_type in self._instances:
            return self._instances[service_type]
        
        # 如果未初始化，尝试初始化
        if descriptor.state == ServiceState.UNINITIALIZED:
            result = await self._initialize_service(service_type)
            if result.success:
                return self._instances[service_type]
        
        return None
    
    def get_required(self, service_type: Type[T]) -> T:
        """获取必需的服务实例（同步版本）"""
        if service_type not in self._instances:
            raise RuntimeError(f"必需服务未初始化: {service_type.__name__}")
        
        return self._instances[service_type]
    
    async def health_check_all(self) -> Dict[Type, bool]:
        """健康检查所有服务"""
        results = {}
        
        for service_type, descriptor in self._services.items():
            if descriptor.health_check and service_type in self._instances:
                try:
                    instance = self._instances[service_type]
                    if asyncio.iscoroutinefunction(descriptor.health_check):
                        is_healthy = await descriptor.health_check(instance)
                    else:
                        is_healthy = descriptor.health_check(instance)
                    
                    results[service_type] = bool(is_healthy)
                    
                except Exception as e:
                    logger.error(f"健康检查失败: {service_type.__name__} - {e}")
                    results[service_type] = False
            else:
                # 没有健康检查函数，假设健康
                results[service_type] = service_type in self._instances
        
        return results
    
    async def shutdown_all(self):
        """关闭所有服务"""
        logger.info("🛑 开始关闭所有服务")
        
        # 按依赖关系反序关闭
        shutdown_order = list(reversed(self._initialization_order))
        
        for service_type in shutdown_order:
            try:
                instance = self._instances.get(service_type)
                if instance:
                    # 尝试调用shutdown方法
                    if hasattr(instance, 'shutdown'):
                        if asyncio.iscoroutinefunction(instance.shutdown):
                            await instance.shutdown()
                        else:
                            await asyncio.get_event_loop().run_in_executor(
                                self._thread_pool,
                                instance.shutdown
                            )
                    elif hasattr(instance, 'stop'):
                        if asyncio.iscoroutinefunction(instance.stop):
                            await instance.stop()
                        else:
                            await asyncio.get_event_loop().run_in_executor(
                                self._thread_pool,
                                instance.stop
                            )
                    
                    logger.debug(f"🛑 服务已关闭: {service_type.__name__}")
                    
            except Exception as e:
                logger.error(f"服务关闭失败: {service_type.__name__} - {e}")
        
        # 清理资源
        self._instances.clear()
        for descriptor in self._services.values():
            descriptor.state = ServiceState.STOPPED
        
        # 关闭线程池
        self._thread_pool.shutdown(wait=True)
        
        logger.info("✅ 所有服务已关闭")
    
    def get_initialization_report(self) -> Dict[str, Any]:
        """获取初始化报告"""
        total_services = len(self._services)
        initialized_services = sum(1 for d in self._services.values() 
                                 if d.state == ServiceState.INITIALIZED)
        failed_services = sum(1 for d in self._services.values() 
                            if d.state == ServiceState.FAILED)
        
        return {
            'total_services': total_services,
            'initialized_services': initialized_services,
            'failed_services': failed_services,
            'fallback_services': len(self._fallback_services),
            'success_rate': initialized_services / total_services * 100 if total_services > 0 else 0,
            'initialization_completed': self._initialization_completed,
            'service_details': {
                service_type.__name__: {
                    'state': descriptor.state.value,
                    'initialization_time': descriptor.initialization_time,
                    'error': str(descriptor.error) if descriptor.error else None,
                    'fallback_used': service_type in self._fallback_services
                }
                for service_type, descriptor in self._services.items()
            }
        }


# 全局容器实例
_global_container: Optional[ServiceContainer] = None


