#!/usr/bin/env python3
"""
MT5终端连接和跟单功能完整测试
验证真实账户连接、交易权限和跟单策略
"""

import asyncio
import sys
import os
import time
from pathlib import Path
from typing import Dict, Any, List

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging
from src.core.mt5_client import MT5Client
from src.core.signal_types import UnifiedTradeSignal, OrderType, SignalAction
from src.messaging.jetstream_client import JetStreamClient
from src.core.copy_utils import UnifiedCopyProcessor
from src.utils.config_manager import ConfigManager

logger = get_logger(__name__)


class MT5CopyTradingTester:
    """MT5跟单功能测试器"""
    
    def __init__(self):
        self.config_manager = None
        self.mt5_clients = {}
        self.jetstream_client = None
        self.strategy_processor = None
        self.test_results = {}
        
    async def setup(self):
        """初始化测试环境"""
        logger.info("🔧 初始化测试环境...")
        
        # 设置环境变量（如果未设置）
        if not os.getenv('MT5_ACC001_PASSWORD'):
            os.environ['MT5_ACC001_PASSWORD'] = 'demo_password_001'
            logger.warning("使用默认密码 MT5_ACC001_PASSWORD")
            
        if not os.getenv('MT5_ACC002_PASSWORD'):
            os.environ['MT5_ACC002_PASSWORD'] = 'demo_password_002'
            logger.warning("使用默认密码 MT5_ACC002_PASSWORD")
        
        # 初始化配置管理器
        self.config_manager = ConfigManager('config/optimized_system.yaml')
        
        # 初始化 JetStream 客户端
        self.jetstream_client = JetStreamClient(['nats://localhost:4222'])
        await self.jetstream_client.connect()
        
        # 初始化统一处理器
        self.copy_processor = UnifiedCopyProcessor()
        
        logger.info("✅ 测试环境初始化完成")
    
    async def test_mt5_connections(self) -> bool:
        """测试 MT5 终端连接"""
        logger.info("🧪 测试 MT5 终端连接...")
        
        accounts = ['ACC001', 'ACC002']
        connection_results = {}
        
        for account_id in accounts:
            try:
                logger.info(f"测试账户 {account_id} 连接...")
                
                # 加载账户配置
                account_config_path = f"config/accounts/{account_id}.yaml"
                if not Path(account_config_path).exists():
                    logger.error(f"账户配置文件不存在: {account_config_path}")
                    connection_results[account_id] = False
                    continue
                
                # 加载账户配置
                import yaml
                with open(account_config_path, 'r', encoding='utf-8') as f:
                    account_config = yaml.safe_load(f)

                # 获取账户信息
                mt5_config = account_config.get('mt5', {}).get('connection', {})
                login = mt5_config.get('login')
                server = mt5_config.get('server')
                password = os.getenv(f'MT5_{account_id}_PASSWORD', mt5_config.get('password', ''))

                if not all([login, server, password]):
                    logger.error(f"账户 {account_id} 配置不完整: login={login}, server={server}, password={'***' if password else 'None'}")
                    connection_results[account_id] = False
                    continue

                # 创建 MT5 客户端
                mt5_client = MT5Client(
                    account_id=account_id,
                    login=int(login),
                    password=password,
                    server=server
                )
                
                # 尝试连接
                success = await mt5_client.connect()
                
                if success:
                    logger.info(f"✅ {account_id} 连接成功")
                    
                    # 获取账户信息
                    account_info = await mt5_client.get_account_info()
                    if account_info:
                        logger.info(f"账户信息: 余额={account_info.get('balance', 'N/A')}, "
                                  f"权益={account_info.get('equity', 'N/A')}")
                    
                    # 获取市场数据
                    symbol_info = await mt5_client.get_symbol_info('EURUSD')
                    if symbol_info:
                        logger.info(f"EURUSD 信息: 买价={symbol_info.get('bid', 'N/A')}, "
                                  f"卖价={symbol_info.get('ask', 'N/A')}")
                    
                    # 检查交易权限
                    trading_allowed = await mt5_client.is_trading_allowed()
                    logger.info(f"交易权限: {'✅ 允许' if trading_allowed else '❌ 禁止'}")
                    
                    self.mt5_clients[account_id] = mt5_client
                    connection_results[account_id] = True
                    
                else:
                    logger.error(f"❌ {account_id} 连接失败")
                    connection_results[account_id] = False
                    
            except Exception as e:
                logger.error(f"❌ {account_id} 连接异常: {e}")
                connection_results[account_id] = False
        
        # 总结连接结果
        successful_connections = sum(1 for result in connection_results.values() if result)
        total_accounts = len(accounts)
        
        logger.info(f"连接测试完成: {successful_connections}/{total_accounts} 个账户连接成功")
        
        self.test_results['mt5_connections'] = connection_results
        return successful_connections > 0
    
    async def test_signal_generation_and_routing(self) -> bool:
        """测试信号生成和路由"""
        logger.info("🧪 测试信号生成和路由...")
        
        try:
            # 创建测试信号
            test_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=0.1,
                price=1.1000,
                position_id=12345,
                sl=1.0950,
                tp=1.1050,
                comment="Copy trading test signal"
            )
            
            logger.info(f"创建测试信号: {test_signal.symbol} {test_signal.order_type.value} {test_signal.volume}")
            
            # 发布信号到 JetStream
            success = await self.jetstream_client.publish(
                subject=f"MT5.TRADES.ACC001",
                data=test_signal.to_dict()
            )
            
            if success:
                logger.info("✅ 信号发布成功")
                
                # 获取流信息验证
                stream_info = await self.jetstream_client.get_stream_info()
                if stream_info:
                    logger.info(f"流状态: 消息数={stream_info['messages']}")
                
                self.test_results['signal_routing'] = True
                return True
            else:
                logger.error("❌ 信号发布失败")
                self.test_results['signal_routing'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ 信号路由测试异常: {e}")
            self.test_results['signal_routing'] = False
            return False
    
    async def test_copy_strategy_processing(self) -> bool:
        """测试跟单策略处理"""
        logger.info("🧪 测试跟单策略处理...")
        
        try:
            # 创建原始信号
            original_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=1.0,
                price=1.1000,
                position_id=12345,
                sl=1.0950,
                tp=1.1050
            )
            
            # 测试正向跟单策略
            logger.info("测试正向跟单策略...")
            forward_signal = self.strategy_processor.apply_copy_strategy(
                signal=original_signal,
                master_account="ACC001",
                slave_account="ACC002"
            )
            
            if forward_signal:
                logger.info(f"✅ 正向跟单信号: {forward_signal.symbol} {forward_signal.order_type.value} "
                          f"{forward_signal.volume} (原始: {original_signal.volume})")
                
                # 验证策略应用
                expected_volume = original_signal.volume * 0.5  # 根据配置文件中的 lot_multiplier
                if abs(forward_signal.volume - expected_volume) < 0.001:
                    logger.info("✅ 手数调整正确")
                else:
                    logger.warning(f"⚠️ 手数调整可能不正确: 期望={expected_volume}, 实际={forward_signal.volume}")
                
                self.test_results['copy_strategy'] = True
                return True
            else:
                logger.error("❌ 跟单策略处理失败")
                self.test_results['copy_strategy'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ 跟单策略测试异常: {e}")
            self.test_results['copy_strategy'] = False
            return False
    
    async def test_end_to_end_copy_trading(self) -> bool:
        """测试端到端跟单功能"""
        logger.info("🧪 测试端到端跟单功能...")
        
        try:
            if 'ACC001' not in self.mt5_clients or 'ACC002' not in self.mt5_clients:
                logger.error("❌ 缺少必要的 MT5 连接，跳过端到端测试")
                return False
            
            master_client = self.mt5_clients['ACC001']
            slave_client = self.mt5_clients['ACC002']
            
            # 获取当前市场价格
            symbol_info = await master_client.get_symbol_info('EURUSD')
            if not symbol_info:
                logger.error("❌ 无法获取 EURUSD 市场信息")
                return False
            
            current_price = symbol_info.get('ask', 1.1000)
            logger.info(f"当前 EURUSD 价格: {current_price}")
            
            # 创建真实交易信号（但不实际执行）
            trade_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=0.01,  # 最小手数
                price=current_price,
                position_id=99999,
                sl=current_price - 0.0050,  # 50点止损
                tp=current_price + 0.0050,  # 50点止盈
                comment="End-to-end test signal"
            )
            
            logger.info(f"创建端到端测试信号: {trade_signal.symbol} {trade_signal.order_type.value} "
                      f"{trade_signal.volume} @ {trade_signal.price}")
            
            # 应用跟单策略
            copy_signal = self.strategy_processor.apply_copy_strategy(
                signal=trade_signal,
                master_account="ACC001",
                slave_account="ACC002"
            )
            
            if copy_signal:
                logger.info(f"✅ 跟单信号生成成功: {copy_signal.symbol} {copy_signal.order_type.value} "
                          f"{copy_signal.volume} @ {copy_signal.price}")
                
                # 验证信号完整性
                if (copy_signal.symbol == trade_signal.symbol and
                    copy_signal.order_type == trade_signal.order_type and
                    copy_signal.price == trade_signal.price):
                    logger.info("✅ 信号完整性验证通过")
                    
                    # 发布到消息队列
                    success = await self.jetstream_client.publish(
                        subject=f"MT5.COPY.ACC002",
                        data=copy_signal.to_dict()
                    )
                    
                    if success:
                        logger.info("✅ 跟单信号发布成功")
                        self.test_results['end_to_end'] = True
                        return True
                    else:
                        logger.error("❌ 跟单信号发布失败")
                else:
                    logger.error("❌ 信号完整性验证失败")
            else:
                logger.error("❌ 跟单信号生成失败")
            
            self.test_results['end_to_end'] = False
            return False
            
        except Exception as e:
            logger.error(f"❌ 端到端测试异常: {e}")
            self.test_results['end_to_end'] = False
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 清理测试环境...")
        
        # 断开 MT5 连接
        for account_id, client in self.mt5_clients.items():
            try:
                await client.disconnect()
                logger.info(f"✅ {account_id} 连接已断开")
            except Exception as e:
                logger.warning(f"⚠️ {account_id} 断开连接时出错: {e}")
        
        # 断开 JetStream 连接
        if self.jetstream_client:
            await self.jetstream_client.disconnect()
            logger.info("✅ JetStream 连接已断开")
    
    def print_test_summary(self):
        """打印测试总结"""
        logger.info("=" * 60)
        logger.info("📊 MT5跟单功能测试总结")
        logger.info("=" * 60)
        
        test_categories = {
            'mt5_connections': 'MT5终端连接',
            'signal_routing': '信号生成和路由',
            'copy_strategy': '跟单策略处理',
            'end_to_end': '端到端跟单功能'
        }
        
        passed_tests = 0
        total_tests = len(test_categories)
        
        for test_key, test_name in test_categories.items():
            if test_key in self.test_results:
                result = self.test_results[test_key]
                if isinstance(result, dict):
                    # MT5连接测试结果是字典
                    success_count = sum(1 for v in result.values() if v)
                    total_count = len(result)
                    status = "✅ 通过" if success_count > 0 else "❌ 失败"
                    logger.info(f"  {test_name}: {status} ({success_count}/{total_count})")
                    if success_count > 0:
                        passed_tests += 1
                else:
                    # 其他测试结果是布尔值
                    status = "✅ 通过" if result else "❌ 失败"
                    logger.info(f"  {test_name}: {status}")
                    if result:
                        passed_tests += 1
            else:
                logger.info(f"  {test_name}: ⏭️ 跳过")
        
        logger.info(f"\n总计: {passed_tests}/{total_tests} 个测试通过")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！MT5跟单系统功能正常")
            return True
        else:
            logger.error("💥 部分测试失败，需要进一步调试")
            return False


async def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard',
        'file': 'logs/mt5_copy_trading_test.log'
    })
    
    logger.info("=" * 60)
    logger.info("🚀 MT5跟单功能完整测试")
    logger.info("=" * 60)
    
    tester = MT5CopyTradingTester()
    
    try:
        # 初始化测试环境
        await tester.setup()
        
        # 运行测试序列
        tests = [
            ("MT5终端连接测试", tester.test_mt5_connections),
            ("信号生成和路由测试", tester.test_signal_generation_and_routing),
            ("跟单策略处理测试", tester.test_copy_strategy_processing),
            ("端到端跟单功能测试", tester.test_end_to_end_copy_trading)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"运行测试: {test_name}")
            try:
                success = await test_func()
                if success:
                    logger.info(f"✅ {test_name}: 通过")
                else:
                    logger.error(f"❌ {test_name}: 失败")
            except Exception as e:
                logger.error(f"💥 {test_name}: 异常 - {e}")
            
            logger.info("-" * 40)
            await asyncio.sleep(2)  # 短暂暂停
        
        # 打印测试总结
        success = tester.print_test_summary()
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"测试运行异常: {e}")
        return 1
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
