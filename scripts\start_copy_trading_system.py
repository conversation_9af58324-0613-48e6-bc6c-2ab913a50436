#!/usr/bin/env python3
"""
启动跟单系统 - 用于实际交易测试
"""

import asyncio
import sys
import signal
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.trading_system import TradingSystem
from src.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)

class CopyTradingSystemRunner:
    """跟单系统运行器"""
    
    def __init__(self):
        self.system = None
        self.running = False
    
    async def start_system(self):
        """启动系统"""
        logger.info("🚀 启动MT5跟单系统...")
        
        try:
            # 创建交易系统
            self.system = TradingSystem(
                config_path='config/optimized_system.yaml',
                host_id='UK-001',
                dry_run=False  # 实际交易模式
            )
            
            # 启动系统
            success = await self.system.start()
            
            if success:
                logger.info("✅ 系统启动成功！")
                logger.info("📊 系统状态:")
                
                # 显示系统状态
                status = self.system.get_system_status()
                for key, value in status.items():
                    logger.info(f"  {key}: {value}")
                
                self.running = True
                return True
            else:
                logger.error("❌ 系统启动失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 系统启动异常: {e}")
            return False
    
    async def monitor_system(self):
        """监控系统运行"""
        logger.info("🔍 开始监控系统运行状态...")
        logger.info("💡 提示:")
        logger.info("  - 现在可以在ACC001账户进行交易操作")
        logger.info("  - 系统将自动监控并在ACC002执行跟单")
        logger.info("  - 按 Ctrl+C 停止系统")
        
        try:
            while self.running:
                await asyncio.sleep(5)  # 每5秒检查一次
                
                if self.system and self.system.running:
                    # 获取系统状态
                    status = self.system.get_system_status()
                    
                    # 记录关键指标
                    if 'processed_signals' in status:
                        logger.info(f"📈 已处理信号数: {status['processed_signals']}")
                    
                    if 'active_connections' in status:
                        logger.info(f"🔗 活跃连接数: {status['active_connections']}")
                else:
                    logger.warning("⚠️ 系统状态异常")
                    break
                    
        except asyncio.CancelledError:
            logger.info("🛑 监控被取消")
        except Exception as e:
            logger.error(f"❌ 监控异常: {e}")
    
    async def stop_system(self):
        """停止系统"""
        logger.info("🛑 停止系统...")
        
        self.running = False
        
        if self.system:
            try:
                await self.system.stop()
                logger.info("✅ 系统已停止")
            except Exception as e:
                logger.error(f"❌ 停止系统时出错: {e}")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，准备停止系统...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard',
        'file': 'logs/copy_trading_system.log'
    })
    
    logger.info("=" * 60)
    logger.info("🚀 MT5跟单系统启动")
    logger.info("=" * 60)
    
    runner = CopyTradingSystemRunner()
    runner.setup_signal_handlers()
    
    try:
        # 启动系统
        success = await runner.start_system()
        
        if not success:
            logger.error("❌ 系统启动失败，退出")
            return 1
        
        # 监控系统
        await runner.monitor_system()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号")
    except Exception as e:
        logger.error(f"❌ 运行异常: {e}")
        return 1
    finally:
        await runner.stop_system()


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 系统已停止")
        sys.exit(0)
