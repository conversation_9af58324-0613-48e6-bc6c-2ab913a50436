#!/usr/bin/env python3
"""
生成架构图的Python脚本
使用matplotlib和networkx生成分布式MT5系统架构图
"""
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

def create_architecture_diagram():
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    
    # 设置背景
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # 颜色定义
    host_color = '#E8F4FD'
    coordinator_color = '#B8E0FF'
    process_color = '#FFE4B5'
    component_color = '#98FB98'
    memory_color = '#FFB6C1'
    network_color = '#DDA0DD'
    
    # 主机A
    host_a = FancyBboxPatch((0.5, 6), 7, 5.5, 
                           boxstyle="round,pad=0.1",
                           facecolor=host_color,
                           edgecolor='black',
                           linewidth=2)
    ax.add_patch(host_a)
    ax.text(4, 11.3, '主机A (Host A)', fontsize=14, weight='bold', ha='center')
    
    # 主协调器
    coordinator = FancyBboxPatch((1, 9.5), 6, 1.5,
                               boxstyle="round,pad=0.05",
                               facecolor=coordinator_color,
                               edgecolor='darkblue',
                               linewidth=1.5)
    ax.add_patch(coordinator)
    ax.text(4, 10.2, 'UnifiedMT5Coordinator', fontsize=12, weight='bold', ha='center')
    
    # 协调器内的组件
    components = [
        ('DI Container', 1.5, 9.7),
        ('Service Discovery', 3.5, 9.7),
        ('Memory Pool', 5.5, 9.7),
    ]
    
    for comp_name, x, y in components:
        comp_box = FancyBboxPatch((x-0.7, y-0.15), 1.4, 0.3,
                                boxstyle="round,pad=0.02",
                                facecolor='white',
                                edgecolor='gray')
        ax.add_patch(comp_box)
        ax.text(x, y, comp_name, fontsize=8, ha='center', va='center')
    
    # MT5进程1
    process1 = FancyBboxPatch((1, 6.5), 3, 2.5,
                            boxstyle="round,pad=0.05",
                            facecolor=process_color,
                            edgecolor='darkorange',
                            linewidth=1.5)
    ax.add_patch(process1)
    ax.text(2.5, 8.7, 'MT5 Process 1', fontsize=11, weight='bold', ha='center')
    ax.text(2.5, 8.4, 'Account: ACC001', fontsize=9, ha='center')
    
    # MT5进程2
    process2 = FancyBboxPatch((4.5, 6.5), 3, 2.5,
                            boxstyle="round,pad=0.05",
                            facecolor=process_color,
                            edgecolor='darkorange',
                            linewidth=1.5)
    ax.add_patch(process2)
    ax.text(6, 8.7, 'MT5 Process 2', fontsize=11, weight='bold', ha='center')
    ax.text(6, 8.4, 'Account: ACC002', fontsize=9, ha='center')
    
    # 进程内组件
    process_components = [
        # Process 1
        ('MT5 Terminal', 2.5, 8),
        ('Monitor', 1.8, 7.3),
        ('Executor', 3.2, 7.3),
        # Process 2
        ('MT5 Terminal', 6, 8),
        ('Monitor', 5.3, 7.3),
        ('Executor', 6.7, 7.3),
    ]
    
    for comp_name, x, y in process_components:
        comp_box = FancyBboxPatch((x-0.4, y-0.15), 0.8, 0.3,
                                boxstyle="round,pad=0.02",
                                facecolor=component_color,
                                edgecolor='darkgreen')
        ax.add_patch(comp_box)
        ax.text(x, y, comp_name, fontsize=8, ha='center', va='center')
    
    # 共享内存区域
    shared_mem = FancyBboxPatch((1, 6.2), 6, 0.3,
                              boxstyle="round,pad=0.02",
                              facecolor=memory_color,
                              edgecolor='darkred',
                              linewidth=1.5)
    ax.add_patch(shared_mem)
    ax.text(4, 6.35, 'Shared Memory Region (mmap)', fontsize=10, ha='center', va='center')
    
    # 主机B
    host_b = FancyBboxPatch((8.5, 6), 7, 5.5,
                          boxstyle="round,pad=0.1",
                          facecolor=host_color,
                          edgecolor='black',
                          linewidth=2)
    ax.add_patch(host_b)
    ax.text(12, 11.3, '主机B (Host B)', fontsize=14, weight='bold', ha='center')
    
    # 网络连接
    network_box = FancyBboxPatch((3, 4.5), 10, 1,
                               boxstyle="round,pad=0.05",
                               facecolor=network_color,
                               edgecolor='purple',
                               linewidth=1.5)
    ax.add_patch(network_box)
    ax.text(8, 5, 'NATS / Redis Network', fontsize=12, weight='bold', ha='center')
    
    # 添加箭头和连接
    # 协调器到进程
    ax.annotate('', xy=(2.5, 9), xytext=(3, 9.5),
                arrowprops=dict(arrowstyle='->', lw=1.5, color='blue'))
    ax.annotate('', xy=(6, 9), xytext=(5, 9.5),
                arrowprops=dict(arrowstyle='->', lw=1.5, color='blue'))
    
    # 进程到共享内存
    ax.annotate('', xy=(2.5, 6.5), xytext=(2.5, 6.8),
                arrowprops=dict(arrowstyle='<->', lw=2, color='red'))
    ax.annotate('', xy=(6, 6.5), xytext=(6, 6.8),
                arrowprops=dict(arrowstyle='<->', lw=2, color='red'))
    
    # 主机间网络连接
    ax.annotate('', xy=(8, 5.5), xytext=(4, 6),
                arrowprops=dict(arrowstyle='<->', lw=2, color='purple'))
    ax.annotate('', xy=(8, 5.5), xytext=(12, 6),
                arrowprops=dict(arrowstyle='<->', lw=2, color='purple'))
    
    # 添加图例
    legend_elements = [
        mpatches.Patch(color=coordinator_color, label='协调器组件'),
        mpatches.Patch(color=process_color, label='MT5进程'),
        mpatches.Patch(color=component_color, label='进程内组件'),
        mpatches.Patch(color=memory_color, label='共享内存'),
        mpatches.Patch(color=network_color, label='网络通信'),
    ]
    ax.legend(handles=legend_elements, loc='lower center', ncol=5, frameon=False)
    
    # 添加标题
    plt.title('分布式多进程隔离MT5跟单交易系统架构', fontsize=16, weight='bold', pad=20)
    
    # 添加关键说明
    info_text = (
        "关键特性:\n"
        "• 每个MT5账户在独立进程中运行\n"
        "• 本地进程使用零拷贝共享内存通信\n"
        "• 跨主机使用NATS/Redis网络通信\n"
        "• 支持故障转移和优雅降级"
    )
    ax.text(0.5, 3.5, info_text, fontsize=10, va='top', 
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightyellow'))
    
    plt.tight_layout()
    return fig

def create_signal_flow_diagram():
    """创建信号流程图"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # 本地信号流
    ax1.set_xlim(0, 14)
    ax1.set_ylim(0, 6)
    ax1.axis('off')
    ax1.set_title('本地信号流（零拷贝）', fontsize=14, weight='bold')
    
    # 步骤
    steps = [
        (2, 4, 'Monitor\n检测信号'),
        (5, 4, '获取内存\n缓冲区'),
        (8, 4, '写入共享\n内存'),
        (11, 4, 'Executor\n读取执行'),
    ]
    
    for i, (x, y, text) in enumerate(steps):
        # 绘制步骤框
        step_box = FancyBboxPatch((x-0.8, y-0.5), 1.6, 1,
                                boxstyle="round,pad=0.1",
                                facecolor='lightblue',
                                edgecolor='darkblue')
        ax1.add_patch(step_box)
        ax1.text(x, y, text, ha='center', va='center', fontsize=10)
        
        # 添加箭头
        if i < len(steps) - 1:
            ax1.annotate('', xy=(steps[i+1][0]-1, y), xytext=(x+0.8, y),
                        arrowprops=dict(arrowstyle='->', lw=2, color='darkblue'))
    
    # 添加时间标注
    ax1.text(7, 2, '延迟: <100μs', fontsize=12, weight='bold', 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen'))
    
    # 跨主机信号流
    ax2.set_xlim(0, 14)
    ax2.set_ylim(0, 6)
    ax2.axis('off')
    ax2.set_title('跨主机信号流（网络传输）', fontsize=14, weight='bold')
    
    # 步骤
    cross_steps = [
        (1.5, 4, 'Monitor\n(Host A)'),
        (4, 4, '序列化\n信号'),
        (6.5, 4, 'NATS/Redis\n传输'),
        (9, 4, '反序列化\n信号'),
        (11.5, 4, 'Executor\n(Host B)'),
    ]
    
    for i, (x, y, text) in enumerate(cross_steps):
        # 绘制步骤框
        color = 'lightcoral' if 'Host' in text else 'lightyellow'
        step_box = FancyBboxPatch((x-0.8, y-0.5), 1.6, 1,
                                boxstyle="round,pad=0.1",
                                facecolor=color,
                                edgecolor='darkred')
        ax2.add_patch(step_box)
        ax2.text(x, y, text, ha='center', va='center', fontsize=10)
        
        # 添加箭头
        if i < len(cross_steps) - 1:
            ax2.annotate('', xy=(cross_steps[i+1][0]-0.9, y), xytext=(x+0.8, y),
                        arrowprops=dict(arrowstyle='->', lw=2, color='darkred'))
    
    # 添加时间标注
    ax2.text(7, 2, '延迟: 1-5ms', fontsize=12, weight='bold',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow'))
    
    plt.tight_layout()
    return fig

if __name__ == "__main__":
    # 生成架构图
    fig1 = create_architecture_diagram()
    fig1.savefig('/mnt/d/github/mt5-python/docs/architecture_diagram.png', dpi=300, bbox_inches='tight')
    plt.close(fig1)
    
    # 生成信号流程图
    fig2 = create_signal_flow_diagram()
    fig2.savefig('/mnt/d/github/mt5-python/docs/signal_flow_diagram.png', dpi=300, bbox_inches='tight')
    plt.close(fig2)
    
    print("架构图已生成:")
    print("- architecture_diagram.png")
    print("- signal_flow_diagram.png")