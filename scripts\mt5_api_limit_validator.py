#!/usr/bin/env python3
"""
MT5 API并发限制验证工具
测试和验证MT5 API的并发连接限制
确保Semaphore控制的有效性
"""
import asyncio
import time
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path
import sys
import json
import statistics
from concurrent.futures import ThreadPoolExecutor
import threading

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ConnectionAttempt:
    """连接尝试记录"""
    start_time: float
    end_time: float = 0.0
    success: bool = False
    error: Optional[str] = None
    connection_id: str = ""
    semaphore_acquired: bool = False
    actual_concurrent: int = 0

@dataclass
class ConcurrencyTestResult:
    """并发测试结果"""
    test_name: str
    max_allowed_concurrent: int
    total_attempts: int
    successful_attempts: int
    failed_attempts: int
    avg_connection_time: float = 0.0
    max_observed_concurrent: int = 0
    semaphore_violations: int = 0
    api_limit_respected: bool = True
    error_patterns: Dict[str, int] = field(default_factory=dict)

class MockMT5Connection:
    """模拟MT5连接"""
    
    # 全局连接计数器和锁
    _active_connections = 0
    _connection_lock = threading.Lock()
    _max_connections = 80  # MT5 API理论限制
    _connection_delay = 0.1  # 模拟连接延迟
    
    def __init__(self, connection_id: str):
        self.connection_id = connection_id
        self.connected = False
        self.connect_time = 0.0
    
    async def connect(self) -> bool:
        """模拟连接过程"""
        with MockMT5Connection._connection_lock:
            if MockMT5Connection._active_connections >= MockMT5Connection._max_connections:
                raise Exception(f"MT5 API连接限制: 最大 {MockMT5Connection._max_connections} 个并发连接")
            
            MockMT5Connection._active_connections += 1
            current_connections = MockMT5Connection._active_connections
        
        try:
            # 模拟连接建立时间
            await asyncio.sleep(MockMT5Connection._connection_delay)
            
            self.connected = True
            self.connect_time = time.time()
            
            logger.debug(f"连接成功: {self.connection_id} (当前并发: {current_connections})")
            return True
            
        except Exception as e:
            with MockMT5Connection._connection_lock:
                MockMT5Connection._active_connections -= 1
            raise e
    
    async def disconnect(self):
        """断开连接"""
        if self.connected:
            with MockMT5Connection._connection_lock:
                MockMT5Connection._active_connections -= 1
                current_connections = MockMT5Connection._active_connections
            
            self.connected = False
            logger.debug(f"连接断开: {self.connection_id} (当前并发: {current_connections})")
    
    @classmethod
    def get_active_connections(cls) -> int:
        """获取当前活跃连接数"""
        with cls._connection_lock:
            return cls._active_connections
    
    @classmethod
    def reset_connections(cls):
        """重置连接计数"""
        with cls._connection_lock:
            cls._active_connections = 0

class MT5ApiLimitValidator:
    """MT5 API限制验证器"""
    
    def __init__(self):
        self.connection_attempts: List[ConnectionAttempt] = []
        self.active_connections: List[MockMT5Connection] = []
        self.max_concurrent_observed = 0
        
    async def test_semaphore_control(self, semaphore_limit: int, total_connections: int) -> ConcurrencyTestResult:
        """测试信号量控制的有效性"""
        logger.info(f"🧪 测试信号量控制: 限制 {semaphore_limit}, 总连接 {total_connections}")
        
        semaphore = asyncio.Semaphore(semaphore_limit)
        self.connection_attempts.clear()
        MockMT5Connection.reset_connections()
        
        async def attempt_connection(conn_id: str) -> ConnectionAttempt:
            """尝试连接"""
            attempt = ConnectionAttempt(
                start_time=time.time(),
                connection_id=conn_id
            )
            
            try:
                # 获取信号量
                await semaphore.acquire()
                attempt.semaphore_acquired = True
                
                # 记录当前并发数
                attempt.actual_concurrent = MockMT5Connection.get_active_connections()
                
                # 更新最大观察到的并发数
                self.max_concurrent_observed = max(self.max_concurrent_observed, attempt.actual_concurrent + 1)
                
                # 创建并连接
                connection = MockMT5Connection(conn_id)
                await connection.connect()
                
                # 保持连接一段时间
                await asyncio.sleep(0.2)
                
                await connection.disconnect()
                
                attempt.success = True
                attempt.end_time = time.time()
                
            except Exception as e:
                attempt.error = str(e)
                attempt.end_time = time.time()
                logger.warning(f"连接失败 {conn_id}: {e}")
                
            finally:
                if attempt.semaphore_acquired:
                    semaphore.release()
            
            return attempt
        
        # 创建连接任务
        tasks = [
            asyncio.create_task(attempt_connection(f"conn_{i:03d}"))
            for i in range(total_connections)
        ]
        
        # 并发执行
        attempts = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常
        valid_attempts = [a for a in attempts if isinstance(a, ConnectionAttempt)]
        self.connection_attempts.extend(valid_attempts)
        
        # 分析结果
        result = self._analyze_test_results("semaphore_control", semaphore_limit, valid_attempts)
        
        return result
    
    async def test_api_overload_protection(self, concurrent_limit: int) -> ConcurrencyTestResult:
        """测试API过载保护"""
        logger.info(f"🧪 测试API过载保护: 并发限制 {concurrent_limit}")
        
        # 尝试超出API限制的连接数
        overload_connections = MockMT5Connection._max_connections + 20
        
        semaphore = asyncio.Semaphore(concurrent_limit)
        self.connection_attempts.clear()
        MockMT5Connection.reset_connections()
        
        async def stress_connect(conn_id: str) -> ConnectionAttempt:
            """压力连接测试"""
            attempt = ConnectionAttempt(
                start_time=time.time(),
                connection_id=conn_id
            )
            
            try:
                async with semaphore:  # 使用上下文管理器确保释放
                    attempt.semaphore_acquired = True
                    
                    connection = MockMT5Connection(conn_id)
                    await connection.connect()
                    
                    # 保持连接较长时间以测试并发限制
                    await asyncio.sleep(0.5)
                    
                    await connection.disconnect()
                    
                    attempt.success = True
                    attempt.end_time = time.time()
                    
            except Exception as e:
                attempt.error = str(e)
                attempt.end_time = time.time()
                
                # 记录错误模式
                error_type = type(e).__name__
                if "连接限制" in str(e):
                    error_type = "API_LIMIT_EXCEEDED"
                elif "timeout" in str(e).lower():
                    error_type = "CONNECTION_TIMEOUT"
                
                logger.debug(f"连接失败 {conn_id}: {error_type}")
            
            return attempt
        
        # 分批创建任务以避免瞬时冲击
        batch_size = 10
        all_attempts = []
        
        for batch_start in range(0, overload_connections, batch_size):
            batch_end = min(batch_start + batch_size, overload_connections)
            
            tasks = [
                asyncio.create_task(stress_connect(f"stress_{i:03d}"))
                for i in range(batch_start, batch_end)
            ]
            
            batch_attempts = await asyncio.gather(*tasks, return_exceptions=True)
            valid_batch = [a for a in batch_attempts if isinstance(a, ConnectionAttempt)]
            all_attempts.extend(valid_batch)
            
            # 批次间短暂等待
            await asyncio.sleep(0.1)
        
        self.connection_attempts.extend(all_attempts)
        
        # 分析结果
        result = self._analyze_test_results("api_overload_protection", concurrent_limit, all_attempts)
        
        return result
    
    async def test_different_concurrency_levels(self) -> List[ConcurrencyTestResult]:
        """测试不同并发级别"""
        logger.info("🧪 测试不同并发级别")
        
        concurrency_levels = [1, 2, 4, 8, 16, 32]
        results = []
        
        for level in concurrency_levels:
            logger.info(f"测试并发级别: {level}")
            
            try:
                result = await self.test_semaphore_control(level, level * 3)
                results.append(result)
                
                # 测试间隔
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"并发级别 {level} 测试失败: {e}")
                
                error_result = ConcurrencyTestResult(
                    test_name=f"concurrency_level_{level}",
                    max_allowed_concurrent=level,
                    total_attempts=0,
                    successful_attempts=0,
                    failed_attempts=0,
                    api_limit_respected=False
                )
                error_result.error_patterns["test_error"] = 1
                results.append(error_result)
        
        return results
    
    def _analyze_test_results(self, test_name: str, max_allowed: int, 
                            attempts: List[ConnectionAttempt]) -> ConcurrencyTestResult:
        """分析测试结果"""
        if not attempts:
            return ConcurrencyTestResult(
                test_name=test_name,
                max_allowed_concurrent=max_allowed,
                total_attempts=0,
                successful_attempts=0,
                failed_attempts=0
            )
        
        successful = [a for a in attempts if a.success]
        failed = [a for a in attempts if not a.success]
        
        # 计算平均连接时间
        if successful:
            connection_times = [a.end_time - a.start_time for a in successful]
            avg_connection_time = statistics.mean(connection_times)
        else:
            avg_connection_time = 0.0
        
        # 检查并发限制违规
        semaphore_violations = sum(1 for a in attempts if a.actual_concurrent > max_allowed)
        
        # 统计错误模式
        error_patterns = {}
        for attempt in failed:
            if attempt.error:
                error_type = "API_LIMIT_EXCEEDED" if "连接限制" in attempt.error else "OTHER_ERROR"
                error_patterns[error_type] = error_patterns.get(error_type, 0) + 1
        
        # 判断API限制是否被遵守
        api_limit_respected = (
            self.max_concurrent_observed <= max_allowed and
            semaphore_violations == 0 and
            error_patterns.get("API_LIMIT_EXCEEDED", 0) == 0
        )
        
        result = ConcurrencyTestResult(
            test_name=test_name,
            max_allowed_concurrent=max_allowed,
            total_attempts=len(attempts),
            successful_attempts=len(successful),
            failed_attempts=len(failed),
            avg_connection_time=avg_connection_time,
            max_observed_concurrent=self.max_concurrent_observed,
            semaphore_violations=semaphore_violations,
            api_limit_respected=api_limit_respected,
            error_patterns=error_patterns
        )
        
        return result
    
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """运行综合验证"""
        logger.info("🚀 开始MT5 API并发限制综合验证")
        
        validation_results = {
            'validation_timestamp': time.time(),
            'test_results': {}
        }
        
        try:
            # 1. 基础信号量控制测试
            basic_result = await self.test_semaphore_control(4, 20)
            validation_results['test_results']['basic_semaphore'] = basic_result.__dict__
            
            # 2. API过载保护测试
            overload_result = await self.test_api_overload_protection(8)
            validation_results['test_results']['api_overload_protection'] = overload_result.__dict__
            
            # 3. 不同并发级别测试
            concurrency_results = await self.test_different_concurrency_levels()
            validation_results['test_results']['concurrency_levels'] = [r.__dict__ for r in concurrency_results]
            
            # 4. 综合评估
            validation_results['overall_assessment'] = self._assess_overall_performance(
                basic_result, overload_result, concurrency_results
            )
            
        except Exception as e:
            logger.error(f"验证过程异常: {e}")
            validation_results['error'] = str(e)
        
        # 生成报告
        await self._generate_validation_report(validation_results)
        
        return validation_results
    
    def _assess_overall_performance(self, basic_result: ConcurrencyTestResult,
                                  overload_result: ConcurrencyTestResult,
                                  concurrency_results: List[ConcurrencyTestResult]) -> Dict[str, Any]:
        """综合性能评估"""
        assessment = {
            'semaphore_effective': basic_result.api_limit_respected,
            'overload_protection_works': overload_result.api_limit_respected,
            'scalability_rating': 'unknown',
            'performance_metrics': {},
            'recommendations': []
        }
        
        # 评估可扩展性
        successful_levels = [r for r in concurrency_results if r.api_limit_respected]
        if successful_levels:
            max_successful_level = max(r.max_allowed_concurrent for r in successful_levels)
            if max_successful_level >= 16:
                assessment['scalability_rating'] = 'excellent'
            elif max_successful_level >= 8:
                assessment['scalability_rating'] = 'good'
            elif max_successful_level >= 4:
                assessment['scalability_rating'] = 'adequate'
            else:
                assessment['scalability_rating'] = 'limited'
        
        # 性能指标
        if concurrency_results:
            avg_connection_times = [r.avg_connection_time for r in concurrency_results if r.avg_connection_time > 0]
            if avg_connection_times:
                assessment['performance_metrics']['avg_connection_time'] = statistics.mean(avg_connection_times)
                assessment['performance_metrics']['connection_time_variance'] = statistics.stdev(avg_connection_times) if len(avg_connection_times) > 1 else 0
        
        # 生成建议
        if not basic_result.api_limit_respected:
            assessment['recommendations'].append("基础信号量控制需要调整")
        
        if not overload_result.api_limit_respected:
            assessment['recommendations'].append("需要增强API过载保护机制")
        
        if assessment['scalability_rating'] in ['limited', 'adequate']:
            assessment['recommendations'].append("考虑优化并发连接策略")
        
        if assessment['performance_metrics'].get('avg_connection_time', 0) > 1.0:
            assessment['recommendations'].append("连接时间过长，需要优化连接建立过程")
        
        return assessment
    
    async def _generate_validation_report(self, results: Dict[str, Any]):
        """生成验证报告"""
        report_file = f"reports/mt5_api_validation_{int(time.time())}.json"
        
        # 确保报告目录存在
        Path("reports").mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 MT5 API验证报告已生成: {report_file}")
        
        # 打印摘要
        self._print_validation_summary(results)
    
    def _print_validation_summary(self, results: Dict[str, Any]):
        """打印验证摘要"""
        print("\n" + "="*70)
        print("🔒 MT5 API并发限制验证报告")
        print("="*70)
        
        test_results = results.get('test_results', {})
        overall = results.get('overall_assessment', {})
        
        # 基础测试结果
        if 'basic_semaphore' in test_results:
            basic = test_results['basic_semaphore']
            status = "✅ 通过" if basic.get('api_limit_respected', False) else "❌ 失败"
            print(f"信号量控制测试: {status}")
            print(f"  成功连接: {basic.get('successful_attempts', 0)}/{basic.get('total_attempts', 0)}")
            print(f"  最大并发: {basic.get('max_observed_concurrent', 0)}")
        
        # 过载保护测试
        if 'api_overload_protection' in test_results:
            overload = test_results['api_overload_protection']
            status = "✅ 通过" if overload.get('api_limit_respected', False) else "❌ 失败"
            print(f"API过载保护测试: {status}")
            print(f"  成功连接: {overload.get('successful_attempts', 0)}/{overload.get('total_attempts', 0)}")
        
        # 并发级别测试
        if 'concurrency_levels' in test_results:
            concurrency_tests = test_results['concurrency_levels']
            successful_levels = [t for t in concurrency_tests if t.get('api_limit_respected', False)]
            print(f"并发级别测试: {len(successful_levels)}/{len(concurrency_tests)} 个级别通过")
        
        # 综合评估
        print(f"\n📊 综合评估:")
        print(f"  信号量有效性: {'✅ 有效' if overall.get('semaphore_effective', False) else '❌ 无效'}")
        print(f"  过载保护: {'✅ 有效' if overall.get('overload_protection_works', False) else '❌ 无效'}")
        print(f"  可扩展性: {overall.get('scalability_rating', 'unknown').upper()}")
        
        if overall.get('performance_metrics'):
            metrics = overall['performance_metrics']
            if 'avg_connection_time' in metrics:
                print(f"  平均连接时间: {metrics['avg_connection_time']:.3f}s")
        
        # 建议
        recommendations = overall.get('recommendations', [])
        if recommendations:
            print(f"\n💡 建议:")
            for rec in recommendations:
                print(f"  • {rec}")
        
        print("\n" + "="*70)

async def main():
    """主函数"""
    validator = MT5ApiLimitValidator()
    results = await validator.run_comprehensive_validation()
    
    # 评估验证结果
    overall = results.get('overall_assessment', {})
    validation_passed = (
        overall.get('semaphore_effective', False) and
        overall.get('overload_protection_works', False) and
        overall.get('scalability_rating', 'unknown') in ['good', 'excellent']
    )
    
    if validation_passed:
        logger.info("🎉 MT5 API并发限制验证通过!")
        return 0
    else:
        logger.warning("⚠️ MT5 API并发限制验证未完全通过")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)