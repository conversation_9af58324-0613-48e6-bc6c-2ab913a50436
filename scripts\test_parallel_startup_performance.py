#!/usr/bin/env python3
"""
并行启动稳定性和性能测试脚本
测试并行账户启动功能的稳定性和性能
验证MT5 API并发限制和错误隔离效果
"""
import asyncio
import time
import logging
import statistics
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
import sys
import traceback
from dataclasses import dataclass, field
import threading
from concurrent.futures import ThreadPoolExecutor

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class AccountStartupMetrics:
    """账户启动指标"""
    account_id: str
    start_time: float
    end_time: float
    duration_ms: float
    success: bool
    error_message: Optional[str] = None
    connection_attempts: int = 1
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0

@dataclass
class ParallelTestResult:
    """并行测试结果"""
    test_name: str
    total_accounts: int
    successful_startups: int
    failed_startups: int
    total_duration_ms: float
    average_startup_time_ms: float
    max_startup_time_ms: float
    min_startup_time_ms: float
    concurrent_connections: int
    success_rate: float
    throughput_per_second: float
    resource_usage: Dict[str, float]
    error_summary: Dict[str, int]

class MockMT5Account:
    """模拟MT5账户"""

    def __init__(self, account_id: str, config: Dict[str, Any]):
        self.account_id = account_id
        self.config = config
        self.connected = False
        self.startup_time = 0
        self.connection_attempts = 0

    async def startup(self) -> bool:
        """模拟账户启动过程"""
        self.connection_attempts += 1

        # 模拟启动延迟
        startup_delay = self.config.get('mock_connection_delay', 0.5)
        await asyncio.sleep(startup_delay)

        # 模拟失败率
        failure_rate = self.config.get('mock_failure_rate', 0.0)
        if failure_rate > 0 and hash(self.account_id) % 10 == 0:
            raise ConnectionError(f"模拟连接失败: {self.account_id}")

        self.connected = True
        self.startup_time = time.time()
        return True

    async def shutdown(self):
        """关闭账户"""
        self.connected = False

class ResourceMonitor:
    """资源监控器"""

    def __init__(self):
        self.baseline_memory = self.get_memory_usage()
        self.baseline_cpu = self.get_cpu_usage()

    def get_memory_usage(self) -> float:
        """获取内存使用量(MB)"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            # 如果没有psutil，返回模拟值
            return 50.0 + len(threading.enumerate()) * 2.0

    def get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        try:
            import psutil
            return psutil.cpu_percent(interval=0.1)
        except ImportError:
            # 如果没有psutil，返回模拟值
            return 10.0 + len(threading.enumerate()) * 0.5

class ParallelStartupTester:
    """并行启动性能测试器"""
    
    def __init__(self):
        self.test_results: List[ParallelTestResult] = []
        self.active_accounts: Dict[str, MockMT5Account] = {}
        self.resource_monitor = ResourceMonitor()

        # 测试配置
        self.test_accounts = self._generate_test_accounts()

    def _generate_test_accounts(self) -> List[Dict]:
        """生成测试账户配置"""
        accounts = []
        for i in range(20):  # 生成20个测试账户
            account = {
                'account_id': f'TEST_ACC_{i:03d}',
                'login': 100000 + i,
                'server': 'MetaQuotes-Demo',
                'host_id': 'test-node-01',
                'terminal_path': f'/opt/mt5/terminal_{i}',
                'enabled': True,
                'mock_connection_delay': 0.1 + (i % 3) * 0.05,  # 模拟不同连接延迟 100-200ms
                'mock_failure_rate': 0.1 if i % 10 == 0 else 0.0  # 10%账户模拟失败
            }
            accounts.append(account)
        return accounts

    async def test_single_account_startup(self, account_id: str, account_config: Dict[str, Any]) -> AccountStartupMetrics:
        """测试单个账户启动"""
        start_time = time.perf_counter()

        try:
            # 记录资源使用
            memory_before = self.resource_monitor.get_memory_usage()
            cpu_before = self.resource_monitor.get_cpu_usage()

            # 创建并启动账户
            account = MockMT5Account(account_id, account_config)
            await account.startup()

            # 记录账户
            self.active_accounts[account_id] = account

            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000

            # 计算资源使用
            memory_after = self.resource_monitor.get_memory_usage()
            cpu_after = self.resource_monitor.get_cpu_usage()

            return AccountStartupMetrics(
                account_id=account_id,
                start_time=start_time,
                end_time=end_time,
                duration_ms=duration_ms,
                success=True,
                connection_attempts=account.connection_attempts,
                memory_usage_mb=memory_after - memory_before,
                cpu_usage_percent=cpu_after - cpu_before
            )

        except Exception as e:
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000

            logger.error(f"启动失败 {account_id}: {e}")

            return AccountStartupMetrics(
                account_id=account_id,
                start_time=start_time,
                end_time=end_time,
                duration_ms=duration_ms,
                success=False,
                error_message=str(e)
            )

    async def test_basic_parallel_startup(self, concurrency_limit: int = 4) -> ParallelTestResult:
        """基础并行启动测试"""
        logger.info(f"🧪 开始基础并行启动测试: 并发限制 {concurrency_limit}")

        # 创建信号量限制并发
        semaphore = asyncio.Semaphore(concurrency_limit)

        async def limited_startup(account_id: str, account_config: Dict[str, Any]) -> AccountStartupMetrics:
            async with semaphore:
                return await self.test_single_account_startup(account_id, account_config)

        # 记录总体开始时间
        total_start_time = time.perf_counter()

        # 并行执行启动测试
        tasks = []
        for account_config in self.test_accounts:
            account_id = account_config['account_id']
            task = limited_startup(account_id, account_config)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        total_end_time = time.perf_counter()
        total_duration_ms = (total_end_time - total_start_time) * 1000

        # 处理结果
        successful_results = []
        failed_results = []
        error_summary = {}

        for result in results:
            if isinstance(result, Exception):
                failed_results.append(result)
                error_type = type(result).__name__
                error_summary[error_type] = error_summary.get(error_type, 0) + 1
            elif isinstance(result, AccountStartupMetrics):
                if result.success:
                    successful_results.append(result)
                else:
                    failed_results.append(result)
                    error_summary[result.error_message or "Unknown"] = \
                        error_summary.get(result.error_message or "Unknown", 0) + 1

        # 计算统计指标
        successful_count = len(successful_results)
        failed_count = len(failed_results)

        if successful_results:
            durations = [r.duration_ms for r in successful_results]
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
            min_duration = min(durations)
        else:
            avg_duration = max_duration = min_duration = 0

        success_rate = successful_count / len(self.test_accounts) if self.test_accounts else 0
        throughput = successful_count / (total_duration_ms / 1000) if total_duration_ms > 0 else 0

        # 资源使用统计
        resource_usage = {
            'total_memory_mb': sum(r.memory_usage_mb for r in successful_results),
            'avg_memory_per_connection_mb': sum(r.memory_usage_mb for r in successful_results) / max(successful_count, 1),
            'total_cpu_percent': sum(r.cpu_usage_percent for r in successful_results),
            'avg_cpu_per_connection_percent': sum(r.cpu_usage_percent for r in successful_results) / max(successful_count, 1)
        }

        return ParallelTestResult(
            test_name="basic_parallel_startup",
            total_accounts=len(self.test_accounts),
            successful_startups=successful_count,
            failed_startups=failed_count,
            total_duration_ms=total_duration_ms,
            average_startup_time_ms=avg_duration,
            max_startup_time_ms=max_duration,
            min_startup_time_ms=min_duration,
            concurrent_connections=concurrency_limit,
            success_rate=success_rate,
            throughput_per_second=throughput,
            resource_usage=resource_usage,
            error_summary=error_summary
        )
    
    async def test_concurrency_scaling(self) -> List[ParallelTestResult]:
        """并发扩展性测试"""
        logger.info("🧪 开始并发扩展性测试")

        # 测试不同并发级别
        concurrency_levels = [1, 2, 4, 8, 16]
        results = []

        for concurrency in concurrency_levels:
            logger.info(f"测试并发级别: {concurrency}")

            try:
                result = await self.test_basic_parallel_startup(concurrency)
                result.test_name = f"concurrency_scaling_{concurrency}"
                results.append(result)

                # 清理连接
                await self.cleanup_connections()

                # 短暂等待避免资源冲突
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"并发级别 {concurrency} 测试失败: {e}")
                error_result = ParallelTestResult(
                    test_name=f"concurrency_scaling_{concurrency}",
                    total_accounts=len(self.test_accounts),
                    successful_startups=0,
                    failed_startups=len(self.test_accounts),
                    total_duration_ms=0,
                    average_startup_time_ms=0,
                    max_startup_time_ms=0,
                    min_startup_time_ms=0,
                    concurrent_connections=concurrency,
                    success_rate=0,
                    throughput_per_second=0,
                    resource_usage={},
                    error_summary={"test_error": 1}
                )
                results.append(error_result)

        return results
    
    async def test_api_limit_effectiveness(self) -> List[ParallelTestResult]:
        """MT5 API限制有效性测试"""
        logger.info("🧪 开始MT5 API限制有效性测试")

        # 测试不同API限制设置
        api_limits = [1, 2, 4, 8]
        results = []

        for limit in api_limits:
            logger.info(f"测试API限制: {limit}")

            try:
                result = await self.test_basic_parallel_startup(limit)
                result.test_name = f"api_limit_{limit}"

                # 验证API限制是否被遵守
                api_limit_respected = result.concurrent_connections <= limit
                if not api_limit_respected:
                    logger.warning(f"API限制 {limit} 被违反: 实际并发 {result.concurrent_connections}")

                results.append(result)

                # 清理连接
                await self.cleanup_connections()
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"API限制 {limit} 测试失败: {e}")
                error_result = ParallelTestResult(
                    test_name=f"api_limit_{limit}",
                    total_accounts=len(self.test_accounts),
                    successful_startups=0,
                    failed_startups=len(self.test_accounts),
                    total_duration_ms=0,
                    average_startup_time_ms=0,
                    max_startup_time_ms=0,
                    min_startup_time_ms=0,
                    concurrent_connections=limit,
                    success_rate=0,
                    throughput_per_second=0,
                    resource_usage={},
                    error_summary={"api_limit_error": 1}
                )
                results.append(error_result)

        return results
    
    async def test_error_isolation(self) -> ParallelTestResult:
        """错误隔离测试"""
        logger.info("🧪 开始错误隔离测试")

        try:
            # 使用包含故意失败账户的配置进行测试
            result = await self.test_basic_parallel_startup(4)
            result.test_name = "error_isolation"

            # 检查错误隔离效果
            expected_failures = sum(1 for acc in self.test_accounts if acc['mock_failure_rate'] > 0)
            actual_failures = result.failed_startups

            isolation_effective = (
                result.successful_startups > 0 and
                result.failed_startups > 0 and
                result.success_rate > 0.5  # 至少50%成功率表示隔离有效
            )

            logger.info(f"✅ 错误隔离测试完成: {result.successful_startups}/{result.total_accounts} 账户成功启动")
            logger.info(f"预期失败: {expected_failures}, 实际失败: {actual_failures}")
            logger.info(f"隔离有效性: {'✅' if isolation_effective else '❌'}")

            # 清理连接
            await self.cleanup_connections()

            return result

        except Exception as e:
            logger.error(f"❌ 错误隔离测试失败: {e}")
            return ParallelTestResult(
                test_name="error_isolation",
                total_accounts=len(self.test_accounts),
                successful_startups=0,
                failed_startups=len(self.test_accounts),
                total_duration_ms=0,
                average_startup_time_ms=0,
                max_startup_time_ms=0,
                min_startup_time_ms=0,
                concurrent_connections=4,
                success_rate=0,
                throughput_per_second=0,
                resource_usage={},
                error_summary={"isolation_test_error": 1}
            )
    
    async def test_stability_under_load(self) -> List[ParallelTestResult]:
        """负载稳定性测试"""
        logger.info("🧪 开始负载稳定性测试")

        results = []

        # 连续运行多次测试
        for run in range(5):
            logger.info(f"稳定性测试 - 第 {run + 1} 轮")

            try:
                result = await self.test_basic_parallel_startup(4)
                result.test_name = f"stability_run_{run + 1}"
                results.append(result)

                # 清理连接
                await self.cleanup_connections()

                # 短暂等待清理资源
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"稳定性测试第 {run + 1} 轮失败: {e}")
                error_result = ParallelTestResult(
                    test_name=f"stability_run_{run + 1}",
                    total_accounts=len(self.test_accounts),
                    successful_startups=0,
                    failed_startups=len(self.test_accounts),
                    total_duration_ms=0,
                    average_startup_time_ms=0,
                    max_startup_time_ms=0,
                    min_startup_time_ms=0,
                    concurrent_connections=4,
                    success_rate=0,
                    throughput_per_second=0,
                    resource_usage={},
                    error_summary={"stability_test_error": 1}
                )
                results.append(error_result)

        # 计算稳定性指标
        successful_runs = [r for r in results if r.success_rate > 0.8]
        if successful_runs:
            startup_times = [r.average_startup_time_ms for r in successful_runs]
            logger.info(f"稳定性指标: 成功率 {len(successful_runs)}/{len(results)}")
            logger.info(f"平均启动时间: {statistics.mean(startup_times):.2f}ms")
            if len(startup_times) > 1:
                logger.info(f"启动时间标准差: {statistics.stdev(startup_times):.2f}ms")

        return results

    async def cleanup_connections(self):
        """清理连接"""
        logger.debug("🧹 清理测试连接...")

        cleanup_tasks = []
        for account in self.active_accounts.values():
            cleanup_tasks.append(account.shutdown())

        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        self.active_accounts.clear()
        logger.debug("✅ 连接清理完成")

    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🚀 开始并行启动性能测试套件")

        all_results = {
            'test_suite': 'parallel_startup_performance',
            'timestamp': time.time(),
            'test_results': {}
        }

        try:
            # 1. 基础并行启动测试
            logger.info("执行基础并行启动测试...")
            basic_result = await self.test_basic_parallel_startup(4)
            all_results['test_results']['basic_parallel_startup'] = basic_result.__dict__

            # 2. 并发扩展性测试
            logger.info("执行并发扩展性测试...")
            concurrency_results = await self.test_concurrency_scaling()
            all_results['test_results']['concurrency_scaling'] = [r.__dict__ for r in concurrency_results]

            # 3. API限制有效性测试
            logger.info("执行API限制有效性测试...")
            api_limit_results = await self.test_api_limit_effectiveness()
            all_results['test_results']['api_limit_effectiveness'] = [r.__dict__ for r in api_limit_results]

            # 4. 错误隔离测试
            logger.info("执行错误隔离测试...")
            error_isolation_result = await self.test_error_isolation()
            all_results['test_results']['error_isolation'] = error_isolation_result.__dict__

            # 5. 负载稳定性测试
            logger.info("执行负载稳定性测试...")
            stability_results = await self.test_stability_under_load()
            all_results['test_results']['stability_under_load'] = [r.__dict__ for r in stability_results]

        except Exception as e:
            logger.error(f"测试套件执行异常: {e}")
            all_results['error'] = str(e)
            all_results['traceback'] = traceback.format_exc()

        # 生成测试报告
        await self._generate_test_report(all_results)

        return all_results
    
    async def _generate_test_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        report_file = f"reports/parallel_startup_test_report_{int(time.time())}.json"
        
        # 确保报告目录存在
        Path("reports").mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 测试报告已生成: {report_file}")
        
        # 生成简要摘要
        self._print_test_summary(results)
    
    def _print_test_summary(self, results: Dict[str, Any]):
        """打印测试摘要"""
        print("\n" + "="*70)
        print("🧪 并行启动性能测试摘要")
        print("="*70)

        test_results = results.get('test_results', {})

        # 基础测试结果
        if 'basic_parallel_startup' in test_results:
            basic = test_results['basic_parallel_startup']
            status = "✅ 通过" if basic.get('success_rate', 0) > 0.8 else "❌ 失败"
            print(f"基础并行启动测试: {status}")
            print(f"  成功率: {basic.get('success_rate', 0)*100:.1f}%")
            print(f"  吞吐量: {basic.get('throughput_per_second', 0):.1f} 账户/秒")
            print(f"  平均启动时间: {basic.get('average_startup_time_ms', 0):.1f}ms")

        # 并发扩展性测试
        if 'concurrency_scaling' in test_results:
            concurrency_tests = test_results['concurrency_scaling']
            successful_levels = [t for t in concurrency_tests if t.get('success_rate', 0) > 0.8]
            print(f"并发扩展性测试: {len(successful_levels)}/{len(concurrency_tests)} 个级别通过")
            if successful_levels:
                best_throughput = max(t.get('throughput_per_second', 0) for t in successful_levels)
                print(f"  最佳吞吐量: {best_throughput:.1f} 账户/秒")

        # API限制测试
        if 'api_limit_effectiveness' in test_results:
            api_tests = test_results['api_limit_effectiveness']
            successful_limits = [t for t in api_tests if t.get('success_rate', 0) > 0.8]
            print(f"API限制有效性测试: {len(successful_limits)}/{len(api_tests)} 个限制通过")

        # 错误隔离测试
        if 'error_isolation' in test_results:
            isolation = test_results['error_isolation']
            status = "✅ 通过" if isolation.get('success_rate', 0) > 0.5 else "❌ 失败"
            print(f"错误隔离测试: {status}")
            print(f"  成功率: {isolation.get('success_rate', 0)*100:.1f}%")

        # 稳定性测试
        if 'stability_under_load' in test_results:
            stability_tests = test_results['stability_under_load']
            successful_runs = [t for t in stability_tests if t.get('success_rate', 0) > 0.8]
            print(f"负载稳定性测试: {len(successful_runs)}/{len(stability_tests)} 轮通过")
            if successful_runs:
                avg_success_rate = sum(t.get('success_rate', 0) for t in successful_runs) / len(successful_runs)
                print(f"  平均成功率: {avg_success_rate*100:.1f}%")

        print("="*70)

async def main():
    """主函数"""
    print("🚀 MT5并行启动性能测试")
    print("=" * 50)

    tester = ParallelStartupTester()
    results = await tester.run_all_tests()

    # 评估测试结果
    test_results = results.get('test_results', {})

    # 检查基础测试是否通过
    basic_passed = False
    if 'basic_parallel_startup' in test_results:
        basic = test_results['basic_parallel_startup']
        basic_passed = basic.get('success_rate', 0) > 0.8

    # 检查错误隔离是否有效
    isolation_passed = False
    if 'error_isolation' in test_results:
        isolation = test_results['error_isolation']
        isolation_passed = isolation.get('success_rate', 0) > 0.5

    # 检查稳定性测试
    stability_passed = False
    if 'stability_under_load' in test_results:
        stability_tests = test_results['stability_under_load']
        successful_runs = [t for t in stability_tests if t.get('success_rate', 0) > 0.8]
        stability_passed = len(successful_runs) >= 3  # 至少3轮成功

    overall_success = basic_passed and isolation_passed and stability_passed

    if overall_success:
        logger.info("🎉 并行启动性能测试通过!")
        return 0
    else:
        logger.warning("⚠️ 部分测试未达到预期标准")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)