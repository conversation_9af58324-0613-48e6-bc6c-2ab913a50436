#!/usr/bin/env python3
"""
跟单系统启动脚本
"""
import asyncio
import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def load_env_file():
    """加载环境变量"""
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    value = value.strip('"\'')
                    os.environ[key] = value
        return True
    return False

async def main():
    """主函数"""
    print("🚀 MT5跟单系统")
    print("=" * 40)
    
    # 加载环境变量
    if not load_env_file():
        print("❌ 无法加载 .env 文件")
        return
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)-5s | %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        # 导入启动器
        from mt5_launcher import MT5Launcher
        
        launcher = MT5Launcher()
        
        logger.info("启动跟单系统...")
        
        # 启动服务
        success = await launcher.start_services("ACC001", ["ACC002"])
        
        if success:
            print("🎉 跟单系统启动成功!")
            
            # 保持运行
            try:
                while launcher.running:
                    await asyncio.sleep(10)
                    
            except KeyboardInterrupt:
                print("\n收到停止信号")
        else:
            print("❌ 跟单系统启动失败")
            
    except Exception as e:
        print(f"❌ 启动过程出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if 'launcher' in locals():
            await launcher.stop_services()

if __name__ == "__main__":
    asyncio.run(main())
