#!/usr/bin/env python3
"""
执行器队列满载问题优化验证测试
测试多工作者执行器池、动态负载均衡和队列深度监控
"""

import asyncio
import time
import sys
import os
from typing import Dict, Any, List
from dataclasses import dataclass
from enum import Enum

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

# Mock依赖
class MockLogger:
    def info(self, msg): print(f"✅ {msg}")
    def warning(self, msg): print(f"⚠️  {msg}")  
    def error(self, msg): print(f"❌ {msg}")
    def debug(self, msg): pass
    def critical(self, msg): print(f"🚨 {msg}")

class MockMetrics:
    def increment(self, *args, **kwargs): pass
    def record_histogram(self, *args, **kwargs): pass
    def gauge(self, *args, **kwargs): pass

# 简化的订单类型和优先级枚举
class OrderTypeEnum(Enum):
    BUY = "BUY"
    SELL = "SELL"
    BUY_LIMIT = "BUY_LIMIT"
    SELL_LIMIT = "SELL_LIMIT"

class MessagePriority(Enum):
    CRITICAL = 0
    HIGH = 1
    NORMAL = 2
    REALTIME_QUERY = 3
    LOW = 4

# Mock执行命令
@dataclass
class ExecutionCommand:
    command_id: str
    command_type: str
    account_id: str
    symbol: str
    volume: float
    order_type: OrderTypeEnum
    price: float = None
    sl: float = None
    tp: float = None
    position_id: int = None
    metadata: Dict[str, Any] = None

# Mock优先级队列
class MockPriorityQueue:
    def __init__(self, max_sizes: Dict):
        self.queue = []
        self.max_sizes = max_sizes
        self.total_size = sum(max_sizes.values())
    
    async def enqueue(self, priority, data, message_id, callback=None):
        self.queue.append({'priority': priority, 'data': data, 'id': message_id})
        return len(self.queue) < self.total_size
    
    async def dequeue(self, timeout=1.0):
        if not self.queue:
            await asyncio.sleep(timeout)
            return None
        
        # 按优先级排序并返回最高优先级项目
        self.queue.sort(key=lambda x: x['priority'].value)
        item = self.queue.pop(0)
        
        # 创建priority_msg对象
        priority_msg = type('PriorityMsg', (), {
            'priority': item['priority'],
            'data': item['data']
        })()
        
        return priority_msg
    
    def is_empty(self):
        return len(self.queue) == 0
    
    def get_stats(self):
        return {
            'heap_size': len(self.queue),
            'queue_sizes': {
                'CRITICAL': len([x for x in self.queue if x['priority'] == MessagePriority.CRITICAL]),
                'HIGH': len([x for x in self.queue if x['priority'] == MessagePriority.HIGH]),
                'NORMAL': len([x for x in self.queue if x['priority'] == MessagePriority.NORMAL]),
                'REALTIME_QUERY': len([x for x in self.queue if x['priority'] == MessagePriority.REALTIME_QUERY]),
                'LOW': len([x for x in self.queue if x['priority'] == MessagePriority.LOW]),
            }
        }

# Mock批处理器
class MockTradeBatchProcessor:
    def __init__(self):
        self.processed_count = 0
    
    def set_trade_executor(self, executor):
        self.executor = executor
    
    async def process_item(self, command):
        # 模拟处理时间
        await asyncio.sleep(0.01)
        self.processed_count += 1

# Mock增强处理器
class MockEnhancedProcessor:
    def __init__(self):
        self.processed_signals = 0
    
    async def process_trade_signal(self, signal_dict, priority, enable_matching=True, enable_validation=True):
        await asyncio.sleep(0.005)  # 模拟处理时间
        self.processed_signals += 1
        return {'status': 'success', 'signal_id': signal_dict.get('signal_id')}

# 简化的TradeExecutor类（只包含核心优化功能）
class OptimizedTradeExecutor:
    """优化版执行器测试类"""
    
    def __init__(self, account_name: str, config: Dict[str, Any] = None):
        self.account_name = account_name
        self.account_id = account_name
        self.config = config or {}
        self.running = False
        self.start_time = None
        
        # 🚀 多线程执行器配置
        self.worker_count = self.config.get('executor_worker_count', 4)
        self.max_worker_count = self.config.get('max_executor_workers', 8)
        self.min_worker_count = self.config.get('min_executor_workers', 2)
        self.worker_scaling_threshold = self.config.get('worker_scaling_threshold', 0.8)
        self.worker_scale_down_threshold = self.config.get('worker_scale_down_threshold', 0.3)
        
        # 创建优先级队列
        queue_sizes = {
            MessagePriority.CRITICAL: self.config.get('critical_queue_size', 100),
            MessagePriority.HIGH: self.config.get('high_queue_size', 200),
            MessagePriority.NORMAL: self.config.get('normal_queue_size', 300),
            MessagePriority.REALTIME_QUERY: self.config.get('realtime_query_queue_size', 250),
            MessagePriority.LOW: self.config.get('low_queue_size', 400)
        }
        
        self.command_queue = MockPriorityQueue(queue_sizes)
        self.queue_size = sum(queue_sizes.values())
        
        # 工作者池管理
        self.worker_tasks: List[asyncio.Task] = []
        self.worker_stats: Dict[str, Dict[str, Any]] = {}
        self.load_balancer_task = None
        self.last_scaling_time = 0
        self.scaling_cooldown = 5  # 缩短到5秒便于测试
        
        # Mock组件
        self.trade_batch_processor = MockTradeBatchProcessor()
        self.trade_batch_processor.set_trade_executor(self)
        self.enhanced_processor = MockEnhancedProcessor()
        
        # 执行统计
        self.execution_stats = {
            'commands_received': 0,
            'commands_executed': 0,
            'commands_failed': 0,
            'execution_errors': 0,
            'queue_full_count': 0
        }
        
        print(f"✅ 优化版执行器初始化: {account_name}")
    
    async def start_executor(self):
        """启动执行器"""
        if self.running:
            return False
        
        try:
            self.running = True
            self.start_time = time.time()
            
            # 启动多工作者执行器池
            await self._start_worker_pool()
            
            # 启动负载均衡器
            self.load_balancer_task = asyncio.create_task(self._load_balancer_loop())
            
            print(f"🚀 执行器启动完成: {self.account_id}")
            return True
            
        except Exception as e:
            print(f"❌ 启动执行器失败: {e}")
            self.running = False
            return False
    
    async def _start_worker_pool(self):
        """启动多工作者执行器池"""
        print(f"🔄 启动工作者池: {self.worker_count}个工作者")
        
        for i in range(self.worker_count):
            worker_id = f"worker-{i}"
            task = asyncio.create_task(self._execution_worker(worker_id))
            self.worker_tasks.append(task)
            
            # 初始化工作者统计
            self.worker_stats[worker_id] = {
                'commands_processed': 0,
                'commands_failed': 0,
                'avg_processing_time_ms': 0.0,
                'last_activity_time': time.time(),
                'is_busy': False,
                'current_command': None
            }
    
    async def _execution_worker(self, worker_id: str):
        """执行工作者"""
        while self.running:
            try:
                priority_msg = await self.command_queue.dequeue(timeout=0.5)
                
                if priority_msg is None:
                    continue
                
                command = priority_msg.data
                start_time = time.time()
                
                # 更新工作者状态
                self.worker_stats[worker_id]['is_busy'] = True
                self.worker_stats[worker_id]['current_command'] = command.command_id
                self.worker_stats[worker_id]['last_activity_time'] = start_time
                
                try:
                    # 使用增强处理器处理命令
                    if self.enhanced_processor:
                        await self._process_with_enhanced_processor(command, priority_msg.priority)
                    else:
                        await self.trade_batch_processor.process_item(command)
                    
                    self.worker_stats[worker_id]['commands_processed'] += 1
                    self.execution_stats['commands_executed'] += 1
                    
                except Exception as cmd_error:
                    self.worker_stats[worker_id]['commands_failed'] += 1
                    self.execution_stats['commands_failed'] += 1
                
                finally:
                    # 更新工作者统计
                    processing_time = (time.time() - start_time) * 1000
                    old_avg = self.worker_stats[worker_id]['avg_processing_time_ms']
                    processed_count = self.worker_stats[worker_id]['commands_processed']
                    
                    if processed_count > 0:
                        self.worker_stats[worker_id]['avg_processing_time_ms'] = (
                            (old_avg * (processed_count - 1) + processing_time) / processed_count
                        )
                    
                    self.worker_stats[worker_id]['is_busy'] = False
                    self.worker_stats[worker_id]['current_command'] = None
                
            except Exception as e:
                self.execution_stats['execution_errors'] += 1
                await asyncio.sleep(0.1)
    
    async def _process_with_enhanced_processor(self, command: ExecutionCommand, priority: MessagePriority):
        """使用增强处理器处理命令"""
        signal_dict = {
            'signal_id': command.command_id,
            'action': command.command_type,
            'account_id': command.account_id,
            'symbol': command.symbol,
            'volume': command.volume,
            'signal_type': command.command_type.upper()
        }
        
        await self.enhanced_processor.process_trade_signal(
            signal_dict, priority, enable_matching=True, enable_validation=True
        )
    
    async def _load_balancer_loop(self):
        """负载均衡器"""
        while self.running:
            try:
                await asyncio.sleep(2)  # 缩短检查间隔便于测试
                
                # 获取队列统计
                queue_stats = self.command_queue.get_stats()
                current_queue_size = queue_stats['heap_size']
                queue_usage_rate = current_queue_size / self.queue_size if self.queue_size > 0 else 0
                
                # 计算工作者繁忙率
                active_workers = len(self.worker_tasks)
                busy_workers = sum(1 for stats in self.worker_stats.values() if stats['is_busy'])
                worker_busy_rate = busy_workers / active_workers if active_workers > 0 else 0
                
                current_time = time.time()
                should_scale = (current_time - self.last_scaling_time) > self.scaling_cooldown
                
                # 动态扩展逻辑
                if should_scale:
                    if (queue_usage_rate > self.worker_scaling_threshold or 
                        worker_busy_rate > 0.9) and active_workers < self.max_worker_count:
                        
                        new_worker_count = min(active_workers + 1, self.max_worker_count)
                        await self._scale_workers(new_worker_count)
                        self.last_scaling_time = current_time
                        
                        print(f"🔥 扩展工作者: {active_workers} → {new_worker_count} "
                              f"(队列: {queue_usage_rate:.1%}, 繁忙率: {worker_busy_rate:.1%})")
                    
                    elif (queue_usage_rate < self.worker_scale_down_threshold and 
                          worker_busy_rate < 0.3) and active_workers > self.min_worker_count:
                        
                        new_worker_count = max(active_workers - 1, self.min_worker_count)
                        await self._scale_workers(new_worker_count)
                        self.last_scaling_time = current_time
                        
                        print(f"📉 缩减工作者: {active_workers} → {new_worker_count}")
                
                # 队列背压处理
                if queue_usage_rate > 0.95:
                    print(f"🚨 队列接近满载: {queue_usage_rate:.1%}")
                    self.execution_stats['queue_full_count'] += 1
                
            except Exception as e:
                print(f"❌ 负载均衡器异常: {e}")
                await asyncio.sleep(1)
    
    async def _scale_workers(self, target_count: int):
        """动态调整工作者数量"""
        current_count = len(self.worker_tasks)
        
        if target_count > current_count:
            # 扩展工作者
            for i in range(current_count, target_count):
                worker_id = f"worker-{i}"
                task = asyncio.create_task(self._execution_worker(worker_id))
                self.worker_tasks.append(task)
                
                self.worker_stats[worker_id] = {
                    'commands_processed': 0,
                    'commands_failed': 0,
                    'avg_processing_time_ms': 0.0,
                    'last_activity_time': time.time(),
                    'is_busy': False,
                    'current_command': None
                }
        
        elif target_count < current_count:
            # 缩减工作者
            workers_to_remove = current_count - target_count
            for _ in range(workers_to_remove):
                if self.worker_tasks:
                    task = self.worker_tasks.pop()
                    task.cancel()
                    
                    worker_id = f"worker-{len(self.worker_tasks)}"
                    if worker_id in self.worker_stats:
                        del self.worker_stats[worker_id]
    
    async def stop_executor(self):
        """停止执行器"""
        if not self.running:
            return
        
        print(f"🛑 开始停止执行器: {self.account_id}")
        self.running = False
        
        # 停止负载均衡器
        if self.load_balancer_task:
            self.load_balancer_task.cancel()
            try:
                await self.load_balancer_task
            except asyncio.CancelledError:
                pass
        
        # 等待所有工作者完成
        if self.worker_tasks:
            await asyncio.sleep(0.5)  # 给工作者一些时间完成当前任务
            
            for task in self.worker_tasks:
                if not task.done():
                    task.cancel()
            
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
            self.worker_tasks.clear()
        
        print(f"✅ 执行器已停止: {self.account_id}")
    
    async def add_command(self, command: ExecutionCommand, priority: MessagePriority = MessagePriority.NORMAL):
        """添加执行命令"""
        success = await self.command_queue.enqueue(
            priority=priority,
            data=command,
            message_id=command.command_id,
            callback=None
        )
        
        if success:
            self.execution_stats['commands_received'] += 1
        else:
            self.execution_stats['commands_failed'] += 1
        
        return success
    
    def get_stats(self):
        """获取统计信息"""
        active_workers = len(self.worker_tasks)
        busy_workers = sum(1 for stats in self.worker_stats.values() if stats['is_busy'])
        total_processed = sum(stats['commands_processed'] for stats in self.worker_stats.values())
        total_failed = sum(stats['commands_failed'] for stats in self.worker_stats.values())
        
        queue_stats = self.command_queue.get_stats()
        
        return {
            **self.execution_stats,
            'account_id': self.account_id,
            'running': self.running,
            'active_workers': active_workers,
            'busy_workers': busy_workers,
            'worker_busy_rate': busy_workers / active_workers if active_workers > 0 else 0,
            'total_worker_processed': total_processed,
            'total_worker_failed': total_failed,
            'queue_size': queue_stats['heap_size'],
            'queue_usage_rate': queue_stats['heap_size'] / self.queue_size if self.queue_size > 0 else 0,
            'uptime_seconds': time.time() - (self.start_time or time.time()),
        }


class ExecutorOptimizationTest:
    """执行器优化测试"""
    
    def __init__(self):
        self.test_results = {}
    
    async def test_multi_worker_performance(self):
        """测试多工作者性能"""
        print("\n🧪 测试多工作者执行器性能...")
        
        config = {
            'executor_worker_count': 4,
            'max_executor_workers': 8,
            'min_executor_workers': 2,
            'worker_scaling_threshold': 0.7,
        }
        
        executor = OptimizedTradeExecutor('test-account', config)
        await executor.start_executor()
        
        # 生成大量命令测试并发处理能力
        commands = []
        for i in range(100):
            cmd = ExecutionCommand(
                command_id=f'cmd_{i}',
                command_type='open' if i % 2 == 0 else 'close',
                account_id='test-account',
                symbol='EURUSD',
                volume=0.1,
                order_type=OrderTypeEnum.BUY if i % 2 == 0 else OrderTypeEnum.SELL
            )
            commands.append(cmd)
        
        # 批量添加命令
        start_time = time.time()
        
        for i, cmd in enumerate(commands):
            priority = MessagePriority.HIGH if i < 20 else MessagePriority.NORMAL
            await executor.add_command(cmd, priority)
        
        # 等待处理完成
        wait_time = 0
        while executor.command_queue.get_stats()['heap_size'] > 0 and wait_time < 30:
            await asyncio.sleep(0.5)
            wait_time += 0.5
        
        # 再等待一点时间确保所有工作者完成
        await asyncio.sleep(2)
        
        total_time = time.time() - start_time
        stats = executor.get_stats()
        
        await executor.stop_executor()
        
        result = {
            'total_commands': len(commands),
            'total_time_seconds': total_time,
            'commands_per_second': len(commands) / total_time,
            'final_stats': stats,
            'performance_good': stats['total_worker_processed'] >= 90  # 90%处理成功率
        }
        
        print(f"  总命令数: {result['total_commands']}")
        print(f"  总时间: {result['total_time_seconds']:.2f}秒")
        print(f"  处理速度: {result['commands_per_second']:.1f} cmd/s")
        print(f"  工作者处理: {stats['total_worker_processed']}/{len(commands)}")
        print(f"  最终工作者数: {stats['active_workers']}")
        print(f"  队列剩余: {stats['queue_size']}")
        print(f"  性能良好: {'✅' if result['performance_good'] else '❌'}")
        
        self.test_results['multi_worker_performance'] = result
        return result
    
    async def test_dynamic_scaling(self):
        """测试动态扩展功能"""
        print("\n🧪 测试动态工作者扩展...")
        
        config = {
            'executor_worker_count': 2,  # 从2个工作者开始
            'max_executor_workers': 6,
            'min_executor_workers': 2,
            'worker_scaling_threshold': 0.6,  # 较低的阈值便于触发扩展
        }
        
        executor = OptimizedTradeExecutor('scaling-test', config)
        await executor.start_executor()
        
        # 分阶段添加命令，观察扩展行为
        scaling_events = []
        
        # 阶段1: 轻负载
        initial_workers = executor.get_stats()['active_workers']
        scaling_events.append(f"初始工作者: {initial_workers}")
        
        # 添加少量命令
        for i in range(10):
            cmd = ExecutionCommand(
                command_id=f'light_{i}',
                command_type='open',
                account_id='scaling-test',
                symbol='EURUSD',
                volume=0.1,
                order_type=OrderTypeEnum.BUY
            )
            await executor.add_command(cmd)
        
        await asyncio.sleep(3)  # 等待负载均衡器检查
        
        # 阶段2: 重负载触发扩展
        for i in range(50):
            cmd = ExecutionCommand(
                command_id=f'heavy_{i}',
                command_type='open',
                account_id='scaling-test',
                symbol='EURUSD',
                volume=0.1,
                order_type=OrderTypeEnum.BUY
            )
            await executor.add_command(cmd, MessagePriority.HIGH)
        
        await asyncio.sleep(5)  # 等待扩展发生
        
        heavy_load_workers = executor.get_stats()['active_workers']
        scaling_events.append(f"重负载后工作者: {heavy_load_workers}")
        
        # 等待处理完成
        wait_time = 0
        while executor.command_queue.get_stats()['heap_size'] > 5 and wait_time < 20:
            await asyncio.sleep(1)
            wait_time += 1
        
        await asyncio.sleep(8)  # 等待可能的缩减
        
        final_workers = executor.get_stats()['active_workers']
        scaling_events.append(f"最终工作者: {final_workers}")
        
        stats = executor.get_stats()
        await executor.stop_executor()
        
        result = {
            'initial_workers': initial_workers,
            'heavy_load_workers': heavy_load_workers,
            'final_workers': final_workers,
            'scaling_occurred': heavy_load_workers > initial_workers,
            'scaling_events': scaling_events,
            'final_stats': stats
        }
        
        print(f"  扩展事件: {' → '.join(scaling_events)}")
        print(f"  发生了扩展: {'✅' if result['scaling_occurred'] else '❌'}")
        print(f"  最终处理: {stats['total_worker_processed']} 个命令")
        
        self.test_results['dynamic_scaling'] = result
        return result
    
    async def test_queue_backpressure(self):
        """测试队列背压处理"""
        print("\n🧪 测试队列背压处理...")
        
        config = {
            'executor_worker_count': 2,
            'critical_queue_size': 20,  # 较小的队列便于测试背压
            'high_queue_size': 30,
            'normal_queue_size': 50,
            'realtime_query_queue_size': 30,
            'low_queue_size': 70,
        }
        
        executor = OptimizedTradeExecutor('backpressure-test', config)
        await executor.start_executor()
        
        # 快速添加大量命令，超过队列容量
        total_commands = 250
        successful_adds = 0
        failed_adds = 0
        
        start_time = time.time()
        
        for i in range(total_commands):
            cmd = ExecutionCommand(
                command_id=f'backpressure_{i}',
                command_type='open',
                account_id='backpressure-test',
                symbol='EURUSD',
                volume=0.1,
                order_type=OrderTypeEnum.BUY
            )
            
            # 混合优先级
            priority = MessagePriority.CRITICAL if i < 5 else MessagePriority.NORMAL
            success = await executor.add_command(cmd, priority)
            
            if success:
                successful_adds += 1
            else:
                failed_adds += 1
        
        add_time = time.time() - start_time
        
        # 检查背压统计
        initial_stats = executor.get_stats()
        
        # 等待处理
        await asyncio.sleep(10)
        
        final_stats = executor.get_stats()
        await executor.stop_executor()
        
        result = {
            'total_commands': total_commands,
            'successful_adds': successful_adds,
            'failed_adds': failed_adds,
            'add_time_seconds': add_time,
            'queue_full_events': final_stats['queue_full_count'],
            'processed_commands': final_stats['total_worker_processed'],
            'backpressure_handled': final_stats['queue_full_count'] > 0,
            'processing_rate': final_stats['total_worker_processed'] / final_stats['uptime_seconds'] if final_stats['uptime_seconds'] > 0 else 0
        }
        
        print(f"  总命令: {total_commands}, 成功入队: {successful_adds}, 失败: {failed_adds}")
        print(f"  队列满载事件: {result['queue_full_events']}")
        print(f"  处理命令: {result['processed_commands']}")
        print(f"  背压处理: {'✅' if result['backpressure_handled'] else '❌'}")
        print(f"  处理速率: {result['processing_rate']:.1f} cmd/s")
        
        self.test_results['queue_backpressure'] = result
        return result
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始执行器优化验证测试")
        print("=" * 60)
        
        await self.test_multi_worker_performance()
        await self.test_dynamic_scaling()
        await self.test_queue_backpressure()
        
        return self.test_results
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 执行器优化效果总结")
        print("=" * 60)
        
        passed = 0
        total = 0
        
        # 多工作者性能测试
        if 'multi_worker_performance' in self.test_results:
            total += 1
            result = self.test_results['multi_worker_performance']
            if result['performance_good']:
                passed += 1
                print("✅ 多工作者性能: 并发处理能力良好")
            else:
                print("❌ 多工作者性能: 需要改进")
        
        # 动态扩展测试
        if 'dynamic_scaling' in self.test_results:
            total += 1
            result = self.test_results['dynamic_scaling']
            if result['scaling_occurred']:
                passed += 1
                print("✅ 动态扩展: 负载均衡工作正常")
            else:
                print("❌ 动态扩展: 扩展机制需要调整")
        
        # 背压测试
        if 'queue_backpressure' in self.test_results:
            total += 1
            result = self.test_results['queue_backpressure']
            if result['backpressure_handled'] and result['processed_commands'] > 0:
                passed += 1
                print("✅ 队列背压: 背压处理和流控正常")
            else:
                print("❌ 队列背压: 背压处理需要改进")
        
        print(f"\n🎯 优化成功率: {passed}/{total} ({passed/max(total, 1)*100:.0f}%)")
        
        # 关键性能指标
        if 'multi_worker_performance' in self.test_results:
            perf = self.test_results['multi_worker_performance']
            print(f"\n📈 关键指标:")
            print(f"  • 并发处理速度: {perf['commands_per_second']:.1f} cmd/s")
            print(f"  • 工作者利用率: {perf['final_stats']['worker_busy_rate']:.1%}")
        
        if 'dynamic_scaling' in self.test_results:
            scaling = self.test_results['dynamic_scaling']
            print(f"  • 工作者扩展: {scaling['initial_workers']} → {scaling['heavy_load_workers']}")
        
        if 'queue_backpressure' in self.test_results:
            backpressure = self.test_results['queue_backpressure']
            print(f"  • 背压处理速率: {backpressure['processing_rate']:.1f} cmd/s")
        
        return passed >= total * 0.8


async def main():
    """主测试函数"""
    tester = ExecutorOptimizationTest()
    results = await tester.run_all_tests()
    success = tester.print_summary()
    
    if success:
        print("\n🎉 执行器队列满载优化测试通过！")
        print("\n🚀 关键优化已生效：")
        print("   1. ✅ 多工作者并发处理 - 提升处理能力")
        print("   2. ✅ 动态负载均衡 - 自适应扩展缩减") 
        print("   3. ✅ 队列背压处理 - 智能流控机制")
        print("   4. ✅ 增强监控统计 - 实时性能指标")
        return 0
    else:
        print("\n⚠️  部分优化测试失败，需要进一步调整")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)