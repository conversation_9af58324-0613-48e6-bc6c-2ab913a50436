#!/usr/bin/env python3
"""
MT5账户角色管理器
支持灵活的主从账户角色切换
"""
import sys
import yaml
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger

logger = get_logger(__name__)


class RoleManager:
    """账户角色管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.accounts_dir = self.config_dir / "accounts"
        self.role_config_path = self.config_dir / "role_assignment.yaml"
        
        # 确保配置目录存在
        self.accounts_dir.mkdir(parents=True, exist_ok=True)
        
    def load_role_assignment(self) -> Dict:
        """加载当前角色分配"""
        try:
            if self.role_config_path.exists():
                with open(self.role_config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
            else:
                # 创建默认配置
                return self._create_default_assignment()
        except Exception as e:
            logger.error(f"加载角色分配配置失败: {e}")
            return {}
    
    def save_role_assignment(self, assignment: Dict):
        """保存角色分配配置"""
        try:
            with open(self.role_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(assignment, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"角色分配配置已保存到: {self.role_config_path}")
        except Exception as e:
            logger.error(f"保存角色分配配置失败: {e}")
            raise
    
    def _create_default_assignment(self) -> Dict:
        """创建默认角色分配"""
        available_accounts = self.get_available_accounts()
        
        default_assignment = {
            'role_assignment': {
                'master_account': available_accounts[0] if available_accounts else "ACC001",
                'slave_accounts': available_accounts[1:] if len(available_accounts) > 1 else [],
                'assignment_history': [
                    {
                        'date': datetime.now().strftime("%Y-%m-%d"),
                        'master': available_accounts[0] if available_accounts else "ACC001",
                        'slaves': available_accounts[1:] if len(available_accounts) > 1 else [],
                        'reason': "初始配置"
                    }
                ],
                'auto_rotation': {
                    'enabled': False,
                    'rotation_interval_days': 1,
                    'criteria': ["daily_pnl", "win_rate", "drawdown"],
                    'next_rotation_date': None
                },
                'manual_control': {
                    'enabled': True,
                    'last_manual_change': datetime.now().isoformat() + "Z",
                    'changed_by': "system"
                },
                'account_status': {}
            }
        }
        
        # 初始化账户状态
        for account in available_accounts:
            default_assignment['role_assignment']['account_status'][account] = {
                'status': 'active',
                'role': 'master' if account == available_accounts[0] else 'slave',
                'last_health_check': None,
                'performance_score': None
            }
        
        # 保存默认配置
        self.save_role_assignment(default_assignment)
        return default_assignment
    
    def get_available_accounts(self) -> List[str]:
        """获取可用的账户列表"""
        accounts = []
        for yaml_file in self.accounts_dir.glob("*.yaml"):
            if yaml_file.name != "README.md":
                # 提取账户ID（从文件名）
                account_id = yaml_file.stem.upper()
                accounts.append(account_id)
        
        accounts.sort()  # 按字母顺序排序
        return accounts
    
    def get_account_config(self, account_id: str) -> Optional[Dict]:
        """获取指定账户的配置"""
        config_file = self.accounts_dir / f"{account_id}.yaml"
        if not config_file.exists():
            logger.error(f"账户配置文件不存在: {config_file}")
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"读取账户配置失败 {account_id}: {e}")
            return None
    
    def validate_account(self, account_id: str) -> bool:
        """验证账户配置是否有效"""
        config = self.get_account_config(account_id)
        if not config:
            return False
        
        # 检查必要的配置项
        required_fields = [
            'account.id',
            'account.connection.login',
            'account.connection.server'
        ]
        
        for field in required_fields:
            keys = field.split('.')
            current = config
            for key in keys:
                if key not in current:
                    logger.error(f"账户 {account_id} 缺少必要配置: {field}")
                    return False
                current = current[key]
        
        return True
    
    def switch_roles(self, new_master: str, new_slaves: List[str]) -> bool:
        """切换主从角色"""
        # 验证账户
        if not self.validate_account(new_master):
            logger.error(f"主账户 {new_master} 配置无效")
            return False
        
        for slave in new_slaves:
            if not self.validate_account(slave):
                logger.error(f"从账户 {slave} 配置无效")
                return False
        
        # 检查重复
        all_accounts = [new_master] + new_slaves
        if len(set(all_accounts)) != len(all_accounts):
            logger.error("存在重复的账户ID")
            return False
        
        # 加载当前配置
        assignment = self.load_role_assignment()
        role_config = assignment.get('role_assignment', {})
        
        # 记录历史
        old_master = role_config.get('master_account')
        old_slaves = role_config.get('slave_accounts', [])
        
        history_entry = {
            'date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'master': new_master,
            'slaves': new_slaves,
            'reason': f"手动切换 - 原主账户: {old_master}",
            'previous_master': old_master,
            'previous_slaves': old_slaves
        }
        
        # 更新角色分配
        role_config['master_account'] = new_master
        role_config['slave_accounts'] = new_slaves
        role_config['assignment_history'] = role_config.get('assignment_history', [])
        role_config['assignment_history'].append(history_entry)
        
        # 更新手动控制信息
        role_config['manual_control'] = {
            'enabled': True,
            'last_manual_change': datetime.now().isoformat() + "Z",
            'changed_by': "user"
        }
        
        # 更新账户状态
        account_status = role_config.get('account_status', {})
        for account in self.get_available_accounts():
            if account not in account_status:
                account_status[account] = {
                    'status': 'active',
                    'last_health_check': None,
                    'performance_score': None
                }
            
            # 更新角色
            if account == new_master:
                account_status[account]['role'] = 'master'
            elif account in new_slaves:
                account_status[account]['role'] = 'slave'
            else:
                account_status[account]['role'] = 'inactive'
        
        role_config['account_status'] = account_status
        assignment['role_assignment'] = role_config
        
        # 保存配置
        self.save_role_assignment(assignment)
        
        logger.info(f"✅ 角色切换成功:")
        logger.info(f"   主账户: {new_master}")
        logger.info(f"   从账户: {', '.join(new_slaves) if new_slaves else '无'}")
        
        return True
    
    def get_current_roles(self) -> Dict:
        """获取当前角色分配"""
        assignment = self.load_role_assignment()
        role_config = assignment.get('role_assignment', {})
        
        return {
            'master': role_config.get('master_account'),
            'slaves': role_config.get('slave_accounts', []),
            'available_accounts': self.get_available_accounts(),
            'last_change': role_config.get('manual_control', {}).get('last_manual_change')
        }
    
    def show_status(self):
        """显示当前状态"""
        roles = self.get_current_roles()
        assignment = self.load_role_assignment()
        role_config = assignment.get('role_assignment', {})
        
        print("\n" + "="*60)
        print("🏆 MT5账户角色分配状态")
        print("="*60)
        
        print(f"\n📊 当前角色分配:")
        print(f"   主账户 (Master): {roles['master']}")
        if roles['slaves']:
            print(f"   从账户 (Slaves): {', '.join(roles['slaves'])}")
        else:
            print(f"   从账户 (Slaves): 无")
        
        print(f"\n📋 可用账户:")
        for account in roles['available_accounts']:
            status = role_config.get('account_status', {}).get(account, {})
            role = status.get('role', 'unknown')
            is_valid = "✅" if self.validate_account(account) else "❌"
            print(f"   {account}: {role} {is_valid}")
        
        print(f"\n🕒 上次变更: {roles['last_change']}")
        
        # 显示历史记录（最近3次）
        history = role_config.get('assignment_history', [])
        if history:
            print(f"\n📜 最近变更历史:")
            for entry in history[-3:]:
                date = entry.get('date', 'Unknown')
                reason = entry.get('reason', 'Unknown')
                master = entry.get('master', 'Unknown')
                slaves = ', '.join(entry.get('slaves', [])) or '无'
                print(f"   {date}: {master} -> [{slaves}] ({reason})")
    
    def interactive_switch(self):
        """交互式角色切换"""
        print("\n🔄 交互式角色切换")
        print("="*40)
        
        available_accounts = self.get_available_accounts()
        if len(available_accounts) < 1:
            print("❌ 没有找到可用的账户配置文件")
            return False
        
        print(f"\n📋 可用账户: {', '.join(available_accounts)}")
        
        # 选择主账户
        print(f"\n选择新的主账户:")
        for i, account in enumerate(available_accounts, 1):
            print(f"   {i}. {account}")
        
        try:
            master_choice = int(input("\n请输入主账户编号: ")) - 1
            if master_choice < 0 or master_choice >= len(available_accounts):
                print("❌ 无效的选择")
                return False
            
            new_master = available_accounts[master_choice]
            remaining_accounts = [acc for acc in available_accounts if acc != new_master]
            
            # 选择从账户
            if remaining_accounts:
                print(f"\n选择从账户 (可多选，用逗号分隔，直接回车跳过):")
                for i, account in enumerate(remaining_accounts, 1):
                    print(f"   {i}. {account}")
                
                slaves_input = input("\n请输入从账户编号 (例如: 1,2): ").strip()
                new_slaves = []
                
                if slaves_input:
                    try:
                        slave_indices = [int(x.strip()) - 1 for x in slaves_input.split(',')]
                        for idx in slave_indices:
                            if 0 <= idx < len(remaining_accounts):
                                new_slaves.append(remaining_accounts[idx])
                            else:
                                print(f"⚠️ 忽略无效选择: {idx + 1}")
                    except ValueError:
                        print("❌ 无效的输入格式")
                        return False
            else:
                new_slaves = []
            
            # 确认切换
            print(f"\n📋 确认新的角色分配:")
            print(f"   主账户: {new_master}")
            print(f"   从账户: {', '.join(new_slaves) if new_slaves else '无'}")
            
            confirm = input("\n确认切换? (y/N): ").lower().strip()
            if confirm == 'y':
                return self.switch_roles(new_master, new_slaves)
            else:
                print("❌ 已取消切换")
                return False
                
        except (ValueError, KeyboardInterrupt):
            print("\n❌ 操作已取消")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MT5账户角色管理器')
    parser.add_argument('--config-dir', '-c', default='config', 
                       help='配置目录路径 (默认: config)')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 显示状态命令
    status_parser = subparsers.add_parser('status', help='显示当前角色分配状态')
    
    # 切换角色命令
    switch_parser = subparsers.add_parser('switch', help='切换账户角色')
    switch_parser.add_argument('--master', '-m', required=True, 
                             help='新的主账户ID')
    switch_parser.add_argument('--slaves', '-s', nargs='*', default=[], 
                             help='新的从账户ID列表')
    
    # 交互式切换命令
    interactive_parser = subparsers.add_parser('interactive', help='交互式角色切换')
    
    # 列出账户命令
    list_parser = subparsers.add_parser('list', help='列出所有可用账户')
    
    args = parser.parse_args()
    
    # 如果没有提供命令，显示帮助
    if not args.command:
        parser.print_help()
        return
    
    try:
        role_manager = RoleManager(args.config_dir)
        
        if args.command == 'status':
            role_manager.show_status()
            
        elif args.command == 'switch':
            success = role_manager.switch_roles(args.master, args.slaves)
            if success:
                print("\n✅ 角色切换成功!")
                role_manager.show_status()
            else:
                print("\n❌ 角色切换失败!")
                sys.exit(1)
                
        elif args.command == 'interactive':
            success = role_manager.interactive_switch()
            if success:
                print("\n✅ 角色切换成功!")
                role_manager.show_status()
                
        elif args.command == 'list':
            accounts = role_manager.get_available_accounts()
            print(f"\n📋 可用账户 ({len(accounts)}个):")
            for account in accounts:
                is_valid = "✅" if role_manager.validate_account(account) else "❌"
                print(f"   {account} {is_valid}")
    
    except Exception as e:
        logger.error(f"操作失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()