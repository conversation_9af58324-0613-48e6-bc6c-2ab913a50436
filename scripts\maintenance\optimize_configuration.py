#!/usr/bin/env python3
"""
配置优化脚本
根据实际环境自动调整配置参数，优化系统性能
"""
import os
import sys
import yaml
import psutil
import argparse
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)


class ConfigurationOptimizer:
    """配置优化器"""
    
    def __init__(self, base_config_path: str = "config/production_optimized.yaml"):
        self.base_config_path = base_config_path
        self.system_info = self._gather_system_info()
        self.optimized_config = {}
        
    def _gather_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        logger.info("📊 收集系统信息...")
        
        try:
            info = {
                'cpu_count': psutil.cpu_count(),
                'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else {},
                'memory': psutil.virtual_memory()._asdict(),
                'disk': psutil.disk_usage('/')._asdict(),
                'network': self._get_network_info(),
                'platform': {
                    'system': os.name,
                    'python_version': sys.version
                }
            }
            
            logger.info(f"✅ 系统信息收集完成:")
            logger.info(f"  CPU核心数: {info['cpu_count']}")
            logger.info(f"  内存总量: {info['memory']['total'] // (1024**3)}GB")
            logger.info(f"  可用内存: {info['memory']['available'] // (1024**3)}GB")
            logger.info(f"  磁盘总量: {info['disk']['total'] // (1024**3)}GB")
            
            return info
            
        except Exception as e:
            logger.error(f"❌ 系统信息收集失败: {e}")
            return {}
    
    def _get_network_info(self) -> Dict[str, Any]:
        """获取网络信息"""
        try:
            net_io = psutil.net_io_counters()
            return {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            }
        except:
            return {}
    
    def optimize_configuration(self) -> Dict[str, Any]:
        """优化配置"""
        logger.info("⚙️ 开始配置优化...")
        
        # 加载基础配置
        self._load_base_config()
        
        # 基于系统资源优化
        self._optimize_for_system_resources()
        
        # 优化性能参数
        self._optimize_performance_parameters()
        
        # 优化网络配置
        self._optimize_network_configuration()
        
        # 优化存储配置
        self._optimize_storage_configuration()
        
        logger.info("✅ 配置优化完成")
        return self.optimized_config
    
    def _load_base_config(self):
        """加载基础配置"""
        try:
            with open(self.base_config_path, 'r', encoding='utf-8') as f:
                self.optimized_config = yaml.safe_load(f)
            logger.info(f"✅ 基础配置加载成功: {self.base_config_path}")
        except Exception as e:
            logger.error(f"❌ 基础配置加载失败: {e}")
            self.optimized_config = {}
    
    def _optimize_for_system_resources(self):
        """基于系统资源优化"""
        logger.info("🔧 基于系统资源优化配置...")
        
        cpu_count = self.system_info.get('cpu_count', 4)
        memory_gb = self.system_info.get('memory', {}).get('total', 0) // (1024**3)
        
        # 优化异步工作线程数
        max_workers = min(cpu_count * 2, 20)
        self._update_config_path('performance.asyncio.max_workers', max_workers)
        
        # 优化Redis连接池
        redis_connections = min(cpu_count * 10, 100)
        self._update_config_path('redis.connection_pool.max_connections', redis_connections)
        
        # 优化内存使用
        max_memory_mb = max(memory_gb * 1024 // 4, 1024)  # 使用1/4内存
        self._update_config_path('performance.memory.max_memory_usage_mb', max_memory_mb)
        
        # 优化批处理大小
        if memory_gb >= 8:
            batch_size = 50
        elif memory_gb >= 4:
            batch_size = 30
        else:
            batch_size = 20
        
        self._update_config_path('data_plane.master_monitor.batch_size', batch_size)
        
        logger.info(f"  最大工作线程: {max_workers}")
        logger.info(f"  Redis连接池: {redis_connections}")
        logger.info(f"  最大内存使用: {max_memory_mb}MB")
        logger.info(f"  批处理大小: {batch_size}")
    
    def _optimize_performance_parameters(self):
        """优化性能参数"""
        logger.info("⚡ 优化性能参数...")
        
        # 根据CPU性能调整超时参数
        cpu_freq = self.system_info.get('cpu_freq', {}).get('current', 2000)
        
        if cpu_freq > 3000:  # 高性能CPU
            batch_timeout = 30
            execution_timeout = 20
            heartbeat_interval = 10
        elif cpu_freq > 2000:  # 中等性能CPU
            batch_timeout = 50
            execution_timeout = 30
            heartbeat_interval = 15
        else:  # 低性能CPU
            batch_timeout = 100
            execution_timeout = 45
            heartbeat_interval = 20
        
        self._update_config_path('data_plane.master_monitor.batch_timeout_ms', batch_timeout)
        self._update_config_path('data_plane.slave_executor.execution_timeout', execution_timeout)
        self._update_config_path('control_plane.coordinator.heartbeat_interval', heartbeat_interval)
        
        logger.info(f"  批处理超时: {batch_timeout}ms")
        logger.info(f"  执行超时: {execution_timeout}s")
        logger.info(f"  心跳间隔: {heartbeat_interval}s")
    
    def _optimize_network_configuration(self):
        """优化网络配置"""
        logger.info("🌐 优化网络配置...")
        
        # 检测网络环境
        network_info = self.system_info.get('network', {})
        
        # 基于网络状况调整超时参数
        if network_info:
            # 假设良好的网络环境
            connection_timeout = 10
            socket_timeout = 30
            ping_interval = 30
        else:
            # 保守的网络配置
            connection_timeout = 15
            socket_timeout = 45
            ping_interval = 45
        
        self._update_config_path('nats.connection.timeout', connection_timeout)
        self._update_config_path('performance.network.socket_timeout', socket_timeout)
        self._update_config_path('nats.connection.ping_interval', ping_interval)
        
        logger.info(f"  连接超时: {connection_timeout}s")
        logger.info(f"  Socket超时: {socket_timeout}s")
        logger.info(f"  Ping间隔: {ping_interval}s")
    
    def _optimize_storage_configuration(self):
        """优化存储配置"""
        logger.info("💾 优化存储配置...")
        
        disk_info = self.system_info.get('disk', {})
        disk_total_gb = disk_info.get('total', 0) // (1024**3)
        disk_free_gb = disk_info.get('free', 0) // (1024**3)
        
        # 基于磁盘空间调整存储参数
        if disk_free_gb > 100:  # 充足的磁盘空间
            max_msgs = 20000000  # 2000万消息
            max_bytes = 21474836480  # 20GB
            max_age = 14400  # 4小时
        elif disk_free_gb > 50:  # 中等磁盘空间
            max_msgs = 10000000  # 1000万消息
            max_bytes = 10737418240  # 10GB
            max_age = 7200  # 2小时
        else:  # 有限的磁盘空间
            max_msgs = 5000000  # 500万消息
            max_bytes = 5368709120  # 5GB
            max_age = 3600  # 1小时
        
        self._update_config_path('nats.jetstream.max_msgs', max_msgs)
        self._update_config_path('nats.jetstream.max_bytes', max_bytes)
        self._update_config_path('nats.jetstream.max_age', max_age)
        
        logger.info(f"  最大消息数: {max_msgs}")
        logger.info(f"  最大存储: {max_bytes // (1024**3)}GB")
        logger.info(f"  数据保留: {max_age // 3600}小时")
    
    def _update_config_path(self, path: str, value: Any):
        """更新配置路径的值"""
        keys = path.split('.')
        config = self.optimized_config
        
        # 导航到目标位置
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
    
    def save_optimized_config(self, output_path: str = "config/optimized_system.yaml"):
        """保存优化后的配置"""
        logger.info(f"💾 保存优化配置到: {output_path}")
        
        try:
            # 确保目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 添加优化信息
            self.optimized_config['optimization'] = {
                'timestamp': str(Path(__file__).stat().st_mtime),
                'system_info': {
                    'cpu_count': self.system_info.get('cpu_count'),
                    'memory_gb': self.system_info.get('memory', {}).get('total', 0) // (1024**3),
                    'disk_free_gb': self.system_info.get('disk', {}).get('free', 0) // (1024**3)
                },
                'optimizations_applied': [
                    'system_resources',
                    'performance_parameters',
                    'network_configuration',
                    'storage_configuration'
                ]
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.optimized_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info("✅ 优化配置保存成功")
            
        except Exception as e:
            logger.error(f"❌ 保存优化配置失败: {e}")
    
    def generate_optimization_report(self):
        """生成优化报告"""
        logger.info("📊 配置优化报告")
        logger.info("=" * 50)
        
        # 系统信息
        logger.info("🖥️ 系统信息:")
        logger.info(f"  CPU核心数: {self.system_info.get('cpu_count', 'N/A')}")
        logger.info(f"  内存总量: {self.system_info.get('memory', {}).get('total', 0) // (1024**3)}GB")
        logger.info(f"  可用内存: {self.system_info.get('memory', {}).get('available', 0) // (1024**3)}GB")
        logger.info(f"  磁盘总量: {self.system_info.get('disk', {}).get('total', 0) // (1024**3)}GB")
        logger.info(f"  可用磁盘: {self.system_info.get('disk', {}).get('free', 0) // (1024**3)}GB")
        
        # 优化建议
        logger.info("💡 优化建议:")
        
        memory_gb = self.system_info.get('memory', {}).get('total', 0) // (1024**3)
        if memory_gb < 4:
            logger.info("  ⚠️ 内存不足，建议升级到至少4GB")
        elif memory_gb >= 8:
            logger.info("  ✅ 内存充足，已启用高性能配置")
        
        cpu_count = self.system_info.get('cpu_count', 0)
        if cpu_count < 4:
            logger.info("  ⚠️ CPU核心数较少，建议升级到至少4核")
        elif cpu_count >= 8:
            logger.info("  ✅ CPU性能良好，已启用多线程优化")
        
        disk_free_gb = self.system_info.get('disk', {}).get('free', 0) // (1024**3)
        if disk_free_gb < 20:
            logger.info("  ⚠️ 磁盘空间不足，建议清理或扩容")
        elif disk_free_gb >= 100:
            logger.info("  ✅ 磁盘空间充足，已启用长期数据保留")
        
        logger.info("=" * 50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MT5系统配置优化工具")
    parser.add_argument("--base-config", default="config/production_optimized.yaml", 
                       help="基础配置文件路径")
    parser.add_argument("--output", default="config/optimized_system.yaml", 
                       help="输出配置文件路径")
    parser.add_argument("--report-only", action="store_true", 
                       help="仅生成优化报告")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging({'level': 'INFO', 'format': 'json'})
    
    # 创建优化器
    optimizer = ConfigurationOptimizer(args.base_config)
    
    if args.report_only:
        # 仅生成报告
        optimizer.generate_optimization_report()
    else:
        # 执行完整优化
        optimizer.optimize_configuration()
        optimizer.save_optimized_config(args.output)
        optimizer.generate_optimization_report()
        
        logger.info("🎉 配置优化完成！")
        logger.info(f"💡 优化后的配置已保存到: {args.output}")
        logger.info("🚀 现在可以使用优化后的配置启动系统")


if __name__ == "__main__":
    main()
