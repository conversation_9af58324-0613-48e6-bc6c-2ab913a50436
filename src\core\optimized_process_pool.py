#!/usr/bin/env python3
"""
优化的进程池管理器
减少进程隔离的性能开销，实现轻量级进程复用
硬迁移：零向后兼容，强制统一，SSOT
"""
import asyncio
import multiprocessing as mp
import time
import logging
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import pickle
import signal
import threading
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import psutil
import queue

logger = logging.getLogger(__name__)

class ProcessState(Enum):
    """进程状态"""
    INITIALIZING = "initializing"
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    TERMINATED = "terminated"

class TaskPriority(Enum):
    """任务优先级"""
    CRITICAL = 0
    HIGH = 1
    NORMAL = 2
    LOW = 3

@dataclass
class ProcessInfo:
    """进程信息"""
    process_id: int
    worker_id: str
    state: ProcessState
    created_time: float
    last_used_time: float
    task_count: int = 0
    error_count: int = 0
    memory_usage: float = 0.0  # MB
    cpu_usage: float = 0.0     # %
    
@dataclass
class TaskRequest:
    """任务请求"""
    task_id: str
    function_name: str
    args: tuple
    kwargs: dict
    priority: TaskPriority = TaskPriority.NORMAL
    timeout: float = 30.0
    retries: int = 0
    created_time: float = field(default_factory=time.time)

@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    worker_id: str = ""
    
class WorkerProcess:
    """工作进程"""
    
    def __init__(self, worker_id: str, task_queue: mp.Queue, result_queue: mp.Queue):
        self.worker_id = worker_id
        self.task_queue = task_queue
        self.result_queue = result_queue
        self.running = True
        
        # 性能统计
        self.task_count = 0
        self.error_count = 0
        
        logger.info(f"工作进程已创建: {worker_id}")
    
    def run(self):
        """运行工作进程"""
        # 设置进程信号处理
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        logger.info(f"工作进程启动: {self.worker_id} (PID: {mp.current_process().pid})")
        
        while self.running:
            try:
                # 从任务队列获取任务（阻塞等待）
                task_request = self.task_queue.get(timeout=1.0)
                
                if task_request is None:  # 停止信号
                    break
                
                # 执行任务
                result = self._execute_task(task_request)
                
                # 发送结果
                self.result_queue.put(result)
                
                self.task_count += 1
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"工作进程异常: {self.worker_id} - {e}")
                self.error_count += 1
        
        logger.info(f"工作进程停止: {self.worker_id}")
    
    def _execute_task(self, task_request: TaskRequest) -> TaskResult:
        """执行任务"""
        start_time = time.time()
        
        try:
            # 这里可以根据function_name动态调用函数
            # 为了简化，我们模拟任务执行
            if task_request.function_name == "mt5_connect":
                result = self._mock_mt5_connect(*task_request.args, **task_request.kwargs)
            elif task_request.function_name == "mt5_trade":
                result = self._mock_mt5_trade(*task_request.args, **task_request.kwargs)
            elif task_request.function_name == "mt5_monitor":
                result = self._mock_mt5_monitor(*task_request.args, **task_request.kwargs)
            else:
                raise ValueError(f"未知任务类型: {task_request.function_name}")
            
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task_request.task_id,
                success=True,
                result=result,
                execution_time=execution_time,
                worker_id=self.worker_id
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task_request.task_id,
                success=False,
                error=str(e),
                execution_time=execution_time,
                worker_id=self.worker_id
            )
    
    def _mock_mt5_connect(self, account_id: str, login: int, server: str) -> Dict[str, Any]:
        """模拟MT5连接"""
        # 模拟连接时间
        time.sleep(0.1)
        
        return {
            'connection_id': f"conn_{account_id}_{self.worker_id}",
            'account_id': account_id,
            'login': login,
            'server': server,
            'connected': True,
            'worker_id': self.worker_id
        }
    
    def _mock_mt5_trade(self, account_id: str, symbol: str, volume: float, action: str) -> Dict[str, Any]:
        """模拟MT5交易"""
        # 模拟交易执行时间
        time.sleep(0.05)
        
        return {
            'ticket': int(time.time() * 1000000) % 1000000,
            'account_id': account_id,
            'symbol': symbol,
            'volume': volume,
            'action': action,
            'price': 1.2345,  # 模拟价格
            'executed': True,
            'worker_id': self.worker_id
        }
    
    def _mock_mt5_monitor(self, account_id: str, symbols: List[str]) -> Dict[str, Any]:
        """模拟MT5监控"""
        # 模拟监控数据收集
        time.sleep(0.02)
        
        positions = []
        for symbol in symbols:
            positions.append({
                'symbol': symbol,
                'volume': 0.1,
                'profit': 10.5,
                'open_price': 1.2340
            })
        
        return {
            'account_id': account_id,
            'positions': positions,
            'balance': 10000.0,
            'equity': 10010.5,
            'worker_id': self.worker_id
        }
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"工作进程收到信号: {signum}")
        self.running = False

class OptimizedProcessPool:
    """
    优化的进程池管理器
    减少进程创建开销，实现进程复用和负载均衡
    """
    
    def __init__(self, max_workers: int = None, worker_timeout: float = 300.0):
        self.max_workers = max_workers or min(mp.cpu_count(), 8)
        self.worker_timeout = worker_timeout
        
        # 进程管理
        self.workers: Dict[str, mp.Process] = {}
        self.worker_info: Dict[str, ProcessInfo] = {}
        self.task_queues: Dict[str, mp.Queue] = {}
        self.result_queue = mp.Queue()
        
        # 任务管理
        self.pending_tasks: Dict[str, TaskRequest] = {}
        self.priority_queues: Dict[TaskPriority, List[TaskRequest]] = {
            priority: [] for priority in TaskPriority
        }
        
        # 性能统计
        self.total_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        
        # 控制和监控
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.result_thread: Optional[threading.Thread] = None
        self.task_dispatcher_thread: Optional[threading.Thread] = None
        
        # 同步原语
        self.task_lock = threading.Lock()
        self.worker_lock = threading.Lock()
        
        logger.info(f"🔧 优化进程池已创建 - 最大工作进程: {self.max_workers}")
    
    async def start(self) -> bool:
        """启动进程池"""
        try:
            self.running = True
            
            # 创建初始工作进程
            await self._create_initial_workers()
            
            # 启动监控线程
            self._start_monitoring_threads()
            
            logger.info("✅ 优化进程池已启动")
            return True
            
        except Exception as e:
            logger.error(f"❌ 进程池启动失败: {e}")
            return False
    
    async def stop(self):
        """停止进程池"""
        logger.info("🛑 停止优化进程池")
        
        self.running = False
        
        # 停止监控线程
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        if self.result_thread and self.result_thread.is_alive():
            self.result_thread.join(timeout=5)
        
        if self.task_dispatcher_thread and self.task_dispatcher_thread.is_alive():
            self.task_dispatcher_thread.join(timeout=5)
        
        # 停止所有工作进程
        await self._stop_all_workers()
        
        logger.info("✅ 优化进程池已停止")
    
    async def _create_initial_workers(self):
        """创建初始工作进程"""
        initial_count = min(2, self.max_workers)  # 启动时只创建2个进程
        
        for i in range(initial_count):
            await self._create_worker(f"worker_{i:02d}")
    
    async def _create_worker(self, worker_id: str) -> bool:
        """创建工作进程"""
        try:
            with self.worker_lock:
                if worker_id in self.workers:
                    logger.warning(f"工作进程已存在: {worker_id}")
                    return False
                
                # 创建任务队列
                task_queue = mp.Queue()
                self.task_queues[worker_id] = task_queue
                
                # 创建工作进程
                worker_process = WorkerProcess(worker_id, task_queue, self.result_queue)
                process = mp.Process(target=worker_process.run, name=f"MT5Worker-{worker_id}")
                process.start()
                
                # 记录进程信息
                self.workers[worker_id] = process
                self.worker_info[worker_id] = ProcessInfo(
                    process_id=process.pid,
                    worker_id=worker_id,
                    state=ProcessState.IDLE,
                    created_time=time.time(),
                    last_used_time=time.time()
                )
                
                logger.info(f"✅ 工作进程已创建: {worker_id} (PID: {process.pid})")
                return True
                
        except Exception as e:
            logger.error(f"❌ 创建工作进程失败: {worker_id} - {e}")
            return False
    
    async def _stop_worker(self, worker_id: str):
        """停止工作进程"""
        try:
            with self.worker_lock:
                if worker_id not in self.workers:
                    return
                
                # 发送停止信号
                task_queue = self.task_queues.get(worker_id)
                if task_queue:
                    task_queue.put(None)  # 停止信号
                
                # 等待进程结束
                process = self.workers[worker_id]
                process.join(timeout=5)
                
                if process.is_alive():
                    # 强制终止
                    process.terminate()
                    process.join(timeout=2)
                    
                    if process.is_alive():
                        process.kill()
                
                # 清理资源
                del self.workers[worker_id]
                del self.worker_info[worker_id]
                if worker_id in self.task_queues:
                    del self.task_queues[worker_id]
                
                logger.info(f"🛑 工作进程已停止: {worker_id}")
                
        except Exception as e:
            logger.error(f"❌ 停止工作进程失败: {worker_id} - {e}")
    
    async def _stop_all_workers(self):
        """停止所有工作进程"""
        worker_ids = list(self.workers.keys())
        
        for worker_id in worker_ids:
            await self._stop_worker(worker_id)
    
    def _start_monitoring_threads(self):
        """启动监控线程"""
        # 结果处理线程
        self.result_thread = threading.Thread(
            target=self._result_handler_thread,
            name="ProcessPool-ResultHandler",
            daemon=True
        )
        self.result_thread.start()
        
        # 任务分发线程
        self.task_dispatcher_thread = threading.Thread(
            target=self._task_dispatcher_thread,
            name="ProcessPool-TaskDispatcher", 
            daemon=True
        )
        self.task_dispatcher_thread.start()
        
        # 性能监控线程
        self.monitor_thread = threading.Thread(
            target=self._performance_monitor_thread,
            name="ProcessPool-Monitor",
            daemon=True
        )
        self.monitor_thread.start()
    
    def _result_handler_thread(self):
        """结果处理线程"""
        logger.info("结果处理线程已启动")
        
        while self.running:
            try:
                # 获取结果（非阻塞）
                try:
                    result = self.result_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 处理结果
                self._process_task_result(result)
                
            except Exception as e:
                logger.error(f"结果处理异常: {e}")
        
        logger.info("结果处理线程已停止")
    
    def _task_dispatcher_thread(self):
        """任务分发线程"""
        logger.info("任务分发线程已启动")
        
        while self.running:
            try:
                # 检查是否有待分发的任务
                task_to_dispatch = self._get_next_priority_task()
                
                if task_to_dispatch:
                    # 选择最佳工作进程
                    worker_id = self._select_best_worker()
                    
                    if worker_id:
                        # 分发任务
                        self._dispatch_task_to_worker(task_to_dispatch, worker_id)
                    else:
                        # 没有可用工作进程，尝试创建新的
                        if len(self.workers) < self.max_workers:
                            new_worker_id = f"worker_{len(self.workers):02d}"
                            success = asyncio.run(self._create_worker(new_worker_id))
                            if success:
                                self._dispatch_task_to_worker(task_to_dispatch, new_worker_id)
                            else:
                                # 创建失败，重新放回队列
                                self._requeue_task(task_to_dispatch)
                        else:
                            # 已达到最大进程数，重新放回队列
                            self._requeue_task(task_to_dispatch)
                
                time.sleep(0.01)  # 10ms循环间隔
                
            except Exception as e:
                logger.error(f"任务分发异常: {e}")
        
        logger.info("任务分发线程已停止")
    
    def _performance_monitor_thread(self):
        """性能监控线程"""
        logger.info("性能监控线程已启动")
        
        while self.running:
            try:
                # 更新进程性能信息
                self._update_worker_performance()
                
                # 检查空闲进程回收
                self._check_idle_workers()
                
                # 每10秒监控一次
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
        
        logger.info("性能监控线程已停止")
    
    def _update_worker_performance(self):
        """更新工作进程性能信息"""
        with self.worker_lock:
            for worker_id, process in self.workers.items():
                try:
                    if process.is_alive():
                        # 获取进程性能信息
                        proc = psutil.Process(process.pid)
                        
                        info = self.worker_info[worker_id]
                        info.memory_usage = proc.memory_info().rss / 1024 / 1024  # MB
                        info.cpu_usage = proc.cpu_percent()
                        
                except Exception as e:
                    logger.warning(f"获取进程性能信息失败: {worker_id} - {e}")
    
    def _check_idle_workers(self):
        """检查空闲进程回收"""
        current_time = time.time()
        min_workers = 1  # 保持最少1个进程
        
        with self.worker_lock:
            idle_workers = []
            
            for worker_id, info in self.worker_info.items():
                if (info.state == ProcessState.IDLE and 
                    current_time - info.last_used_time > self.worker_timeout):
                    idle_workers.append(worker_id)
            
            # 回收多余的空闲进程
            workers_to_remove = max(0, len(self.workers) - min_workers)
            
            for i, worker_id in enumerate(idle_workers):
                if i >= workers_to_remove:
                    break
                
                logger.info(f"回收空闲进程: {worker_id}")
                asyncio.run(self._stop_worker(worker_id))
    
    async def submit_task(self, function_name: str, *args, priority: TaskPriority = TaskPriority.NORMAL, 
                         timeout: float = 30.0, **kwargs) -> str:
        """提交任务"""
        task_id = f"task_{int(time.time() * 1000000)}_{len(self.pending_tasks)}"
        
        task_request = TaskRequest(
            task_id=task_id,
            function_name=function_name,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout
        )
        
        with self.task_lock:
            self.pending_tasks[task_id] = task_request
            self.priority_queues[priority].append(task_request)
            self.total_tasks += 1
        
        logger.debug(f"任务已提交: {task_id} ({function_name})")
        return task_id
    
    def _get_next_priority_task(self) -> Optional[TaskRequest]:
        """获取下一个优先级任务"""
        with self.task_lock:
            # 按优先级顺序检查
            for priority in TaskPriority:
                if self.priority_queues[priority]:
                    return self.priority_queues[priority].pop(0)
        
        return None
    
    def _select_best_worker(self) -> Optional[str]:
        """选择最佳工作进程"""
        with self.worker_lock:
            idle_workers = [
                worker_id for worker_id, info in self.worker_info.items()
                if info.state == ProcessState.IDLE
            ]
            
            if not idle_workers:
                return None
            
            # 选择任务数最少的空闲进程
            best_worker = min(
                idle_workers,
                key=lambda w: self.worker_info[w].task_count
            )
            
            return best_worker
    
    def _dispatch_task_to_worker(self, task_request: TaskRequest, worker_id: str):
        """分发任务到工作进程"""
        try:
            task_queue = self.task_queues[worker_id]
            task_queue.put(task_request)
            
            # 更新工作进程状态
            info = self.worker_info[worker_id]
            info.state = ProcessState.BUSY
            info.last_used_time = time.time()
            
            logger.debug(f"任务已分发: {task_request.task_id} → {worker_id}")
            
        except Exception as e:
            logger.error(f"任务分发失败: {task_request.task_id} → {worker_id} - {e}")
            self._requeue_task(task_request)
    
    def _requeue_task(self, task_request: TaskRequest):
        """重新排队任务"""
        with self.task_lock:
            self.priority_queues[task_request.priority].append(task_request)
    
    def _process_task_result(self, result: TaskResult):
        """处理任务结果"""
        task_id = result.task_id
        
        # 更新统计
        if result.success:
            self.completed_tasks += 1
        else:
            self.failed_tasks += 1
        
        # 更新工作进程状态
        worker_id = result.worker_id
        if worker_id in self.worker_info:
            info = self.worker_info[worker_id]
            info.state = ProcessState.IDLE
            info.task_count += 1
            
            if not result.success:
                info.error_count += 1
        
        # 清理待处理任务
        with self.task_lock:
            self.pending_tasks.pop(task_id, None)
        
        logger.debug(f"任务结果已处理: {task_id} ({'成功' if result.success else '失败'})")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取进程池统计信息"""
        with self.worker_lock:
            worker_states = {}
            total_memory = 0
            
            for worker_id, info in self.worker_info.items():
                worker_states[worker_id] = {
                    'state': info.state.value,
                    'task_count': info.task_count,
                    'error_count': info.error_count,
                    'memory_usage': info.memory_usage,
                    'cpu_usage': info.cpu_usage,
                    'uptime': time.time() - info.created_time
                }
                total_memory += info.memory_usage
        
        with self.task_lock:
            pending_by_priority = {
                priority.name: len(tasks)
                for priority, tasks in self.priority_queues.items()
            }
        
        return {
            'total_workers': len(self.workers),
            'max_workers': self.max_workers,
            'total_tasks': self.total_tasks,
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'pending_tasks': len(self.pending_tasks),
            'success_rate': self.completed_tasks / max(self.total_tasks, 1) * 100,
            'total_memory_usage': total_memory,
            'pending_by_priority': pending_by_priority,
            'worker_details': worker_states
        }

# 全局进程池实例
_global_process_pool: Optional[OptimizedProcessPool] = None

def get_optimized_process_pool() -> OptimizedProcessPool:
    """获取全局优化进程池"""
    global _global_process_pool
    if _global_process_pool is None:
        _global_process_pool = OptimizedProcessPool()
    return _global_process_pool

async def initialize_optimized_process_pool(max_workers: int = None) -> bool:
    """初始化优化进程池"""
    pool = get_optimized_process_pool()
    if max_workers:
        pool.max_workers = max_workers
    return await pool.start()