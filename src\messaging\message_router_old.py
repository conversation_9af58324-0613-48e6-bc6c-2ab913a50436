#!/usr/bin/env python3
# 🚨 AUTO-MIGRATED: Old messaging components replaced with QueueManager
# See MIGRATION_GUIDE.md for details

"""
混合消息路由器 - 集成混合队列管理器，消除NATS单点依赖
智能路由：本地直达，跨主机NATS，支持Redis备份和本地降级
集成MT5 API速率限制处理和自动故障转移
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Set, Callable
from dataclasses import dataclass
from datetime import datetime
import json

from .queue_manager import QueueManager, HybridQueueConfig, QueueBackendType
from .priority_queue import MessagePriority, PriorityAnalyzer, get_priority_queue
from .message_types import MessageEnvelope


from src.utils.logger import get_logger
from src.utils.metrics import get_metrics_collector

logger = get_logger(__name__)
metrics = get_metrics_collector()


class NATSManagerProxy:
    """NATS管理器代理，用于向后兼容"""
    
    def __init__(self, queue_manager, host_id: str):
        self.jetstream = queue_manager
        self.host_id = host_id
    
    async def publish_with_priority(self, data: Dict[str, Any], priority: MessagePriority = None) -> bool:
        """发布优先级消息（兼容接口）"""
        if self.jetstream:
            return await self.jetstream.publish(data, priority)
        return False


@dataclass
class RouteRule:
    """路由规则"""
    source: str
    target: str
    host_id: str
    priority: MessagePriority
    route_type: str  # "local", "remote", "broadcast"
    enabled: bool = True
    retry_count: int = 3
    timeout_seconds: float = 30.0
    weight: int = 1
    
    def matches_pattern(self, source: str, target: str, host_id: str) -> bool:
        """检查规则是否匹配给定的模式"""
        import fnmatch
        return (
            fnmatch.fnmatch(source, self.source) and
            fnmatch.fnmatch(target, self.target) and
            fnmatch.fnmatch(host_id, self.host_id)
        )


@dataclass
class RateLimitConfig:
    """API速率限制配置"""
    max_calls_per_second: int = 80
    burst_size: int = 10
    window_size: int = 1  # 秒
    cooldown_period: int = 5  # 秒


class MessageRouter:
    """
    消息路由器 - 集成队列管理器，消除NATS单点依赖
    
    核心功能：
    1. 智能路由决策（本地直达 vs 跨主机多后端）
    2. NATS + Redis双写备份，本地降级支持
    3. MT5 API速率限制处理
    4. 优先级感知路由
    5. 自动故障转移和恢复
    6. 批量消息处理优化
    """
    
    def __init__(self, config: Dict[str, Any] = None, host_id: str = None):
        self.config = config or {}
        self.host_id = host_id or self.config.get('host_id', 'default-host')
        self.running = False
        
        # 创建混合队列管理器
        self.queue_manager = self._create_queue_manager()
        
        # 保持向后兼容的属性（旧代码可能使用）
        self.queue_manager = None  # 将在启动时通过queue_manager获取
        
        # 路由表
        self.local_routes: Dict[str, str] = {}  # account_id -> local_target
        self.cross_host_routes: Dict[str, str] = {}  # account_id -> target_host_id
        self.route_rules: List[RouteRule] = []
        self.rule_cache: Dict[str, RouteRule] = {}  # 缓存匹配的规则
        
        # 优先级队列集成
        self.priority_queue = get_priority_queue()
        
        # API速率限制
        self.rate_limits: Dict[str, RateLimitConfig] = {}  # account_id -> config
        self.api_call_history: Dict[str, List[float]] = {}  # account_id -> [timestamps]
        self.cooldown_accounts: Set[str] = set()
        
        # 重试管理
        self.retry_queues: Dict[str, List[Tuple[Dict[str, Any], int, float]]] = {}
        self.max_retry_attempts = self.config.get('max_retry_attempts', 3)
        self.retry_delay_base = self.config.get('retry_delay_base', 1.0)
        
        # 消息处理器
        self.message_handlers: Dict[str, Callable] = {}
        
        # 性能统计
        self.stats = {
            'local_routes': 0,
            'remote_routes': 0,
            'broadcast_routes': 0,
            'rate_limited_calls': 0,
            'failed_routes': 0,
            'total_messages': 0,
            'successful_routes': 0,
            'retries_attempted': 0,
            'avg_route_time_ms': 0.0,
            'last_activity': None,
            'config_reloads': 0
        }
        
        # 后台任务
        self._background_tasks: List[asyncio.Task] = []
        
        # 加载配置驱动的路由规则
        self._load_routing_config()
        
        logger.info(f"✅ 混合消息路由器初始化完成 - 主机: {self.host_id}")
    
    def _create_queue_manager(self) -> QueueManager:
        """创建混合队列管理器"""
        try:
            # 从配置中提取队列管理器配置
            queue_config_data = self.config.get('queue_manager', {})
            
            # 创建混合队列配置
            hybrid_config = HybridQueueConfig(
                primary_backend=QueueBackendType(
                    queue_config_data.get('primary_backend', 'nats')
                ),
                backup_backends=[
                    QueueBackendType(backend) 
                    for backend in queue_config_data.get('backup_backends', ['redis_streams'])
                ],
                local_fallback=queue_config_data.get('local_fallback', True),
                enable_dual_write=queue_config_data.get('enable_dual_write', True),
                dual_write_async=queue_config_data.get('dual_write_async', True),
                ignore_backup_failures=queue_config_data.get('ignore_backup_failures', True),
                
                # 故障转移配置
                health_check_interval=queue_config_data.get('health_check_interval', 5.0),
                failure_threshold=queue_config_data.get('failure_threshold', 3),
                circuit_breaker_timeout=queue_config_data.get('circuit_breaker_timeout', 30.0),
                
                # 性能配置
                max_latency_ms=queue_config_data.get('max_latency_ms', 100.0),
                max_error_rate=queue_config_data.get('max_error_rate', 0.05),
                
                # 后端特定配置
                backend_configs={
                    QueueBackendType.NATS: {
                        'nats': {
                            'host_id': self.host_id,
                            'servers': queue_config_data.get('nats_servers', ['nats://localhost:4222']),
                            'user': queue_config_data.get('nats_user'),
                            'password': queue_config_data.get('nats_password')
                        }
                    },
                    QueueBackendType.REDIS_STREAMS: {
                        'redis': {
                            'host': queue_config_data.get('redis_host', 'localhost'),
                            'port': queue_config_data.get('redis_port', 6379),
                            'db': queue_config_data.get('redis_db', 0),
                            'password': queue_config_data.get('redis_password'),
                            'consumer_group': f'mt5-workers-{self.host_id}'
                        }
                    }
                }
            )
            
            return QueueManager(hybrid_config)
            
        except Exception as e:
            logger.error(f"创建队列管理器失败: {e}")
            # 创建一个基本配置的队列管理器
            basic_config = HybridQueueConfig(
                primary_backend=QueueBackendType.LOCAL_MEMORY,
                backup_backends=[],
                local_fallback=True
            )
            return QueueManager(basic_config)
    
    def _load_routing_config(self):
        """加载配置驱动的路由规则"""
        try:
            from src.core.config_manager import get_config_manager
            config_manager = get_config_manager()
            
            # 加载路由规则配置
            routing_config = config_manager.get('messaging.routing', {})
            
            # 解析路由规则
            rules_config = routing_config.get('rules', [])
            config_driven_rules = []
            
            for rule_data in rules_config:
                try:
                    rule = RouteRule(
                        source=rule_data.get('source', '*'),
                        target=rule_data.get('target', '*'),
                        host_id=rule_data.get('host_id', self.host_id),
                        priority=MessagePriority[rule_data.get('priority', 'REALTIME_QUERY')],
                        route_type=rule_data.get('route_type', 'local'),
                        enabled=rule_data.get('enabled', True),
                        retry_count=rule_data.get('retry_count', 3),
                        timeout_seconds=rule_data.get('timeout_seconds', 30.0),
                        weight=rule_data.get('weight', 1)
                    )
                    config_driven_rules.append(rule)
                except Exception as e:
                    logger.warning(f"解析路由规则失败: {rule_data}, 错误: {e}")
            
            # 如果有配置规则，使用配置规则；否则使用默认规则
            if config_driven_rules:
                self.route_rules.extend(config_driven_rules)
                logger.info(f"加载配置驱动路由规则: {len(config_driven_rules)}条")
            
            # 加载速率限制配置
            rate_limit_config = routing_config.get('rate_limits', [])
            for rate_rule_data in rate_limit_config:
                account_pattern = rate_rule_data.get('account_pattern', '*')
                rate_config = RateLimitConfig(
                    max_calls_per_second=rate_rule_data.get('max_calls_per_second', 80),
                    burst_size=rate_rule_data.get('burst_size', 10),
                    window_size=rate_rule_data.get('window_size', 1),
                    cooldown_period=rate_rule_data.get('cooldown_period', 5)
                )
                # 应用到匹配的账户（这里简化为通配符处理）
                if account_pattern == '*':
                    # 默认速率限制应用到所有账户
                    pass
            
            self.stats['config_reloads'] += 1
            
        except Exception as e:
            logger.warning(f"加载路由配置失败，将使用默认配置: {e}")
    
    async def start(self) -> bool:
        """启动路由器"""
        try:
            # 1. 初始化混合队列管理器
            if not await self.queue_manager.initialize():
                logger.error("混合队列管理器初始化失败")
                return False
            
            # 2. 设置向后兼容属性
            # 尝试获取NATS后端以保持兼容性
            nats_backend = self.queue_manager.backends.get(QueueBackendType.NATS)
            if nats_backend and hasattr(nats_backend, 'jetstream'):
                # 创建一个兼容的NATS管理器代理
                self.queue_manager = NATSManagerProxy(nats_backend.jetstream, self.host_id)
            
            self.running = True
            
            # 3. 如果没有配置规则，初始化默认路由规则
            if not self.route_rules:
                self._initialize_default_routes()
            
            # 4. 启动后台任务
            self._background_tasks = [
                asyncio.create_task(self._rate_limit_cleaner_task()),
                asyncio.create_task(self._retry_processor_task())
            ]
            
            logger.info("🚀 混合消息路由器启动成功 - 支持多后端故障转移")
            return True
            
        except Exception as e:
            logger.error(f"启动混合路由器失败: {e}")
            return False
    
    def _initialize_default_routes(self):
        """初始化默认路由规则"""
        # 本地路由规则示例
        default_rules = [
            RouteRule(
                source="monitor",
                target="executor", 
                host_id=self.host_id,
                priority=MessagePriority.RISK_COMMAND,
                route_type="local",
                retry_count=2,
                timeout_seconds=10.0
            ),
            RouteRule(
                source="*",
                target="*",
                host_id="*",
                priority=MessagePriority.SIGNAL_EXECUTION,
                route_type="remote",
                retry_count=3,
                timeout_seconds=30.0
            ),
            RouteRule(
                source="coordinator",
                target="*",
                host_id="*",
                priority=MessagePriority.SYSTEM_CRITICAL,
                route_type="broadcast",
                retry_count=1,
                timeout_seconds=15.0
            )
        ]
        
        self.route_rules.extend(default_rules)
        logger.info(f"默认路由规则已加载: {len(default_rules)} 条")
    
    async def route_signal(self, signal_data: Dict[str, Any]) -> bool:
        """智能路由信号 - 增强版本，支持配置驱动和错误重试"""
        start_time = time.perf_counter()
        
        try:
            # 更新统计
            self.stats['total_messages'] += 1
            self.stats['last_activity'] = time.time()
            
            # 生成消息ID
            message_id = signal_data.get('message_id', f"msg_{int(time.time() * 1000000)}")
            
            # 解析路由信息
            target_host = signal_data.get('target_host_id', self.host_id)
            target_account = signal_data.get('target_account', 'unknown') 
            source_type = signal_data.get('source_type', 'unknown')
            
            logger.debug(f"路由信号: {message_id} - {source_type} -> {target_account}@{target_host}")
            
            # 查找匹配的路由规则
            matching_rule = self._find_matching_rule(source_type, target_account, target_host)
            
            if not matching_rule:
                logger.warning(f"未找到匹配的路由规则: {source_type} -> {target_account}@{target_host}")
                self.stats['failed_routes'] += 1
                return False
            
            # 检查速率限制
            if target_account and not await self._check_rate_limit(target_account):
                logger.warning(f"速率限制触发: {target_account}")
                return await self._handle_rate_limited_message(signal_data, matching_rule.priority)
            
            # 执行路由
            success = await self._execute_route_with_rule(signal_data, matching_rule)
            
            if success:
                self.stats['successful_routes'] += 1
                route_time = (time.perf_counter() - start_time) * 1000
                self._update_avg_route_time(route_time)
                
                # 记录指标
                metrics.record("route_time_ms", route_time, {
                    'route_type': matching_rule.route_type,
                    'host': self.host_id
                })
            else:
                self.stats['failed_routes'] += 1
                
                # 尝试重试
                if matching_rule.retry_count > 0:
                    await self._schedule_retry(signal_data, matching_rule, 1)
            
            return success
            
        except Exception as e:
            logger.error(f"信号路由失败: {e}")
            self.stats['failed_routes'] += 1
            return False
    
    def _find_matching_rule(self, source: str, target: str, host_id: str) -> Optional[RouteRule]:
        """查找匹配的路由规则"""
        cache_key = f"{source}:{target}:{host_id}"
        
        # 检查缓存
        if cache_key in self.rule_cache:
            cached_rule = self.rule_cache[cache_key]
            if cached_rule.enabled:
                return cached_rule
        
        # 搜索匹配规则
        for rule in self.route_rules:
            if rule.enabled and rule.matches_pattern(source, target, host_id):
                # 缓存规则
                self.rule_cache[cache_key] = rule
                return rule
        
        return None
    
    async def _execute_route_with_rule(self, signal_data: Dict[str, Any], rule: RouteRule) -> bool:
        """使用规则执行路由"""
        try:
            if rule.route_type == "local":
                success = await self._route_local_enhanced(signal_data, rule)
                if success:
                    self.stats['local_routes'] += 1
            elif rule.route_type == "remote":
                success = await self._route_remote_enhanced(signal_data, rule)
                if success:
                    self.stats['remote_routes'] += 1
            elif rule.route_type == "broadcast":
                success = await self._route_broadcast_enhanced(signal_data, rule)
                if success:
                    self.stats['broadcast_routes'] += 1
            else:
                logger.error(f"未支持的路由类型: {rule.route_type}")
                return False
            
            return success
            
        except Exception as e:
            logger.error(f"路由执行异常: {e}")
            return False
    
    async def _route_local_enhanced(self, signal_data: Dict[str, Any], rule: RouteRule) -> bool:
        """增强的本地路由 - 使用混合队列管理器"""
        try:
            # 使用规则指定的优先级或分析得出的优先级
            priority = rule.priority
            if priority == MessagePriority.REALTIME_QUERY:  # 如果是默认优先级，重新分析
                priority = self._analyze_message_priority(signal_data)
            
            # 创建消息信封
            message = MessageEnvelope(
                id=signal_data.get('message_id', f"local_{int(time.time() * 1000000)}"),
                subject=signal_data.get('subject', 'local.route'),
                payload=signal_data,
                timestamp=time.time()
            )
            
            # 使用混合队列管理器发布（本地路由仍会优化为最快路径）
            success = await self.queue_manager.publish(
                subject=message.subject,
                message=message,
                priority=priority
            )
            
            if success:
                logger.debug(f"本地路由成功: {priority.name}")
            
            return success
            
        except Exception as e:
            logger.error(f"本地路由失败: {e}")
            return False
    
    async def _route_remote_enhanced(self, signal_data: Dict[str, Any], rule: RouteRule) -> bool:
        """增强的远程路由 - 使用混合队列管理器（自动故障转移）"""  
        for attempt in range(rule.retry_count + 1):
            try:
                if attempt > 0:
                    delay = self.retry_delay_base * (2 ** (attempt - 1))
                    await asyncio.sleep(delay)
                    logger.info(f"远程路由重试: 尝试 {attempt + 1}")
                
                # 设置目标主机信息
                enhanced_data = signal_data.copy()
                enhanced_data['source_host_id'] = self.host_id
                enhanced_data['target_host_id'] = signal_data.get('target_host_id', 'remote')
                
                # 创建消息信封
                message = MessageEnvelope(
                    id=enhanced_data.get('message_id', f"remote_{int(time.time() * 1000000)}"),
                    subject=enhanced_data.get('subject', 'remote.route'),
                    payload=enhanced_data,
                    timestamp=time.time()
                )
                
                # 使用混合队列管理器发布，自动处理NATS/Redis/本地降级
                success = await asyncio.wait_for(
                    self.queue_manager.publish(
                        subject=message.subject,
                        message=message,
                        priority=rule.priority
                    ),
                    timeout=rule.timeout_seconds
                )
                
                if success:
                    logger.debug(f"远程路由成功 - 已自动选择最佳后端")
                    return True
                
            except asyncio.TimeoutError:
                logger.warning(f"远程路由超时: 尝试 {attempt + 1}")
            except Exception as e:
                logger.warning(f"远程路由异常: 尝试 {attempt + 1}, 错误: {e}")
        
        logger.error(f"远程路由最终失败 - 所有后端均不可用")
        return False
    
    async def _route_broadcast_enhanced(self, signal_data: Dict[str, Any], rule: RouteRule) -> bool:
        """增强的广播路由"""
        # 创建本地和远程的副本
        local_data = signal_data.copy()
        remote_data = signal_data.copy()
        remote_data['broadcast'] = True
        
        # 并行执行本地和远程路由
        local_success = await self._route_local_enhanced(local_data, rule)
        remote_success = await self._route_remote_enhanced(remote_data, rule)
        
        # 只要有一个成功就认为广播成功
        success = local_success or remote_success
        if success:
            logger.debug("广播路由成功")
        
        return success
    
    def _analyze_message_priority(self, signal_data: Dict[str, Any]) -> MessagePriority:
        """分析消息优先级 - 统一使用PriorityAnalyzer"""
        try:
            # 直接使用统一的优先级分析器
            method = signal_data.get('command_type', 'route_message')
            return PriorityAnalyzer.get_message_priority(method, signal_data)
            
        except Exception as e:
            logger.warning(f"优先级分析失败，使用默认: {e}")
            return MessagePriority.NORMAL
    
    async def _check_rate_limit(self, account_id: str) -> bool:
        """检查API速率限制"""
        try:
            if account_id in self.cooldown_accounts:
                return False
            
            current_time = time.time()
            
            # 获取或创建速率限制配置
            if account_id not in self.rate_limits:
                self.rate_limits[account_id] = RateLimitConfig()
            
            config = self.rate_limits[account_id]
            
            # 获取调用历史
            if account_id not in self.api_call_history:
                self.api_call_history[account_id] = []
            
            call_history = self.api_call_history[account_id]
            
            # 清理过期的调用记录
            window_start = current_time - config.window_size
            call_history[:] = [t for t in call_history if t >= window_start]
            
            # 检查是否超过速率限制
            if len(call_history) >= config.max_calls_per_second:
                logger.warning(f"API速率限制触发: {account_id} ({len(call_history)}/s)")
                self.stats['rate_limited_calls'] += 1
                
                # 将账户加入冷却列表
                self.cooldown_accounts.add(account_id)
                asyncio.create_task(self._remove_from_cooldown(account_id, config.cooldown_period))
                
                return False
            
            # 记录本次调用
            call_history.append(current_time)
            return True
            
        except Exception as e:
            logger.error(f"速率限制检查失败: {e}")
            return True  # 出错时允许通过
    
    async def _schedule_retry(self, signal_data: Dict[str, Any], rule: RouteRule, attempt: int):
        """安排重试"""
        if attempt > rule.retry_count:
            logger.warning(f"超过最大重试次数，放弃消息: {signal_data.get('message_id')}")
            return
        
        delay = self.retry_delay_base * (2 ** (attempt - 1))
        retry_time = time.time() + delay
        
        # 添加到重试队列
        rule_key = f"{rule.source}:{rule.target}:{rule.host_id}"
        if rule_key not in self.retry_queues:
            self.retry_queues[rule_key] = []
        
        self.retry_queues[rule_key].append((signal_data, attempt, retry_time))
        self.stats['retries_attempted'] += 1
        
        logger.info(f"安排重试: 尝试 {attempt}, 延迟 {delay}s")
    
    async def _handle_rate_limited_message(self, signal_data: Dict[str, Any], 
                                         priority: MessagePriority) -> bool:
        """处理被速率限制的消息"""
        try:
            # 降级到低优先级队列，延迟处理
            signal_data['rate_limited'] = True
            signal_data['delayed'] = True
            
            # 使用低优先级入队
            success = await self.priority_queue.enqueue(MessagePriority.BACKGROUND_TASK, signal_data)
            
            if success:
                logger.debug("速率限制消息已降级处理")
            
            return success
            
        except Exception as e:
            logger.error(f"处理速率限制消息失败: {e}")
            return False
    
    async def _remove_from_cooldown(self, account_id: str, delay: int):
        """从冷却列表中移除账户"""
        await asyncio.sleep(delay)
        self.cooldown_accounts.discard(account_id)
        logger.debug(f"账户 {account_id} 冷却期结束")
    
    async def _retry_processor_task(self):
        """重试处理任务"""
        while self.running:
            try:
                current_time = time.time()
                
                for rule_key, retry_queue in list(self.retry_queues.items()):
                    # 处理到期的重试
                    ready_retries = [
                        (signal_data, attempt, retry_time) 
                        for signal_data, attempt, retry_time in retry_queue 
                        if retry_time <= current_time
                    ]
                    
                    # 从队列中移除已处理的重试
                    self.retry_queues[rule_key] = [
                        item for item in retry_queue 
                        if item[2] > current_time
                    ]
                    
                    # 执行重试
                    for signal_data, attempt, _ in ready_retries:
                        logger.info(f"执行重试: {rule_key}, 尝试 {attempt}")
                        success = await self.route_signal(signal_data)
                        
                        if not success:
                            # 重试失败，安排下次重试
                            parts = rule_key.split(':')
                            if len(parts) == 3:
                                matching_rule = self._find_matching_rule(parts[0], parts[1], parts[2])
                                if matching_rule:
                                    await self._schedule_retry(signal_data, matching_rule, attempt + 1)
                
                await asyncio.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                logger.error(f"重试处理任务异常: {e}")
                await asyncio.sleep(1)
    
    async def _rate_limit_cleaner_task(self):
        """定期清理速率限制历史数据"""
        while self.running:
            try:
                current_time = time.time()
                
                for account_id in list(self.api_call_history.keys()):
                    call_history = self.api_call_history[account_id]
                    
                    # 清理5分钟前的记录
                    cutoff_time = current_time - 300
                    call_history[:] = [t for t in call_history if t >= cutoff_time]
                    
                    # 如果历史记录为空，删除条目
                    if not call_history:
                        del self.api_call_history[account_id]
                
                await asyncio.sleep(60)  # 每分钟清理一次
                
            except Exception as e:
                logger.error(f"速率限制清理任务异常: {e}")
                await asyncio.sleep(60)
    
    def add_local_route(self, account_id: str, target: str):
        """添加本地路由"""
        self.local_routes[account_id] = target
        logger.info(f"添加本地路由: {account_id} -> {target}")
    
    def add_remote_route(self, account_id: str, target_host: str):
        """添加远程路由"""
        self.cross_host_routes[account_id] = target_host
        logger.info(f"添加远程路由: {account_id} -> {target_host}")
    
    def remove_route(self, account_id: str):
        """移除路由"""
        self.local_routes.pop(account_id, None)
        self.cross_host_routes.pop(account_id, None)
        logger.info(f"移除路由: {account_id}")
    
    def set_rate_limit(self, account_id: str, config: RateLimitConfig):
        """设置账户的API速率限制"""
        self.rate_limits[account_id] = config
        logger.info(f"设置速率限制: {account_id} - {config.max_calls_per_second}/s")
    
    def register_message_handler(self, message_type: str, handler: Callable):
        """注册消息处理器"""
        self.message_handlers[message_type] = handler
        logger.info(f"注册消息处理器: {message_type}")
    
    async def handle_message(self, message_type: str, message_data: Dict[str, Any]) -> bool:
        """处理消息"""
        if message_type in self.message_handlers:
            try:
                handler = self.message_handlers[message_type]
                return await handler(message_data)
            except Exception as e:
                logger.error(f"消息处理器异常: {message_type} - {e}")
                return False
        else:
            # 默认使用路由处理
            return await self.route_signal(message_data)
    
    def _update_avg_route_time(self, route_time: float):
        """更新平均路由时间"""
        current_avg = self.stats['avg_route_time_ms']
        total_messages = self.stats['total_messages']
        
        if total_messages > 0:
            self.stats['avg_route_time_ms'] = (
                (current_avg * (total_messages - 1) + route_time) / total_messages
            )
    
    def reload_config(self):
        """重新加载配置"""
        logger.info("重新加载路由配置")
        self.rule_cache.clear()  # 清空规则缓存
        self.route_rules.clear()  # 清空现有规则
        self._load_routing_config()
    
    async def stop(self):
        """停止路由器"""
        if not self.running:
            return
        
        try:
            self.running = False
            
            # 取消后台任务
            for task in self._background_tasks:
                task.cancel()
            
            # 等待任务完成
            if self._background_tasks:
                await asyncio.gather(*self._background_tasks, return_exceptions=True)
            
            # 关闭混合队列管理器
            await self.queue_manager.shutdown()
            
            # 清理资源
            self.route_rules.clear()
            self.local_routes.clear()
            self.cross_host_routes.clear()
            self.message_handlers.clear()
            self.api_call_history.clear()
            self.cooldown_accounts.clear()
            self.retry_queues.clear()
            self.rule_cache.clear()
            
            logger.info("🛑 混合消息路由器已停止 - 包括所有后端队列")
            
        except Exception as e:
            logger.error(f"停止混合路由器失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取路由器状态（包含混合队列管理器状态）"""
        # 获取混合队列管理器状态
        queue_status = self.queue_manager.get_status()
        
        return {
            'running': self.running,
            'host_id': self.host_id,
            'routes': {
                'local_routes': len(self.local_routes),
                'remote_routes': len(self.cross_host_routes),
                'route_rules': len(self.route_rules),
                'rule_cache_size': len(self.rule_cache)
            },
            'rate_limiting': {
                'tracked_accounts': len(self.api_call_history),
                'cooldown_accounts': len(self.cooldown_accounts),
                'rate_limited_calls': self.stats['rate_limited_calls']
            },
            'retry_management': {
                'retry_queues_count': len(self.retry_queues),
                'pending_retries': sum(len(q) for q in self.retry_queues.values()),
                'retries_attempted': self.stats['retries_attempted']
            },
            'performance': {
                'total_messages': self.stats['total_messages'],
                'successful_routes': self.stats['successful_routes'],
                'local_routes': self.stats['local_routes'],
                'remote_routes': self.stats['remote_routes'],
                'broadcast_routes': self.stats['broadcast_routes'],
                'failed_routes': self.stats['failed_routes'],
                'success_rate': self._calculate_success_rate(),
                'avg_route_time_ms': self.stats['avg_route_time_ms'],
                'config_reloads': self.stats['config_reloads']
            },
            'message_handlers': list(self.message_handlers.keys()),
            # 混合队列管理器状态
            'queue_manager': queue_status,
            # 后端健康状态摘要
            'backend_summary': {
                'primary_backend': queue_status.get('primary_backend'),
                'backup_backends': queue_status.get('backup_backends', []),
                'fallback_backend': queue_status.get('fallback_backend'),
                'routing_mode': queue_status.get('routing_mode'),
                'healthy_backends': [
                    backend for backend, health in queue_status.get('backend_health', {}).items()
                    if health.get('healthy', False)
                ],
                'total_queue_messages': queue_status.get('statistics', {}).get('total_messages', 0),
                'failover_count': queue_status.get('statistics', {}).get('failover_count', 0)
            }
        }
    
    def _calculate_success_rate(self) -> float:
        """计算成功率"""
        total = self.stats['total_messages']
        if total == 0:
            return 0.0
        
        successful = self.stats['successful_routes']
        return (successful / total) * 100.0
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        total_routes = (
            self.stats['local_routes'] + 
            self.stats['remote_routes'] + 
            self.stats['broadcast_routes']
        )
        
        return {
            'routing_distribution': {
                'local_percentage': (self.stats['local_routes'] / max(total_routes, 1)) * 100,
                'remote_percentage': (self.stats['remote_routes'] / max(total_routes, 1)) * 100,
                'broadcast_percentage': (self.stats['broadcast_routes'] / max(total_routes, 1)) * 100
            },
            'efficiency_metrics': {
                'success_rate': self._calculate_success_rate(),
                'avg_route_time_ms': self.stats['avg_route_time_ms'],
                'rate_limit_ratio': (self.stats['rate_limited_calls'] / max(self.stats['total_messages'], 1)) * 100
            },
            'capacity_utilization': {
                'active_routes': len(self.local_routes) + len(self.cross_host_routes),
                'rate_limit_active': len(self.cooldown_accounts),
                'message_handlers_count': len(self.message_handlers)
            }
        }


class MT5APIRateLimiter:
    """MT5 API专用速率限制器"""
    
    def __init__(self):
        self.account_limits: Dict[str, RateLimitConfig] = {}
        self.call_counters: Dict[str, int] = {}
        self.last_reset_time: Dict[str, float] = {}
        
    def set_account_limit(self, account_id: str, max_calls_per_second: int = 80):
        """设置账户API调用限制"""
        self.account_limits[account_id] = RateLimitConfig(
            max_calls_per_second=max_calls_per_second
        )
        self.call_counters[account_id] = 0
        self.last_reset_time[account_id] = time.time()
    
    async def acquire_permission(self, account_id: str) -> bool:
        """获取API调用许可"""
        if account_id not in self.account_limits:
            # 未配置限制的账户默认允许
            return True
        
        current_time = time.time()
        last_reset = self.last_reset_time.get(account_id, 0)
        
        # 检查是否需要重置计数器
        if current_time - last_reset >= 1.0:
            self.call_counters[account_id] = 0
            self.last_reset_time[account_id] = current_time
        
        # 检查是否超过限制
        config = self.account_limits[account_id]
        if self.call_counters[account_id] >= config.max_calls_per_second:
            return False
        
        # 增加计数器
        self.call_counters[account_id] += 1
        return True
    
    def get_remaining_calls(self, account_id: str) -> int:
        """获取剩余调用次数"""
        if account_id not in self.account_limits:
            return 999  # 未限制账户返回大数值
        
        config = self.account_limits[account_id]
        used = self.call_counters.get(account_id, 0)
        return max(0, config.max_calls_per_second - used)


# ========== 扩展HybridMessageRouter的信号路由功能 ==========

class SignalRoutingMixin:
    """信号路由混入类 - 为HybridMessageRouter添加专门的信号处理功能"""
    
    async def publish_trade_signal(self, signal_data: Dict[str, Any]) -> bool:
        """发布交易信号 - 统一信号路由接口"""
        try:
            # 增强信号数据
            enhanced_signal = signal_data.copy()
            enhanced_signal['signal_type'] = 'trade_signal'
            enhanced_signal['source_type'] = 'monitor'
            
            # 分析优先级并设置主题
            priority = self._analyze_message_priority(enhanced_signal)
            account_id = enhanced_signal.get('account_id') or enhanced_signal.get('master_id')
            
            # 使用配置驱动的主题格式
            from src.core.config_manager import get_stream_config_manager
            config_mgr = get_stream_config_manager()
            topic = config_mgr.get_subject_pattern('global_signals', priority=priority.name, master_account=account_id)
            enhanced_signal['topic'] = topic
            
            # 路由信号
            success = await self.route_signal(enhanced_signal)
            
            if success:
                logger.info(f"发布交易信号成功: {account_id} -> {topic} (优先级: {priority.name})")
            
            return success
            
        except Exception as e:
            logger.error(f"发布交易信号失败: {e}")
            return False
    
    async def publish_copy_signal(self, target_account: str, signal_data: Dict[str, Any]) -> bool:
        """发布跟单信号 - 专门用于跨账户信号复制"""
        try:
            # 设置跟单信号属性
            copy_signal = signal_data.copy()
            copy_signal['signal_type'] = 'copy_signal'
            copy_signal['target_account'] = target_account
            copy_signal['source_type'] = 'copy_engine'
            
            # 跟单信号通常使用HIGH优先级
            copy_signal['priority_override'] = MessagePriority.HIGH
            
            # 路由到目标账户
            success = await self.route_signal(copy_signal)
            
            if success:
                master_account = signal_data.get('master_account', 'UNKNOWN')
                logger.info(f"发布跟单信号成功: {master_account} -> {target_account}")
            
            return success
            
        except Exception as e:
            logger.error(f"发布跟单信号失败: {e}")
            return False
    
    async def subscribe_signals(self, account_id: str, signal_types: List[str] = None) -> bool:
        """订阅信号 - 统一信号订阅接口"""
        try:
            if signal_types is None:
                signal_types = ['trade_signal', 'copy_signal', 'monitor_event']
            
            subscription_success = True
            
            # 获取配置管理器一次
            from src.core.config_manager import get_stream_config_manager
            config_mgr = get_stream_config_manager()
            
            for signal_type in signal_types:
                if signal_type == 'trade_signal':
                    # 订阅交易信号（所有优先级） - 使用通配符
                    topic = config_mgr.get_subject_pattern('global_signals', priority='*', master_account=account_id)
                elif signal_type == 'copy_signal':
                    # 订阅跟单信号
                    topic = config_mgr.get_subject_pattern('copy_signals', priority='HIGH', slave_account=account_id)
                elif signal_type == 'monitor_event':
                    # 订阅监控事件
                    topic = config_mgr.get_subject_pattern('monitor', host_id=self.host_id, metric_type=account_id)
                else:
                    logger.warning(f"未知的信号类型: {signal_type}")
                    continue
                
                # 注册订阅模式（实际订阅由各组件执行）
                logger.info(f"注册信号订阅: {account_id} -> {topic} ({signal_type})")
            
            return subscription_success
            
        except Exception as e:
            logger.error(f"订阅信号失败: {e}")
            return False
    
    def get_signal_routing_stats(self) -> Dict[str, Any]:
        """获取信号路由统计"""
        return {
            'total_signals_routed': self.stats['total_messages'],
            'local_signal_routes': self.stats['local_routes'],
            'remote_signal_routes': self.stats['remote_routes'],
            'broadcast_signal_routes': self.stats['broadcast_routes'],
            'failed_signal_routes': self.stats['failed_routes'],
            'avg_signal_route_time_ms': self.stats['avg_route_time_ms'],
            'rate_limited_signals': self.stats['rate_limited_calls']
        }


# 将信号路由功能混入到MessageRouter
# 使用更安全的方式添加方法
for method_name in ['publish_trade_signal', 'subscribe_to_signals', 'get_signal_routing_stats']:
    if hasattr(SignalRoutingMixin, method_name):
        setattr(MessageRouter, method_name, getattr(SignalRoutingMixin, method_name))


