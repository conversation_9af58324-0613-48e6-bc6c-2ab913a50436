#!/usr/bin/env python3
"""
🚨 DEPRECATED MESSAGING COMPONENTS

This file marks the following components as DEPRECATED and FORBIDDEN:

❌ REMOVED COMPONENTS (DO NOT USE):
- nats_manager.py / NATSManager
- nats_client.py / NATSClient  
- jetstream_client.py / JetStreamClient
- message_codec.py / MessageCodec
- protobuf_codec.py / ProtobufCodec

✅ NEW UNIFIED IMPLEMENTATION:
- hybrid_queue_manager.py / HybridQueueManager
- message_queue_interface.py / MessageQueueInterface

These components have been PERMANENTLY REMOVED as part of the hard migration
to eliminate NATS single point of failure and establish a single source of truth.

See MIGRATION_GUIDE.md for complete migration instructions.
"""

import warnings
import sys

# Issue deprecation warning when this module is imported
warnings.warn(
    "\n🚨 CRITICAL: You are importing DEPRECATED messaging components!\n\n"
    "The following components are NO LONGER AVAILABLE:\n"
    "❌ NATSManager, NATSClient, JetStreamClient, MessageCodec, ProtobufCodec\n\n"
    "✅ Use the NEW unified implementation:\n"
    "from src.messaging.hybrid_queue_manager import HybridQueueManager\n\n"
    "See MIGRATION_GUIDE.md for complete migration instructions.\n"
    "This is a HARD MIGRATION with ZERO backward compatibility.\n",
    DeprecationWarning,
    stacklevel=2
)


class DeprecatedComponentError(RuntimeError):
    """Raised when attempting to use deprecated messaging components"""
    pass


def _raise_deprecated_error(component_name: str):
    """Raise error for deprecated component usage"""
    raise DeprecatedComponentError(
        f"\n🚨 DEPRECATED COMPONENT: {component_name}\n\n"
        f"This component has been PERMANENTLY REMOVED.\n"
        f"Use HybridQueueManager instead.\n\n"
        f"Migration guide: MIGRATION_GUIDE.md\n"
    )


# Create fake classes that raise errors when instantiated
class NATSManager:
    def __init__(self, *args, **kwargs):
        _raise_deprecated_error("NATSManager")


class NATSClient:
    def __init__(self, *args, **kwargs):
        _raise_deprecated_error("NATSClient")


class JetStreamClient:
    def __init__(self, *args, **kwargs):
        _raise_deprecated_error("JetStreamClient")


class MessageCodec:
    def __init__(self, *args, **kwargs):
        _raise_deprecated_error("MessageCodec")


class ProtobufCodec:
    def __init__(self, *args, **kwargs):
        _raise_deprecated_error("ProtobufCodec")


# Block any attempts to import these modules
_DEPRECATED_MODULES = [
    'src.messaging.nats_manager',
    'src.messaging.nats_client', 
    'src.messaging.jetstream_client',
    'src.messaging.message_codec',
    'src.messaging.protobuf_codec'
]

for module_name in _DEPRECATED_MODULES:
    if module_name in sys.modules:
        warnings.warn(f"🚨 DEPRECATED MODULE IMPORTED: {module_name}", DeprecationWarning)