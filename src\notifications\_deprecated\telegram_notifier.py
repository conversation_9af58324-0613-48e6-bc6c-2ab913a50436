"""
Telegram通知系统实现
"""
import asyncio
import os
from typing import Dict, Any, Optional
from datetime import datetime
import aiohttp
from ..utils.logger import get_logger


class TelegramNotifier:
    """Telegram通知发送器"""
    
    def __init__(self, bot_token: str, chat_id: str):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.base_url = f"https://api.telegram.org/bot{bot_token}"
        self.logger = get_logger(self.__class__.__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def start(self):
        """启动通知器"""
        self.session = aiohttp.ClientSession()
        self.logger.info("Telegram通知器已启动")
        
    async def stop(self):
        """停止通知器"""
        if self.session:
            await self.session.close()
            
    async def send_message(self, message: str, parse_mode: str = "HTML") -> bool:
        """发送消息到Telegram"""
        if not self.session:
            self.logger.error("Session未初始化")
            return False
            
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": parse_mode
            }
            
            async with self.session.post(url, json=data) as response:
                if response.status == 200:
                    self.logger.debug(f"消息发送成功: {message[:50]}...")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"发送失败: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"发送消息异常: {e}")
            return False
            
    async def send_trade_notification(self, trade_data: Dict[str, Any], event_type: str):
        """发送交易通知"""
        symbol = trade_data.get('symbol', 'N/A')
        volume = trade_data.get('volume', 0)
        price = trade_data.get('price', 0)
        profit = trade_data.get('profit', 0)
        account_id = trade_data.get('account_id', 'Unknown')
        
        if event_type == "trade_open":
            message = f"""
<b>📈 新交易开仓</b>
账户: <code>{account_id}</code>
品种: <b>{symbol}</b>
手数: {volume}
价格: {price}
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        elif event_type == "trade_close":
            profit_emoji = "✅" if profit >= 0 else "❌"
            message = f"""
<b>{profit_emoji} 交易平仓</b>
账户: <code>{account_id}</code>
品种: <b>{symbol}</b>
手数: {volume}
平仓价: {price}
盈亏: <b>${profit:.2f}</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        else:
            message = f"未知交易事件: {event_type}"
            
        await self.send_message(message)
        
    async def send_error_notification(self, error_data: Dict[str, Any]):
        """发送错误通知"""
        account_id = error_data.get('account_id', 'Unknown')
        error_type = error_data.get('error_type', 'Unknown')
        error_message = error_data.get('error_message', 'No details')
        
        message = f"""
<b>⚠️ 系统错误</b>
账户: <code>{account_id}</code>
错误类型: <b>{error_type}</b>
错误信息: {error_message}
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        await self.send_message(message)
        
    async def send_daily_report(self, report_data: Dict[str, Any]):
        """发送日报告"""
        account_id = report_data.get('account_id', 'Unknown')
        total_trades = report_data.get('total_trades', 0)
        profitable_trades = report_data.get('profitable_trades', 0)
        total_profit = report_data.get('total_profit', 0)
        
        win_rate = (profitable_trades / total_trades * 100) if total_trades > 0 else 0
        
        message = f"""
<b>📊 日交易报告</b>
账户: <code>{account_id}</code>
总交易数: {total_trades}
盈利交易: {profitable_trades}
胜率: {win_rate:.1f}%
总盈亏: <b>${total_profit:.2f}</b>
日期: {datetime.now().strftime('%Y-%m-%d')}
"""
        await self.send_message(message)


class NotificationManager:
    """通知管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.telegram_notifier = None
        self._init_notifiers()
        
    def _init_notifiers(self):
        """初始化通知器"""
        notifications_config = self.config.get('notifications', {})
        
        # Telegram通知
        telegram_config = notifications_config.get('telegram', {})
        if telegram_config.get('enabled', False):
            bot_token = os.getenv('TELEGRAM_BOT_TOKEN', telegram_config.get('bot_token', ''))
            chat_id = os.getenv('TELEGRAM_CHAT_ID', telegram_config.get('chat_id', ''))
            
            if bot_token and chat_id:
                self.telegram_notifier = TelegramNotifier(bot_token, chat_id)
                self.logger.info("Telegram通知已启用")
            else:
                self.logger.warning("Telegram配置不完整，通知未启用")
                
    async def start(self):
        """启动所有通知器"""
        if self.telegram_notifier:
            await self.telegram_notifier.start()
            
    async def stop(self):
        """停止所有通知器"""
        if self.telegram_notifier:
            await self.telegram_notifier.stop()
            
    async def notify(self, event_type: str, data: Dict[str, Any]):
        """发送通知"""
        if not self.telegram_notifier:
            return
            
        # 检查是否需要发送此类型的通知
        telegram_config = self.config.get('notifications', {}).get('telegram', {})
        notify_on = telegram_config.get('notify_on', [])
        
        if event_type not in notify_on:
            return
            
        try:
            if event_type in ["trade_open", "trade_close"]:
                await self.telegram_notifier.send_trade_notification(data, event_type)
            elif event_type == "error":
                await self.telegram_notifier.send_error_notification(data)
            elif event_type == "daily_report":
                await self.telegram_notifier.send_daily_report(data)
            else:
                # 通用消息
                message = data.get('message', f"事件: {event_type}")
                await self.telegram_notifier.send_message(message)
                
        except Exception as e:
            self.logger.error(f"发送通知失败: {e}")