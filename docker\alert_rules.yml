# MT5高频异步轮询系统告警规则
groups:
  - name: mt5_high_frequency_alerts
    interval: 5s  # 高频检查
    rules:
      # 延迟告警
      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(mt5_signal_latency_ms_bucket[30s])) > 20
        for: 10s
        labels:
          severity: warning
          component: latency
        annotations:
          summary: "MT5信号延迟过高"
          description: "P95延迟超过20ms，当前值: {{ $value }}ms"

      - alert: CriticalLatency
        expr: histogram_quantile(0.95, rate(mt5_signal_latency_ms_bucket[30s])) > 50
        for: 5s
        labels:
          severity: critical
          component: latency
        annotations:
          summary: "MT5信号延迟严重过高"
          description: "P95延迟超过50ms，当前值: {{ $value }}ms，需要立即处理"

      # 吞吐量告警
      - alert: LowThroughput
        expr: rate(mt5_signals_processed_total[1m]) < 100
        for: 30s
        labels:
          severity: warning
          component: throughput
        annotations:
          summary: "信号处理吞吐量过低"
          description: "信号处理速率低于100/秒，当前值: {{ $value }}/秒"

      - alert: ZeroThroughput
        expr: rate(mt5_signals_processed_total[1m]) == 0
        for: 10s
        labels:
          severity: critical
          component: throughput
        annotations:
          summary: "信号处理完全停止"
          description: "系统未处理任何信号，可能存在严重故障"

      # 错误率告警
      - alert: HighErrorRate
        expr: rate(mt5_signals_failed_total[1m]) / rate(mt5_signals_total[1m]) > 0.05
        for: 30s
        labels:
          severity: warning
          component: error_rate
        annotations:
          summary: "信号失败率过高"
          description: "信号失败率超过5%，当前值: {{ $value | humanizePercentage }}"

      - alert: CriticalErrorRate
        expr: rate(mt5_signals_failed_total[1m]) / rate(mt5_signals_total[1m]) > 0.20
        for: 10s
        labels:
          severity: critical
          component: error_rate
        annotations:
          summary: "信号失败率严重过高"
          description: "信号失败率超过20%，当前值: {{ $value | humanizePercentage }}"

      # 系统资源告警
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[1m]) * 100 > 80
        for: 2m
        labels:
          severity: warning
          component: cpu
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ $value }}%"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 2048
        for: 2m
        labels:
          severity: warning
          component: memory
        annotations:
          summary: "内存使用过高"
          description: "内存使用超过2GB，当前值: {{ $value }}MB"

      # 连接告警
      - alert: TooManyConnections
        expr: mt5_active_connections > 100
        for: 1m
        labels:
          severity: warning
          component: connections
        annotations:
          summary: "活跃连接数过多"
          description: "活跃连接数超过100，当前值: {{ $value }}"

      - alert: NoActiveConnections
        expr: mt5_active_connections == 0
        for: 30s
        labels:
          severity: critical
          component: connections
        annotations:
          summary: "没有活跃连接"
          description: "系统没有活跃连接，可能存在网络问题"

  - name: mt5_infrastructure_alerts
    interval: 10s
    rules:
      # NATS告警
      - alert: NATSDown
        expr: up{job="nats"} == 0
        for: 30s
        labels:
          severity: critical
          component: nats
        annotations:
          summary: "NATS服务不可用"
          description: "NATS消息队列服务已停止"

      - alert: NATSSlowConsumers
        expr: nats_varz_slow_consumers > 0
        for: 1m
        labels:
          severity: warning
          component: nats
        annotations:
          summary: "NATS存在慢消费者"
          description: "检测到{{ $value }}个慢消费者"

      # Redis告警
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 30s
        labels:
          severity: critical
          component: redis
        annotations:
          summary: "Redis服务不可用"
          description: "Redis缓存服务已停止"

      - alert: RedisHighMemory
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 2m
        labels:
          severity: warning
          component: redis
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过90%"

      # API服务告警
      - alert: APIDown
        expr: up{job="mt5-api"} == 0
        for: 30s
        labels:
          severity: critical
          component: api
        annotations:
          summary: "API服务不可用"
          description: "MT5 API服务已停止"

      - alert: APIHighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 2m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "API响应时间过长"
          description: "API P95响应时间超过1秒"

  - name: mt5_trading_alerts
    interval: 1s  # 交易相关告警需要更高频率
    rules:
      # 交易执行告警
      - alert: ExecutionFailures
        expr: rate(mt5_execution_errors_total[30s]) > 5
        for: 10s
        labels:
          severity: critical
          component: execution
        annotations:
          summary: "交易执行失败率过高"
          description: "30秒内执行失败超过5次"

      - alert: LargePositionSize
        expr: mt5_position_size > 10
        for: 0s  # 立即告警
        labels:
          severity: warning
          component: risk
        annotations:
          summary: "检测到大额持仓"
          description: "持仓规模超过10手: {{ $value }}手"

      # 风险管理告警
      - alert: RiskLimitExceeded
        expr: mt5_risk_exposure_ratio > 0.8
        for: 0s  # 立即告警
        labels:
          severity: critical
          component: risk
        annotations:
          summary: "风险敞口超限"
          description: "风险敞口比例超过80%: {{ $value | humanizePercentage }}"

      - alert: MaxDrawdownExceeded
        expr: mt5_max_drawdown_ratio > 0.1
        for: 0s  # 立即告警
        labels:
          severity: critical
          component: risk
        annotations:
          summary: "最大回撤超限"
          description: "最大回撤超过10%: {{ $value | humanizePercentage }}"

      # 账户状态告警
      - alert: AccountDisconnected
        expr: mt5_account_connected == 0
        for: 10s
        labels:
          severity: critical
          component: account
        annotations:
          summary: "MT5账户连接断开"
          description: "账户{{ $labels.account_id }}连接已断开"

      - alert: InsufficientMargin
        expr: mt5_margin_level < 200
        for: 30s
        labels:
          severity: warning
          component: margin
        annotations:
          summary: "保证金水平过低"
          description: "账户{{ $labels.account_id }}保证金水平: {{ $value }}%"
