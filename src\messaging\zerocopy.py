#!/usr/bin/env python3
"""
零拷贝消息传递系统 - 架构唯一权威实现
使用内存映射和共享内存池替代JSON序列化
实现微秒级消息传递，消除序列化开销
硬迁移：零向后兼容，强制统一，SSOT
"""
import asyncio
import mmap
import struct
import time
import logging
from typing import Dict, Any, Optional, List, Union, Tuple, Callable
from dataclasses import dataclass
from enum import Enum
import uuid
import ctypes
import threading
from concurrent.futures import ThreadPoolExecutor
import os
import tempfile


from src.utils.memory_pool import MemoryPool, get_unified_memory_pool
 

logger = logging.getLogger(__name__)

class MessageType(Enum):
    """消息类型"""
    TRADE_SIGNAL = 1
    ACCOUNT_INFO = 2
    POSITION_UPDATE = 3
    ORDER_RESULT = 4
    SYSTEM_COMMAND = 5
    HEARTBEAT = 6

class MessageEncoding(Enum):
    """消息编码方式"""
    BINARY_STRUCT = "binary_struct"     # 二进制结构体
    MEMORY_VIEW = "memory_view"         # 内存视图
    CTYPES_STRUCT = "ctypes_struct"     # C类型结构体
    CUSTOM_PROTOCOL = "custom_protocol"  # 自定义协议

@dataclass
class MessageHeader:
    """消息头部结构"""
    magic_number: int = 0xABCD1234       # 魔数用于验证
    version: int = 1                     # 协议版本
    message_type: int = 0                # 消息类型
    message_length: int = 0              # 消息长度
    timestamp: int = 0                   # 时间戳（微秒）
    sequence_id: int = 0                 # 序列号
    source_id: int = 0                   # 源ID
    target_id: int = 0                   # 目标ID
    priority: int = 0                    # 优先级
    flags: int = 0                       # 标志位
    checksum: int = 0                    # 校验和
    reserved: int = 0                    # 保留字段

# 定义消息头部的C结构体
class MessageHeaderStruct(ctypes.Structure):
    _fields_ = [
        ("magic_number", ctypes.c_uint32),
        ("version", ctypes.c_uint16),
        ("message_type", ctypes.c_uint16),
        ("message_length", ctypes.c_uint32),
        ("timestamp", ctypes.c_uint64),
        ("sequence_id", ctypes.c_uint64),
        ("source_id", ctypes.c_uint32),
        ("target_id", ctypes.c_uint32),
        ("priority", ctypes.c_uint8),
        ("flags", ctypes.c_uint8),
        ("checksum", ctypes.c_uint16),
        ("reserved", ctypes.c_uint32),
    ]

HEADER_SIZE = ctypes.sizeof(MessageHeaderStruct)

@dataclass
class ZeroCopyMessage:
    """零拷贝消息"""
    header: MessageHeader
    payload: memoryview
    original_buffer: Optional[bytearray] = None

class SharedMemoryRegion:
    """共享内存区域"""
    
    def __init__(self, name: str, size: int):
        self.name = name
        self.size = size
        self.memory_map: Optional[mmap.mmap] = None
        self.temp_file: Optional[tempfile.NamedTemporaryFile] = None
        self._lock = threading.RLock()
        
        self._create_shared_memory()
    
    def _create_shared_memory(self):
        """创建共享内存"""
        try:
            # 使用临时文件创建内存映射
            self.temp_file = tempfile.NamedTemporaryFile(delete=False)
            
            # 预分配空间
            self.temp_file.write(b'\x00' * self.size)
            self.temp_file.flush()
            
            # 创建内存映射
            self.memory_map = mmap.mmap(
                self.temp_file.fileno(),
                self.size,
                access=mmap.ACCESS_WRITE
            )
            
            logger.info(f"✅ 创建共享内存区域: {self.name} ({self.size} bytes)")
            
        except Exception as e:
            logger.error(f"❌ 创建共享内存失败: {self.name} - {e}")
            raise
    
    def write(self, offset: int, data: bytes) -> bool:
        """写入数据"""
        with self._lock:
            try:
                if offset + len(data) > self.size:
                    logger.error(f"❌ 写入数据超出边界: {offset + len(data)} > {self.size}")
                    return False
                
                self.memory_map.seek(offset)
                self.memory_map.write(data)
                self.memory_map.flush()
                
                return True
                
            except Exception as e:
                logger.error(f"❌ 写入共享内存失败: {e}")
                return False
    
    def read(self, offset: int, length: int) -> Optional[bytes]:
        """读取数据"""
        with self._lock:
            try:
                if offset + length > self.size:
                    logger.error(f"❌ 读取数据超出边界: {offset + length} > {self.size}")
                    return None
                
                self.memory_map.seek(offset)
                data = self.memory_map.read(length)
                
                return data
                
            except Exception as e:
                logger.error(f"❌ 读取共享内存失败: {e}")
                return None
    
    def get_memory_view(self, offset: int, length: int) -> Optional[memoryview]:
        """获取内存视图（零拷贝）"""
        with self._lock:
            try:
                if offset + length > self.size:
                    return None
                
                # 创建内存视图，避免数据拷贝
                return memoryview(self.memory_map)[offset:offset + length]
                
            except Exception as e:
                logger.error(f"❌ 获取内存视图失败: {e}")
                return None
    
    def close(self):
        """关闭共享内存"""
        try:
            if self.memory_map:
                self.memory_map.close()
                self.memory_map = None
            
            if self.temp_file:
                self.temp_file.close()
                os.unlink(self.temp_file.name)
                self.temp_file = None
            
            logger.debug(f"🗑️ 共享内存区域已关闭: {self.name}")
            
        except Exception as e:
            logger.error(f"❌ 关闭共享内存失败: {e}")

class ZeroCopyMessageCodec:
    """零拷贝消息编解码器"""
    
    def __init__(self):
        self._sequence_counter = 0
        self._lock = threading.Lock()
        
        # 预定义的消息结构
        self._message_structures = {
            MessageType.TRADE_SIGNAL: self._define_trade_signal_structure(),
            MessageType.ACCOUNT_INFO: self._define_account_info_structure(),
            MessageType.POSITION_UPDATE: self._define_position_update_structure(),
            MessageType.ORDER_RESULT: self._define_order_result_structure(),
        }
    
    def _define_trade_signal_structure(self):
        """定义交易信号结构"""
        class TradeSignalStruct(ctypes.Structure):
            _fields_ = [
                ("signal_type", ctypes.c_uint8),      # 信号类型
                ("action", ctypes.c_uint8),           # 动作：0=BUY, 1=SELL, 2=CLOSE
                ("symbol", ctypes.c_char * 16),       # 交易品种
                ("volume", ctypes.c_double),          # 交易量
                ("price", ctypes.c_double),           # 价格
                ("stop_loss", ctypes.c_double),       # 止损
                ("take_profit", ctypes.c_double),     # 止盈
                ("magic_number", ctypes.c_uint32),    # 魔数
                ("comment", ctypes.c_char * 32),      # 注释
                ("expiration", ctypes.c_uint64),      # 过期时间
            ]
        
        return TradeSignalStruct
    
    def _define_account_info_structure(self):
        """定义账户信息结构"""
        class AccountInfoStruct(ctypes.Structure):
            _fields_ = [
                ("login", ctypes.c_uint32),
                ("balance", ctypes.c_double),
                ("equity", ctypes.c_double),
                ("margin", ctypes.c_double),
                ("free_margin", ctypes.c_double),
                ("margin_level", ctypes.c_double),
                ("profit", ctypes.c_double),
                ("server", ctypes.c_char * 64),
                ("currency", ctypes.c_char * 8),
                ("company", ctypes.c_char * 64),
            ]
        
        return AccountInfoStruct
    
    def _define_position_update_structure(self):
        """定义持仓更新结构"""
        class PositionUpdateStruct(ctypes.Structure):
            _fields_ = [
                ("ticket", ctypes.c_uint64),
                ("symbol", ctypes.c_char * 16),
                ("position_type", ctypes.c_uint8),    # 0=BUY, 1=SELL
                ("volume", ctypes.c_double),
                ("price_open", ctypes.c_double),
                ("price_current", ctypes.c_double),
                ("stop_loss", ctypes.c_double),
                ("take_profit", ctypes.c_double),
                ("profit", ctypes.c_double),
                ("swap", ctypes.c_double),
                ("commission", ctypes.c_double),
                ("magic_number", ctypes.c_uint32),
                ("comment", ctypes.c_char * 32),
                ("time_open", ctypes.c_uint64),
                ("time_update", ctypes.c_uint64),
            ]
        
        return PositionUpdateStruct
    
    def _define_order_result_structure(self):
        """定义订单结果结构"""
        class OrderResultStruct(ctypes.Structure):
            _fields_ = [
                ("request_id", ctypes.c_uint64),
                ("retcode", ctypes.c_uint32),
                ("deal", ctypes.c_uint64),
                ("order", ctypes.c_uint64),
                ("volume", ctypes.c_double),
                ("price", ctypes.c_double),
                ("bid", ctypes.c_double),
                ("ask", ctypes.c_double),
                ("comment", ctypes.c_char * 64),
                ("request_time", ctypes.c_uint64),
                ("response_time", ctypes.c_uint64),
            ]
        
        return OrderResultStruct
    
    def encode_message(self,
                      message_type: MessageType,
                      data: Dict[str, Any],
                      source_id: int = 0,
                      target_id: int = 0,
                      priority: int = 0) -> ZeroCopyMessage:
        """编码消息为零拷贝格式"""
        try:
            # 获取消息结构
            struct_class = self._message_structures.get(message_type)
            if not struct_class:
                raise ValueError(f"不支持的消息类型: {message_type}")
            
            # 创建结构体实例
            struct_instance = struct_class()
            
            # 填充数据
            self._fill_struct_from_dict(struct_instance, data)
            
            # 创建消息头部
            with self._lock:
                self._sequence_counter += 1
                sequence_id = self._sequence_counter
            
            header = MessageHeader(
                message_type=message_type.value,
                message_length=ctypes.sizeof(struct_class),
                timestamp=int(time.time() * 1000000),  # 微秒时间戳
                sequence_id=sequence_id,
                source_id=source_id,
                target_id=target_id,
                priority=priority
            )
            
            # 序列化头部和载荷
            header_bytes = self._serialize_header(header)
            payload_bytes = bytes(struct_instance)
            
            # 创建总缓冲区
            total_buffer = bytearray(len(header_bytes) + len(payload_bytes))
            total_buffer[:len(header_bytes)] = header_bytes
            total_buffer[len(header_bytes):] = payload_bytes
            
            # 创建载荷的内存视图
            payload_view = memoryview(total_buffer)[len(header_bytes):]
            
            return ZeroCopyMessage(
                header=header,
                payload=payload_view,
                original_buffer=total_buffer
            )
            
        except Exception as e:
            logger.error(f"❌ 消息编码失败: {e}")
            raise
    
    def decode_message(self, buffer: Union[bytes, memoryview]) -> Optional[Tuple[MessageHeader, Dict[str, Any]]]:
        """解码零拷贝消息"""
        try:
            if len(buffer) < HEADER_SIZE:
                logger.error("❌ 缓冲区太小，无法包含消息头部")
                return None
            
            # 解析头部
            header_data = buffer[:HEADER_SIZE]
            header = self._deserialize_header(header_data)
            
            if header.magic_number != 0xABCD1234:
                logger.error("❌ 消息头部魔数验证失败")
                return None
            
            # 获取消息类型
            try:
                message_type = MessageType(header.message_type)
            except ValueError:
                logger.error(f"❌ 未知消息类型: {header.message_type}")
                return None
            
            # 获取载荷数据
            payload_start = HEADER_SIZE
            payload_end = payload_start + header.message_length
            
            if len(buffer) < payload_end:
                logger.error("❌ 缓冲区太小，无法包含完整载荷")
                return None
            
            payload_data = buffer[payload_start:payload_end]
            
            # 解析载荷
            payload_dict = self._parse_payload(message_type, payload_data)
            
            return header, payload_dict
            
        except Exception as e:
            logger.error(f"❌ 消息解码失败: {e}")
            return None
    
    def _fill_struct_from_dict(self, struct_instance, data: Dict[str, Any]):
        """从字典填充结构体"""
        for field_name, field_type in struct_instance._fields_:
            if field_name in data:
                value = data[field_name]
                
                # 处理字符串类型
                if hasattr(field_type, '_length_'):  # c_char array
                    if isinstance(value, str):
                        value = value.encode('utf-8')[:field_type._length_ - 1]
                
                try:
                    setattr(struct_instance, field_name, value)
                except Exception as e:
                    logger.warning(f"⚠️ 设置字段失败: {field_name} = {value} - {e}")
    
    def _serialize_header(self, header: MessageHeader) -> bytes:
        """序列化消息头部"""
        header_struct = MessageHeaderStruct(
            magic_number=header.magic_number,
            version=header.version,
            message_type=header.message_type,
            message_length=header.message_length,
            timestamp=header.timestamp,
            sequence_id=header.sequence_id,
            source_id=header.source_id,
            target_id=header.target_id,
            priority=header.priority,
            flags=header.flags,
            checksum=header.checksum,
            reserved=header.reserved
        )
        
        return bytes(header_struct)
    
    def _deserialize_header(self, header_data: Union[bytes, memoryview]) -> MessageHeader:
        """反序列化消息头部"""
        header_struct = MessageHeaderStruct.from_buffer_copy(header_data)
        
        return MessageHeader(
            magic_number=header_struct.magic_number,
            version=header_struct.version,
            message_type=header_struct.message_type,
            message_length=header_struct.message_length,
            timestamp=header_struct.timestamp,
            sequence_id=header_struct.sequence_id,
            source_id=header_struct.source_id,
            target_id=header_struct.target_id,
            priority=header_struct.priority,
            flags=header_struct.flags,
            checksum=header_struct.checksum,
            reserved=header_struct.reserved
        )
    
    def _parse_payload(self, message_type: MessageType, payload_data: Union[bytes, memoryview]) -> Dict[str, Any]:
        """解析载荷数据"""
        try:
            struct_class = self._message_structures.get(message_type)
            if not struct_class:
                return {}
            
            # 从缓冲区创建结构体
            struct_instance = struct_class.from_buffer_copy(payload_data)
            
            # 转换为字典
            result = {}
            for field_name, field_type in struct_instance._fields_:
                value = getattr(struct_instance, field_name)
                
                # 处理字符串类型
                if hasattr(field_type, '_length_'):  # c_char array
                    if isinstance(value, bytes):
                        value = value.decode('utf-8').rstrip('\x00')
                
                result[field_name] = value
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 载荷解析失败: {e}")
            return {}

class ZeroCopyMessageBus:
    """
    零拷贝消息总线 - 架构唯一权威实现
    使用共享内存和内存映射实现高性能消息传递
    """
    
    def __init__(self, bus_name: str = "mt5_message_bus", region_size: int = 64 * 1024 * 1024):
        self.bus_name = bus_name
        self.region_size = region_size
        
        # 核心组件
        self.codec = ZeroCopyMessageCodec()
        self.memory_pool = MemoryPool, get_unified_memory_pool()
        
        # 共享内存区域
        self.shared_regions: Dict[str, SharedMemoryRegion] = {}
        
        # 消息订阅者
        self._subscribers: Dict[MessageType, List[Callable]] = {}
        
        # 性能统计
        self._stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'bytes_transmitted': 0,
            'zero_copy_operations': 0,
            'serialization_avoided': 0
        }
        
        # 运行状态
        self.running = False
        
        logger.info(f"🚌 零拷贝消息总线已创建: {bus_name}")
    
    async def start(self):
        """启动消息总线"""
        if self.running:
            logger.warning("消息总线已在运行")
            return
        
        self.running = True
        
        # 创建默认共享内存区域
        self._create_default_regions()
        
        logger.info("✅ 零拷贝消息总线已启动")
    
    async def stop(self):
        """停止消息总线"""
        if not self.running:
            return
        
        self.running = False
        
        # 关闭所有共享内存区域
        for region in self.shared_regions.values():
            region.close()
        
        self.shared_regions.clear()
        
        logger.info("🛑 零拷贝消息总线已停止")
    
    def _create_default_regions(self):
        """创建默认共享内存区域"""
        try:
            # 创建主消息区域
            main_region = SharedMemoryRegion(
                name=f"{self.bus_name}_main",
                size=self.region_size
            )
            self.shared_regions["main"] = main_region
            
            # 创建高优先级消息区域
            priority_region = SharedMemoryRegion(
                name=f"{self.bus_name}_priority", 
                size=self.region_size // 4
            )
            self.shared_regions["priority"] = priority_region
            
            logger.info("✅ 默认共享内存区域已创建")
            
        except Exception as e:
            logger.error(f"❌ 创建默认共享内存区域失败: {e}")
            raise
    
    async def send_message(self,
                          message_type: MessageType,
                          data: Dict[str, Any],
                          target_id: int = 0,
                          priority: int = 0,
                          use_shared_memory: bool = True) -> str:
        """发送零拷贝消息"""
        try:
            # 生成消息ID
            message_id = str(uuid.uuid4())
            
            # 编码消息
            zero_copy_msg = self.codec.encode_message(
                message_type=message_type,
                data=data,
                target_id=target_id,
                priority=priority
            )
            
            if use_shared_memory:
                # 使用共享内存发送
                success = await self._send_via_shared_memory(zero_copy_msg)
            else:
                # 使用直接内存发送
                success = await self._send_via_direct_memory(zero_copy_msg)
            
            if success:
                # 更新统计
                self._stats['messages_sent'] += 1
                self._stats['bytes_transmitted'] += len(zero_copy_msg.original_buffer)
                self._stats['zero_copy_operations'] += 1
                self._stats['serialization_avoided'] += 1
                
                logger.debug(f"📤 零拷贝消息已发送: {message_id}")
            
            return message_id
            
        except Exception as e:
            logger.error(f"❌ 发送零拷贝消息失败: {e}")
            raise
    
    async def _send_via_shared_memory(self, message: ZeroCopyMessage) -> bool:
        """通过共享内存发送消息"""
        try:
            # 选择共享内存区域
            region_name = "priority" if message.header.priority > 2 else "main"
            region = self.shared_regions.get(region_name)
            
            if not region:
                logger.error(f"❌ 共享内存区域不存在: {region_name}")
                return False
            
            # 写入消息到共享内存
            # 这里使用简化的偏移策略，实际应该使用环形缓冲区
            offset = (message.header.sequence_id * 1024) % (region.size - 1024)
            
            success = region.write(offset, message.original_buffer)
            
            if success:
                # 通知订阅者（使用信号量或事件）
                await self._notify_subscribers(message)
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 共享内存发送失败: {e}")
            return False
    
    async def _send_via_direct_memory(self, message: ZeroCopyMessage) -> bool:
        """通过直接内存发送消息"""
        try:
            # 直接调用订阅者，零拷贝传递
            await self._notify_subscribers(message)
            return True
            
        except Exception as e:
            logger.error(f"❌ 直接内存发送失败: {e}")
            return False
    
    async def _notify_subscribers(self, message: ZeroCopyMessage):
        """通知订阅者"""
        try:
            message_type = MessageType(message.header.message_type)
            subscribers = self._subscribers.get(message_type, [])
            
            # 解码消息（只在需要时才解码）
            if subscribers:
                header, payload_dict = self.codec.decode_message(message.original_buffer)
                
                # 并行通知所有订阅者
                tasks = []
                for subscriber in subscribers:
                    if asyncio.iscoroutinefunction(subscriber):
                        task = asyncio.create_task(subscriber(header, payload_dict))
                    else:
                        task = asyncio.create_task(
                            asyncio.get_event_loop().run_in_executor(
                                None, subscriber, header, payload_dict
                            )
                        )
                    tasks.append(task)
                
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)
            
            self._stats['messages_received'] += len(subscribers)
            
        except Exception as e:
            logger.error(f"❌ 通知订阅者失败: {e}")
    
    def subscribe(self, message_type: MessageType, callback: Callable):
        """订阅消息类型"""
        if message_type not in self._subscribers:
            self._subscribers[message_type] = []
        
        self._subscribers[message_type].append(callback)
        
        logger.info(f"📝 订阅消息类型: {message_type.name}")
    
    def unsubscribe(self, message_type: MessageType, callback: Callable):
        """取消订阅"""
        if message_type in self._subscribers:
            try:
                self._subscribers[message_type].remove(callback)
                if not self._subscribers[message_type]:
                    del self._subscribers[message_type]
                
                logger.info(f"🗑️ 取消订阅消息类型: {message_type.name}")
                
            except ValueError:
                logger.warning(f"⚠️ 回调未找到: {message_type.name}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            **self._stats,
            'shared_regions': len(self.shared_regions),
            'subscribers': sum(len(subs) for subs in self._subscribers.values()),
            'memory_pool_stats': self.memory_pool.get_stats() if hasattr(self.memory_pool, 'get_stats') else {}
        }

# 全局零拷贝消息总线实例
_global_message_bus: Optional[ZeroCopyMessageBus] = None

def get_zero_copy_message_bus(bus_name: str = "mt5_message_bus") -> ZeroCopyMessageBus:
    """获取全局零拷贝消息总线实例"""
    global _global_message_bus
    if _global_message_bus is None:
        _global_message_bus = ZeroCopyMessageBus(bus_name)
    return _global_message_bus

def reset_zero_copy_message_bus():
    """重置零拷贝消息总线（主要用于测试）"""
    global _global_message_bus
    _global_message_bus = None