# 分布式多进程隔离MT5跟单交易系统架构方案

## 1. 架构概述

本文档详细说明了如何将新创建的优化组件正确集成到现有的分布式多进程隔离MT5跟单交易系统中，同时保持系统的分布式特性和进程隔离要求。

## 2. MT5 API约束和架构设计原则

### 2.1 核心约束
- **MT5 API限制**：一个进程只能连接一个MT5终端
- **进程隔离**：每个MT5账户必须在独立的OS进程中运行
- **分布式部署**：支持跨主机的账户管理和信号传递
- **高性能要求**：微秒级的信号传递延迟

### 2.2 设计原则
- **单一职责**：每个组件只负责一个明确的功能
- **松耦合**：组件间通过接口通信，避免直接依赖
- **高内聚**：相关功能集中在同一组件内
- **故障隔离**：单个组件故障不影响整体系统

## 3. 组件架构和集成方案

### 3.1 进程架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        主机A (Host A)                            │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐   │
│  │            UnifiedMT5Coordinator (主进程)                │   │
│  │  ┌─────────────────┐  ┌─────────────────┐              │   │
│  │  │ DI Container     │  │Service Discovery│              │   │
│  │  └─────────────────┘  └─────────────────┘              │   │
│  │  ┌─────────────────┐  ┌─────────────────┐              │   │
│  │  │ Memory Pool      │  │ Zero-Copy Bus   │              │   │
│  │  └─────────────────┘  └─────────────────┘              │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────┐  ┌─────────────────────┐             │
│  │  MT5 Process 1      │  │  MT5 Process 2      │             │
│  │  Account: ACC001    │  │  Account: ACC002    │             │
│  │  ┌───────────────┐  │  │  ┌───────────────┐  │             │
│  │  │ MT5 Terminal  │  │  │  │ MT5 Terminal  │  │             │
│  │  └───────────────┘  │  │  └───────────────┘  │             │
│  │  ┌───────────────┐  │  │  ┌───────────────┐  │             │
│  │  │Monitor Thread │  │  │  │Monitor Thread │  │             │
│  │  └───────────────┘  │  │  └───────────────┘  │             │
│  │  ┌───────────────┐  │  │  ┌───────────────┐  │             │
│  │  │Executor Thread│  │  │  │Executor Thread│  │             │
│  │  └───────────────┘  │  │  └───────────────┘  │             │
│  └─────────────────────┘  └─────────────────────┘             │
│           ↕ 零拷贝通信              ↕ 零拷贝通信                  │
│  ┌──────────────────────────────────────────────┐             │
│  │        Shared Memory Region (mmap)           │             │
│  └──────────────────────────────────────────────┘             │
└─────────────────────────────────────────────────────────────────┘
                            ↕ NATS/Redis
┌─────────────────────────────────────────────────────────────────┐
│                        主机B (Host B)                            │
└─────────────────────────────────────────────────────────────────┘
```

### 3.2 信号流程（优化后）

#### 本地信号流（同主机）
```
1. MT5 Monitor (Process 1) 检测到交易信号
2. 获取 UnifiedMemoryPool 的预分配缓冲区
3. 使用 ZeroCopyMessageBus 将信号写入共享内存
4. MT5 Executor (Process 2) 直接从共享内存读取（零拷贝）
5. 执行交易
```

#### 跨主机信号流
```
1. MT5 Monitor (Host A) 检测到交易信号
2. 判断目标账户在远程主机
3. 使用 HybridQueueManager 通过 NATS/Redis 发送
4. Host B 的 HybridMessageRouter 接收信号
5. 路由到对应的 MT5 Executor 进程
6. 执行交易
```

## 4. 组件详细集成方案

### 4.1 依赖注入容器集成

```python
# src/core/mt5_process_manager.py 中的修改
class MT5ProcessManager:
    async def start_terminal_process_new(self, account_id: str, config: Dict):
        """启动新的MT5终端进程"""
        # 创建进程专用的依赖注入容器
        process_container = UnifiedDependencyContainer()
        
        # 注册进程内组件
        process_container.register_singleton(
            MT5AccountMonitor,
            factory=lambda: MT5AccountMonitor(account_id, config),
            required=True
        )
        
        process_container.register_singleton(
            MT5AccountExecutor,
            factory=lambda: MT5AccountExecutor(account_id, config),
            required=True
        )
        
        # 创建进程启动脚本
        process_script = self._generate_process_script(account_id, config)
        
        # 启动独立进程
        process = await asyncio.create_subprocess_exec(
            sys.executable,
            process_script,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        return process.pid
```

### 4.2 零拷贝通信实现

```python
# src/core/mt5_account_monitor.py 中的集成
class MT5AccountMonitor:
    def __init__(self, account_id: str, config: Dict):
        self.account_id = account_id
        self.zero_copy_bus = None
        self.memory_pool = None
        
    async def initialize(self):
        """初始化监听器"""
        # 连接到主机的共享内存池
        self.memory_pool = get_unified_memory_pool()
        
        # 连接到零拷贝消息总线
        self.zero_copy_bus = get_zero_copy_message_bus()
        
    async def on_trade_detected(self, trade_signal):
        """检测到交易信号时的处理"""
        # 判断目标账户位置
        if self.is_local_target(trade_signal.target_account):
            # 本地账户：使用零拷贝
            await self.send_via_zero_copy(trade_signal)
        else:
            # 远程账户：使用网络传输
            await self.send_via_network(trade_signal)
    
    async def send_via_zero_copy(self, signal):
        """通过零拷贝发送信号"""
        # 从内存池获取缓冲区
        buffer = self.memory_pool.allocate_zero_copy_buffer(1024)
        
        # 直接写入二进制数据（避免序列化）
        self._write_signal_to_buffer(buffer, signal)
        
        # 发送消息（只传递内存地址）
        await self.zero_copy_bus.send_message(
            MessageType.TRADE_SIGNAL,
            signal.to_dict(),
            use_shared_memory=True
        )
```

### 4.3 服务发现集成

```python
# src/core/mt5_process_launcher.py
class MT5ProcessLauncher:
    """MT5进程启动器 - 在每个MT5进程内运行"""
    
    async def main(self, account_id: str, config: Dict):
        # 创建进程内的服务发现客户端
        service_discovery = UnifiedServiceDiscovery()
        
        # 注册监听器服务
        monitor_service_id = await service_discovery.register_service(
            service_name=f"mt5-monitor-{account_id}",
            endpoint=ServiceEndpoint(
                host=config['host_id'],
                port=self.monitor_port,
                protocol="tcp"
            ),
            service_type=ServiceType.CORE,
            health_checker=self.check_monitor_health
        )
        
        # 注册执行器服务
        executor_service_id = await service_discovery.register_service(
            service_name=f"mt5-executor-{account_id}",
            endpoint=ServiceEndpoint(
                host=config['host_id'],
                port=self.executor_port,
                protocol="tcp"
            ),
            service_type=ServiceType.CORE,
            health_checker=self.check_executor_health
        )
```

### 4.4 直接信号路由集成

```python
# src/messaging/distributed_signal_router.py
class DistributedSignalRouter(DirectSignalRouter):
    """分布式信号路由器 - 支持本地和远程路由"""
    
    def __init__(self, host_id: str):
        super().__init__(host_id)
        self.local_executors = {}  # 本地执行器映射
        self.remote_hosts = {}     # 远程主机映射
        
    async def route_signal(self, signal: DirectSignal):
        """智能路由信号"""
        routing_decisions = []
        
        for target_account in signal.target_accounts:
            location = await self.get_account_location(target_account)
            
            if location.host_id == self.host_id:
                # 本地路由：使用零拷贝
                routing_decisions.append({
                    'account': target_account,
                    'method': SignalDeliveryMode.DIRECT_MEMORY,
                    'executor': self.local_executors[target_account]
                })
            else:
                # 远程路由：使用网络
                routing_decisions.append({
                    'account': target_account,
                    'method': SignalDeliveryMode.SHARED_MEMORY,
                    'host': location.host_id
                })
        
        # 并行执行路由
        await asyncio.gather(*[
            self.execute_routing(decision, signal)
            for decision in routing_decisions
        ])
```

## 5. 统一接口定义

### 5.1 进程管理接口

```python
from abc import ABC, abstractmethod

class IProcessManager(ABC):
    """进程管理器接口"""
    
    @abstractmethod
    async def start_process(self, account_id: str, config: Dict) -> int:
        """启动进程，返回PID"""
        
    @abstractmethod
    async def stop_process(self, account_id: str) -> bool:
        """停止进程"""
        
    @abstractmethod
    async def restart_process(self, account_id: str) -> bool:
        """重启进程"""
        
    @abstractmethod
    async def get_process_status(self, account_id: str) -> ProcessStatus:
        """获取进程状态"""
```

### 5.2 消息传递接口

```python
class IMessageTransport(ABC):
    """消息传递接口"""
    
    @abstractmethod
    async def send_local(self, target_process: int, message: Any) -> bool:
        """本地进程间通信"""
        
    @abstractmethod
    async def send_remote(self, target_host: str, message: Any) -> bool:
        """跨主机通信"""
        
    @abstractmethod
    async def subscribe(self, topic: str, handler: Callable) -> bool:
        """订阅消息"""
```

### 5.3 MT5组件接口

```python
class IMT5Component(ABC):
    """MT5组件基础接口"""
    
    @abstractmethod
    async def initialize(self, container: UnifiedDependencyContainer) -> bool:
        """初始化组件"""
        
    @abstractmethod
    async def start(self) -> bool:
        """启动组件"""
        
    @abstractmethod
    async def stop(self) -> bool:
        """停止组件"""
        
    @abstractmethod
    async def health_check(self) -> HealthStatus:
        """健康检查"""
```

## 6. 配置管理统一

### 6.1 系统默认配置
```yaml
# config/core/system_defaults.yaml
system:
  process:
    memory_pool_size: ********  # 16MB per process
    zero_copy_enabled: true
    monitor_thread_count: 1
    executor_thread_count: 2
    
  messaging:
    local_buffer_size: 1048576  # 1MB
    network_timeout: 5000       # 5 seconds
    retry_count: 3
    
  performance:
    queue_worker_count: 6
    batch_size: 100
    latency_target_us: 100     # 100 microseconds
```

### 6.2 进程配置模板
```yaml
# config/templates/process_template.yaml
process:
  account_id: ${ACCOUNT_ID}
  terminal_path: ${TERMINAL_PATH}
  
  components:
    monitor:
      enabled: true
      class: "MT5AccountMonitor"
      config:
        check_interval: 100  # milliseconds
        
    executor:
      enabled: true
      class: "MT5AccountExecutor"
      config:
        max_concurrent_orders: 10
        
  communication:
    zero_copy:
      enabled: true
      buffer_size: 1048576
    
    network:
      fallback: true
      protocol: "nats"
```

## 7. 性能优化关键点

### 7.1 内存管理优化
- 使用 `UnifiedMemoryPool` 预分配内存，避免频繁的内存分配/释放
- 每个进程有独立的内存区域，避免竞争
- 使用内存视图（memoryview）实现真正的零拷贝

### 7.2 进程间通信优化
- 本地进程使用共享内存（mmap）通信
- 避免序列化/反序列化，直接传递二进制数据
- 使用环形缓冲区减少锁竞争

### 7.3 网络通信优化
- 使用 `HybridQueueManager` 实现主备切换
- 批量发送减少网络往返
- 使用二进制协议替代JSON

## 8. 监控和运维

### 8.1 健康检查机制
```python
# 每个组件都实现健康检查
async def health_check_all_components():
    health_status = {}
    
    # 检查所有本地MT5进程
    for account_id, process_info in local_processes.items():
        status = await check_process_health(process_info['pid'])
        health_status[f"mt5_{account_id}"] = status
    
    # 检查消息系统
    health_status['messaging'] = await queue_manager.is_healthy()
    
    # 检查内存池
    health_status['memory_pool'] = memory_pool.get_performance_stats()
    
    return health_status
```

### 8.2 性能监控
```python
# 关键性能指标
performance_metrics = {
    'signal_latency_us': [],      # 信号延迟（微秒）
    'memory_usage_mb': 0,          # 内存使用
    'message_throughput': 0,       # 消息吞吐量
    'process_cpu_percent': {},     # 进程CPU使用率
}
```

## 9. 故障处理和恢复

### 9.1 进程级故障
- 自动检测进程崩溃并重启
- 保存进程状态用于恢复
- 通知相关依赖组件

### 9.2 网络故障
- 自动切换到备用通信方式
- 缓存未发送的消息
- 网络恢复后自动重传

### 9.3 主机级故障
- 其他主机接管故障主机的账户
- 基于服务发现的自动故障转移
- 保持交易连续性

## 10. 实施建议

### 10.1 第一阶段：本地优化
1. 实现进程内的监听器和执行器分离
2. 集成零拷贝消息传递
3. 验证延迟降低到微秒级

### 10.2 第二阶段：分布式增强
1. 完善服务发现机制
2. 实现跨主机的故障转移
3. 优化网络通信协议

### 10.3 第三阶段：生产就绪
1. 完善监控和告警
2. 性能调优和压力测试
3. 编写运维文档和SOP

## 11. 总结

通过以上架构设计和实施方案，我们成功地：

1. **保持了分布式多进程隔离架构**：每个MT5账户在独立进程中运行，满足API约束
2. **优化了性能**：通过零拷贝和直接路由，将延迟降低到微秒级
3. **提高了可靠性**：通过依赖注入和服务发现，实现了优雅降级和故障恢复
4. **统一了系统接口**：所有组件遵循一致的生命周期和通信接口

这个架构既保留了原有系统的分布式特性，又通过新的优化组件大幅提升了性能和可靠性。