#!/usr/bin/env python3
"""
统一架构验证脚本 - 硬迁移验证工具
验证所有新组件的集成状态和功能正确性
硬迁移：零向后兼容，强制统一，SSOT
"""
import asyncio
import sys
import logging
from pathlib import Path
from typing import Dict, Any, List
import time

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入统一架构组件
from src.core.unified_coordinator import UnifiedMT5Coordinator
from src.core.dependency_injection import get_container, reset_container
from src.core.service_discovery import get_service_discovery, reset_service_discovery
from src.messaging.direct_signal_router import get_direct_router, reset_direct_router
from src.messaging.zero_copy_messaging import get_zero_copy_message_bus, reset_zero_copy_message_bus
from src.utils.unified_memory_pool import get_unified_memory_pool, reset_unified_memory_pool
from src.messaging.hybrid_queue_manager import HybridQueueManager

logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

class UnifiedArchitectureValidator:
    """统一架构验证器"""
    
    def __init__(self):
        self.test_results: List[Dict[str, Any]] = []
        self.coordinator = None
        self.host_id = "test-validation-host"
        
    async def run_all_validations(self) -> bool:
        """运行所有验证测试"""
        logger.info("🚀 开始统一架构验证")
        
        try:
            # 重置所有全局状态
            await self._reset_global_components()
            
            # 验证组件独立性
            await self._validate_component_independence()
            
            # 验证依赖注入容器
            await self._validate_dependency_injection()
            
            # 验证服务发现机制
            await self._validate_service_discovery()
            
            # 验证统一内存池
            await self._validate_unified_memory_pool()
            
            # 验证零拷贝消息传递
            await self._validate_zero_copy_messaging()
            
            # 验证直接信号路由
            await self._validate_direct_signal_routing()
            
            # 验证统一协调器集成
            await self._validate_unified_coordinator()
            
            # 验证旧组件兼容性
            await self._validate_compatibility_layer()
            
            # 生成验证报告
            return self._generate_validation_report()
            
        except Exception as e:
            logger.error(f"❌ 验证过程异常: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return False
        finally:
            # 清理资源
            await self._cleanup_resources()
    
    async def _reset_global_components(self):
        """重置所有全局组件状态"""
        logger.info("🔄 重置全局组件状态")
        
        try:
            reset_container()
            reset_service_discovery()
            reset_direct_router()
            reset_zero_copy_message_bus()
            reset_unified_memory_pool()
            
            logger.info("✅ 全局组件状态重置完成")
            
        except Exception as e:
            logger.error(f"❌ 重置全局组件失败: {e}")
            raise
    
    async def _validate_component_independence(self):
        """验证组件独立性"""
        logger.info("🔍 验证组件独立性")
        
        start_time = time.time()
        success = True
        errors = []
        
        try:
            # 验证每个组件都可以独立实例化
            memory_pool = get_unified_memory_pool()
            message_bus = get_zero_copy_message_bus()
            direct_router = get_direct_router(self.host_id)
            container = get_container()
            service_discovery = get_service_discovery()
            
            # 验证组件间没有强依赖
            if memory_pool is None:
                errors.append("统一内存池实例化失败")
                success = False
            
            if message_bus is None:
                errors.append("零拷贝消息总线实例化失败")
                success = False
            
            if direct_router is None:
                errors.append("直接信号路由器实例化失败")
                success = False
            
            if container is None:
                errors.append("依赖注入容器实例化失败")
                success = False
            
            if service_discovery is None:
                errors.append("服务发现实例化失败")
                success = False
            
        except Exception as e:
            errors.append(f"组件独立性验证异常: {e}")
            success = False
        
        self.test_results.append({
            'test_name': '组件独立性验证',
            'success': success,
            'duration': time.time() - start_time,
            'errors': errors,
            'details': '验证所有核心组件可以独立实例化'
        })
        
        if success:
            logger.info("✅ 组件独立性验证通过")
        else:
            logger.error(f"❌ 组件独立性验证失败: {errors}")
    
    async def _validate_dependency_injection(self):
        """验证依赖注入容器"""
        logger.info("🔍 验证依赖注入容器")
        
        start_time = time.time()
        success = True
        errors = []
        
        try:
            container = get_container()
            
            # 测试服务注册
            class TestService:
                def __init__(self):
                    self.initialized = True
            
            container.register_singleton(
                TestService,
                factory=lambda: TestService(),
                required=True
            )
            
            # 测试异步初始化
            results = await container.initialize_all(allow_partial_failure=False)
            
            if TestService not in results:
                errors.append("测试服务未在初始化结果中")
                success = False
            elif not results[TestService].success:
                errors.append("测试服务初始化失败")
                success = False
            
            # 测试服务获取
            service_instance = await container.get(TestService)
            if service_instance is None:
                errors.append("无法获取测试服务实例")
                success = False
            elif not hasattr(service_instance, 'initialized'):
                errors.append("测试服务实例状态错误")
                success = False
            
            # 测试健康检查
            health_results = await container.health_check_all()
            if TestService not in health_results:
                errors.append("健康检查结果中缺少测试服务")
                success = False
            
        except Exception as e:
            errors.append(f"依赖注入验证异常: {e}")
            success = False
        
        self.test_results.append({
            'test_name': '依赖注入容器验证',
            'success': success,
            'duration': time.time() - start_time,
            'errors': errors,
            'details': '验证服务注册、初始化、获取和健康检查功能'
        })
        
        if success:
            logger.info("✅ 依赖注入容器验证通过")
        else:
            logger.error(f"❌ 依赖注入容器验证失败: {errors}")
    
    async def _validate_service_discovery(self):
        """验证服务发现机制"""
        logger.info("🔍 验证服务发现机制")
        
        start_time = time.time()
        success = True
        errors = []
        
        try:
            from src.core.service_discovery import ServiceEndpoint, ServiceType
            
            service_discovery = get_service_discovery()
            await service_discovery.start()
            
            # 测试服务注册
            test_endpoint = ServiceEndpoint(
                host="localhost",
                port=8080,
                protocol="http"
            )
            
            service_id = await service_discovery.register_service(
                service_name="test-service",
                endpoint=test_endpoint,
                service_type=ServiceType.CORE,
                metadata={"test": True}
            )
            
            if not service_id:
                errors.append("服务注册失败")
                success = False
            
            # 测试服务发现
            discovered_services = await service_discovery.discover_services("test-service")
            if len(discovered_services) == 0:
                errors.append("服务发现失败")
                success = False
            
            # 测试健康服务获取
            healthy_service = await service_discovery.get_healthy_service("test-service")
            if healthy_service is None:
                errors.append("获取健康服务失败")
                success = False
            
            # 测试服务注销
            unregister_success = await service_discovery.unregister_service(service_id)
            if not unregister_success:
                errors.append("服务注销失败")
                success = False
            
            await service_discovery.stop()
            
        except Exception as e:
            errors.append(f"服务发现验证异常: {e}")
            success = False
        
        self.test_results.append({
            'test_name': '服务发现机制验证',
            'success': success,
            'duration': time.time() - start_time,
            'errors': errors,
            'details': '验证服务注册、发现、健康检查和注销功能'
        })
        
        if success:
            logger.info("✅ 服务发现机制验证通过")
        else:
            logger.error(f"❌ 服务发现机制验证失败: {errors}")
    
    async def _validate_unified_memory_pool(self):
        """验证统一内存池"""
        logger.info("🔍 验证统一内存池")
        
        start_time = time.time()
        success = True
        errors = []
        
        try:
            memory_pool = get_unified_memory_pool()
            await memory_pool.start()
            
            # 测试内存分配
            block = memory_pool.allocate_memory(1024, "test_owner")
            if block is None:
                errors.append("内存块分配失败")
                success = False
            
            # 测试零拷贝缓冲区
            buffer = memory_pool.allocate_zero_copy_buffer(512)
            if buffer is None:
                errors.append("零拷贝缓冲区分配失败")
                success = False
            
            # 测试对象池
            def test_factory():
                return {"created": True}
            
            object_pool = memory_pool.create_object_pool("test_pool", test_factory, max_size=10)
            obj = object_pool.acquire()
            if obj is None or not obj.get("created"):
                errors.append("对象池功能异常")
                success = False
            
            object_pool.release(obj)
            
            # 测试内存释放
            if block:
                release_success = memory_pool.deallocate_memory(block)
                if not release_success:
                    errors.append("内存块释放失败")
                    success = False
            
            # 测试性能统计
            stats = memory_pool.get_performance_stats()
            if 'global_stats' not in stats:
                errors.append("性能统计功能异常")
                success = False
            
            await memory_pool.stop()
            
        except Exception as e:
            errors.append(f"统一内存池验证异常: {e}")
            success = False
        
        self.test_results.append({
            'test_name': '统一内存池验证',
            'success': success,
            'duration': time.time() - start_time,
            'errors': errors,
            'details': '验证内存分配、零拷贝缓冲区、对象池和性能统计功能'
        })
        
        if success:
            logger.info("✅ 统一内存池验证通过")
        else:
            logger.error(f"❌ 统一内存池验证失败: {errors}")
    
    async def _validate_zero_copy_messaging(self):
        """验证零拷贝消息传递"""
        logger.info("🔍 验证零拷贝消息传递")
        
        start_time = time.time()
        success = True
        errors = []
        
        try:
            from src.messaging.zero_copy_messaging import MessageType
            
            message_bus = get_zero_copy_message_bus()
            await message_bus.start()
            
            # 测试消息发送和订阅
            received_messages = []
            
            async def test_subscriber(header, payload):
                received_messages.append((header, payload))
            
            message_bus.subscribe(MessageType.TRADE_SIGNAL, test_subscriber)
            
            # 发送测试消息
            test_data = {
                'signal_type': 1,
                'action': 0,  # BUY
                'symbol': 'EURUSD',
                'volume': 1.0,
                'price': 1.1000,
                'stop_loss': 1.0950,
                'take_profit': 1.1100,
                'magic_number': 12345,
                'comment': 'test_signal',
                'expiration': int(time.time() + 3600)
            }
            
            message_id = await message_bus.send_message(
                MessageType.TRADE_SIGNAL,
                test_data,
                target_id=1,
                priority=2
            )
            
            if not message_id:
                errors.append("消息发送失败")
                success = False
            
            # 等待消息处理
            await asyncio.sleep(0.1)
            
            if len(received_messages) == 0:
                errors.append("消息接收失败")
                success = False
            else:
                header, payload = received_messages[0]
                if payload.get('symbol') != 'EURUSD':
                    errors.append("消息内容错误")
                    success = False
            
            # 测试性能统计
            stats = message_bus.get_performance_stats()
            if stats.get('messages_sent', 0) == 0:
                errors.append("性能统计异常")
                success = False
            
            message_bus.unsubscribe(MessageType.TRADE_SIGNAL, test_subscriber)
            await message_bus.stop()
            
        except Exception as e:
            errors.append(f"零拷贝消息传递验证异常: {e}")
            success = False
        
        self.test_results.append({
            'test_name': '零拷贝消息传递验证',
            'success': success,
            'duration': time.time() - start_time,
            'errors': errors,
            'details': '验证消息编码、发送、订阅和零拷贝传递功能'
        })
        
        if success:
            logger.info("✅ 零拷贝消息传递验证通过")
        else:
            logger.error(f"❌ 零拷贝消息传递验证失败: {errors}")
    
    async def _validate_direct_signal_routing(self):
        """验证直接信号路由"""
        logger.info("🔍 验证直接信号路由")
        
        start_time = time.time()
        success = True
        errors = []
        
        try:
            from src.messaging.direct_signal_router import DirectSignalExecutor, SignalDeliveryMode
            from src.messaging.priority_queue import MessagePriority
            
            direct_router = get_direct_router(self.host_id)
            await direct_router.start()
            
            # 创建测试执行器
            test_executor = DirectSignalExecutor("test_account")
            
            # 注册测试信号处理器
            executed_signals = []
            
            def test_handler(signal_data):
                executed_signals.append(signal_data)
                return {"status": "executed"}
            
            test_executor.register_handler("test_signal", test_handler)
            
            # 注册执行器和路由
            direct_router.register_master_account("master_test", ["test_account"])
            direct_router.register_executor("test_account", test_executor)
            
            # 发送直接信号
            signal_id = await direct_router.send_direct_signal(
                master_account="master_test",
                signal_type="test_signal",
                signal_data={"action": "buy", "symbol": "EURUSD"},
                priority=MessagePriority.SIGNAL_EXECUTION,
                delivery_mode=SignalDeliveryMode.DIRECT_MEMORY
            )
            
            if not signal_id:
                errors.append("直接信号发送失败")
                success = False
            
            # 等待信号处理
            await asyncio.sleep(0.1)
            
            if len(executed_signals) == 0:
                errors.append("信号执行失败")
                success = False
            else:
                executed_signal = executed_signals[0]
                if executed_signal.get('symbol') != 'EURUSD':
                    errors.append("信号数据错误")
                    success = False
            
            # 测试性能统计
            stats = direct_router.get_performance_stats()
            if stats.get('total_signals', 0) == 0:
                errors.append("性能统计异常")
                success = False
            
            await direct_router.stop()
            
        except Exception as e:
            errors.append(f"直接信号路由验证异常: {e}")
            success = False
        
        self.test_results.append({
            'test_name': '直接信号路由验证',
            'success': success,
            'duration': time.time() - start_time,
            'errors': errors,
            'details': '验证信号直达路由、执行器注册和性能统计功能'
        })
        
        if success:
            logger.info("✅ 直接信号路由验证通过")
        else:
            logger.error(f"❌ 直接信号路由验证失败: {errors}")
    
    async def _validate_unified_coordinator(self):
        """验证统一协调器集成"""
        logger.info("🔍 验证统一协调器集成")
        
        start_time = time.time()
        success = True
        errors = []
        
        try:
            # 创建测试配置文件路径
            test_config_path = "config/core/system.yaml"
            
            # 创建统一协调器
            self.coordinator = UnifiedMT5Coordinator(self.host_id, test_config_path)
            
            # 测试初始化（允许部分失败）
            init_success = await self.coordinator.initialize(allow_partial_failure=True)
            if not init_success:
                errors.append("统一协调器初始化失败")
                success = False
            
            # 测试系统状态获取
            status = await self.coordinator.get_system_status()
            if 'host_id' not in status:
                errors.append("系统状态格式错误")
                success = False
            
            if status['host_id'] != self.host_id:
                errors.append("主机ID不匹配")
                success = False
            
            # 测试组件状态
            components = status.get('components', {})
            expected_components = ['account_manager', 'queue_manager', 'message_router', 'connection_pool']
            
            for component in expected_components:
                if component not in components:
                    errors.append(f"缺少组件状态: {component}")
                    success = False
            
            # 测试容器状态
            container_status = status.get('container_status', {})
            if 'total_services' not in container_status:
                errors.append("容器状态格式错误")
                success = False
            
        except Exception as e:
            errors.append(f"统一协调器验证异常: {e}")
            success = False
        
        self.test_results.append({
            'test_name': '统一协调器集成验证',
            'success': success,
            'duration': time.time() - start_time,
            'errors': errors,
            'details': '验证协调器初始化、状态获取和组件集成功能'
        })
        
        if success:
            logger.info("✅ 统一协调器集成验证通过")
        else:
            logger.error(f"❌ 统一协调器集成验证失败: {errors}")
    
    async def _validate_compatibility_layer(self):
        """验证旧组件兼容性"""
        logger.info("🔍 验证旧组件兼容性")
        
        start_time = time.time()
        success = True
        errors = []
        
        try:
            # 测试兼容层导入
            from src.core.mt5_coordinator import DistributedMT5Coordinator, get_migration_guide
            
            # 测试兼容层实例化
            compat_coordinator = DistributedMT5Coordinator(self.host_id, "config/core/system.yaml")
            
            if not hasattr(compat_coordinator, '_unified_coordinator'):
                errors.append("兼容层内部统一协调器缺失")
                success = False
            
            # 测试属性映射
            if compat_coordinator.host_id != self.host_id:
                errors.append("兼容层属性映射错误")
                success = False
            
            # 测试迁移指南
            guide = get_migration_guide()
            if "UnifiedMT5Coordinator" not in guide:
                errors.append("迁移指南内容错误")
                success = False
            
        except Exception as e:
            errors.append(f"兼容层验证异常: {e}")
            success = False
        
        self.test_results.append({
            'test_name': '兼容层验证',
            'success': success,
            'duration': time.time() - start_time,
            'errors': errors,
            'details': '验证向后兼容性和迁移指南功能'
        })
        
        if success:
            logger.info("✅ 兼容层验证通过")
        else:
            logger.error(f"❌ 兼容层验证失败: {errors}")
    
    def _generate_validation_report(self) -> bool:
        """生成验证报告"""
        logger.info("📊 生成验证报告")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        total_duration = sum(result['duration'] for result in self.test_results)
        
        print("\n" + "="*80)
        print("🔍 统一架构验证报告")
        print("="*80)
        print(f"📊 测试总数: {total_tests}")
        print(f"✅ 通过测试: {passed_tests}")
        print(f"❌ 失败测试: {failed_tests}")
        print(f"⏱️  总耗时: {total_duration:.2f}s")
        print(f"📈 成功率: {(passed_tests/total_tests*100):.1f}%")
        
        print("\n📋 详细结果:")
        for i, result in enumerate(self.test_results, 1):
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            print(f"{i:2d}. {status} - {result['test_name']} ({result['duration']:.2f}s)")
            
            if result['errors']:
                for error in result['errors']:
                    print(f"    ⚠️  {error}")
        
        if failed_tests > 0:
            print("\n❌ 验证失败，请检查以上错误并修复后重试")
            return False
        else:
            print("\n🎉 所有验证测试通过！统一架构运行正常")
            return True
    
    async def _cleanup_resources(self):
        """清理资源"""
        logger.info("🧹 清理测试资源")
        
        try:
            if self.coordinator:
                await self.coordinator.stop()
            
            # 重置全局组件
            await self._reset_global_components()
            
            logger.info("✅ 资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 资源清理异常: {e}")

async def main():
    """主函数"""
    print("🚀 统一架构验证器")
    print("="*50)
    
    validator = UnifiedArchitectureValidator()
    
    try:
        success = await validator.run_all_validations()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        logger.info("⚠️ 验证被用户中断")
        return 130
    except Exception as e:
        logger.error(f"❌ 验证器异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)