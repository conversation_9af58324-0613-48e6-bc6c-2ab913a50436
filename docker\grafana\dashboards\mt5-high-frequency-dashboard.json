{"dashboard": {"id": null, "title": "MT5高频异步轮询系统监控", "tags": ["mt5", "trading", "high-frequency"], "style": "dark", "timezone": "browser", "refresh": "1s", "time": {"from": "now-5m", "to": "now"}, "panels": [{"id": 1, "title": "实时交易延迟 (P95)", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(mt5_signal_latency_ms_bucket[30s]))", "legendFormat": "P95延迟"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 20}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "信号处理吞吐量", "type": "stat", "targets": [{"expr": "rate(mt5_signals_processed_total[1m])", "legendFormat": "信号/秒"}], "fieldConfig": {"defaults": {"unit": "ops", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 100}, {"color": "green", "value": 500}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "系统健康评分", "type": "gauge", "targets": [{"expr": "mt5_system_health_score", "legendFormat": "健康评分"}], "fieldConfig": {"defaults": {"min": 0, "max": 100, "unit": "percent", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 60}, {"color": "green", "value": 80}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "活跃连接数", "type": "stat", "targets": [{"expr": "mt5_active_connections", "legendFormat": "连接数"}], "fieldConfig": {"defaults": {"unit": "short", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "延迟分布趋势", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(mt5_signal_latency_ms_bucket[30s]))", "legendFormat": "P50"}, {"expr": "histogram_quantile(0.95, rate(mt5_signal_latency_ms_bucket[30s]))", "legendFormat": "P95"}, {"expr": "histogram_quantile(0.99, rate(mt5_signal_latency_ms_bucket[30s]))", "legendFormat": "P99"}], "fieldConfig": {"defaults": {"unit": "ms", "custom": {"drawStyle": "line", "lineInterpolation": "smooth", "pointSize": 3}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "信号处理速率", "type": "timeseries", "targets": [{"expr": "rate(mt5_signals_sent_total[1m])", "legendFormat": "发送速率"}, {"expr": "rate(mt5_signals_received_total[1m])", "legendFormat": "接收速率"}, {"expr": "rate(mt5_signals_executed_total[1m])", "legendFormat": "执行速率"}], "fieldConfig": {"defaults": {"unit": "ops", "custom": {"drawStyle": "line", "lineInterpolation": "smooth"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "错误率监控", "type": "timeseries", "targets": [{"expr": "rate(mt5_signals_failed_total[1m]) / rate(mt5_signals_total[1m]) * 100", "legendFormat": "信号失败率"}, {"expr": "rate(mt5_execution_errors_total[1m]) / rate(mt5_executions_total[1m]) * 100", "legendFormat": "执行错误率"}], "fieldConfig": {"defaults": {"unit": "percent", "custom": {"drawStyle": "line", "lineInterpolation": "smooth"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "系统资源使用率", "type": "timeseries", "targets": [{"expr": "rate(process_cpu_seconds_total[1m]) * 100", "legendFormat": "CPU使用率"}, {"expr": "process_resident_memory_bytes / 1024 / 1024", "legendFormat": "内存使用(MB)"}], "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "smooth"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "NATS消息队列状态", "type": "timeseries", "targets": [{"expr": "nats_varz_in_msgs", "legendFormat": "入站消息"}, {"expr": "nats_varz_out_msgs", "legendFormat": "出站消息"}, {"expr": "nats_varz_slow_consumers", "legendFormat": "慢消费者"}], "fieldConfig": {"defaults": {"unit": "short", "custom": {"drawStyle": "line", "lineInterpolation": "smooth"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 10, "title": "Redis性能指标", "type": "timeseries", "targets": [{"expr": "redis_commands_processed_total", "legendFormat": "命令处理数"}, {"expr": "redis_connected_clients", "legendFormat": "连接客户端"}, {"expr": "redis_memory_used_bytes / 1024 / 1024", "legendFormat": "内存使用(MB)"}], "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "smooth"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(mt5_signals_total, instance)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "部署事件", "datasource": "Prometheus", "expr": "changes(mt5_system_start_time[1h])", "titleFormat": "系统重启", "textFormat": "MT5系统重新启动"}]}}, "overwrite": true}