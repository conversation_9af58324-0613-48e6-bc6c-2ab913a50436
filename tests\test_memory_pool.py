#!/usr/bin/env python3
"""
内存池优化功能单元测试
"""
import pytest
import time
import threading
from unittest.mock import Mock, patch

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.unified_batch_processor import AdvancedMemoryPool, get_memory_pool


class TestAdvancedMemoryPool:
    """测试高级内存池功能"""

    def test_memory_pool_initialization(self):
        """测试内存池初始化"""
        pool = AdvancedMemoryPool(max_pool_size=500)
        
        assert pool.max_pool_size == 500
        assert len(pool.object_pools) == 0
        assert pool.pool_stats['total_created'] == 0
        assert pool.pool_stats['total_reused'] == 0

    def test_object_creation_and_reuse(self):
        """测试对象创建和复用"""
        pool = AdvancedMemoryPool(max_pool_size=100)
        
        # 第一次获取对象（创建新对象）
        obj1 = pool.get_optimized_object('list')
        assert isinstance(obj1, list)
        assert pool.pool_stats['total_created'] == 1
        assert pool.pool_stats['total_reused'] == 0
        assert pool.pool_stats['pool_misses'] == 1
        
        # 归还对象
        pool.return_object('list', obj1)
        assert pool.pool_stats['total_returned'] == 1
        
        # 第二次获取对象（应该复用）
        obj2 = pool.get_optimized_object('list')
        assert obj2 is obj1  # 应该是同一个对象
        assert pool.pool_stats['total_created'] == 1
        assert pool.pool_stats['total_reused'] == 1
        assert pool.pool_stats['pool_hits'] == 1

    def test_different_object_types(self):
        """测试不同对象类型"""
        pool = AdvancedMemoryPool()
        
        # 测试各种对象类型
        list_obj = pool.get_optimized_object('list')
        dict_obj = pool.get_optimized_object('dict')
        set_obj = pool.get_optimized_object('set')
        batch_obj = pool.get_optimized_object('batch_container')
        result_obj = pool.get_optimized_object('result_container')
        
        assert isinstance(list_obj, list)
        assert isinstance(dict_obj, dict)
        assert isinstance(set_obj, set)
        assert isinstance(batch_obj, dict)
        assert isinstance(result_obj, dict)
        
        # 检查batch_container的结构
        assert 'items' in batch_obj
        assert 'metadata' in batch_obj
        assert 'timestamp' in batch_obj
        assert 'processed' in batch_obj
        
        # 检查result_container的结构
        assert 'results' in result_obj
        assert 'errors' in result_obj
        assert 'success_count' in result_obj
        assert 'error_count' in result_obj

    def test_object_state_reset(self):
        """测试对象状态重置"""
        pool = AdvancedMemoryPool()
        
        # 创建并修改list对象
        list_obj = pool.get_optimized_object('list')
        list_obj.extend([1, 2, 3])
        assert len(list_obj) == 3
        
        # 归还对象
        pool.return_object('list', list_obj)
        
        # 重新获取对象，应该已被重置
        reused_obj = pool.get_optimized_object('list')
        assert len(reused_obj) == 0  # 应该被清空
        assert reused_obj is list_obj  # 但应该是同一个对象

    def test_batch_container_reset(self):
        """测试批次容器重置"""
        pool = AdvancedMemoryPool()
        
        # 获取并修改batch_container
        batch_obj = pool.get_optimized_object('batch_container')
        batch_obj['items'].extend(['item1', 'item2'])
        batch_obj['metadata']['key'] = 'value'
        batch_obj['processed'] = True
        
        # 归还对象
        pool.return_object('batch_container', batch_obj)
        
        # 重新获取，应该被重置
        reused_obj = pool.get_optimized_object('batch_container')
        assert len(reused_obj['items']) == 0
        assert len(reused_obj['metadata']) == 0
        assert reused_obj['processed'] is False
        assert reused_obj is batch_obj

    def test_pool_size_limit(self):
        """测试池大小限制"""
        pool = AdvancedMemoryPool(max_pool_size=2)
        
        # 创建多个对象
        obj1 = pool.get_optimized_object('list')
        obj2 = pool.get_optimized_object('list')
        obj3 = pool.get_optimized_object('list')
        
        # 归还对象
        pool.return_object('list', obj1)
        pool.return_object('list', obj2)
        pool.return_object('list', obj3)  # 这个应该被忽略，因为池已满
        
        # 检查池的统计
        stats = pool.get_pool_stats()
        assert stats['total_returned'] == 2  # 只有前两个被接受

    def test_custom_factory_function(self):
        """测试自定义工厂函数"""
        pool = AdvancedMemoryPool()
        
        def custom_factory(size=10):
            return [0] * size
        
        # 使用自定义工厂
        obj = pool.get_optimized_object('custom', custom_factory, size=5)
        assert len(obj) == 5
        assert all(x == 0 for x in obj)

    def test_thread_safety(self):
        """测试线程安全"""
        pool = AdvancedMemoryPool(max_pool_size=1000)
        results = []
        errors = []
        
        def worker_thread(thread_id):
            try:
                for i in range(100):
                    # 获取对象
                    obj = pool.get_optimized_object('list')
                    obj.append(f"thread_{thread_id}_item_{i}")
                    
                    # 短暂持有
                    time.sleep(0.001)
                    
                    # 归还对象
                    pool.return_object('list', obj)
                    
                results.append(f"thread_{thread_id}_completed")
            except Exception as e:
                errors.append(f"thread_{thread_id}_error: {e}")
        
        # 启动多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        assert len(errors) == 0, f"Thread errors: {errors}"
        assert len(results) == 5
        
        # 检查统计信息
        stats = pool.get_pool_stats()
        assert stats['total_created'] > 0
        assert stats['total_reused'] > 0

    def test_pool_statistics(self):
        """测试池统计信息"""
        pool = AdvancedMemoryPool(max_pool_size=10)
        
        # 执行一些操作
        obj1 = pool.get_optimized_object('list')
        obj2 = pool.get_optimized_object('dict')
        pool.return_object('list', obj1)
        obj3 = pool.get_optimized_object('list')  # 应该复用obj1
        
        stats = pool.get_pool_stats()
        
        # 检查统计字段
        assert 'total_created' in stats
        assert 'total_reused' in stats
        assert 'total_returned' in stats
        assert 'pool_hits' in stats
        assert 'pool_misses' in stats
        assert 'total_pooled_objects' in stats
        assert 'active_pools' in stats
        assert 'hit_rate_percent' in stats
        assert 'memory_savings_estimate' in stats
        
        # 检查具体数值
        assert stats['total_created'] == 2  # list和dict各一个
        assert stats['total_reused'] == 1   # list被复用一次
        assert stats['pool_hits'] == 1
        assert stats['pool_misses'] == 2
        assert stats['hit_rate_percent'] > 0

    def test_pool_cleanup(self):
        """测试池清理"""
        pool = AdvancedMemoryPool()
        
        # 添加一些对象
        obj1 = pool.get_optimized_object('list')
        obj2 = pool.get_optimized_object('dict')
        pool.return_object('list', obj1)
        pool.return_object('dict', obj2)
        
        # 清理前检查
        stats_before = pool.get_pool_stats()
        assert stats_before['total_pooled_objects'] > 0
        
        # 清理
        pool.cleanup_pools()
        
        # 清理后检查
        stats_after = pool.get_pool_stats()
        assert stats_after['total_pooled_objects'] == 0
        assert len(pool.object_pools) == 0

    def test_global_memory_pool(self):
        """测试全局内存池实例"""
        # 获取全局实例
        pool1 = get_memory_pool()
        pool2 = get_memory_pool()
        
        # 应该是同一个实例
        assert pool1 is pool2
        
        # 测试基本功能
        obj = pool1.get_optimized_object('list')
        assert isinstance(obj, list)

    def test_error_handling(self):
        """测试错误处理"""
        pool = AdvancedMemoryPool()
        
        # 测试未知对象类型
        with pytest.raises(ValueError):
            pool.get_optimized_object('unknown_type')
        
        # 测试工厂函数错误
        def failing_factory():
            raise RuntimeError("Factory failed")
        
        with pytest.raises(RuntimeError):
            pool.get_optimized_object('custom', failing_factory)

    def test_memory_estimate(self):
        """测试内存使用估算"""
        pool = AdvancedMemoryPool()
        
        # 添加一些对象到池中
        for i in range(10):
            obj = pool.get_optimized_object('list')
            pool.return_object('list', obj)
        
        stats = pool.get_pool_stats()
        
        # 检查内存估算
        assert stats['memory_savings_estimate'] >= 0
        assert isinstance(stats['memory_savings_estimate'], (int, float))

    def test_hit_rate_calculation(self):
        """测试命中率计算"""
        pool = AdvancedMemoryPool()
        
        # 第一次获取（miss）
        obj1 = pool.get_optimized_object('list')
        pool.return_object('list', obj1)
        
        # 第二次获取（hit）
        obj2 = pool.get_optimized_object('list')
        pool.return_object('list', obj2)
        
        # 第三次获取（hit）
        obj3 = pool.get_optimized_object('list')
        
        stats = pool.get_pool_stats()
        
        # 1次miss，2次hit，命中率应该是66.67%
        expected_hit_rate = 2 / 3 * 100
        assert abs(stats['hit_rate_percent'] - expected_hit_rate) < 0.01


if __name__ == '__main__':
    pytest.main([__file__])