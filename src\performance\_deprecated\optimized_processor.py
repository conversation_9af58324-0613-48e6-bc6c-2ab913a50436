#!/usr/bin/env python3
"""
集成的性能优化处理器
与现有架构兼容的性能优化组件
"""
import asyncio
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector
from ..messaging.message_types import TradeSignal, MessageEnvelope
from ..messaging.priority_queue import PriorityMessageQueue, MessagePriority
from ..infrastructure.connection_pool import ConnectionPool

logger = get_logger(__name__)
metrics = get_metrics_collector()


@dataclass
class OptimizedConfig:
    """优化配置"""
    enable_batch_processing: bool = True
    batch_size: int = 50
    batch_timeout_ms: int = 100
    enable_connection_pool: bool = True
    max_connections: int = 10
    enable_priority_queue: bool = True
    queue_workers: int = 4


class OptimizedProcessor:
    """集成的优化处理器"""
    
    def __init__(self, config: OptimizedConfig = None):
        self.config = config or OptimizedConfig()
        self.running = False
        
        # 组件引用
        self.priority_queue: Optional[PriorityMessageQueue] = None
        self.connection_pool: Optional[ConnectionPool] = None
        
        # 批处理缓冲区
        self._batch_buffer: List[Dict[str, Any]] = []
        self._batch_lock = asyncio.Lock()
        self._batch_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self.stats = {
            'messages_processed': 0,
            'batches_processed': 0,
            'avg_processing_time_ms': 0.0,
            'total_processing_time_ms': 0.0
        }
    
    async def initialize(self, priority_queue: PriorityMessageQueue = None, 
                        connection_pool: ConnectionPool = None):
        """初始化优化器"""
        logger.info("初始化性能优化处理器...")
        
        try:
            # 使用注入的组件或创建新的
            if self.config.enable_priority_queue:
                if priority_queue:
                    self.priority_queue = priority_queue
                else:
                    from ..messaging.priority_queue import get_priority_queue
                    self.priority_queue = get_priority_queue()
                    await self.priority_queue.start_workers(self.config.queue_workers)
                
                # 注册处理器
                self.priority_queue.register_processor(
                    MessagePriority.CRITICAL, self._process_critical_signal
                )
                self.priority_queue.register_processor(
                    MessagePriority.HIGH, self._process_high_signal
                )
                self.priority_queue.register_processor(
                    MessagePriority.NORMAL, self._process_normal_signal
                )
            
            if self.config.enable_connection_pool:
                if connection_pool:
                    self.connection_pool = connection_pool
                else:
                    from ..infrastructure.connection_pool import get_connection_pool
                    self.connection_pool = get_connection_pool()
                    await self.connection_pool.start()
            
            # 启动批处理器
            if self.config.enable_batch_processing:
                self._batch_task = asyncio.create_task(self._batch_processor())
            
            self.running = True
            logger.info("✅ 性能优化处理器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 性能优化处理器初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭优化器"""
        logger.info("关闭性能优化处理器...")
        
        self.running = False
        
        try:
            # 停止批处理器
            if self._batch_task:
                self._batch_task.cancel()
                try:
                    await self._batch_task
                except asyncio.CancelledError:
                    pass
            
            # 处理剩余批次
            async with self._batch_lock:
                if self._batch_buffer:
                    await self._flush_batch()
            
            # 关闭优先级队列
            if self.priority_queue:
                await self.priority_queue.stop_workers()
            
            # 关闭连接池
            if self.connection_pool:
                await self.connection_pool.stop()
            
            logger.info("✅ 性能优化处理器已关闭")
            
        except Exception as e:
            logger.error(f"❌ 关闭性能优化处理器失败: {e}")
    
    async def process_signal(self, signal: TradeSignal, 
                           priority: MessagePriority = MessagePriority.NORMAL) -> bool:
        """处理交易信号"""
        start_time = time.time()
        
        try:
            if self.config.enable_batch_processing:
                # 添加到批处理缓冲区
                async with self._batch_lock:
                    self._batch_buffer.append({
                        'signal': signal,
                        'priority': priority,
                        'timestamp': start_time
                    })
                    
                    # 如果缓冲区满了，立即处理
                    if len(self._batch_buffer) >= self.config.batch_size:
                        await self._flush_batch()
                
                return True
            
            elif self.priority_queue:
                # 直接入队
                success = await self.priority_queue.enqueue(
                    priority, signal.__dict__, signal.signal_id
                )
                
                if success:
                    self._update_stats(time.time() - start_time)
                
                return success
            
            else:
                # 直接处理
                await self._process_signal_direct(signal)
                self._update_stats(time.time() - start_time)
                return True
                
        except Exception as e:
            logger.error(f"处理信号失败: {e}")
            metrics.increment_counter("optimized_processor_error_total")
            return False
    
    async def _batch_processor(self):
        """批处理器任务"""
        logger.info("🔄 启动批处理器")
        
        while self.running:
            try:
                await asyncio.sleep(self.config.batch_timeout_ms / 1000.0)
                
                async with self._batch_lock:
                    if self._batch_buffer:
                        await self._flush_batch()
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"批处理器异常: {e}")
        
        logger.info("批处理器停止")
    
    async def _flush_batch(self):
        """刷新批处理缓冲区"""
        if not self._batch_buffer:
            return
        
        batch_size = len(self._batch_buffer)
        logger.debug(f"处理批次: {batch_size} 个信号")
        
        start_time = time.time()
        
        try:
            # 按优先级分组
            critical_signals = []
            high_signals = []
            normal_signals = []
            
            for item in self._batch_buffer:
                signal = item['signal']
                priority = item['priority']
                
                if priority == MessagePriority.CRITICAL:
                    critical_signals.append(signal)
                elif priority == MessagePriority.HIGH:
                    high_signals.append(signal)
                else:
                    normal_signals.append(signal)
            
            # 按优先级顺序处理
            for signals in [critical_signals, high_signals, normal_signals]:
                if signals:
                    if self.priority_queue:
                        # 批量入队
                        for signal in signals:
                            await self.priority_queue.enqueue(
                                MessagePriority.NORMAL, signal.__dict__, signal.signal_id
                            )
                    else:
                        # 直接批量处理
                        for signal in signals:
                            await self._process_signal_direct(signal)
            
            # 更新统计
            processing_time = (time.time() - start_time) * 1000
            self.stats['batches_processed'] += 1
            self.stats['messages_processed'] += batch_size
            
            metrics.increment_counter("optimized_processor_batch_processed_total")
            metrics.record_histogram("optimized_processor_batch_size", batch_size)
            metrics.record_histogram("optimized_processor_batch_time_ms", processing_time)
            
            logger.debug(f"批次处理完成: {batch_size} 个信号，耗时 {processing_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"批次处理失败: {e}")
        finally:
            # 清空缓冲区
            self._batch_buffer.clear()
    
    async def _process_critical_signal(self, data: Dict[str, Any]):
        """处理关键信号"""
        logger.info(f"处理关键信号: {data.get('signal_id')}")
        await self._process_signal_with_connection(data)
    
    async def _process_high_signal(self, data: Dict[str, Any]):
        """处理高优先级信号"""
        logger.debug(f"处理高优先级信号: {data.get('signal_id')}")
        await self._process_signal_with_connection(data)
    
    async def _process_normal_signal(self, data: Dict[str, Any]):
        """处理普通信号"""
        logger.debug(f"处理普通信号: {data.get('signal_id')}")
        await self._process_signal_with_connection(data)
    
    async def _process_signal_with_connection(self, signal_data: Dict[str, Any]):
        """使用连接池处理信号"""
        account_id = signal_data.get('account_id')
        
        if not account_id:
            logger.error("信号缺少账户ID")
            return
        
        if self.connection_pool:
            try:
                async with self.connection_pool.get_connection(account_id) as connection:
                    if connection:
                        await self._execute_signal(connection.connection_object, signal_data)
                    else:
                        logger.error(f"无法获取连接 (账户: {account_id})")
            except Exception as e:
                logger.error(f"连接池处理失败: {e}")
                # 降级到直接处理
                await self._process_signal_direct_from_data(signal_data)
        else:
            await self._process_signal_direct_from_data(signal_data)
    
    async def _process_signal_direct(self, signal: TradeSignal):
        """直接处理信号"""
        logger.debug(f"直接处理信号: {signal.signal_id}")
        # 这里应该调用实际的MT5执行逻辑
        await asyncio.sleep(0.001)  # 模拟处理时间
    
    async def _process_signal_direct_from_data(self, signal_data: Dict[str, Any]):
        """从数据直接处理信号"""
        logger.debug(f"直接处理信号数据: {signal_data.get('signal_id')}")
        await asyncio.sleep(0.001)  # 模拟处理时间
    
    async def _execute_signal(self, connection, signal_data: Dict[str, Any]):
        """执行信号"""
        logger.debug(f"执行信号: {signal_data.get('signal_id')}")
        await asyncio.sleep(0.005)  # 模拟执行时间
    
    def _update_stats(self, processing_time_ms: float):
        """更新统计信息"""
        self.stats['messages_processed'] += 1
        self.stats['total_processing_time_ms'] += processing_time_ms * 1000
        
        # 计算移动平均
        total_processed = self.stats['messages_processed']
        if total_processed > 0:
            self.stats['avg_processing_time_ms'] = (
                self.stats['total_processing_time_ms'] / total_processed
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = dict(self.stats)
        
        if self.priority_queue:
            stats['priority_queue'] = self.priority_queue.get_stats()
        
        if self.connection_pool:
            stats['connection_pool'] = self.connection_pool.get_stats()
        
        stats['batch_buffer_size'] = len(self._batch_buffer)
        stats['running'] = self.running
        
        return stats


# 全局优化处理器实例
_optimized_processor = None


async def get_optimized_processor(config: OptimizedConfig = None) -> OptimizedProcessor:
    """获取全局优化处理器实例"""
    global _optimized_processor
    
    if _optimized_processor is None:
        _optimized_processor = OptimizedProcessor(config)
        await _optimized_processor.initialize()
    
    return _optimized_processor


def get_optimized_processor_sync() -> OptimizedProcessor:
    """获取全局优化处理器实例（同步版本，仅用于配置）"""
    global _optimized_processor
    
    if _optimized_processor is None:
        _optimized_processor = OptimizedProcessor()
    
    return _optimized_processor