#!/usr/bin/env python3
"""
生成API文档脚本
"""
import json
import yaml
import argparse
from pathlib import Path
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.api.main import app
from src.api.openapi_config import create_custom_openapi


def generate_openapi_json(output_path: str = "docs/openapi.json"):
    """生成OpenAPI JSON文档"""
    try:
        # 创建输出目录
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成OpenAPI schema
        openapi_schema = create_custom_openapi(app)
        
        # 写入JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(openapi_schema, f, indent=2, ensure_ascii=False)
        
        print(f"✅ OpenAPI JSON文档已生成: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 生成OpenAPI JSON文档失败: {e}")
        return False


def generate_openapi_yaml(output_path: str = "docs/openapi.yaml"):
    """生成OpenAPI YAML文档"""
    try:
        # 创建输出目录
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成OpenAPI schema
        openapi_schema = create_custom_openapi(app)
        
        # 写入YAML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(openapi_schema, f, indent=2, allow_unicode=True, sort_keys=False)
        
        print(f"✅ OpenAPI YAML文档已生成: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 生成OpenAPI YAML文档失败: {e}")
        return False


def generate_postman_collection(output_path: str = "docs/postman_collection.json"):
    """生成Postman集合"""
    try:
        # 创建输出目录
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成OpenAPI schema
        openapi_schema = create_custom_openapi(app)
        
        # 转换为Postman集合格式
        postman_collection = {
            "info": {
                "name": openapi_schema["info"]["title"],
                "description": openapi_schema["info"]["description"],
                "version": openapi_schema["info"]["version"],
                "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
            },
            "auth": {
                "type": "apikey",
                "apikey": [
                    {
                        "key": "key",
                        "value": "X-API-Key",
                        "type": "string"
                    },
                    {
                        "key": "value",
                        "value": "{{api_key}}",
                        "type": "string"
                    }
                ]
            },
            "variable": [
                {
                    "key": "base_url",
                    "value": "http://localhost:8000",
                    "type": "string"
                },
                {
                    "key": "api_key",
                    "value": "your-api-key-here",
                    "type": "string"
                }
            ],
            "item": []
        }
        
        # 为每个路径生成请求
        for path, methods in openapi_schema["paths"].items():
            for method, details in methods.items():
                if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                    # 创建请求项
                    request_item = {
                        "name": details.get("summary", f"{method.upper()} {path}"),
                        "request": {
                            "method": method.upper(),
                            "header": [
                                {
                                    "key": "Content-Type",
                                    "value": "application/json",
                                    "type": "text"
                                }
                            ],
                            "url": {
                                "raw": "{{base_url}}" + path,
                                "host": ["{{base_url}}"],
                                "path": path.strip("/").split("/")
                            },
                            "description": details.get("description", "")
                        }
                    }
                    
                    # 添加请求体示例
                    if "requestBody" in details:
                        request_body = details["requestBody"]
                        if "application/json" in request_body.get("content", {}):
                            schema = request_body["content"]["application/json"]["schema"]
                            if "example" in schema:
                                request_item["request"]["body"] = {
                                    "mode": "raw",
                                    "raw": json.dumps(schema["example"], indent=2)
                                }
                    
                    # 添加到集合
                    postman_collection["item"].append(request_item)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(postman_collection, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Postman集合已生成: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 生成Postman集合失败: {e}")
        return False


def generate_markdown_docs(output_path: str = "docs/API.md"):
    """生成Markdown文档"""
    try:
        # 创建输出目录
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成OpenAPI schema
        openapi_schema = create_custom_openapi(app)
        
        # 生成Markdown内容
        markdown_content = f"""# {openapi_schema['info']['title']}

{openapi_schema['info']['description']}

## 版本信息
- **版本**: {openapi_schema['info']['version']}
- **基础URL**: http://localhost:8000
- **文档**: [Swagger UI](/docs) | [ReDoc](/redoc)

## 认证

本API支持以下认证方式：

### API Key
```
X-API-Key: your-api-key-here
```

### Bearer Token
```
Authorization: Bearer your-jwt-token
```

## 端点列表

"""
        
        # 按标签分组端点
        tags = {}
        for path, methods in openapi_schema["paths"].items():
            for method, details in methods.items():
                if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                    tag = details.get("tags", ["其他"])[0]
                    if tag not in tags:
                        tags[tag] = []
                    tags[tag].append({
                        "path": path,
                        "method": method.upper(),
                        "summary": details.get("summary", ""),
                        "description": details.get("description", ""),
                        "parameters": details.get("parameters", []),
                        "requestBody": details.get("requestBody", None),
                        "responses": details.get("responses", {})
                    })
        
        # 生成每个标签的文档
        for tag, endpoints in tags.items():
            markdown_content += f"### {tag}\n\n"
            
            for endpoint in endpoints:
                markdown_content += f"#### {endpoint['method']} {endpoint['path']}\n\n"
                markdown_content += f"**{endpoint['summary']}**\n\n"
                
                if endpoint['description']:
                    markdown_content += f"{endpoint['description']}\n\n"
                
                # 参数
                if endpoint['parameters']:
                    markdown_content += "**参数**:\n\n"
                    for param in endpoint['parameters']:
                        required = " (必需)" if param.get('required', False) else " (可选)"
                        markdown_content += f"- `{param['name']}`{required}: {param.get('description', '')}\n"
                    markdown_content += "\n"
                
                # 请求体
                if endpoint['requestBody']:
                    markdown_content += "**请求体**:\n\n"
                    markdown_content += "```json\n"
                    if "application/json" in endpoint['requestBody'].get('content', {}):
                        schema = endpoint['requestBody']['content']['application/json']['schema']
                        if 'example' in schema:
                            markdown_content += json.dumps(schema['example'], indent=2)
                    markdown_content += "\n```\n\n"
                
                # 响应
                markdown_content += "**响应**:\n\n"
                for code, response in endpoint['responses'].items():
                    markdown_content += f"- `{code}`: {response.get('description', '')}\n"
                markdown_content += "\n"
        
        # 模型定义
        if "components" in openapi_schema and "schemas" in openapi_schema["components"]:
            markdown_content += "## 数据模型\n\n"
            
            for model_name, model_schema in openapi_schema["components"]["schemas"].items():
                markdown_content += f"### {model_name}\n\n"
                
                if "description" in model_schema:
                    markdown_content += f"{model_schema['description']}\n\n"
                
                if "properties" in model_schema:
                    markdown_content += "**属性**:\n\n"
                    for prop_name, prop_schema in model_schema["properties"].items():
                        prop_type = prop_schema.get("type", "unknown")
                        prop_desc = prop_schema.get("description", "")
                        markdown_content += f"- `{prop_name}` ({prop_type}): {prop_desc}\n"
                    markdown_content += "\n"
                
                if "example" in model_schema:
                    markdown_content += "**示例**:\n\n"
                    markdown_content += "```json\n"
                    markdown_content += json.dumps(model_schema["example"], indent=2)
                    markdown_content += "\n```\n\n"
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"✅ Markdown文档已生成: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 生成Markdown文档失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成MT5交易系统API文档")
    parser.add_argument("--format", choices=["json", "yaml", "postman", "markdown", "all"], 
                       default="all", help="输出格式")
    parser.add_argument("--output-dir", default="docs", help="输出目录")
    
    args = parser.parse_args()
    
    print("🚀 开始生成API文档...")
    
    success_count = 0
    total_count = 0
    
    if args.format in ["json", "all"]:
        total_count += 1
        if generate_openapi_json(f"{args.output_dir}/openapi.json"):
            success_count += 1
    
    if args.format in ["yaml", "all"]:
        total_count += 1
        if generate_openapi_yaml(f"{args.output_dir}/openapi.yaml"):
            success_count += 1
    
    if args.format in ["postman", "all"]:
        total_count += 1
        if generate_postman_collection(f"{args.output_dir}/postman_collection.json"):
            success_count += 1
    
    if args.format in ["markdown", "all"]:
        total_count += 1
        if generate_markdown_docs(f"{args.output_dir}/API.md"):
            success_count += 1
    
    print(f"\n📊 完成情况: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有文档生成成功!")
    else:
        print("⚠️  部分文档生成失败，请检查错误信息")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())