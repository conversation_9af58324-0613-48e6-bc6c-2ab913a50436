#!/usr/bin/env python3
"""
配置文件安全迁移脚本
将旧配置文件结构迁移到新的模块化结构
"""
import os
import sys
import yaml
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))


class ConfigMigrator:
    """配置迁移器"""
    
    def __init__(self, config_root: str = "config"):
        self.config_root = Path(config_root)
        self.backup_dir = self.config_root / "backup" / datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def migrate(self, force: bool = False):
        """执行完整迁移"""
        print("🚀 开始配置文件迁移...")
        
        # 1. 创建备份
        if not force:
            self._create_backup()
        
        # 2. 创建新目录结构
        self._create_new_structure()
        
        # 3. 迁移系统配置
        self._migrate_system_config()
        
        # 4. 迁移账户配置
        self._migrate_account_configs()
        
        # 5. 创建角色和配对配置
        self._create_role_configs()
        self._create_pairing_configs()
        
        # 6. 更新环境变量文件
        self._update_env_file()
        
        # 7. 清理旧文件
        if force:
            self._cleanup_old_files()
        
        print("✅ 配置迁移完成！")
        print(f"📁 备份位置: {self.backup_dir}")
        print("🔧 请运行验证: python scripts/config_manager.py validate")
        
    def _create_backup(self):
        """创建配置备份"""
        print("📦 创建配置备份...")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份重要文件和目录
        backup_items = [
            "system.yaml",
            "config.yaml", 
            "main.yaml",
            "accounts/",
            "templates/",
            "performance/",
            "security/",
            "terminals/"
        ]
        
        for item in backup_items:
            source = self.config_root / item
            if source.exists():
                target = self.backup_dir / item
                if source.is_dir():
                    if target.exists():
                        shutil.rmtree(target)
                    shutil.copytree(source, target)
                else:
                    target.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(source, target)
                print(f"  ✅ 备份: {item}")
        
        print(f"📁 备份完成: {self.backup_dir}")
    
    def _create_new_structure(self):
        """创建新目录结构"""
        print("📁 创建新目录结构...")
        
        new_dirs = [
            "accounts_new",
            "roles", 
            "pairings"
        ]
        
        for dir_name in new_dirs:
            dir_path = self.config_root / dir_name
            dir_path.mkdir(exist_ok=True)
            print(f"  ✅ 创建目录: {dir_name}")
    
    def _migrate_system_config(self):
        """迁移系统配置"""
        print("⚙️ 迁移系统配置...")
        
        # 查找现有系统配置文件
        system_files = ["system.yaml", "config.yaml", "main.yaml"]
        source_config = {}
        
        for file_name in system_files:
            file_path = self.config_root / file_name
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f) or {}
                        source_config.update(config)
                    print(f"  📄 读取: {file_name}")
                except Exception as e:
                    print(f"  ❌ 读取失败 {file_name}: {e}")
        
        # 检查是否已存在新配置
        new_config_file = self.config_root / "system_distributed.yaml"
        if new_config_file.exists():
            print(f"  ℹ️ 新配置文件已存在，跳过系统配置迁移")
            return
        
        # 如果没有源配置，使用默认配置
        if not source_config:
            print("  ⚠️ 未找到现有系统配置，将使用默认配置")
            return
        
        # 转换配置格式
        new_config = self._convert_system_config(source_config)
        
        # 保存新配置
        with open(new_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(new_config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"  ✅ 系统配置已迁移到: system_distributed.yaml")
    
    def _migrate_account_configs(self):
        """迁移账户配置"""
        print("👥 迁移账户配置...")
        
        old_accounts_dir = self.config_root / "accounts"
        new_accounts_dir = self.config_root / "accounts_new"
        
        if not old_accounts_dir.exists():
            print("  ⚠️ 未找到旧账户配置目录")
            return
        
        # 迁移每个账户配置
        for config_file in old_accounts_dir.glob("*.yaml"):
            if config_file.name in ["README.md", "README_EN.md"]:
                continue
                
            account_id = config_file.stem
            new_config_file = new_accounts_dir / f"{account_id}.yaml"
            
            # 如果新配置已存在，跳过
            if new_config_file.exists():
                print(f"  ℹ️ 账户配置已存在，跳过: {account_id}")
                continue
            
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    old_config = yaml.safe_load(f)
                
                # 转换配置格式
                new_config = self._convert_account_config(old_config, account_id)
                
                # 保存新配置
                with open(new_config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(new_config, f, default_flow_style=False, allow_unicode=True)
                
                print(f"  ✅ 迁移账户: {account_id}")
                
            except Exception as e:
                print(f"  ❌ 迁移失败 {account_id}: {e}")
    
    def _create_role_configs(self):
        """创建角色配置"""
        print("🎭 创建角色配置...")
        
        roles_dir = self.config_root / "roles"
        role_file = roles_dir / "role_definitions.yaml"
        
        if role_file.exists():
            print("  ℹ️ 角色配置已存在，跳过创建")
            return
        
        # 角色配置已在前面创建，这里只需要确认
        print("  ✅ 角色配置文件已准备就绪")
    
    def _create_pairing_configs(self):
        """创建配对配置"""
        print("🔗 创建配对配置...")
        
        pairings_dir = self.config_root / "pairings"
        pairing_file = pairings_dir / "dynamic_pairings.yaml"
        
        if pairing_file.exists():
            print("  ℹ️ 配对配置已存在，跳过创建")
            return
        
        # 配对配置已在前面创建，这里只需要确认
        print("  ✅ 配对配置文件已准备就绪")
    
    def _convert_system_config(self, old_config: Dict) -> Dict:
        """转换系统配置格式"""
        # 基础系统信息
        system_info = old_config.get('system', {})
        
        new_config = {
            'system': {
                'name': system_info.get('name', 'MT5 Distributed Trading System'),
                'version': '3.0.0',
                'environment': system_info.get('environment', 'production'),
                'host': {
                    'host_id': '${MT5_HOST_ID}',
                    'hostname': '${HOSTNAME}',
                    'region': 'asia-east',
                    'capabilities': ['master', 'slave', 'forwarder'],
                    'max_accounts': 10
                }
            },
            'distributed': {
                'enabled': True,
                'mode': 'cluster',
                'discovery': {
                    'method': 'nats',
                    'interval': 30,
                    'timeout': 10
                },
                'cluster': {
                    'name': 'mt5-trading-cluster',
                    'heartbeat_interval': 15,
                    'health_check_timeout': 10,
                    'failover_enabled': True
                },
                'roles': {
                    'dynamic_switching': True,
                    'switch_cooldown': 300,
                    'auto_rebalance': True
                }
            },
            'messaging': {
                'provider': 'jetstream',
                'jetstream': {
                    'servers': [
                        'nats://************:4222',
                        'nats://************:4222', 
                        'nats://************:4222'
                    ],
                    'connection': {
                        'name': 'mt5-distributed-${MT5_HOST_ID}',
                        'connect_timeout': 10.0,
                        'max_reconnect_attempts': -1,
                        'reconnect_time_wait': 2.0,
                        'ping_interval': 60.0
                    },
                    'stream': {
                        'name': 'MT5_DISTRIBUTED',
                        'subjects': [
                            'MT5.TRADES.*',
                            'MT5.TRADES.LOCAL.*',
                            'MT5.TRADES.REMOTE.*.*',
                            'MT5.FORWARD.*.*',
                            'MT5.ROLES.*',
                            'MT5.DISCOVERY.*',
                            'MT5.HEARTBEAT.*'
                        ],
                        'retention': 'workqueue',
                        'max_age': 86400,
                        'max_msgs': 1000000,
                        'max_bytes': **********,
                        'replicas': 3
                    }
                }
            },
            'storage': {
                'redis': {
                    'provider': 'sentinel',
                    'sentinel': {
                        'sentinels': [
                            {'host': '************', 'port': 26379},
                            {'host': '************', 'port': 26379},
                            {'host': '************', 'port': 26379}
                        ],
                        'service_name': 'mt5-redis-master',
                        'socket_timeout': 5.0,
                        'socket_connect_timeout': 5.0,
                        'retry_on_timeout': True
                    },
                    'namespaces': {
                        'accounts': 'mt5:accounts',
                        'roles': 'mt5:roles',
                        'pairings': 'mt5:pairings',
                        'hosts': 'mt5:hosts',
                        'status': 'mt5:status'
                    }
                }
            },
            'accounts': {
                'config_path': './config/accounts_new',
                'role_management': {
                    'enabled': True,
                    'default_role': 'auto',
                    'role_persistence': True
                }
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - [%(host_id)s] - %(message)s',
                'handlers': [
                    {
                        'type': 'file',
                        'filename': 'logs/mt5_distributed.log',
                        'max_bytes': ********,
                        'backup_count': 5
                    },
                    {
                        'type': 'console',
                        'level': 'INFO'
                    }
                ]
            }
        }
        
        return new_config
    
    def _convert_account_config(self, old_config: Dict, account_id: str) -> Dict:
        """转换账户配置格式"""
        account_info = old_config.get('account', {})
        
        # 确定角色和模板
        role = account_info.get('role', 'auto')
        if role == 'master':
            template = 'high_frequency_master'
        elif role == 'slave':
            template = 'conservative_slave'
        else:
            template = 'master'  # 默认模板
        
        new_config = {
            'account': {
                'id': account_id,
                'name': account_info.get('name', f'Account {account_id}'),
                'enabled': account_info.get('enabled', True),
                'connection': account_info.get('connection', {}),
                'properties': {
                    'currency': 'USD',
                    'timezone': 0,
                    'account_type': 'demo',
                    'broker': 'Unknown'
                }
            },
            'current_role': {
                'role': 'auto',
                'template': template
            },
            'deployment': {
                'preferred_hosts': ['host-001'],
                'constraints': {
                    'require_local_mt5': True,
                    'min_cpu_cores': 2,
                    'min_memory_gb': 4,
                    'max_network_latency_ms': 100
                },
                'failover': {
                    'enabled': True,
                    'backup_hosts': [],
                    'auto_failover': True
                }
            },
            'trading': {
                'allowed_symbols': old_config.get('distributed', {}).get('trading', {}).get('allowed_symbols', []),
                'limits': {
                    'max_spread': 3.0,
                    'slippage': 10,
                    'max_lot_size': 10.0,
                    'min_lot_size': 0.01
                },
                'trading_hours': {
                    'enabled': True,
                    'start_time': '00:00',
                    'end_time': '23:59',
                    'exclude_weekends': True
                }
            },
            'risk_management': {
                'base_limits': {
                    'max_daily_loss': old_config.get('distributed', {}).get('risk_management', {}).get('max_daily_loss', 1000.0),
                    'max_daily_trades': old_config.get('distributed', {}).get('risk_management', {}).get('max_daily_trades', 50),
                    'max_positions': old_config.get('distributed', {}).get('risk_management', {}).get('max_positions', 10),
                    'max_drawdown': 0.2
                },
                'emergency_stop': {
                    'enabled': True,
                    'loss_threshold': 5000.0,
                    'drawdown_threshold': 0.3
                },
                'checks': {
                    'pre_trade': True,
                    'real_time': True,
                    'post_trade': True
                }
            },
            'monitoring': {
                'performance': {
                    'enabled': True,
                    'polling_interval': 0.1,
                    'adaptive_polling': True
                },
                'connection': {
                    'health_check_interval': 30,
                    'reconnect_attempts': 5,
                    'reconnect_delay': 5
                },
                'metrics': {
                    'enabled': True,
                    'collection_interval': 60,
                    'retention_days': 30
                }
            },
            'notifications': old_config.get('notifications', {}),
            'local': {
                'data_path': f'./data/accounts/{account_id}',
                'logs_path': f'./logs/accounts/{account_id}',
                'cache': {
                    'enabled': True,
                    'ttl': 300,
                    'max_size': 1000
                },
                'backup': {
                    'enabled': True,
                    'interval': 3600,
                    'retention_days': 7
                }
            },
            'metadata': {
                'created_at': datetime.utcnow().isoformat() + 'Z',
                'updated_at': datetime.utcnow().isoformat() + 'Z',
                'version': '3.0.0',
                'tags': [],
                'description': f'从旧配置迁移的账户: {account_info.get("name", account_id)}'
            }
        }
        
        return new_config
    
    def _update_env_file(self):
        """更新环境变量文件"""
        print("🔧 更新环境变量文件...")
        
        env_file = Path(".env")
        env_example_file = Path(".env.example")
        
        # 创建示例环境变量文件
        env_content = """# 分布式MT5交易系统环境变量

# 主机配置
MT5_HOST_ID=host-001
MT5_CONFIG_PATH=config/
HOSTNAME=trading-server-01

# 账户密码
MT5_ACC001_PASSWORD=your_password_1
MT5_ACC002_PASSWORD=your_password_2
MT5_ACC003_PASSWORD=your_password_3

# 基础设施地址
NATS_SERVERS=nats://************:4222,nats://************:4222,nats://************:4222
REDIS_SENTINELS=************:26379,************:26379,************:26379

# 通知配置
TELEGRAM_CHAT_ID=**********
TELEGRAM_BOT_TOKEN=your_bot_token

# 其他配置
LOG_LEVEL=INFO
DEBUG=false
"""
        
        with open(env_example_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print(f"  ✅ 创建环境变量示例: {env_example_file}")
        
        if not env_file.exists():
            shutil.copy2(env_example_file, env_file)
            print(f"  ✅ 创建环境变量文件: {env_file}")
        else:
            print(f"  ℹ️ 环境变量文件已存在: {env_file}")
    
    def _cleanup_old_files(self):
        """清理旧文件（可选）"""
        print("🧹 清理旧配置文件...")
        
        # 这里可以选择性删除旧文件
        # 为了安全，暂时不自动删除
        print("  ℹ️ 旧文件已保留，如需删除请手动操作")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='配置文件迁移工具')
    parser.add_argument('--config-root', default='config', help='配置文件根目录')
    parser.add_argument('--force', action='store_true', help='强制迁移，不创建备份')
    parser.add_argument('--dry-run', action='store_true', help='试运行，不实际修改文件')
    
    args = parser.parse_args()
    
    if args.dry_run:
        print("🔍 试运行模式，不会实际修改文件")
        return
    
    migrator = ConfigMigrator(args.config_root)
    migrator.migrate(args.force)


if __name__ == "__main__":
    main()
