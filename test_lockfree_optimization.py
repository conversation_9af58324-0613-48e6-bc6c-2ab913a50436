#!/usr/bin/env python3
"""
无锁优先级队列和缓存优化测试
验证性能提升效果
"""

import asyncio
import time
import sys
import os
from typing import Dict, Any, List
import concurrent.futures

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

# Mock必要的依赖
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARN: {msg}")  
    def error(self, msg): print(f"ERROR: {msg}")
    def debug(self, msg): pass

class MockMetrics:
    def record(self, *args, **kwargs): pass
    def increment(self, *args, **kwargs): pass

# 创建mock模块
import types
utils_module = types.ModuleType('utils')
utils_module.logger = types.ModuleType('logger')
utils_module.logger.get_logger = lambda name: MockLogger()
utils_module.metrics = types.ModuleType('metrics')
utils_module.metrics.get_metrics_collector = lambda: MockMetrics()
sys.modules['utils'] = utils_module
sys.modules['utils.logger'] = utils_module.logger
sys.modules['utils.metrics'] = utils_module.metrics


class LockFreeOptimizationTest:
    """无锁优化测试类"""
    
    def __init__(self):
        self.test_results = {}
    
    async def test_enhanced_cache_performance(self):
        """测试增强缓存性能"""
        print("\n🧪 测试增强缓存性能...")
        
        try:
            from src.messaging.message_router import OptimizedMessageRouter
            
            config = {
                'host_id': 'test-host',
                'queue_manager': {
                    'primary_backend': 'local_memory',
                    'backup_backends': [],
                    'local_fallback': True
                }
            }
            
            router = OptimizedMessageRouter(config, 'test-host')
            await router.start()
            
            # 测试热点缓存效果
            test_signals = []
            hot_patterns = [
                ('monitor', 'executor', 'test-host'),
                ('signal_processor', 'executor', 'test-host'),
                ('trade_engine', 'executor', 'test-host')
            ]
            
            # 创建大量重复信号（模拟热点）
            for i in range(300):  # 每个模式100次
                source, target, host = hot_patterns[i % 3]
                signal = {
                    'message_id': f'hot_test_{i}',
                    'source_type': source,
                    'target_account': target,
                    'target_host_id': host,
                    'command_type': 'place_order',
                    'action': 'buy'
                }
                test_signals.append(signal)
            
            # 性能测试
            start_time = time.perf_counter()
            
            for signal in test_signals:
                await router.route_signal(signal)
            
            total_time = (time.perf_counter() - start_time) * 1000
            
            # 获取缓存统计
            status = router.get_status()
            cache_hit_rate = status['routes'].get('cache_hit_rate', 0)
            
            result = {
                'total_messages': len(test_signals),
                'total_time_ms': total_time,
                'avg_time_per_message_ms': total_time / len(test_signals),
                'cache_hit_rate': cache_hit_rate,
                'hot_cache_size': len(router.hot_cache) if hasattr(router, 'hot_cache') else 0,
                'priority_hot_cache_size': len(router.priority_hot_cache) if hasattr(router, 'priority_hot_cache') else 0,
                'cache_improvement': cache_hit_rate > 98.0  # 期望>98%命中率
            }
            
            await router.stop()
            
            print(f"  总消息数: {result['total_messages']}")
            print(f"  总时间: {result['total_time_ms']:.2f}ms")
            print(f"  平均每消息: {result['avg_time_per_message_ms']:.4f}ms")
            print(f"  缓存命中率: {result['cache_hit_rate']:.2f}%")
            print(f"  热点缓存大小: {result['hot_cache_size']}")
            print(f"  缓存优化达标: {'✅' if result['cache_improvement'] else '❌'}")
            
            self.test_results['enhanced_cache'] = result
            return result
            
        except Exception as e:
            print(f"❌ 增强缓存测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_lockfree_queue_performance(self):
        """测试无锁队列性能"""
        print("\n🧪 测试无锁队列性能...")
        
        try:
            from src.messaging.priority_queue import LockFreePriorityQueue, MessagePriority
            
            # 创建无锁队列
            queue = LockFreePriorityQueue(shard_count=4, worker_count=6)
            
            # 注册处理器
            processed_messages = []
            
            async def test_processor(data):
                processed_messages.append(data)
            
            for priority in MessagePriority:
                queue.register_processor(priority, test_processor)
            
            await queue.start_workers()
            
            # 性能测试数据
            test_messages = []
            priorities = list(MessagePriority)
            
            for i in range(1000):  # 1000条消息
                priority = priorities[i % len(priorities)]
                message_data = {
                    'message_id': f'lockfree_test_{i}',
                    'target_account': f'ACC{i % 10:03d}',  # 10个不同账户
                    'command_type': 'place_order',
                    'action': 'buy' if i % 2 == 0 else 'sell',
                    'symbol': 'EURUSD',
                    'volume': 0.1
                }
                test_messages.append((priority, message_data))
            
            # 1. 入队性能测试
            enqueue_start = time.perf_counter()
            
            for priority, data in test_messages:
                success = await queue.enqueue(priority, data)
                if not success:
                    print(f"❌ 入队失败: {data['message_id']}")
            
            enqueue_time = (time.perf_counter() - enqueue_start) * 1000
            
            # 2. 等待处理完成
            wait_start = time.perf_counter()
            max_wait = 10  # 最长等待10秒
            
            while len(processed_messages) < len(test_messages) and (time.perf_counter() - wait_start) < max_wait:
                await asyncio.sleep(0.01)
            
            total_time = (time.perf_counter() - enqueue_start) * 1000
            
            # 3. 获取统计信息
            stats = queue.get_stats()
            
            result = {
                'total_messages': len(test_messages),
                'processed_messages': len(processed_messages),
                'enqueue_time_ms': enqueue_time,
                'total_time_ms': total_time,
                'avg_enqueue_time_ms': enqueue_time / len(test_messages),
                'processing_success_rate': len(processed_messages) / len(test_messages) * 100,
                'throughput_msg_per_sec': len(test_messages) / (enqueue_time / 1000),
                'shard_count': stats['shard_count'],
                'total_queue_size': stats['total_queue_size'],
                'performance_good': enqueue_time < 100 and len(processed_messages) >= len(test_messages) * 0.9
            }
            
            await queue.stop_workers()
            
            print(f"  总消息数: {result['total_messages']}")
            print(f"  处理成功数: {result['processed_messages']}")
            print(f"  入队时间: {result['enqueue_time_ms']:.2f}ms")
            print(f"  总处理时间: {result['total_time_ms']:.2f}ms")
            print(f"  平均入队时间: {result['avg_enqueue_time_ms']:.4f}ms")
            print(f"  处理成功率: {result['processing_success_rate']:.1f}%")
            print(f"  入队吞吐量: {result['throughput_msg_per_sec']:.0f} msg/s")
            print(f"  分片数: {result['shard_count']}")
            print(f"  性能达标: {'✅' if result['performance_good'] else '❌'}")
            
            self.test_results['lockfree_queue'] = result
            return result
            
        except Exception as e:
            print(f"❌ 无锁队列测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_concurrent_performance_comparison(self):
        """测试并发性能对比"""
        print("\n🧪 测试并发性能对比...")
        
        try:
            from src.messaging.priority_queue import LockFreePriorityQueue, MessagePriority
            
            # 测试配置
            concurrent_tasks = 20
            messages_per_task = 50
            total_messages = concurrent_tasks * messages_per_task
            
            # 创建无锁队列
            queue = LockFreePriorityQueue(shard_count=8, worker_count=8)
            
            processed_count = 0
            
            async def counter_processor(data):
                nonlocal processed_count
                processed_count += 1
            
            for priority in MessagePriority:
                queue.register_processor(priority, counter_processor)
            
            await queue.start_workers()
            
            # 并发任务函数
            async def concurrent_enqueue_task(task_id: int):
                results = []
                for i in range(messages_per_task):
                    message_data = {
                        'message_id': f'concurrent_{task_id}_{i}',
                        'target_account': f'ACC{task_id:03d}',
                        'command_type': 'place_order',
                        'task_id': task_id,
                        'seq': i
                    }
                    
                    priority = MessagePriority.SIGNAL_EXECUTION
                    success = await queue.enqueue(priority, message_data)
                    results.append(success)
                
                return results
            
            # 执行并发测试
            start_time = time.perf_counter()
            
            tasks = [concurrent_enqueue_task(i) for i in range(concurrent_tasks)]
            task_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            enqueue_time = (time.perf_counter() - start_time) * 1000
            
            # 等待处理完成
            wait_start = time.perf_counter()
            while processed_count < total_messages and (time.perf_counter() - wait_start) < 15:
                await asyncio.sleep(0.01)
            
            total_time = (time.perf_counter() - start_time) * 1000
            
            # 统计结果
            successful_enqueues = 0
            for task_result in task_results:
                if isinstance(task_result, list):
                    successful_enqueues += sum(1 for r in task_result if r)
            
            # 获取队列统计
            stats = queue.get_stats()
            
            result = {
                'concurrent_tasks': concurrent_tasks,
                'messages_per_task': messages_per_task,
                'total_messages': total_messages,
                'successful_enqueues': successful_enqueues,
                'processed_messages': processed_count,
                'enqueue_time_ms': enqueue_time,
                'total_time_ms': total_time,
                'concurrent_throughput': total_messages / (enqueue_time / 1000),
                'enqueue_success_rate': successful_enqueues / total_messages * 100,
                'processing_success_rate': processed_count / total_messages * 100,
                'avg_shard_load': sum(stats['shard_loads']) / len(stats['shard_loads']),
                'performance_excellent': (
                    enqueue_time < 200 and 
                    successful_enqueues >= total_messages * 0.95 and
                    processed_count >= total_messages * 0.9
                )
            }
            
            await queue.stop_workers()
            
            print(f"  并发任务数: {result['concurrent_tasks']}")
            print(f"  总消息数: {result['total_messages']}")
            print(f"  成功入队数: {result['successful_enqueues']}")
            print(f"  处理完成数: {result['processed_messages']}")
            print(f"  入队时间: {result['enqueue_time_ms']:.2f}ms")
            print(f"  并发吞吐量: {result['concurrent_throughput']:.0f} msg/s")
            print(f"  入队成功率: {result['enqueue_success_rate']:.1f}%")
            print(f"  处理成功率: {result['processing_success_rate']:.1f}%")
            print(f"  平均分片负载: {result['avg_shard_load']:.2f}")
            print(f"  性能优异: {'✅' if result['performance_excellent'] else '❌'}")
            
            self.test_results['concurrent_performance'] = result
            return result
            
        except Exception as e:
            print(f"❌ 并发性能测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始无锁优化验证测试")
        print("=" * 60)
        
        await self.test_enhanced_cache_performance()
        await self.test_lockfree_queue_performance()
        await self.test_concurrent_performance_comparison()
        
        return self.test_results
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 无锁优化效果总结")
        print("=" * 60)
        
        optimizations_passed = 0
        total_optimizations = 0
        
        # 缓存优化
        if 'enhanced_cache' in self.test_results:
            total_optimizations += 1
            cache_result = self.test_results['enhanced_cache']
            if cache_result['cache_improvement']:
                print("✅ 缓存优化: 命中率提升到98%+")
                optimizations_passed += 1
            else:
                print("❌ 缓存优化: 命中率未达到预期")
        
        # 无锁队列
        if 'lockfree_queue' in self.test_results:
            total_optimizations += 1
            queue_result = self.test_results['lockfree_queue']
            if queue_result['performance_good']:
                print("✅ 无锁队列: 性能达标")
                optimizations_passed += 1
            else:
                print("❌ 无锁队列: 性能未达标")
        
        # 并发性能
        if 'concurrent_performance' in self.test_results:
            total_optimizations += 1
            concurrent_result = self.test_results['concurrent_performance']
            if concurrent_result['performance_excellent']:
                print("✅ 并发性能: 表现优异")
                optimizations_passed += 1
            else:
                print("❌ 并发性能: 需要改进")
        
        print(f"\n🎯 优化成功率: {optimizations_passed}/{total_optimizations} ({optimizations_passed/max(total_optimizations, 1)*100:.0f}%)")
        
        print("\n📈 关键性能指标:")
        
        if 'enhanced_cache' in self.test_results:
            cache = self.test_results['enhanced_cache']
            print(f"  • 缓存命中率: {cache['cache_hit_rate']:.2f}%")
            print(f"  • 平均路由时间: {cache['avg_time_per_message_ms']:.4f}ms")
        
        if 'lockfree_queue' in self.test_results:
            queue = self.test_results['lockfree_queue']
            print(f"  • 无锁队列吞吐量: {queue['throughput_msg_per_sec']:.0f} msg/s")
            print(f"  • 平均入队时间: {queue['avg_enqueue_time_ms']:.4f}ms")
        
        if 'concurrent_performance' in self.test_results:
            concurrent = self.test_results['concurrent_performance']
            print(f"  • 并发吞吐量: {concurrent['concurrent_throughput']:.0f} msg/s")
            print(f"  • 并发成功率: {concurrent['processing_success_rate']:.1f}%")
        
        return optimizations_passed >= total_optimizations * 0.8  # 80%成功率为通过


async def main():
    """主测试函数"""
    tester = LockFreeOptimizationTest()
    results = await tester.run_all_tests()
    success = tester.print_summary()
    
    if success:
        print("\n🎉 无锁优化测试通过！")
        return 0
    else:
        print("\n⚠️  无锁优化测试部分失败，需要进一步调整")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)