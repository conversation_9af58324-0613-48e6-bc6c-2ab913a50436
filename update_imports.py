#!/usr/bin/env python3
"""
更新导入语句脚本
根据REFACTORING_PLAN.md中的重命名标准更新所有导入语句
"""
import os
import re
from pathlib import Path

# 文件重命名映射
FILE_RENAMES = {
    # core目录
    'unified_coordinator': 'main_coordinator',
    'mt5_account_executor': 'trade_executor', 
    'mt5_account_monitor': 'account_monitor',
    'mt5_process_manager': 'process_manager',
    'system_components': 'components',
    'dependency_injection': 'service_container',
    
    # messaging目录
    'hybrid_message_router': 'message_router',
    'hybrid_queue_manager': 'queue_manager',
    'nats_message_queue': 'nats_queue',
    'redis_message_queue': 'redis_queue',
    'local_message_queue': 'local_queue',
    'zero_copy_messaging': 'zerocopy',
    
    # infrastructure目录
    'production_optimizer': 'optimizer',
    'redis_sentinel_client': 'redis_client',
    'redis_hash_manager': 'redis_manager',
    
    # performance目录
    'enhanced_processor': 'processor',
    
    # utils目录
    'unified_memory_pool': 'memory_pool',
    'thread_safe_stats': 'stats',
    'recovery_strategies': 'recovery',
    'password_manager': 'password',
    
    # distributed目录
    'service_registry': 'registry',
    'process_guardian': 'guardian',
    'failover_manager': 'failover',
    'state_manager': 'state',
    'account_registry': 'accounts',
    
    # multi_terminal目录
    'independent_role_manager': 'roles_manager',
}

# 类名重命名映射
CLASS_RENAMES = {
    'UnifiedMT5Coordinator': 'MainCoordinator',
    'MT5AccountExecutor': 'TradeExecutor',
    'MT5AccountMonitor': 'AccountMonitor', 
    'MT5ProcessManager': 'ProcessManager',
    'HybridMessageRouter': 'MessageRouter',
    'HybridQueueManager': 'QueueManager',
    'ProductionOptimizer': 'Optimizer',
    'EnhancedProcessor': 'PerformanceProcessor',
}

def update_imports_in_file(file_path: Path):
    """更新单个文件中的导入语句"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 更新文件名导入
        for old_name, new_name in FILE_RENAMES.items():
            # 更新 from ... import 语句
            patterns = [
                rf'from\s+([.\w]*){old_name}\s+import',
                rf'from\s+([.\w]*\.){old_name}\s+import',
                rf'import\s+([.\w]*){old_name}',
                rf'import\s+([.\w]*\.){old_name}',
            ]
            
            for pattern in patterns:
                content = re.sub(pattern, lambda m: m.group(0).replace(old_name, new_name), content)
        
        # 更新类名引用
        for old_class, new_class in CLASS_RENAMES.items():
            # 只替换类名，不替换字符串中的内容
            content = re.sub(rf'\b{old_class}\b', new_class, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 更新: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ 错误处理文件 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始更新导入语句...")
    
    src_dir = Path("src")
    updated_files = 0
    total_files = 0
    
    # 遍历所有Python文件
    for py_file in src_dir.rglob("*.py"):
        # 跳过__pycache__和_deprecated目录
        if "__pycache__" in str(py_file) or "_deprecated" in str(py_file):
            continue
            
        total_files += 1
        if update_imports_in_file(py_file):
            updated_files += 1
    
    print(f"\n📊 更新完成:")
    print(f"   总文件数: {total_files}")
    print(f"   更新文件数: {updated_files}")
    print(f"   未变更文件数: {total_files - updated_files}")
    
    if updated_files > 0:
        print(f"\n✨ 成功更新了 {updated_files} 个文件的导入语句!")
    else:
        print(f"\n✅ 所有文件的导入语句都是最新的!")

if __name__ == "__main__":
    main()
