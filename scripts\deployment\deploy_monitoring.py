#!/usr/bin/env python3
"""
监控系统部署脚本
部署Prometheus+Grafana监控栈，建立完整的可观测性体系
"""
import os
import sys
import yaml
import json
import subprocess
import argparse
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)


class MonitoringDeployer:
    """监控系统部署器"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.monitoring_dir = self.project_root / "monitoring"
        
    def deploy_monitoring_stack(self) -> bool:
        """部署监控栈"""
        logger.info("🚀 部署监控系统...")
        
        try:
            # 1. 创建监控目录结构
            self._create_monitoring_directories()
            
            # 2. 生成Prometheus配置
            self._setup_prometheus_config()
            
            # 3. 生成Grafana配置
            self._setup_grafana_config()
            
            # 4. 创建告警规则
            self._create_alert_rules()
            
            # 5. 启动监控服务
            self._start_monitoring_services()
            
            logger.info("✅ 监控系统部署成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 监控系统部署失败: {e}")
            return False
    
    def _create_monitoring_directories(self):
        """创建监控目录结构"""
        logger.info("📁 创建监控目录结构...")
        
        directories = [
            self.monitoring_dir,
            self.monitoring_dir / "prometheus",
            self.monitoring_dir / "grafana" / "dashboards",
            self.monitoring_dir / "grafana" / "datasources",
            self.monitoring_dir / "rules",
            self.monitoring_dir / "data" / "prometheus",
            self.monitoring_dir / "data" / "grafana"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"创建目录: {directory}")
        
        logger.info("✅ 监控目录结构创建完成")
    
    def _setup_prometheus_config(self):
        """设置Prometheus配置"""
        logger.info("⚙️ 设置Prometheus配置...")
        
        # 使用增强版配置
        enhanced_config = self.project_root / "monitoring" / "prometheus_enhanced.yml"
        target_config = self.monitoring_dir / "prometheus" / "prometheus.yml"
        
        if enhanced_config.exists():
            import shutil
            shutil.copy2(enhanced_config, target_config)
            logger.info(f"✅ Prometheus配置已复制: {target_config}")
        else:
            logger.warning("⚠️ 增强版Prometheus配置不存在，使用默认配置")
    
    def _setup_grafana_config(self):
        """设置Grafana配置"""
        logger.info("📊 设置Grafana配置...")
        
        # 创建数据源配置
        datasource_config = {
            "apiVersion": 1,
            "datasources": [
                {
                    "name": "Prometheus",
                    "type": "prometheus",
                    "access": "proxy",
                    "url": "http://localhost:9090",
                    "isDefault": True,
                    "editable": True
                }
            ]
        }
        
        datasource_file = self.monitoring_dir / "grafana" / "datasources" / "prometheus.yml"
        with open(datasource_file, 'w', encoding='utf-8') as f:
            yaml.dump(datasource_config, f, default_flow_style=False)
        
        # 创建仪表板配置
        dashboard_config = {
            "apiVersion": 1,
            "providers": [
                {
                    "name": "MT5 Trading System",
                    "orgId": 1,
                    "folder": "",
                    "type": "file",
                    "disableDeletion": False,
                    "updateIntervalSeconds": 10,
                    "allowUiUpdates": True,
                    "options": {
                        "path": "/etc/grafana/provisioning/dashboards"
                    }
                }
            ]
        }
        
        dashboard_file = self.monitoring_dir / "grafana" / "dashboards" / "dashboard.yml"
        with open(dashboard_file, 'w', encoding='utf-8') as f:
            yaml.dump(dashboard_config, f, default_flow_style=False)
        
        logger.info("✅ Grafana配置设置完成")
    
    def _create_alert_rules(self):
        """创建告警规则"""
        logger.info("🚨 创建告警规则...")
        
        alert_rules = {
            "groups": [
                {
                    "name": "mt5_trading_alerts",
                    "rules": [
                        {
                            "alert": "MT5SystemDown",
                            "expr": "up{job=\"mt5-system\"} == 0",
                            "for": "1m",
                            "labels": {
                                "severity": "critical"
                            },
                            "annotations": {
                                "summary": "MT5交易系统离线",
                                "description": "MT5交易系统已离线超过1分钟"
                            }
                        },
                        {
                            "alert": "HighSignalLatency",
                            "expr": "histogram_quantile(0.95, rate(mt5_signal_latency_seconds_bucket[5m])) > 0.1",
                            "for": "2m",
                            "labels": {
                                "severity": "warning"
                            },
                            "annotations": {
                                "summary": "交易信号延迟过高",
                                "description": "P95延迟超过100ms，当前值: {{ $value }}s"
                            }
                        },
                        {
                            "alert": "HighErrorRate",
                            "expr": "rate(mt5_errors_total[5m]) > 0.1",
                            "for": "1m",
                            "labels": {
                                "severity": "warning"
                            },
                            "annotations": {
                                "summary": "错误率过高",
                                "description": "系统错误率超过阈值: {{ $value }}/s"
                            }
                        },
                        {
                            "alert": "NATSJetStreamDown",
                            "expr": "up{job=\"nats-jetstream\"} == 0",
                            "for": "30s",
                            "labels": {
                                "severity": "critical"
                            },
                            "annotations": {
                                "summary": "NATS JetStream离线",
                                "description": "NATS JetStream服务不可用"
                            }
                        }
                    ]
                }
            ]
        }
        
        rules_file = self.monitoring_dir / "rules" / "mt5_alerts.yml"
        with open(rules_file, 'w', encoding='utf-8') as f:
            yaml.dump(alert_rules, f, default_flow_style=False)
        
        logger.info("✅ 告警规则创建完成")
    
    def _start_monitoring_services(self):
        """启动监控服务"""
        logger.info("🔄 启动监控服务...")
        
        try:
            # 使用增强版Docker Compose启动监控服务
            result = subprocess.run(
                ["docker-compose", "-f", "docker-compose-enhanced.yml", "up", "-d", 
                 "prometheus", "grafana"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("✅ 监控服务启动成功")
                logger.info("📊 Grafana访问地址: http://localhost:3000")
                logger.info("📈 Prometheus访问地址: http://localhost:9090")
                logger.info("🔑 Grafana默认登录: admin/admin123")
            else:
                logger.error(f"❌ 监控服务启动失败: {result.stderr}")
                
        except Exception as e:
            logger.error(f"❌ 启动监控服务异常: {e}")
    
    def verify_monitoring_deployment(self) -> Dict[str, bool]:
        """验证监控部署"""
        logger.info("🔍 验证监控部署...")
        
        results = {}
        
        # 检查Prometheus
        try:
            import requests
            response = requests.get("http://localhost:9090/-/healthy", timeout=5)
            results['prometheus'] = response.status_code == 200
            logger.info(f"Prometheus健康检查: {'✅' if results['prometheus'] else '❌'}")
        except:
            results['prometheus'] = False
            logger.error("❌ Prometheus健康检查失败")
        
        # 检查Grafana
        try:
            import requests
            response = requests.get("http://localhost:3000/api/health", timeout=5)
            results['grafana'] = response.status_code == 200
            logger.info(f"Grafana健康检查: {'✅' if results['grafana'] else '❌'}")
        except:
            results['grafana'] = False
            logger.error("❌ Grafana健康检查失败")
        
        # 检查配置文件
        prometheus_config = self.monitoring_dir / "prometheus" / "prometheus.yml"
        results['prometheus_config'] = prometheus_config.exists()
        logger.info(f"Prometheus配置: {'✅' if results['prometheus_config'] else '❌'}")
        
        grafana_datasource = self.monitoring_dir / "grafana" / "datasources" / "prometheus.yml"
        results['grafana_config'] = grafana_datasource.exists()
        logger.info(f"Grafana配置: {'✅' if results['grafana_config'] else '❌'}")
        
        alert_rules = self.monitoring_dir / "rules" / "mt5_alerts.yml"
        results['alert_rules'] = alert_rules.exists()
        logger.info(f"告警规则: {'✅' if results['alert_rules'] else '❌'}")
        
        return results
    
    def generate_monitoring_report(self, verification_results: Dict[str, bool]):
        """生成监控部署报告"""
        logger.info("📊 监控部署报告")
        logger.info("=" * 50)
        
        logger.info("🔧 监控组件状态:")
        logger.info(f"  Prometheus: {'✅ 运行中' if verification_results.get('prometheus') else '❌ 离线'}")
        logger.info(f"  Grafana: {'✅ 运行中' if verification_results.get('grafana') else '❌ 离线'}")
        
        logger.info("📋 配置文件状态:")
        logger.info(f"  Prometheus配置: {'✅' if verification_results.get('prometheus_config') else '❌'}")
        logger.info(f"  Grafana配置: {'✅' if verification_results.get('grafana_config') else '❌'}")
        logger.info(f"  告警规则: {'✅' if verification_results.get('alert_rules') else '❌'}")
        
        logger.info("🌐 访问地址:")
        logger.info("  📊 Grafana: http://localhost:3000")
        logger.info("  📈 Prometheus: http://localhost:9090")
        
        logger.info("🔑 默认凭据:")
        logger.info("  Grafana用户名: admin")
        logger.info("  Grafana密码: admin123")
        
        # 整体状态
        all_services_up = verification_results.get('prometheus', False) and verification_results.get('grafana', False)
        all_configs_ready = all(verification_results.get(key, False) for key in ['prometheus_config', 'grafana_config', 'alert_rules'])
        
        logger.info("🎯 整体状态:")
        if all_services_up and all_configs_ready:
            logger.info("  ✅ 监控系统完全就绪")
        elif all_configs_ready:
            logger.info("  ⚠️ 配置完成，等待服务启动")
        else:
            logger.error("  ❌ 监控系统未完全就绪")
        
        logger.info("=" * 50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MT5监控系统部署工具")
    parser.add_argument("--verify-only", action="store_true", help="仅验证监控部署")
    parser.add_argument("--no-docker", action="store_true", help="不启动Docker服务")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging({'level': 'INFO', 'format': 'json'})
    
    # 创建部署器
    deployer = MonitoringDeployer()
    
    if args.verify_only:
        # 仅验证部署
        results = deployer.verify_monitoring_deployment()
        deployer.generate_monitoring_report(results)
    else:
        # 执行完整部署
        if not args.no_docker:
            success = deployer.deploy_monitoring_stack()
        else:
            # 仅配置，不启动服务
            deployer._create_monitoring_directories()
            deployer._setup_prometheus_config()
            deployer._setup_grafana_config()
            deployer._create_alert_rules()
            success = True
        
        if success:
            # 验证部署
            results = deployer.verify_monitoring_deployment()
            deployer.generate_monitoring_report(results)
            
            logger.info("🎉 监控系统部署完成！")
            logger.info("💡 请访问 http://localhost:3000 查看Grafana仪表板")
        else:
            logger.error("❌ 监控系统部署失败")
            sys.exit(1)


if __name__ == "__main__":
    main()
