#!/usr/bin/env python3
"""
分布式日志收集工具
用于收集和分析多主机MT5系统的日志
使用方法: python scripts/collect_logs.py [选项]
"""

import asyncio
import os
import sys
import time
import json
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import tempfile
import zipfile

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logger
from src.messaging.nats_client import NATSClient, NATSConfig
from src.config.redis_client import RedisClient

# 设置日志
setup_logger(level="INFO", log_to_file=False, format_type="simple")
logger = get_logger("log_collector")


class LogCollector:
    """分布式日志收集器"""
    
    def __init__(self, central_server: str = "localhost"):
        self.central_server = central_server
        self.nats_client = None
        self.redis_client = None
        self.collected_logs = []
        self.hosts_discovered = {}
    
    async def initialize(self) -> bool:
        """初始化连接"""
        try:
            # 初始化NATS连接
            nats_servers = [f"nats://{self.central_server}:4222"]
            nats_config = NATSConfig(
                servers=nats_servers,
                name="log_collector",
                connect_timeout=5.0
            )
            
            self.nats_client = NATSClient(nats_config)
            if not await self.nats_client.connect():
                logger.error("❌ NATS连接失败")
                return False
            
            logger.info("✅ NATS连接成功")
            
            # 初始化Redis连接
            self.redis_client = RedisClient(
                url=f"redis://{self.central_server}:6379",
                socket_timeout=5.0
            )
            
            if not await self.redis_client.connect():
                logger.error("❌ Redis连接失败")
                return False
            
            logger.info("✅ Redis连接成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 初始化失败: {e}")
            return False
    
    async def discover_hosts(self) -> Dict[str, Any]:
        """发现活跃的主机"""
        logger.info("🔍 发现分布式系统中的主机...")
        
        hosts = {}
        try:
            # 从Redis服务注册中心获取主机信息
            service_keys = await self.redis_client.keys("services:*")
            
            for key in service_keys:
                service_info = await self.redis_client.get(key)
                if service_info:
                    host_id = service_info.get('host_id', 'unknown')
                    service_type = service_info.get('service_type', 'unknown')
                    status = service_info.get('status', 'unknown')
                    timestamp = service_info.get('timestamp', 0)
                    accounts = service_info.get('accounts', [])
                    
                    # 检查服务是否新鲜（5分钟内更新）
                    if time.time() - timestamp < 300:
                        if host_id not in hosts:
                            hosts[host_id] = {
                                'host_id': host_id,
                                'services': [],
                                'accounts': [],
                                'last_seen': timestamp,
                                'status': status
                            }
                        
                        hosts[host_id]['services'].append({
                            'type': service_type,
                            'status': status,
                            'timestamp': timestamp
                        })
                        
                        if accounts:
                            hosts[host_id]['accounts'].extend(accounts)
            
            # 去重账户列表
            for host_info in hosts.values():
                host_info['accounts'] = list(set(host_info['accounts']))
            
            self.hosts_discovered = hosts
            
            logger.info(f"📊 发现 {len(hosts)} 个活跃主机:")
            for host_id, info in hosts.items():
                logger.info(f"   - {host_id}: {len(info['accounts'])} 账户, "
                          f"{len(info['services'])} 服务, 状态: {info['status']}")
            
            return hosts
            
        except Exception as e:
            logger.error(f"❌ 主机发现失败: {e}")
            return {}
    
    async def collect_logs_from_nats(self, duration_minutes: int = 10) -> List[Dict]:
        """从NATS收集实时日志"""
        logger.info(f"📝 开始收集实时日志 (持续 {duration_minutes} 分钟)...")
        
        logs = []
        end_time = time.time() + (duration_minutes * 60)
        
        try:
            # 订阅日志主题
            log_subject = "MT5.LOGS.*"
            
            async def log_handler(message):
                try:
                    log_data = json.loads(message.data.decode())
                    log_data['collected_at'] = time.time()
                    logs.append(log_data)
                    
                    # 显示日志摘要
                    host_id = log_data.get('host_id', 'unknown')
                    level = log_data.get('level', 'unknown')
                    msg = log_data.get('message', '')[:50]
                    
                    logger.debug(f"📨 [{host_id}] {level}: {msg}...")
                    
                except Exception as e:
                    logger.error(f"日志解析失败: {e}")
            
            # 订阅日志
            success = await self.nats_client.subscribe(log_subject, log_handler)
            
            if not success:
                logger.error("❌ 日志订阅失败")
                return []
            
            logger.info(f"✅ 开始监听日志: {log_subject}")
            
            # 等待收集完成
            while time.time() < end_time:
                await asyncio.sleep(1)
                
                # 每10秒显示进度
                if int(time.time()) % 10 == 0:
                    remaining = int(end_time - time.time())
                    logger.info(f"📊 已收集 {len(logs)} 条日志，剩余 {remaining} 秒...")
            
            logger.info(f"✅ 日志收集完成，共收集 {len(logs)} 条日志")
            return logs
            
        except Exception as e:
            logger.error(f"❌ 日志收集失败: {e}")
            return []
    
    async def collect_historical_logs(self, hours: int = 1) -> List[Dict]:
        """收集历史日志（如果有存储）"""
        logger.info(f"📚 尝试收集 {hours} 小时内的历史日志...")
        
        try:
            # 从Redis查找缓存的日志
            historical_logs = []
            
            # 查找所有主机的日志键
            for host_id in self.hosts_discovered:
                log_keys = await self.redis_client.keys(f"logs:{host_id}:*")
                
                for key in log_keys:
                    log_entries = await self.redis_client.lrange(key, 0, -1)
                    
                    for entry in log_entries:
                        if isinstance(entry, dict):
                            timestamp = entry.get('timestamp', 0)
                            # 只获取指定时间范围内的日志
                            if time.time() - timestamp < (hours * 3600):
                                entry['source'] = 'historical'
                                historical_logs.append(entry)
            
            logger.info(f"📊 收集到 {len(historical_logs)} 条历史日志")
            return historical_logs
            
        except Exception as e:
            logger.error(f"❌ 历史日志收集失败: {e}")
            return []
    
    def analyze_logs(self, logs: List[Dict]) -> Dict[str, Any]:
        """分析日志数据"""
        logger.info("🔍 分析日志数据...")
        
        analysis = {
            'total_logs': len(logs),
            'by_host': {},
            'by_level': {},
            'by_service': {},
            'error_summary': [],
            'performance_issues': [],
            'cross_host_events': [],
            'time_range': {}
        }
        
        if not logs:
            return analysis
        
        timestamps = []
        
        for log in logs:
            timestamp = log.get('timestamp', time.time())
            timestamps.append(timestamp)
            
            host_id = log.get('host_id', 'unknown')
            level = log.get('level', 'INFO')
            service = log.get('service', 'unknown')
            message = log.get('message', '')
            
            # 按主机统计
            if host_id not in analysis['by_host']:
                analysis['by_host'][host_id] = 0
            analysis['by_host'][host_id] += 1
            
            # 按级别统计
            if level not in analysis['by_level']:
                analysis['by_level'][level] = 0
            analysis['by_level'][level] += 1
            
            # 按服务统计
            if service not in analysis['by_service']:
                analysis['by_service'][service] = 0
            analysis['by_service'][service] += 1
            
            # 错误分析
            if level in ['ERROR', 'CRITICAL']:
                analysis['error_summary'].append({
                    'host': host_id,
                    'service': service,
                    'message': message[:100],
                    'timestamp': timestamp
                })
            
            # 跨主机事件
            if '跨主机' in message or 'cross_host' in message.lower():
                analysis['cross_host_events'].append({
                    'host': host_id,
                    'message': message[:100],
                    'timestamp': timestamp
                })
            
            # 性能问题
            if any(keyword in message.lower() for keyword in ['slow', '慢', 'timeout', '超时', 'delay', '延迟']):
                analysis['performance_issues'].append({
                    'host': host_id,
                    'message': message[:100],
                    'timestamp': timestamp
                })
        
        # 时间范围
        if timestamps:
            analysis['time_range'] = {
                'start': min(timestamps),
                'end': max(timestamps),
                'duration_minutes': (max(timestamps) - min(timestamps)) / 60
            }
        
        return analysis
    
    def generate_report(self, analysis: Dict[str, Any], output_file: str = None) -> str:
        """生成分析报告"""
        logger.info("📊 生成分析报告...")
        
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"log_analysis_report_{timestamp}.txt"
        
        report_lines = [
            "=" * 80,
            "MT5 分布式系统日志分析报告",
            "=" * 80,
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"中央服务器: {self.central_server}",
            ""
        ]
        
        # 基本统计
        report_lines.extend([
            "📊 基本统计",
            "-" * 40,
            f"总日志数量: {analysis['total_logs']}",
            ""
        ])
        
        # 时间范围
        if analysis['time_range']:
            start_time = datetime.fromtimestamp(analysis['time_range']['start'])
            end_time = datetime.fromtimestamp(analysis['time_range']['end'])
            duration = analysis['time_range']['duration_minutes']
            
            report_lines.extend([
                "⏰ 时间范围",
                "-" * 40,
                f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"持续时间: {duration:.1f} 分钟",
                ""
            ])
        
        # 主机分布
        if analysis['by_host']:
            report_lines.extend([
                "🖥️  主机分布",
                "-" * 40
            ])
            
            for host, count in sorted(analysis['by_host'].items()):
                percentage = (count / analysis['total_logs']) * 100
                report_lines.append(f"{host}: {count} 条 ({percentage:.1f}%)")
            
            report_lines.append("")
        
        # 日志级别分布
        if analysis['by_level']:
            report_lines.extend([
                "📈 日志级别分布",
                "-" * 40
            ])
            
            for level, count in sorted(analysis['by_level'].items()):
                percentage = (count / analysis['total_logs']) * 100
                emoji = "❌" if level in ['ERROR', 'CRITICAL'] else "⚠️" if level == 'WARNING' else "ℹ️"
                report_lines.append(f"{emoji} {level}: {count} 条 ({percentage:.1f}%)")
            
            report_lines.append("")
        
        # 错误汇总
        if analysis['error_summary']:
            report_lines.extend([
                "❌ 错误汇总 (最近10条)",
                "-" * 40
            ])
            
            for error in analysis['error_summary'][-10:]:
                error_time = datetime.fromtimestamp(error['timestamp'])
                report_lines.append(f"[{error_time.strftime('%H:%M:%S')}] {error['host']}: {error['message']}")
            
            report_lines.append("")
        
        # 跨主机事件
        if analysis['cross_host_events']:
            report_lines.extend([
                "📡 跨主机事件 (最近10条)",
                "-" * 40
            ])
            
            for event in analysis['cross_host_events'][-10:]:
                event_time = datetime.fromtimestamp(event['timestamp'])
                report_lines.append(f"[{event_time.strftime('%H:%M:%S')}] {event['host']}: {event['message']}")
            
            report_lines.append("")
        
        # 性能问题
        if analysis['performance_issues']:
            report_lines.extend([
                "⚡ 性能问题 (最近10条)",
                "-" * 40
            ])
            
            for issue in analysis['performance_issues'][-10:]:
                issue_time = datetime.fromtimestamp(issue['timestamp'])
                report_lines.append(f"[{issue_time.strftime('%H:%M:%S')}] {issue['host']}: {issue['message']}")
            
            report_lines.append("")
        
        # 建议
        recommendations = []
        
        error_count = analysis['by_level'].get('ERROR', 0) + analysis['by_level'].get('CRITICAL', 0)
        if error_count > 0:
            error_rate = (error_count / analysis['total_logs']) * 100
            if error_rate > 5:
                recommendations.append(f"错误率较高 ({error_rate:.1f}%)，建议检查系统配置")
        
        if len(analysis['cross_host_events']) > 0:
            recommendations.append("检测到跨主机通信，监控网络延迟和稳定性")
        
        if len(analysis['performance_issues']) > 0:
            recommendations.append("检测到性能问题，建议优化系统配置或硬件资源")
        
        if recommendations:
            report_lines.extend([
                "💡 建议",
                "-" * 40
            ])
            
            for i, rec in enumerate(recommendations, 1):
                report_lines.append(f"{i}. {rec}")
            
            report_lines.append("")
        
        report_lines.append("=" * 80)
        
        # 写入文件
        report_content = "\n".join(report_lines)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            logger.info(f"📁 报告已保存到: {output_file}")
            
        except Exception as e:
            logger.error(f"❌ 保存报告失败: {e}")
        
        return report_content
    
    async def export_logs(self, logs: List[Dict], format_type: str = "json") -> str:
        """导出日志数据"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type.lower() == "json":
            output_file = f"distributed_logs_{timestamp}.json"
            
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        'metadata': {
                            'export_time': time.time(),
                            'total_logs': len(logs),
                            'central_server': self.central_server,
                            'hosts_discovered': self.hosts_discovered
                        },
                        'logs': logs
                    }, f, indent=2, ensure_ascii=False, default=str)
                
                logger.info(f"📁 日志已导出到: {output_file}")
                return output_file
                
            except Exception as e:
                logger.error(f"❌ 导出失败: {e}")
                return ""
        
        elif format_type.lower() == "csv":
            output_file = f"distributed_logs_{timestamp}.csv"
            
            try:
                import csv
                
                with open(output_file, 'w', newline='', encoding='utf-8') as f:
                    if logs:
                        fieldnames = set()
                        for log in logs:
                            fieldnames.update(log.keys())
                        
                        writer = csv.DictWriter(f, fieldnames=list(fieldnames))
                        writer.writeheader()
                        writer.writerows(logs)
                
                logger.info(f"📁 日志已导出到: {output_file}")
                return output_file
                
            except Exception as e:
                logger.error(f"❌ 导出失败: {e}")
                return ""
        
        else:
            logger.error(f"❌ 不支持的导出格式: {format_type}")
            return ""
    
    async def cleanup(self):
        """清理资源"""
        if self.nats_client:
            await self.nats_client.disconnect()
        
        if self.redis_client:
            await self.redis_client.disconnect()


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MT5分布式系统日志收集工具")
    parser.add_argument("--server", "-s", default="localhost", 
                       help="中央服务器地址 (默认: localhost)")
    parser.add_argument("--duration", "-d", type=int, default=5,
                       help="实时日志收集持续时间(分钟) (默认: 5)")
    parser.add_argument("--historical", "-H", type=int, default=0,
                       help="收集历史日志的小时数 (默认: 0, 不收集)")
    parser.add_argument("--export", "-e", choices=["json", "csv"], default="json",
                       help="导出格式 (默认: json)")
    parser.add_argument("--output", "-o", help="输出文件前缀")
    parser.add_argument("--analyze-only", "-a", action="store_true",
                       help="仅分析现有日志，不收集新日志")
    
    args = parser.parse_args()
    
    logger.info("=" * 60)
    logger.info("🚀 MT5 分布式系统日志收集器")
    logger.info("=" * 60)
    logger.info(f"中央服务器: {args.server}")
    logger.info(f"收集持续时间: {args.duration} 分钟")
    if args.historical > 0:
        logger.info(f"历史日志范围: {args.historical} 小时")
    logger.info("=" * 60)
    
    collector = LogCollector(args.server)
    
    try:
        # 初始化连接
        if not await collector.initialize():
            logger.error("❌ 初始化失败")
            return 1
        
        # 发现主机
        hosts = await collector.discover_hosts()
        if not hosts:
            logger.warning("⚠️  未发现活跃主机，但继续执行...")
        
        all_logs = []
        
        # 收集历史日志
        if args.historical > 0:
            historical_logs = await collector.collect_historical_logs(args.historical)
            all_logs.extend(historical_logs)
        
        # 收集实时日志
        if not args.analyze_only and args.duration > 0:
            realtime_logs = await collector.collect_logs_from_nats(args.duration)
            all_logs.extend(realtime_logs)
        
        if not all_logs:
            logger.warning("⚠️  未收集到任何日志")
            return 1
        
        # 分析日志
        analysis = collector.analyze_logs(all_logs)
        
        # 生成报告
        output_prefix = args.output or f"mt5_distributed_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        report_file = f"{output_prefix}_report.txt"
        collector.generate_report(analysis, report_file)
        
        # 导出日志
        export_file = await collector.export_logs(all_logs, args.export)
        
        # 显示摘要
        logger.info("=" * 60)
        logger.info("📊 收集完成摘要")
        logger.info("=" * 60)
        logger.info(f"发现主机: {len(hosts)}")
        logger.info(f"收集日志: {len(all_logs)} 条")
        logger.info(f"错误日志: {analysis['by_level'].get('ERROR', 0)} 条")
        logger.info(f"跨主机事件: {len(analysis['cross_host_events'])} 个")
        logger.info(f"性能问题: {len(analysis['performance_issues'])} 个")
        logger.info("")
        logger.info("输出文件:")
        logger.info(f"  - 分析报告: {report_file}")
        if export_file:
            logger.info(f"  - 日志数据: {export_file}")
        logger.info("=" * 60)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("👋 用户中断收集")
        return 0
    except Exception as e:
        logger.error(f"❌ 收集失败: {e}")
        return 1
    finally:
        await collector.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)