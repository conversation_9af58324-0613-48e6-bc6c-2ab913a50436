{"test_suite": "parallel_startup_performance", "timestamp": **********.611388, "test_results": {"basic_parallel_startup": {"test_name": "basic_parallel_startup", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4082.*************, "average_startup_time_ms": 592.*************, "max_startup_time_ms": 720.*************, "min_startup_time_ms": 403.**************, "concurrent_connections": 4, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0859375, "avg_memory_per_connection_mb": 0.*********, "total_cpu_percent": -179.5, "avg_cpu_per_connection_percent": -8.975}, "error_summary": {}}, "concurrency_scaling": [{"test_name": "concurrency_scaling_1", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 7092.************, "average_startup_time_ms": 253.**************, "max_startup_time_ms": 309.*************, "min_startup_time_ms": 204.**************, "concurrent_connections": 1, "success_rate": 1.0, "throughput_per_second": 2.****************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": 26.***************, "avg_cpu_per_connection_percent": 1.****************}, "error_summary": {}}, {"test_name": "concurrency_scaling_1", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 1, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"test_error": 1}}, {"test_name": "concurrency_scaling_2", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4107.***********, "average_startup_time_ms": 297.**************, "max_startup_time_ms": 317.**************, "min_startup_time_ms": 201.**************, "concurrent_connections": 2, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": -41.**************, "avg_cpu_per_connection_percent": -2.****************}, "error_summary": {}}, {"test_name": "concurrency_scaling_2", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 2, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"test_error": 1}}, {"test_name": "concurrency_scaling_4", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4058.*************, "average_startup_time_ms": 588.*************, "max_startup_time_ms": 719.*************, "min_startup_time_ms": 403.**************, "concurrent_connections": 4, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0078125, "avg_memory_per_connection_mb": 0.*********, "total_cpu_percent": 112.**************, "avg_cpu_per_connection_percent": 5.***************}, "error_summary": {}}, {"test_name": "concurrency_scaling_4", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 4, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"test_error": 1}}, {"test_name": "concurrency_scaling_8", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4039.*************, "average_startup_time_ms": 918.*************, "max_startup_time_ms": 1514.************, "min_startup_time_ms": 605.*************, "concurrent_connections": 8, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": 27.4, "avg_cpu_per_connection_percent": 1.****************}, "error_summary": {}}, {"test_name": "concurrency_scaling_8", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 8, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"test_error": 1}}, {"test_name": "concurrency_scaling_16", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4032.************, "average_startup_time_ms": 1451.*************, "max_startup_time_ms": 2115.*************, "min_startup_time_ms": 605.************, "concurrent_connections": 16, "success_rate": 1.0, "throughput_per_second": 4.****************, "resource_usage": {"total_memory_mb": 0.0625, "avg_memory_per_connection_mb": 0.003125, "total_cpu_percent": -43.**************, "avg_cpu_per_connection_percent": -2.****************}, "error_summary": {}}, {"test_name": "concurrency_scaling_16", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 16, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"test_error": 1}}], "api_limit_effectiveness": [{"test_name": "api_limit_1", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 7066.************, "average_startup_time_ms": 252.**************, "max_startup_time_ms": 304.**************, "min_startup_time_ms": 201.**************, "concurrent_connections": 1, "success_rate": 1.0, "throughput_per_second": 2.****************, "resource_usage": {"total_memory_mb": 0.03125, "avg_memory_per_connection_mb": 0.0015625, "total_cpu_percent": -48.***************, "avg_cpu_per_connection_percent": -2.405}, "error_summary": {}}, {"test_name": "api_limit_1", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 1, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"api_limit_error": 1}}, {"test_name": "api_limit_2", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4138.************, "average_startup_time_ms": 299.**************, "max_startup_time_ms": 319.**************, "min_startup_time_ms": 212.**************, "concurrent_connections": 2, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": -57.**************, "avg_cpu_per_connection_percent": -2.****************}, "error_summary": {}}, {"test_name": "api_limit_2", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 2, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"api_limit_error": 1}}, {"test_name": "api_limit_4", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4035.************, "average_startup_time_ms": 585.*************, "max_startup_time_ms": 707.*************, "min_startup_time_ms": 402.**************, "concurrent_connections": 4, "success_rate": 1.0, "throughput_per_second": 4.****************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": 24.***************, "avg_cpu_per_connection_percent": 1.23}, "error_summary": {}}, {"test_name": "api_limit_4", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 4, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"api_limit_error": 1}}, {"test_name": "api_limit_8", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4037.************, "average_startup_time_ms": 918.*************, "max_startup_time_ms": 1513.*************, "min_startup_time_ms": 605.*************, "concurrent_connections": 8, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": -0.*****************, "avg_cpu_per_connection_percent": -0.004999999999999893}, "error_summary": {}}, {"test_name": "api_limit_8", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 8, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"api_limit_error": 1}}], "error_isolation": {"test_name": "error_isolation", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 4, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"isolation_test_error": 1}}, "stability_under_load": [{"test_name": "stability_run_1", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4115.*************, "average_startup_time_ms": 597.*************, "max_startup_time_ms": 725.*************, "min_startup_time_ms": 405.**************, "concurrent_connections": 4, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": -126.**************, "avg_cpu_per_connection_percent": -6.***************}, "error_summary": {}}, {"test_name": "stability_run_1", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 4, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"stability_test_error": 1}}, {"test_name": "stability_run_2", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4088.*************, "average_startup_time_ms": 593.************, "max_startup_time_ms": 720.*************, "min_startup_time_ms": 405.**************, "concurrent_connections": 4, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": 94.**************, "avg_cpu_per_connection_percent": 4.***************}, "error_summary": {}}, {"test_name": "stability_run_2", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 4, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"stability_test_error": 1}}, {"test_name": "stability_run_3", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4035.************, "average_startup_time_ms": 585.*************, "max_startup_time_ms": 707.*************, "min_startup_time_ms": 403.**********, "concurrent_connections": 4, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": -0.125, "avg_memory_per_connection_mb": -0.00625, "total_cpu_percent": -2.***************, "avg_cpu_per_connection_percent": -0.*****************}, "error_summary": {}}, {"test_name": "stability_run_3", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 4, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"stability_test_error": 1}}, {"test_name": "stability_run_4", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4038.*************, "average_startup_time_ms": 585.*************, "max_startup_time_ms": 707.*************, "min_startup_time_ms": 403.*************, "concurrent_connections": 4, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": -40.7, "avg_cpu_per_connection_percent": -2.035}, "error_summary": {}}, {"test_name": "stability_run_4", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 4, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"stability_test_error": 1}}, {"test_name": "stability_run_5", "total_accounts": 20, "successful_startups": 20, "failed_startups": 0, "total_duration_ms": 4061.*************, "average_startup_time_ms": 589.*************, "max_startup_time_ms": 733.*************, "min_startup_time_ms": 403.**************, "concurrent_connections": 4, "success_rate": 1.0, "throughput_per_second": 4.***************, "resource_usage": {"total_memory_mb": 0.0, "avg_memory_per_connection_mb": 0.0, "total_cpu_percent": 45.**************, "avg_cpu_per_connection_percent": 2.****************}, "error_summary": {}}, {"test_name": "stability_run_5", "total_accounts": 20, "successful_startups": 0, "failed_startups": 20, "total_duration_ms": 0, "average_startup_time_ms": 0, "max_startup_time_ms": 0, "min_startup_time_ms": 0, "concurrent_connections": 4, "success_rate": 0, "throughput_per_second": 0, "resource_usage": {}, "error_summary": {"stability_test_error": 1}}]}}