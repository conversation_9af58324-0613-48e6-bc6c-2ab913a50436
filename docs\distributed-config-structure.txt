# ========== hosts.yaml - 主机配置 ==========
hosts:
  host001:
    id: "host001"
    name: "交易服务器1"
    datacenter: "dc1"
    nats_servers:
      - "nats://nats1.dc1:4222"
      - "nats://nats2.dc1:4222"
    resources:
      max_accounts: 20
      max_memory_gb: 16
      max_cpu_cores: 8
    
  host002:
    id: "host002" 
    name: "交易服务器2"
    datacenter: "dc1"
    nats_servers:
      - "nats://nats1.dc1:4222"
      - "nats://nats2.dc1:4222"
    resources:
      max_accounts: 20
      max_memory_gb: 16
      max_cpu_cores: 8

  host003:
    id: "host003"
    name: "交易服务器3"
    datacenter: "dc2"
    nats_servers:
      - "nats://nats1.dc2:4222"
      - "nats://nats2.dc2:4222"
    resources:
      max_accounts: 15
      max_memory_gb: 8
      max_cpu_cores: 4

# ========== accounts.yaml - 账户配置 ==========
accounts:
  # 主账户
  ACC001:
    type: "master"
    name: "主策略账户1"
    host: "host001"  # 指定运行的主机
    broker: "XM"
    login: ********
    server: "XMGlobal-MT5 3"
    settings:
      allowed_symbols: ["EURUSD", "GBPUSD", "USDJPY"]
      max_positions: 10
      risk_percentage: 2.0
    monitoring:
      enabled: true
      check_interval: 100  # ms
      
  ACC002:
    type: "master"
    name: "主策略账户2"
    host: "host001"
    broker: "ICMarkets"
    login: ********
    server: "ICMarkets-MT5"
    settings:
      allowed_symbols: ["GOLD", "SILVER", "EURUSD"]
      max_positions: 5
      risk_percentage: 1.5
    monitoring:
      enabled: true
      check_interval: 100
      
  ACC003:
    type: "master"
    name: "主策略账户3"
    host: "host002"  # 不同主机
    broker: "Exness"
    login: ********
    server: "Exness-MT5"
    settings:
      allowed_symbols: ["BTCUSD", "ETHUSD"]
      max_positions: 3
      risk_percentage: 3.0
    monitoring:
      enabled: true
      check_interval: 200
      
  # 从账户
  ACC101:
    type: "slave"
    name: "跟单账户1"
    host: "host001"  # 与ACC001同主机
    broker: "XM"
    login: 10000001
    server: "XMGlobal-MT5 3"
    execution:
      algorithm: "market"
      max_slippage: 0.001
      
  ACC102:
    type: "slave"
    name: "跟单账户2"
    host: "host002"  # 跨主机
    broker: "ICMarkets"
    login: ********
    server: "ICMarkets-MT5"
    execution:
      algorithm: "market"
      max_slippage: 0.002
      
  ACC103:
    type: "slave"
    name: "跟单账户3"
    host: "host003"  # 跨数据中心
    broker: "Exness"
    login: ********
    server: "Exness-MT5"
    execution:
      algorithm: "twap"
      max_slippage: 0.003

# ========== active_pairings.yaml - 配对关系 ==========
active_pairings:
  # 配对组1：ACC001的跟单组
  - master_account_id: "ACC001"
    description: "主策略1跟单组"
    slaves:
      - slave_account_id: "ACC101"  # 同主机
        copy_ratio: 1.0
        enabled: true
        filters:
          symbols: ["EURUSD", "GBPUSD"]  # 只复制特定品种
          
      - slave_account_id: "ACC102"  # 跨主机
        copy_ratio: 0.5
        enabled: true
        filters:
          symbols: null  # 复制所有品种
          
      - slave_account_id: "ACC103"  # 跨数据中心
        copy_ratio: 0.3
        enabled: true
        filters:
          symbols: ["EURUSD"]
          min_volume: 0.01
          
  # 配对组2：ACC002的跟单组
  - master_account_id: "ACC002"
    description: "主策略2跟单组"
    slaves:
      - slave_account_id: "ACC102"  # 一个从账户可以跟多个主账户
        copy_ratio: 0.8
        enabled: true
        
  # 配对组3：ACC003的跟单组
  - master_account_id: "ACC003"
    description: "加密货币策略跟单组"
    slaves:
      - slave_account_id: "ACC103"
        copy_ratio: 1.0
        enabled: true
        filters:
          symbols: ["BTCUSD"]