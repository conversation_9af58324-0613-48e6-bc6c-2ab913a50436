"""
NATS JetStream客户端封装
提供持久化消息传递功能
"""
import asyncio
import json
import time
from typing import Callable, Optional, Dict, Any, List
from dataclasses import dataclass
import nats
from nats.aio.client import Client as NATS
from nats.js.api import StreamConfig, ConsumerConfig, DeliverPolicy, AckPolicy
from nats.js.errors import BadRequestError, NotFoundError

from .message_types import MessageEnvelope
from .message_codec import MessageCodec
from ..utils.logger import get_logger


logger = get_logger(__name__)

# 安全的metrics包装器
class SafeMetrics:
    def __init__(self):
        self._metrics = None
        self._initialized = False

    def _ensure_initialized(self):
        if not self._initialized:
            # 暂时完全禁用metrics以避免阻塞
            logger.debug("Metrics已禁用")
            self._metrics = None
            self._initialized = True

    def increment(self, name: str, value: int = 1, labels: dict = None):
        try:
            self._ensure_initialized()
            if self._metrics:
                self._metrics.increment(name, value, labels)
        except Exception as e:
            logger.debug(f"Metrics increment失败: {e}")

    def set_gauge(self, name: str, value: float, labels: dict = None):
        try:
            self._ensure_initialized()
            if self._metrics:
                self._metrics.set_gauge(name, value, labels)
        except Exception as e:
            logger.debug(f"Metrics gauge失败: {e}")

    def observe(self, name: str, value: float, labels: dict = None):
        try:
            self._ensure_initialized()
            if self._metrics:
                self._metrics.observe(name, value, labels)
        except Exception as e:
            logger.debug(f"Metrics observe失败: {e}")

metrics = SafeMetrics()


@dataclass
class JetStreamConfig:
    """JetStream配置"""
    servers: List[str]
    name: str = "mt5-jetstream"
    user: Optional[str] = None
    password: Optional[str] = None
    token: Optional[str] = None
    max_reconnect_attempts: int = 10
    reconnect_time_wait: float = 2.0
    connect_timeout: float = 10.0
    # JetStream特有配置
    stream_name: str = "MT5_SIGNALS"  # 默认使用新的信号流
    subjects: List[str] = None
    max_age: int = 24 * 60 * 60  # 24小时
    max_msgs: int = 1000000  # 最大消息数
    max_bytes: int = 1024 * 1024 * 1024  # 1GB
    replicas: int = 1  # 副本数量

    def __post_init__(self):
        if self.subjects is None:
            # 新的分层流主题配置 - 对应4层流架构
            self.subjects = [
                "MT5.SIGNALS.*",     # 交易信号流 - 支持优先级分层
                "MT5.RPC.*",         # RPC通信流
                "MT5.CONTROL.*",     # 系统控制流
                "MT5.HEARTBEAT.*",   # 心跳信息
  
            ]


class JetStreamClient:
    """JetStream客户端封装 - 增强分层流架构"""

    def __init__(self, config_or_servers):
        # 兼容多种初始化方式
        if isinstance(config_or_servers, list):
            # 传递服务器列表
            self.config = JetStreamConfig(servers=config_or_servers)
        elif isinstance(config_or_servers, JetStreamConfig):
            # 传递配置对象
            self.config = config_or_servers
        elif isinstance(config_or_servers, dict):
            # 传递字典配置（新增支持）
            servers = config_or_servers.get('servers', [])
            # 如果 servers 不是列表，尝试从其他配置中构建
            if not isinstance(servers, list):
                # 从配置获取服务器列表
                from ..core.config_manager import get_config_manager
                config_manager = get_config_manager()
                servers = config_manager.get('nats.servers', ["nats://localhost:4222"])
            
            # 处理subjects配置
            subjects_config = config_or_servers.get('jetstream', {}).get('subjects', {})
            subjects = []
            if isinstance(subjects_config, dict):
                # 从字典中提取subjects值
                subjects = list(subjects_config.values())
            elif isinstance(subjects_config, list):
                subjects = subjects_config
            else:
                # 使用默认subjects
                subjects = [
                    "MT5.SIGNALS.*", "MT5.RPC.*", "MT5.CONTROL.*", "MT5.HEARTBEAT.*"
                    # 使用新的4层分层流架构主题
                ]
            
            self.config = JetStreamConfig(
                servers=servers,
                name=config_or_servers.get('name', 'mt5-jetstream'),
                user=config_or_servers.get('user'),
                password=config_or_servers.get('password'),
                token=config_or_servers.get('token'),
                max_reconnect_attempts=config_or_servers.get('connection', {}).get('max_reconnect', 10),
                reconnect_time_wait=config_or_servers.get('connection', {}).get('reconnect_wait', 2.0),
                stream_name=config_or_servers.get('jetstream', {}).get('stream_name', 'MT5_SIGNALS'),
                subjects=subjects,
                connect_timeout=config_or_servers.get('connection', {}).get('timeout', 10.0),
                max_age=config_or_servers.get('jetstream', {}).get('max_age', 86400),
                max_msgs=config_or_servers.get('jetstream', {}).get('max_msgs', 10000000),
                max_bytes=config_or_servers.get('jetstream', {}).get('max_bytes', 10737418240),
                replicas=config_or_servers.get('jetstream', {}).get('replicas', 1)
            )
        else:
            raise ValueError("config_or_servers must be a list of servers, dict config, or JetStreamConfig object")

        self.nc: Optional[NATS] = None
        self.js = None
        self._connection_status = "disconnected"
        self._last_error = None
        self._consumers: Dict[str, Any] = {}
        self._reconnect_count = 0
        
        # 分层流架构状态
        self._layered_streams: Dict[str, bool] = {}  # 记录已创建的流
        self._priority_consumers: Dict[str, Any] = {}  # 优先级消费者
        self.host_id: Optional[str] = None  # 主机ID，用于本地流
        
    async def connect(self) -> bool:
        """连接到NATS服务器并初始化JetStream - 改进版"""
        if self.nc and self.nc.is_connected:
            try:
                await self.nc.flush(timeout=1.0)
                return True
            except:
                logger.warning("检测到连接异常，清理并重连")
                await self._cleanup_connection()

        try:
            if self.nc:
                await self._cleanup_connection()

            self.nc = NATS()

            await self.nc.connect(
                servers=self.config.servers,
                name=self.config.name,
                user=self.config.user,
                password=self.config.password,
                token=self.config.token,
                max_reconnect_attempts=self.config.max_reconnect_attempts,
                reconnect_time_wait=self.config.reconnect_time_wait,
                connect_timeout=self.config.connect_timeout,

                ping_interval=60,  
                max_outstanding_pings=3,
                flusher_queue_size=1024,
                pending_size=2 * 1024 * 1024,
                drain_timeout=30,

                error_cb=self._error_cb,
                disconnected_cb=self._disconnected_cb,
                reconnected_cb=self._reconnected_cb,
                closed_cb=self._closed_cb
            )
            
            self.js = self.nc.jetstream()
            
            logger.info("NATS连接成功，准备初始化流架构")
            
            self._connection_status = "connected"
            logger.info(f"JetStream连接成功: {self.config.servers}")
            
            metrics.increment("jetstream_connections_total")
            metrics.set_gauge("jetstream_connection_status", 1)

            return True
            
        except Exception as e:
            self._last_error = str(e)
            logger.error(f"JetStream连接失败: {e}")
            metrics.increment("jetstream_connection_errors_total")
            metrics.set_gauge("jetstream_connection_status", 0)
            return False
    
    async def initialize(self, host_id: str = None, use_layered_streams: bool = True) -> bool:
        """初始化JetStream客户端 - 现在默认使用分层流架构"""
        try:
            if not await self.connect():
                return False
                
            if use_layered_streams and host_id:
                logger.info("使用分层流架构初始化")
                success = await self.create_layered_streams(host_id)
                if not success:
                    logger.error("分层流架构初始化失败，回退到单流模式")
                    await self._ensure_stream()
                    return True
                else:
                    logger.info("分层流架构初始化成功")
                    return True
            else:
                logger.warning("使用已废弃的单流架构，建议升级到分层架构")
                await self._ensure_stream()
                return True
                
        except Exception as e:
            logger.error(f"JetStream初始化失败: {e}")
            return False
    
    async def _ensure_stream(self):
        """确保Stream存在 """
        try:
            stream_info = await self.js.stream_info(self.config.stream_name)
            logger.info(f"Stream已存在: {self.config.stream_name}, 消息数: {stream_info.state.messages}")
            
            existing_subjects = set(stream_info.config.subjects)
            required_subjects = set(self.config.subjects) if self.config.subjects else set()

            # 如果required_subjects为空，说明客户端没有特定的subjects要求
            # 在这种情况下，使用现有的Stream而不是删除它
            if required_subjects and existing_subjects != required_subjects:
                logger.warning(f"Stream subjects不匹配，删除并重新创建")
                logger.info(f"现有subjects: {existing_subjects}")
                logger.info(f"需要subjects: {required_subjects}")

                await self.js.delete_stream(self.config.stream_name)
                logger.info(f"已删除现有Stream: {self.config.stream_name}")

                await self._create_stream()
            elif not required_subjects:
                logger.info(f"客户端没有特定subjects要求，使用现有Stream: {self.config.stream_name}")
                logger.info(f"现有subjects: {existing_subjects}")
            else:
                logger.info(f"Stream subjects匹配，继续使用现有Stream: {self.config.stream_name}")

        except NotFoundError:
            logger.info(f"创建新Stream: {self.config.stream_name}")
            await self._create_stream()

        except Exception as e:
            logger.error(f"Stream检查失败: {e}")
            if "subjects overlap" in str(e).lower():
                logger.warning("检测到主题重叠，尝试查找并使用现有流...")
                existing_stream = await self._find_existing_stream_with_subjects()
                if existing_stream:
                    logger.info(f"使用现有流: {existing_stream}")
                    self.config.stream_name = existing_stream
                else:
                    await self._resolve_subject_overlap()
            else:
                try:
                    logger.info("尝试重新创建Stream...")
                    await self._create_stream()
                except Exception as create_error:
                    logger.error(f"重新创建Stream失败: {create_error}")
                    raise

    async def _create_stream(self):
        """创建Stream"""
        try:
            stream_config = StreamConfig(
                name=self.config.stream_name,
                subjects=self.config.subjects,
                max_age=self.config.max_age,
                max_msgs=self.config.max_msgs,
                max_bytes=self.config.max_bytes,
                num_replicas=self.config.replicas,
                storage="file",  # 文件存储
                retention="limits"  # 明确指定保留策略
            )

            await self.js.add_stream(stream_config)
            logger.info(f"Stream创建成功: {self.config.stream_name}")

        except BadRequestError as e:
            if "already exists" in str(e).lower():
                logger.info(f"Stream已存在: {self.config.stream_name}")
            elif "subjects overlap" in str(e).lower():
                logger.warning(f"主题重叠错误: {e}")
                # 处理主题重叠
                await self._resolve_subject_overlap()
            else:
                logger.error(f"Stream创建失败: {e}")
                raise
        except Exception as e:
            logger.error(f"Stream创建异常: {e}")
            raise

    async def _find_existing_stream_with_subjects(self) -> str:
        """查找使用相同主题的现有流"""
        try:
            streams = await self.js.streams_info()

            for stream in streams:
                for our_subject in self.config.subjects:
                    for existing_subject in stream.config.subjects:
                        if self._subjects_overlap(our_subject, existing_subject):
                            logger.info(f"找到重叠流: {stream.config.name}, 主题: {existing_subject}")
                            return stream.config.name

            return None

        except Exception as e:
            logger.error(f"查找现有流失败: {e}")
            return None

    def _subjects_overlap(self, subject1: str, subject2: str) -> bool:
        """检查两个主题是否重叠"""
        if subject1 == subject2:
            return True

        if subject1.endswith('*') and subject2.startswith(subject1[:-1]):
            return True
        if subject2.endswith('*') and subject1.startswith(subject2[:-1]):
            return True

        return False

    async def _resolve_subject_overlap(self):
        """解决主题重叠问题"""
        try:
            logger.info("尝试解决主题重叠问题...")

            streams = await self.js.streams_info()

            conflicting_streams = []
            for stream in streams:
                for our_subject in self.config.subjects:
                    for existing_subject in stream.config.subjects:
                        if self._subjects_overlap(our_subject, existing_subject):
                            conflicting_streams.append(stream.config.name)
                            break

            if conflicting_streams:
                logger.warning(f"发现冲突流: {conflicting_streams}")

                if conflicting_streams:
                    self.config.stream_name = conflicting_streams[0]
                    logger.info(f"使用现有冲突流: {self.config.stream_name}")
                    return

            import time
            unique_name = f"{self.config.stream_name}_{int(time.time())}"
            self.config.stream_name = unique_name
            logger.info(f"使用唯一流名称: {unique_name}")
            await self._create_stream()

        except Exception as e:
            logger.error(f"解决主题重叠失败: {e}")
            raise

    # ==================== 分层流架构方法 ====================
    
    async def create_layered_streams(self, host_id: str) -> bool:
        """创建分层流架构 """
        self.host_id = host_id
        
        if not self.js:
            logger.error("JetStream未连接，无法创建分层流")
            return False
        
        try:
            logger.info(f"开始创建分层流架构，主机ID: {host_id}")
            
            from ..core.config_manager import get_stream_config_manager
            stream_config_manager = get_stream_config_manager()
            all_streams = stream_config_manager.get_all_streams()
            
            if not all_streams:
                logger.error("未找到任何流配置，无法创建流架构")
                return False
            
            created_streams = 0
            for stream_type, stream_config in all_streams.items():
                try:
                    logger.info(f"创建流: {stream_type} ({stream_config.get('description', 'No description')})")
                    
                    success = await self._create_stream_from_config(stream_type, stream_config, host_id)
                    
                    if success:
                        created_streams += 1
                        self._layered_streams[stream_config.get('name', stream_type)] = True
                        logger.info(f"流创建成功: {stream_type}")
                    else:
                        logger.warning(f"流创建失败: {stream_type}")
                        
                except Exception as e:
                    logger.error(f"创建流 {stream_type} 时发生异常: {e}")
                    continue
            
            if created_streams > 0:
                logger.info(f"配置驱动流架构创建完成: {created_streams}/{len(all_streams)} 个流")
                return True
            else:
                logger.error("没有成功创建任何流")
                return False
            
        except Exception as e:
            logger.error(f"创建配置驱动流架构失败: {e}")
            return False
    
    async def _create_stream_from_config(self, stream_type: str, stream_config: Dict[str, Any], host_id: str) -> bool:
        """通用的配置驱动流创建方法 - 真正的配置驱动核心"""
        try:
            stream_name = stream_config.get('name', f'MT5_{stream_type.upper()}')
            if '{host_id}' in stream_name:
                stream_name = stream_name.format(host_id=host_id)
            
            subjects = []
            for subject_template in stream_config.get('subjects', []):
                if '{host_id}' in subject_template:
                    subjects.append(subject_template.format(host_id=host_id))
                else:
                    subjects.append(subject_template)
            
            if not subjects:
                logger.error(f"流 {stream_type} 没有定义主题，跳过创建")
                return False
            
            stream_nats_config = StreamConfig(
                name=stream_name,
                subjects=subjects,
                max_age=stream_config.get('max_age', 3600),
                max_msgs=stream_config.get('max_msgs', 100000),
                max_bytes=stream_config.get('max_bytes', 1073741824),
                storage=stream_config.get('storage', 'file'),
                num_replicas=stream_config.get('replicas', 1),
                retention=stream_config.get('retention', 'limits'),
                duplicate_window=stream_config.get('duplicate_window', 120)
            )
            
            try:
                await self.js.add_stream(stream_nats_config)
                logger.info(f"流创建成功: {stream_name} (类型: {stream_type})")
                return True
                
            except BadRequestError as e:
                if "already exists" in str(e).lower():
                    logger.info(f"流已存在: {stream_name} (类型: {stream_type})")
                    return True
                else:
                    logger.error(f"流创建失败: {stream_name} - {e}")
                    return False
                    
        except Exception as e:
            logger.error(f"流创建异常: {stream_type} - {e}")
            return False
    
    async def setup_priority_consumers(self) -> bool:
        """设置优先级消费者 """
        if not self.js:
            logger.error("JetStream未连接，无法设置优先级消费者")
            return False
        
        try:
            logger.info("开始设置配置驱动的消费者...")
            
            from ..core.config_manager import get_stream_config_manager
            stream_config_manager = get_stream_config_manager()
            consumers_config = stream_config_manager.get_all_consumers()
            
            if not consumers_config:
                logger.warning("未找到任何消费者配置，跳过设置")
                return True
            
            created_count = 0
            for consumer_key, config in consumers_config.items():
                try:
                    success = await self._create_configurable_consumer(consumer_key, config)
                    if success:
                        created_count += 1
                except Exception as e:
                    logger.error(f"创建消费者失败 {consumer_key}: {e}")
            
            logger.info(f"消费者设置完成: {created_count}/{len(consumers_config)} 个")
            return created_count > 0
            
        except Exception as e:
            logger.error(f"设置优先级消费者失败: {e}")
            return False

    async def _create_configurable_consumer(self, consumer_key: str, config: Dict[str, Any]) -> bool:
        """消费者创建方法"""
        try:
            consumer_name = config.get('name', consumer_key)
            subject_filter = config.get('filter_subject', '')
            queue_group = config.get('queue_group')
            mode = config.get('mode', 'queue')
            
            if '{host_id}' in subject_filter and self.host_id:
                subject_filter = subject_filter.format(host_id=self.host_id)
            if '{host_id}' in consumer_name and self.host_id:
                consumer_name = consumer_name.format(host_id=self.host_id)
            if queue_group and '{host_id}' in queue_group and self.host_id:
                queue_group = queue_group.format(host_id=self.host_id)
            
            if not subject_filter:
                logger.warning(f"消费者 {consumer_key} 没有主题过滤器，跳过")
                return False
            
            deliver_policy_map = {'new': DeliverPolicy.NEW, 'all': DeliverPolicy.ALL}
            ack_policy_map = {'explicit': AckPolicy.EXPLICIT, 'none': AckPolicy.NONE}

            consumer_nats_config = ConsumerConfig(
                name=consumer_name,
                durable_name=consumer_name,
                deliver_policy=deliver_policy_map.get(config.get('deliver_policy', 'new').lower(), DeliverPolicy.NEW),
                ack_policy=ack_policy_map.get(config.get('ack_policy', 'explicit').lower(), AckPolicy.EXPLICIT),
                filter_subject=subject_filter,
                max_deliver=config.get('max_deliver', 3),
                ack_wait=config.get('ack_wait', 30),
                max_ack_pending=config.get('max_ack_pending', 100)
            )
            
            stream_name = self._get_stream_name_for_subject(subject_filter)
            if not stream_name:
                logger.error(f"无法为主题 '{subject_filter}' 确定流名称，跳过消费者 '{consumer_name}'")
                return False

            await self.js.add_consumer(stream_name, consumer_nats_config)
            self._priority_consumers[consumer_name] = {'subject': subject_filter, 'stream': stream_name, 'config': config}
            logger.info(f"消费者创建成功: {consumer_name} on stream {stream_name}")
            return True

        except BadRequestError as e:
            if "already exists" in str(e).lower():
                logger.info(f"消费者已存在: {consumer_name}")
                return True
            else:
                logger.error(f"创建消费者失败: {consumer_name} - {e}")
                return False
        except Exception as e:
            logger.error(f"创建可配置消费者时发生异常: {consumer_name} - {e}")
            return False
    
    def _get_stream_name_for_subject(self, subject: str) -> str:
        """根据主题确定对应的流名称 - 配置驱动"""
        try:
            from ..core.config_manager import get_stream_config_manager
            stream_config_manager = get_stream_config_manager()
            all_streams = stream_config_manager.get_all_streams()
            
            for stream_type, stream_config in all_streams.items():
                stream_subjects = stream_config.get('subjects', [])
                for stream_subject in stream_subjects:
                    if self._subject_matches(subject, stream_subject):
                        return stream_config.get('name', '')
            
            logger.warning(f"未找到匹配的流，主题: {subject}，使用默认流")
            return self.config.stream_name
            
        except Exception as e:
            logger.error(f"确定流名称失败: {e}")

            if "MT5.SIGNALS." in subject:
                return "MT5_SIGNALS"
            elif "MT5.RPC." in subject:
                return "MT5_RPC"
            elif "MT5.LOCAL." in subject:
                return f"MT5_LOCAL_{self.host_id}"
            elif "MT5.CONTROL." in subject:
                return "MT5_CONTROL"
            else:
                return self.config.stream_name
    
    def _subject_matches(self, subject: str, pattern: str) -> bool:
        """检查主题是否匹配模式"""
        if pattern.endswith('*'):
            prefix = pattern[:-1]
            return subject.startswith(prefix)
        else:
            return subject == pattern
    
    async def publish_with_priority(self, signal_data: dict, priority=None) -> bool:
        """根据优先级发布消息 - 配置驱动主题构造"""
        try:
            from .priority_queue import PriorityAnalyzer, MessagePriority
            from ..core.config_manager import get_stream_config_manager
            
            if priority is None:
                priority = PriorityAnalyzer.get_command_priority(
                    method=signal_data.get('command_type', 'unknown'),
                    params=signal_data
                )
            
            stream_config_manager = get_stream_config_manager()
            account_id = signal_data.get('account_id', 'unknown')
            
            if signal_data.get('target_host_id') == self.host_id:
                subject = stream_config_manager.get_subject_pattern(
                    'local_signals', 
                    host_id=self.host_id,
                    account_id=account_id
                )
            else:
                subject = stream_config_manager.get_subject_pattern(
                    'global_signals',
                    priority=priority.name,
                    master_account=account_id
                )
            
            if not subject:
                logger.warning("配置获取主题失败，使用默认主题模式")
                if signal_data.get('target_host_id') == self.host_id:
                    subject = f"MT5.LOCAL.{self.host_id}.PRIORITY.{priority.name}.{account_id}"
                else:
                    subject = f"MT5.SIGNALS.{priority.name}.{account_id}"
            
            headers = {
                'priority': str(priority.value),
                'priority_name': priority.name,
                'timestamp': str(time.time()),
                'host_id': self.host_id or 'unknown'
            }
            
            success = await self.publish(subject, signal_data, headers)
            
            if success:
                logger.debug(f"优先级消息发布成功: {priority.name} -> {subject}")
            else:
                logger.warning(f"优先级消息发布失败: {priority.name} -> {subject}")
            
            return success
            
        except Exception as e:
            logger.error(f"优先级消息发布异常: {e}")
            return False
    
    def get_layered_streams_status(self) -> Dict[str, Any]:
        """获取分层流状态"""
        return {
            'host_id': self.host_id,
            'layered_streams': dict(self._layered_streams),
            'priority_consumers': {
                name: {
                    'subject': info['subject'],
                    'priority': info['priority'].name,
                    'stream': info['stream'],
                    'queue_group': info.get('queue_group'),
                    'broadcast': info.get('broadcast', False)
                }
                for name, info in self._priority_consumers.items()
            },
            'total_streams': len(self._layered_streams),
            'total_consumers': len(self._priority_consumers)
        }
    
    async def disconnect(self):
        """断开连接"""
        if self.nc:
            try:
                for consumer in list(self._consumers.values()):
                    try:
                        if hasattr(consumer, 'stop'):
                            await consumer.stop()
                    except:
                        pass

                self._consumers.clear()

                if self.nc.is_connected:
                    try:
                        await asyncio.wait_for(self.nc.drain(), timeout=3.0)
                    except asyncio.TimeoutError:
                        logger.warning("连接排空超时，强制关闭")
                    except Exception as e:
                        logger.warning(f"连接排空失败: {e}")

                    await self.nc.close()

                self._connection_status = "disconnected"
                logger.info("JetStream连接已断开")
                metrics.set_gauge("jetstream_connection_status", 0)

            except Exception as e:
                logger.error(f"断开JetStream连接失败: {e}")
            finally:
                self.nc = None
                self.js = None
                self._consumers.clear()
                await asyncio.sleep(0.05)

    async def _cleanup_connection(self):
        """清理连接资源"""
        try:
            if self.nc:
                for consumer in list(self._consumers.values()):
                    try:
                        if hasattr(consumer, 'stop'):
                            await consumer.stop()
                    except:
                        pass

                if self.nc.is_connected:
                    try:
                        await asyncio.wait_for(self.nc.close(), timeout=2.0)
                    except:
                        pass

                self.nc = None

            self.js = None
            self._consumers.clear()
            self._connection_status = "disconnected"

        except Exception as e:
            logger.debug(f"清理连接资源时出错: {e}")

    async def publish(self, subject: str, data: Any, headers: Dict[str, str] = None,
                     max_retries: int = 3) -> bool:
        """发布持久化消息 """
        if not self.js:
            logger.error("JetStream未连接")
            return False

        for attempt in range(max_retries + 1):
            try:
                start_time = time.perf_counter()

                payload = MessageCodec.encode(data)

                ack = await self.js.publish(subject, payload, headers=headers)

                publish_time = (time.perf_counter() - start_time) * 1000
                metrics.observe("jetstream_publish_duration_ms", publish_time)
                metrics.increment("jetstream_messages_published_total")

                if attempt > 0:
                    logger.info(f"JetStream消息发布成功 (重试 {attempt} 次): {subject}, seq={ack.seq}")
                else:
                    logger.debug(f"JetStream消息发布成功: {subject}, seq={ack.seq}")
                return True

            except Exception as e:
                error_msg = str(e)

                if "no response from stream" in error_msg:
                    logger.warning(f"流不存在，跳过消息发布: {subject} - {error_msg}")
                    return False

                if attempt < max_retries:
                    wait_time = (2 ** attempt) * 0.1  # 指数退避
                    logger.warning(f"JetStream消息发布失败，{wait_time:.1f}s后重试 ({attempt + 1}/{max_retries}): {subject} - {e}")
                    await asyncio.sleep(wait_time)

                    if not self.is_connected():
                        logger.info("检测到连接断开，尝试重连...")
                        if not await self.connect():
                            logger.error("重连失败，放弃发布")
                            break
                else:
                    logger.error(f"JetStream消息发布最终失败: {subject} - {e}")
                    metrics.increment("jetstream_publish_errors_total")
                    return False

        return False
    
    async def publish_message(self, message: MessageEnvelope) -> bool:
        """发布消息信封"""
        headers = message.headers.copy()
        headers.update({
            'message-id': message.id,
            'timestamp': str(message.timestamp)
        })
        
        if message.ttl:
            headers['ttl'] = str(message.ttl)
        
        return await self.publish(message.subject, message.payload, headers)

    async def add_stream(self, name=None, subjects=None, retention='limits',
                        max_age=3600, max_msgs=100000, **kwargs) -> bool:
        """添加流"""
        if not self.js:
            logger.error("JetStream未连接")
            return False

        try:
            from nats.js.api import StreamConfig

            if isinstance(name, dict):
                config_dict = name
                name = config_dict.get('name')
                subjects = config_dict.get('subjects', [])
                retention = config_dict.get('retention', 'limits')
                max_age = config_dict.get('max_age', 3600)
                max_msgs = config_dict.get('max_msgs', 100000)

            stream_config = StreamConfig(
                name=name,
                subjects=subjects or [],
                retention=retention,
                max_age=max_age,
                max_msgs=max_msgs
            )

            await self.js.add_stream(stream_config)
            logger.info(f"流创建成功: {name}")
            return True
        except Exception as e:
            if "already exists" not in str(e).lower():
                logger.error(f"流创建失败: {name} - {e}")
            return False

    async def subscribe(self, subject: str, callback: Callable, queue_group: str = None):
        """订阅消息"""
        if not self.js:
            logger.error("JetStream未连接")
            return None

        try:
            consumer_name = f"consumer_{subject.replace('.', '_').replace('*', 'wildcard')}"
            if queue_group:
                consumer_name = f"{queue_group}_{consumer_name}"

            if consumer_name in self._consumers:
                existing_sub = self._consumers[consumer_name]
                try:
                    if hasattr(existing_sub, 'is_valid') and existing_sub.is_valid:
                        logger.debug(f"复用现有订阅: {subject}")
                        return existing_sub
                except:
                    pass

                try:
                    await existing_sub.unsubscribe()
                except:
                    pass
                del self._consumers[consumer_name]

            async def js_callback(msg):
                try:
                    decoded_data = MessageCodec.decode(msg.data)
                    if decoded_data is not None:
                        class DecodedMessage:
                            def __init__(self, original_msg, data):
                                self.data = data
                                self.subject = original_msg.subject
                                self.reply = getattr(original_msg, 'reply', None)
                                self.headers = getattr(original_msg, 'headers', {})
                                self._original = original_msg

                            async def ack(self):
                                try:
                                    await self._original.ack()
                                except Exception as e:
                                    logger.warning(f"消息ACK失败: {e}")

                            async def nak(self):
                                try:
                                    await self._original.nak()
                                except Exception as e:
                                    logger.warning(f"消息NAK失败: {e}")

                        decoded_msg = DecodedMessage(msg, decoded_data)
                        await callback(decoded_msg)
                    else:
                        logger.error(f"消息解码失败，跳过处理: {subject}")
                        await msg.nak()
                        return


                except Exception as e:
                    logger.error(f"消息处理失败: {e}")
                    try:
                        if not getattr(msg, '_ackd', False):
                            await msg.nak()
                    except Exception as nak_error:
                        logger.warning(f"NAK失败: {nak_error}")

            try:
                subscription = await self.js.subscribe(
                    subject,
                    cb=js_callback,
                    durable=consumer_name,
                    queue=queue_group
                )

                self._consumers[consumer_name] = subscription

                logger.info(f"订阅成功: {subject}")
                return subscription

            except Exception as e:
                error_msg = str(e).lower()
                if "already bound" in error_msg or "consumer is already bound" in error_msg:
                    logger.warning(f"消费者已绑定，尝试重新创建: {consumer_name}")
                    try:
                        stream_name = self.config.stream_name  # 使用配置中的流名称
                        await self.js.delete_consumer(stream_name, consumer_name)
                        await asyncio.sleep(0.1)  # 短暂等待

                        subscription = await self.js.subscribe(
                            subject,
                            cb=js_callback,
                            durable=consumer_name,
                            queue=queue_group
                        )

                        self._consumers[consumer_name] = subscription
                        logger.info(f"重新订阅成功: {subject}")
                        return subscription

                    except Exception as retry_e:
                        logger.error(f"重新订阅失败: {subject} - {retry_e}")
                        return None
                else:
                    raise e
        except Exception as e:
            logger.error(f"订阅失败: {subject} - {e}")
            return None


    async def subscribe_durable(self, subject: str, callback: Callable,
                              consumer_name: str, queue_group: str = None) -> bool:
        """订阅持久化消息"""
        if not self.js:
            logger.error("JetStream未连接")
            return False
        
        try:
            consumer_config = ConsumerConfig(
                name=consumer_name,
                durable_name=consumer_name,
                deliver_policy=DeliverPolicy.ALL,
                ack_policy=AckPolicy.EXPLICIT,
                filter_subject=subject,
                max_deliver=3,  # 最大重试次数
                ack_wait=30,    # 确认等待时间(秒)
                max_ack_pending=1000  # 最大未确认消息数
            )
            
            try:
                consumer = await self.js.consumer_info(self.config.stream_name, consumer_name)
                logger.info(f"使用现有消费者: {consumer_name}")
            except NotFoundError:
                consumer = await self.js.add_consumer(self.config.stream_name, consumer_config)
                logger.info(f"创建新消费者: {consumer_name}")
            
            async def message_handler(msg):
                start_time = time.perf_counter()
                try:
                    data = MessageCodec.decode(msg.data)
                    if data is None:
                        logger.error(f"消息解码失败: {msg.subject}")
                        await msg.nak()
                        return

                    headers = msg.headers or {}
                    message = MessageEnvelope(
                        id=headers.get('message-id', ''),
                        subject=msg.subject,
                        payload=data,
                        headers=dict(headers),
                        timestamp=float(headers.get('timestamp', time.time()))
                    )

                    await callback(message)
                    
                    await msg.ack()

                    process_time = (time.perf_counter() - start_time) * 1000
                    metrics.observe("jetstream_message_process_duration_ms", process_time)
                    metrics.increment("jetstream_messages_processed_total")
                    
                except Exception as e:
                    logger.error(f"JetStream消息处理失败: {e}")
                    metrics.increment("jetstream_message_process_errors_total")
                    try:
                        await msg.nak()
                    except Exception as nak_error:
                        logger.warning(f"NAK失败: {nak_error}")
            
            subscription = await self.js.subscribe(
                subject=subject,
                cb=message_handler,
                stream=self.config.stream_name,
                config=consumer_config,
                manual_ack=True
            )
            
            self._consumers[consumer_name] = subscription
            logger.info(f"JetStream订阅成功: {subject}, consumer={consumer_name}")
            metrics.increment("jetstream_subscriptions_total")
            
            return True
            
        except Exception as e:
            logger.error(f"JetStream订阅失败: {e}")
            metrics.increment("jetstream_subscription_errors_total")
            return False
    
    async def unsubscribe(self, consumer_name: str) -> bool:
        """取消订阅"""
        if consumer_name not in self._consumers:
            return True
        
        try:
            subscription = self._consumers[consumer_name]
            await subscription.unsubscribe()
            del self._consumers[consumer_name]
            
            logger.info(f"取消JetStream订阅成功: {consumer_name}")
            metrics.increment("jetstream_unsubscriptions_total")
            return True
            
        except Exception as e:
            logger.error(f"取消JetStream订阅失败: {e}")
            return False
    
    async def get_stream_info(self) -> Optional[Dict[str, Any]]:
        """获取Stream信息"""
        if not self.js:
            return None
        
        try:
            info = await self.js.stream_info(self.config.stream_name)
            return {
                'name': info.config.name,
                'subjects': info.config.subjects,
                'messages': info.state.messages,
                'bytes': info.state.bytes,
                'first_seq': info.state.first_seq,
                'last_seq': info.state.last_seq,
                'consumer_count': info.state.consumer_count
            }
        except Exception as e:
            logger.error(f"获取Stream信息失败: {e}")
            return None
    
    async def get_consumer_info(self, consumer_name: str) -> Optional[Dict[str, Any]]:
        """获取消费者信息"""
        if not self.js:
            return None
        
        try:
            info = await self.js.consumer_info(self.config.stream_name, consumer_name)
            return {
                'name': info.name,
                'stream_name': info.stream_name,
                'num_pending': info.num_pending,
                'num_redelivered': info.num_redelivered,
                'num_ack_pending': info.num_ack_pending,
                'delivered': info.delivered.stream_seq if info.delivered else 0,
                'ack_floor': info.ack_floor.stream_seq if info.ack_floor else 0
            }
        except Exception as e:
            logger.error(f"获取消费者信息失败: {e}")
            return None
    
    async def request(self, subject: str, data: Dict[str, Any], timeout: float = 10.0) -> Optional[Dict[str, Any]]:
        """
        发送请求并等待响应 - 使用JetStream的发布/订阅机制实现request/response模式
        """
        if not self.nc or not self.nc.is_connected:
            logger.error("JetStream未连接，无法发送请求")
            return None
        
        try:
            start_time = time.perf_counter()
            payload = MessageCodec.encode(data)  # MessageCodec.encode returns bytes
            response = await self.nc.request(subject, payload, timeout=timeout)
            response_data =MessageCodec.decode(response.data.decode('utf-8'))
            
            request_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("jetstream_request_duration_ms", request_time)
            metrics.increment("jetstream_requests_total")
            
            logger.debug(f"JetStream请求成功: {subject}")
            return response_data
            
        except asyncio.TimeoutError:
            logger.error(f"JetStream请求超时: {subject}")
            metrics.increment("jetstream_request_timeouts_total")
            return None
        except Exception as e:
            logger.error(f"JetStream请求失败: {e}")
            logger.error(f"请求详情: subject={subject}, timeout={timeout}")
            logger.error(f"连接状态: connected={self.nc.is_connected if self.nc else 'None'}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            metrics.increment("jetstream_request_errors_total")
            return None
    
    async def _error_cb(self, e):
        """错误回调"""
        self._last_error = str(e)
        logger.error(f"JetStream错误: {e}")
        metrics.increment("jetstream_errors_total")

    async def _disconnected_cb(self):
        """断开连接回调"""
        self._connection_status = "disconnected"
        logger.warning("JetStream连接已断开")
        metrics.set_gauge("jetstream_connection_status", 0)

    async def _reconnected_cb(self):
        """重新连接回调"""
        self._connection_status = "connected"
        self._reconnect_count += 1
        logger.info("JetStream重新连接成功")
        metrics.increment("jetstream_reconnections_total")
        metrics.set_gauge("jetstream_connection_status", 1)

        try:
            await self._ensure_stream()
        except Exception as e:
            logger.error(f"重新连接后Stream初始化失败: {e}")

    async def _closed_cb(self):
        """连接关闭回调"""
        self._connection_status = "closed"
        logger.info("JetStream连接已关闭")
        metrics.set_gauge("jetstream_connection_status", 0)
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.nc is not None and self.nc.is_connected
    
    def get_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        return {
            'connection_status': self._connection_status,
            'is_connected': self.is_connected(),
            'last_error': self._last_error,
            'reconnect_count': self._reconnect_count,
            'consumers': list(self._consumers.keys()),
            'servers': self.config.servers,
            'stream_name': self.config.stream_name
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.is_connected():
            return False
        
        try:
            stream_info = await self.get_stream_info()
            if not stream_info:
                return False
            
            test_subject = "health.check"
            test_data = {"timestamp": time.time()}
            
            await self.publish(test_subject, test_data)
            return True
            
        except Exception as e:
            logger.error(f"JetStream健康检查失败: {e}")
            return False

    async def create_consumer(self, stream_name: str, consumer_name: str, filter_subject: str = None) -> Any:
        """创建JetStream消费者"""
        try:
            if not self.js:
                raise RuntimeError("JetStream未启用")

            from nats.js.api import ConsumerConfig

            config_dict = {
                "durable_name": consumer_name,
                "deliver_policy": "new",
                "ack_policy": "explicit"
            }

            if filter_subject:
                config_dict["filter_subject"] = filter_subject

            consumer_config = ConsumerConfig(**config_dict)

            consumer_info = await self.js.add_consumer(
                stream=stream_name,
                config=consumer_config
            )

            logger.info(f"消费者创建成功: {consumer_name} (流: {stream_name})")
            return consumer_info

        except Exception as e:
            logger.error(f"消费者创建失败: {consumer_name} - {e}")
            raise