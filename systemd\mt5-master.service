[Unit]
Description=MT5 Master Monitor Service
After=network.target redis.service nats.service mt5-api.service
Wants=redis.service nats.service mt5-api.service
StartLimitIntervalSec=60
StartLimitBurst=3

[Service]
Type=simple
User=mt5
Group=mt5
WorkingDirectory=/opt/mt5-copier
Environment="PYTHONPATH=/opt/mt5-copier"
Environment="MT5_CONFIG_PATH=/opt/mt5-copier/config/config.yaml"
Environment="MT5_LOG_LEVEL=INFO"
Environment="MT5_ACCOUNT_ID=master_001"
ExecStart=/opt/mt5-copier/venv/bin/python scripts/start_master.py --config ${MT5_CONFIG_PATH} --account ${MT5_ACCOUNT_ID}
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
TimeoutStopSec=30
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mt5-master

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/mt5-copier/logs /opt/mt5-copier/data
CapabilityBoundingSet=

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=1G
CPUQuota=100%

[Install]
WantedBy=multi-user.target