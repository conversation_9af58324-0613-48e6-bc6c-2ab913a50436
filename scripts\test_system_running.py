#!/usr/bin/env python3
"""
测试系统是否正在运行
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging
from src.messaging.jetstream_client import JetStreamClient
from src.core.signal_types import UnifiedTradeSignal, OrderType, SignalAction

logger = get_logger(__name__)


async def test_system_communication():
    """测试系统通信"""
    logger.info("🧪 测试系统通信...")
    
    try:
        # 连接到 JetStream
        client = JetStreamClient(['nats://localhost:4222'])
        success = await client.connect()
        
        if not success:
            logger.error("❌ 无法连接到 JetStream")
            return False
        
        logger.info("✅ 连接到 JetStream 成功")
        
        # 创建测试信号
        test_signal = UnifiedTradeSignal.create_open_signal(
            master_account="TEST_MASTER",
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=0.1,
            price=1.1000,
            position_id=99999,
            comment="System test signal"
        )
        
        # 发布测试信号
        success = await client.publish(
            subject="MT5.TRADES.TEST_MASTER",
            data=test_signal.to_dict()
        )
        
        if success:
            logger.info("✅ 测试信号发布成功")
        else:
            logger.error("❌ 测试信号发布失败")
        
        # 获取流信息
        stream_info = await client.get_stream_info()
        if stream_info:
            logger.info(f"✅ 流状态: {stream_info['name']}, 消息数: {stream_info['messages']}")
        
        await client.disconnect()
        return True
        
    except Exception as e:
        logger.error(f"❌ 系统通信测试失败: {e}")
        return False


async def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard'
    })
    
    logger.info("=" * 60)
    logger.info("🚀 MT5系统运行状态测试")
    logger.info("=" * 60)
    
    # 测试系统通信
    success = await test_system_communication()
    
    if success:
        logger.info("🎉 系统运行正常！")
        return 0
    else:
        logger.error("💥 系统可能未运行或有问题")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
