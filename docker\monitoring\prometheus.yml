# Prometheus配置文件 - MT5分布式交易系统监控
# 针对混合架构优化的监控配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'mt5-trading-system'
    environment: 'production'

# 规则文件
rule_files:
  - "rules/*.yml"

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # MT5系统核心指标
  - job_name: 'mt5-system'
    static_configs:
      - targets:
        - 'mt5-system:8000'  # 主系统指标端点
        - 'mt5-hybrid-system:8000'
    scrape_interval: 10s  # 高频采集交易指标
    metrics_path: /metrics
    scrape_timeout: 8s

  # NATS JetStream监控
  - job_name: 'nats-jetstream'
    static_configs:
      - targets: ['nats:8222', 'mt5-nats-optimized:8222']
    scrape_interval: 10s
    metrics_path: /varz
    scrape_timeout: 5s

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379', 'mt5-redis-optimized:6379']
    scrape_interval: 15s
    metrics_path: /metrics

  # Pushgateway监控
  - job_name: 'pushgateway'
    static_configs:
      - targets: ['pushgateway:9091', 'mt5-pushgateway-optimized:9091']
    honor_labels: true
    scrape_interval: 15s
    metrics_path: /metrics

  # 系统资源监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # MT5主账户监控器
  - job_name: 'mt5-master-monitors'
    static_configs:
      - targets:
        - 'localhost:8001'  # ACC001监控器
        - 'localhost:8002'  # ACC002监控器
        - 'localhost:8003'  # ACC003监控器
    scrape_interval: 10s
    scrape_timeout: 8s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__address__]
        regex: 'localhost:800([0-9])'
        target_label: account_id
        replacement: 'ACC00${1}'

  # MT5从账户执行器
  - job_name: 'mt5-slave-executors'
    static_configs:
      - targets:
        - 'localhost:8011'  # 从执行器1
        - 'localhost:8012'  # 从执行器2
        - 'localhost:8013'  # 从执行器3
    scrape_interval: 10s
    scrape_timeout: 8s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__address__]
        regex: 'localhost:801([0-9])'
        target_label: executor_id
        replacement: 'EXEC0${1}'

  # 系统协调器监控
  - job_name: 'mt5-coordinator'
    static_configs:
      - targets: ['localhost:8020']
    scrape_interval: 10s
    metrics_path: /metrics

  # 分布式组件监控
  - job_name: 'mt5-distributed-components'
    static_configs:
      - targets:
        - 'localhost:8030'  # 账户注册表
        - 'localhost:8031'  # 消息路由器
        - 'localhost:8032'  # 故障转移管理器
    scrape_interval: 15s
    metrics_path: /metrics

# 存储配置通过命令行参数设置
# --storage.tsdb.retention.time=30d
# --storage.tsdb.retention.size=50GB
