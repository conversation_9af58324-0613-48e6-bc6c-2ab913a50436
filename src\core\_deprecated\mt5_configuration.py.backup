# src/core/mt5_configuration.py
"""
MT5配置管理器
合并了账户配置管理和高级功能：
- 账户部署配置管理
- 热重载和配置验证
- 资源限制和性能监控
- 配置缓存和优化
- 环境变量安全管理
"""
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Set
import yaml
import os
import time
from pathlib import Path
import logging
import hashlib
import json
import asyncio
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from dotenv import load_dotenv
load_dotenv()   

logger = logging.getLogger(__name__)

@dataclass
class AccountCapabilities:
    """账户能力配置"""
    supported_symbols: List[str] = field(default_factory=list)  # 空列表表示支持所有
    max_positions: int = 10
    max_lot_size: float = 1.0
    min_lot_size: float = 0.01
    allowed_operations: List[str] = field(default_factory=lambda: ["BUY", "SELL", "CLOSE"])
    enable_monitoring: bool = True  # 是否启用持仓监控
    enable_execution: bool = True   # 是否启用交易执行

    def validate(self) -> List[str]:
        """验证能力配置"""
        errors = []
        if self.max_lot_size <= 0:
            errors.append("最大手数必须大于0")
        if self.min_lot_size <= 0 or self.min_lot_size > self.max_lot_size:
            errors.append("最小手数配置无效")
        if not self.enable_monitoring and not self.enable_execution:
            errors.append("至少要启用监控或执行功能之一")
        return errors

@dataclass  
class DeploymentConfig:
    """部署配置"""
    host_id: str
    terminal_path: str
    data_folder: Optional[str] = None
    process_priority: str = "normal"  # low, normal, high
    restart_on_failure: bool = True
    max_restart_attempts: int = 3
    terminal_startup_timeout: int = 60  # 秒
    resource_limits: Dict[str, Any] = field(default_factory=lambda: {
        "max_memory_mb": 512,
        "max_cpu_percent": 50
    })
    
    def validate(self) -> List[str]:
        """验证部署配置"""
        errors = []
        if not self.host_id:
            errors.append("host_id不能为空")
        if not self.terminal_path:
            errors.append(f"终端路径不能为空")
        if self.process_priority not in ["low", "normal", "high"]:
            errors.append(f"无效的进程优先级: {self.process_priority}")
        return errors

@dataclass
class AccountConfig:
    """账户配置"""
    account_id: str
    login: int
    server: str
    description: str = ""
    
    deployment: DeploymentConfig = None
    capabilities: AccountCapabilities = None
    
    enabled: bool = True
    tags: List[str] = field(default_factory=list)  # 用于分组和筛选
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    password: Optional[str] = field(default=None, repr=False, compare=False)
    
    config_hash: str = field(default="", compare=False)
    last_modified: float = field(default_factory=time.time, compare=False)
    
    def __post_init__(self):
        """初始化后处理"""
        if self.deployment is None:
            self.deployment = DeploymentConfig(host_id="", terminal_path="")
        if self.capabilities is None:
            self.capabilities = AccountCapabilities()
            
        if not self.password:
            env_key = f"MT5_{self.account_id.upper()}_PASSWORD"
            self.password = os.getenv(env_key)
            if self.password:
                logger.debug(f"从环境变量加载密码成功: {env_key}")
            else:
                logger.warning(f"环境变量未设置或为空: {env_key}")
            
        self.config_hash = self._calculate_hash()
    
    def _calculate_hash(self) -> str:
        """计算配置哈希值"""
        config_dict = {
            'account_id': self.account_id,
            'login': self.login,
            'server': self.server,
            'deployment': {
                'host_id': self.deployment.host_id,
                'terminal_path': self.deployment.terminal_path
            },
            'capabilities': {
                'supported_symbols': sorted(self.capabilities.supported_symbols),
                'max_positions': self.capabilities.max_positions,
                'max_lot_size': self.capabilities.max_lot_size
            }
        }
        config_str = json.dumps(config_dict, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def has_changed(self) -> bool:
        """检查配置是否已更改"""
        return self._calculate_hash() != self.config_hash
    
    def validate(self) -> List[str]:
        """验证配置完整性"""
        errors = []
        
        if not self.account_id:
            errors.append("account_id不能为空")
        if not self.login or self.login <= 0:
            errors.append("login必须是正整数")
        if not self.server:
            errors.append("server不能为空")
            
        if not self.password:
            errors.append(f"密码未设置(环境变量: MT5_{self.account_id.upper()}_PASSWORD)")
            
        errors.extend(self.deployment.validate())
        
        errors.extend(self.capabilities.validate())
        
        return errors
        
    # 兼容性属性，用于与旧代码兼容
    @property
    def name(self) -> str:
        return self.account_id
        
    @property
    def host_id(self) -> str:
        return self.deployment.host_id if self.deployment else ""
        
    @property
    def terminal_path(self) -> str:
        return self.deployment.terminal_path if self.deployment else ""
    
    @property
    def magic_number(self) -> int:
        return self.metadata.get('magic_number', 12345)
    
    @property
    def max_volume(self) -> float:
        return self.metadata.get('max_volume', self.capabilities.max_lot_size if self.capabilities else 10.0)
    
    @property
    def min_volume(self) -> float:
        return self.metadata.get('min_volume', self.capabilities.min_lot_size if self.capabilities else 0.01)
    
    @property
    def allowed_symbols(self) -> list:
        return self.capabilities.supported_symbols if self.capabilities else []
    
    @property
    def max_daily_loss(self) -> float:
        return self.metadata.get('max_daily_loss', 5000.0)
    
    @property
    def max_positions(self) -> int:
        return self.metadata.get('max_positions', self.capabilities.max_positions if self.capabilities else 10)

# 兼容性增强
@dataclass
class LegacyAccountConfig:
    """为了兼容旧代码的账户配置简化版本"""
    account_id: str
    name: str
    enabled: bool
    login: int
    server: str
    terminal_path: str
    host_id: str
    account_type: str = "demo"
    magic_number: int = 12345
    max_volume: float = 10.0
    min_volume: float = 0.01
    allowed_symbols: List[str] = field(default_factory=list)
    max_daily_loss: float = 5000.0
    max_positions: int = 15

class ConfigChangeHandler(FileSystemEventHandler):
    """配置文件变化监控"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.last_reload = 0
        self.reload_delay = 2  # 2秒防抖
    
    def on_modified(self, event):
        """文件修改时触发"""
        if event.is_directory or not event.src_path.endswith('.yaml'):
            return
            
        current_time = time.time()
        if current_time - self.last_reload < self.reload_delay:
            return
            
        self.last_reload = current_time
        
        try:
            account_file = Path(event.src_path)
            if account_file.parent.name == 'accounts':
                logger.info(f"检测到账户配置变化: {account_file.name}")
                self.config_manager.reload_all_configs()
        except Exception as e:
            logger.error(f"处理配置变化时出错: {e}")

class AccountConfigManager:
    """
    账户配置管理器 - 管理账户的部署配置
    不包含跟单关系，专注于账户本身的配置
    """
    
    def __init__(
        self,
        config_dir: str = None,
        enable_hot_reload: bool = True,
        enable_validation: bool = True
    ):
        if config_dir is None:
            config_dir = os.getenv('MT5_CONFIG_ROOT', 'config')
        
        self.config_dir = Path(config_dir)
        self.accounts_dir = self.config_dir / "accounts"
        self.enable_hot_reload = enable_hot_reload
        self.enable_validation = enable_validation
        
        self._configs: Dict[str, AccountConfig] = {}
        self._config_by_host: Dict[str, Set[str]] = {}  # host_id -> {account_ids}
        self._config_by_tag: Dict[str, Set[str]] = {}   # tag -> {account_ids}
        
        self._last_reload = 0
        self._reload_count = 0
        self._validation_errors: Dict[str, List[str]] = {}
        
        self._observer = None
        
        self._config_change_callbacks = []
        
        self.reload_all_configs()
        
        if self.enable_hot_reload:
            self._start_hot_reload()
    
    def reload_all_configs(self) -> Dict[str, List[str]]:
        """重新加载所有配置"""
        logger.info("=" * 60)
        logger.info("开始重新加载账户配置...")
        
        old_configs = self._configs.copy()
        self._configs.clear()
        self._config_by_host.clear()
        self._config_by_tag.clear()
        self._validation_errors.clear()
        
        self.accounts_dir.mkdir(parents=True, exist_ok=True)
        
        loaded_count = 0
        changed_accounts = []
        
        for config_file in self.accounts_dir.glob("*.yaml"):
            if config_file.name.startswith('account_base_template'):
                continue
                
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    preview_data = yaml.safe_load(f)
                account_data = preview_data.get('account', {})
                if not account_data.get('enabled', True):  # 默认启用
                    logger.debug(f"跳过禁用的账户配置: {config_file.name}")
                    continue
                
                account_config = self._load_account_config(config_file)
                
                if self.enable_validation:
                    errors = account_config.validate()
                    if errors:
                        self._validation_errors[account_config.account_id] = errors
                        logger.warning(
                            f"账户 {account_config.account_id} 配置验证失败: {errors}"
                        )
                
                old_config = old_configs.get(account_config.account_id)
                if old_config and old_config.config_hash != account_config.config_hash:
                    changed_accounts.append(account_config.account_id)
                
                self._add_to_cache(account_config)
                loaded_count += 1
                
                logger.info(
                    f"加载账户 {account_config.account_id} "
                    f"(主机: {account_config.deployment.host_id})"
                )
                
            except Exception as e:
                logger.error(f"加载配置文件失败 {config_file}: {e}")
        
        self._last_reload = time.time()
        self._reload_count += 1
        
        logger.info(f"配置加载完成: {loaded_count} 个账户")
        logger.info(f"验证错误: {len(self._validation_errors)} 个")
        if changed_accounts:
            logger.info(f"配置变化: {changed_accounts}")
        logger.info("=" * 60)
        
        if changed_accounts:
            self._notify_config_changes(changed_accounts)
        
        return self._validation_errors
    
    def _load_account_config(self, config_file: Path) -> AccountConfig:
        """加载单个账户配置"""
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
            content = self._expand_env_variables(content)
            data = yaml.safe_load(content)
        
        account_data = data.get('account', {})
        
        mt5_data = data.get('mt5', {})
        connection_data = mt5_data.get('connection', {})
        
        deployment_data = data.get('deployment', {})
        deployment = DeploymentConfig(
            host_id=deployment_data.get('host_id', ''),
            terminal_path=connection_data.get('terminal_path', deployment_data.get('terminal_path', '')),
            data_folder=deployment_data.get('data_folder'),
            process_priority=deployment_data.get('process_priority', 'normal'),
            restart_on_failure=deployment_data.get('restart_on_failure', True),
            max_restart_attempts=deployment_data.get('max_restart_attempts', 3),
            terminal_startup_timeout=deployment_data.get('terminal_startup_timeout', 60),
            resource_limits=deployment_data.get('resource_limits', {
                "max_memory_mb": 512,
                "max_cpu_percent": 50
            })
        )
        
        capabilities_data = account_data.get('capabilities', {})
        capabilities = AccountCapabilities(
            supported_symbols=capabilities_data.get('supported_symbols', []),
            max_positions=capabilities_data.get('max_positions', 10),
            max_lot_size=capabilities_data.get('max_lot_size', 1.0),
            min_lot_size=capabilities_data.get('min_lot_size', 0.01),
            allowed_operations=capabilities_data.get('allowed_operations', ["BUY", "SELL", "CLOSE"]),
            enable_monitoring=capabilities_data.get('enable_monitoring', True),
            enable_execution=capabilities_data.get('enable_execution', True)
        )
        
        trading_data = data.get('trading', {})
        trading_params = trading_data.get('parameters', {})
        volume_limits = trading_data.get('volume_limits', {})
        
        risk_data = data.get('risk_management', {})
        risk_limits = risk_data.get('limits', {})
        
        config = AccountConfig(
            account_id=account_data.get('id', config_file.stem),
            login=connection_data.get('login', 0),
            password=connection_data.get('password'),  # 添加密码字段
            server=connection_data.get('server', ''),
            description=account_data.get('description', ''),
            deployment=deployment,
            capabilities=capabilities,
            enabled=account_data.get('enabled', True),
            tags=data.get('metadata', {}).get('tags', []),
            metadata=data.get('metadata', {})
        )
        
        config.metadata.update({
            'magic_number': trading_params.get('magic_number', 12345),
            'max_volume': volume_limits.get('max_volume', 10.0),
            'min_volume': volume_limits.get('min_volume', 0.01),
            'max_daily_loss': risk_limits.get('max_daily_loss', 5000.0),
            'max_positions': risk_limits.get('max_positions', 10)
        })
        
        return config
    
    def _expand_env_variables(self, content: str) -> str:
        """展开环境变量 ${VAR_NAME} 或 ${VAR_NAME:-default}"""
        import re
        
        def replace_env_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.getenv(var_name.strip(), default_value.strip())
            else:
                var_name = var_expr.strip()
                value = os.getenv(var_name, '')
                if not value:
                    logger.warning(f"环境变量未设置: {var_name}")
                return value
        
        pattern = r'\$\{([^}]+)\}'
        expanded = re.sub(pattern, replace_env_var, content)
        
        return expanded
    
    def _add_to_cache(self, config: AccountConfig):
        """添加配置到缓存"""
        account_id = config.account_id
        self._configs[account_id] = config
        
        host_id = config.host_id
        if host_id not in self._config_by_host:
            self._config_by_host[host_id] = set()
        self._config_by_host[host_id].add(account_id)
        
        tags = getattr(config, 'tags', [])
        for tag in tags:
            if tag not in self._config_by_tag:
                self._config_by_tag[tag] = set()
            self._config_by_tag[tag].add(account_id)
    
    # ==================== 查询接口 ====================
    
    def get_account_config(self, account_id: str) -> Optional[AccountConfig]:
        """获取账户配置"""
        return self._configs.get(account_id)

    def load_all_accounts(self) -> Dict[str, AccountConfig]:
        """加载所有账户配置（仅返回启用的账户）"""
        return {k: v for k, v in self._configs.items() if v.enabled}
    
    def get_accounts_by_host(self, host_id: str) -> List[AccountConfig]:
        """获取指定主机的所有账户"""
        account_ids = self._config_by_host.get(host_id, set())
        return [self._configs[aid] for aid in account_ids if aid in self._configs]
    
    def get_accounts_by_tag(self, tag: str) -> List[AccountConfig]:
        """根据标签获取账户"""
        account_ids = self._config_by_tag.get(tag, set())
        return [self._configs[aid] for aid in account_ids if aid in self._configs]
    
    def get_enabled_accounts(self) -> List[AccountConfig]:
        """获取所有启用的账户"""
        return [cfg for cfg in self._configs.values() if cfg.enabled]
    
    def get_accounts_supporting_symbol(self, symbol: str) -> List[AccountConfig]:
        """获取支持指定品种的账户"""
        result = []
        for config in self._configs.values():
            if not config.capabilities.supported_symbols:  # 空列表表示支持所有
                result.append(config)
            elif symbol in config.capabilities.supported_symbols:
                result.append(config)
        return result
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'total_accounts': len(self._configs),
            'enabled_accounts': len(self.get_enabled_accounts()),
            'validation_errors': len(self._validation_errors),
            'last_reload': self._last_reload,
            'reload_count': self._reload_count,
            'accounts_by_host': {
                host_id: len(account_ids) 
                for host_id, account_ids in self._config_by_host.items()
            }
        }
    
    def validate_all_configs(self) -> Dict[str, List[str]]:
        """验证所有配置"""
        all_errors = {}
        for account_id, config in self._configs.items():
            errors = config.validate()
            if errors:
                all_errors[account_id] = errors
        return all_errors
    
    # ==================== 热重载支持 ====================
    
    def _start_hot_reload(self):
        """启动配置文件热重载"""
        try:
            self._observer = Observer()
            event_handler = ConfigChangeHandler(self)
            self._observer.schedule(
                event_handler, 
                str(self.accounts_dir), 
                recursive=False
            )
            self._observer.start()
            logger.info("配置文件热重载已启动")
        except Exception as e:
            logger.error(f"启动热重载失败: {e}")
    
    def _stop_hot_reload(self):
        """停止热重载"""
        if self._observer:
            self._observer.stop()
            self._observer.join()
            logger.info("配置文件热重载已停止")
    
    def add_config_change_callback(self, callback):
        """添加配置变化回调"""
        self._config_change_callbacks.append(callback)
    
    def _notify_config_changes(self, changed_accounts: List[str]):
        """通知配置变化"""
        for callback in self._config_change_callbacks:
            try:
                callback(changed_accounts)
            except Exception as e:
                logger.error(f"配置变化回调执行失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self._stop_hot_reload()