#!/usr/bin/env python3
"""
优化的消息路由器 - 解决阻塞问题，实现高性能异步路由
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Set, Callable, Tuple
from dataclasses import dataclass
from collections import defaultdict
import fnmatch
from concurrent.futures import ThreadPoolExecutor
import json

from .queue_manager import QueueManager, HybridQueueConfig, QueueBackendType
from .priority_queue import MessagePriority, PriorityAnalyzer
from .message_types import MessageEnvelope

from src.utils.logger import get_logger
from src.utils.metrics import get_metrics_collector

logger = get_logger(__name__)
metrics = get_metrics_collector()


@dataclass
class RouteRule:
    """路由规则"""
    source: str
    target: str
    host_id: str
    priority: MessagePriority
    route_type: str  # "local", "remote", "broadcast"
    enabled: bool = True
    retry_count: int = 3
    timeout_seconds: float = 30.0
    weight: int = 1
    
    def matches_pattern(self, source: str, target: str, host_id: str) -> bool:
        """检查规则是否匹配给定的模式"""
        return (
            fnmatch.fnmatch(source, self.source) and
            fnmatch.fnmatch(target, self.target) and
            fnmatch.fnmatch(host_id, self.host_id)
        )


@dataclass
class RateLimitConfig:
    """API速率限制配置"""
    max_calls_per_second: int = 80
    burst_size: int = 10
    window_size: int = 1  # 秒
    cooldown_period: int = 5  # 秒


class OptimizedMessageRouter:
    """
    优化的消息路由器 - 解决原有阻塞问题
    
    优化点：
    1. 异步初始化，避免启动阻塞
    2. 规则匹配缓存，减少重复计算
    3. 异步优先级分析
    4. 批量异步重试处理
    5. 非阻塞的队列管理器创建
    """
    
    def __init__(self, config: Dict[str, Any] = None, host_id: str = None):
        self.config = config or {}
        self.host_id = host_id or self.config.get('host_id', 'default-host')
        self.running = False
        
        # 队列管理器（异步创建）
        self.queue_manager: Optional[QueueManager] = None
        self._queue_manager_lock = asyncio.Lock()
        
        # 路由表
        self.local_routes: Dict[str, str] = {}
        self.cross_host_routes: Dict[str, str] = {}
        self.route_rules: List[RouteRule] = []
        
        # 规则缓存（优化）- 分层缓存设计
        self.rule_cache: Dict[str, Tuple[Optional[RouteRule], float, int]] = {}  # (rule, timestamp, access_count)
        self.rule_cache_ttl = 3600  # 1小时缓存（延长TTL）
        self.rule_cache_max_size = 10000  # 最大缓存条目
        self._cache_lock = asyncio.Lock()
        
        # 热点缓存（永不过期的高频规则）
        self.hot_cache: Dict[str, RouteRule] = {}
        self.hot_cache_threshold = 100  # 访问次数超过100次进入热点缓存
        
        # 优先级分析缓存（增强版）
        self.priority_cache: Dict[str, Tuple[MessagePriority, float, int]] = {}  # 增加访问计数
        self.priority_cache_ttl = 1800  # 30分钟缓存（延长TTL）
        self.priority_hot_cache: Dict[str, MessagePriority] = {}  # 热点优先级缓存
        
        # 预填充常用模式
        self._preload_common_patterns()
        
        # 线程池用于CPU密集型操作
        self._executor = ThreadPoolExecutor(max_workers=4)
        
        # API速率限制（使用异步锁）
        self.rate_limits: Dict[str, RateLimitConfig] = {}
        self.api_call_history: Dict[str, List[float]] = defaultdict(list)
        self.cooldown_accounts: Set[str] = set()
        self._rate_limit_lock = asyncio.Lock()
        
        # 批量重试管理
        self.retry_batch: List[Tuple[Dict[str, Any], RouteRule, int, float]] = []
        self.retry_batch_size = 100
        self._retry_lock = asyncio.Lock()
        
        # 消息处理器
        self.message_handlers: Dict[str, Callable] = {}
        
        # 性能统计
        self.stats = {
            'local_routes': 0,
            'remote_routes': 0,
            'broadcast_routes': 0,
            'rate_limited_calls': 0,
            'failed_routes': 0,
            'total_messages': 0,
            'successful_routes': 0,
            'retries_attempted': 0,
            'avg_route_time_ms': 0.0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # 后台任务
        self._background_tasks: List[asyncio.Task] = []
        
        logger.info(f"✅ 优化消息路由器初始化完成 - 主机: {self.host_id}")
    
    def _preload_common_patterns(self):
        """预填充常用模式到热点缓存"""
        # 常用的路由模式
        common_patterns = [
            ('monitor', 'executor', self.host_id),
            ('monitor', '*', self.host_id), 
            ('coordinator', '*', self.host_id),
            ('*', 'executor', self.host_id),
            ('signal_processor', 'executor', self.host_id),
            ('trade_engine', 'executor', self.host_id),
        ]
        
        # 预填充到热点缓存（这些模式永不过期）
        for source, target, host in common_patterns:
            cache_key = f"{source}:{target}:{host}"
            # 创建虚拟规则用于热点缓存
            hot_rule = RouteRule(
                source=source,
                target=target,
                host_id=host,
                priority=MessagePriority.SIGNAL_EXECUTION,
                route_type="local" if host == self.host_id else "remote",
                enabled=True
            )
            self.hot_cache[cache_key] = hot_rule
        
        # 常用优先级模式
        common_priorities = [
            ('place_order', 'buy'),
            ('place_order', 'sell'),
            ('close_order', 'market'),
            ('modify_order', 'sl_tp'),
            ('market_data', 'tick'),
            ('account_info', 'balance'),
        ]
        
        for cmd_type, action in common_priorities:
            cache_key = f"{cmd_type}:{action}"
            self.priority_hot_cache[cache_key] = MessagePriority.SIGNAL_EXECUTION
        
        logger.info(f"预填充缓存: {len(common_patterns)}个路由规则, {len(common_priorities)}个优先级模式")
    
    async def _get_or_create_queue_manager(self) -> QueueManager:
        """异步获取或创建队列管理器"""
        if self.queue_manager:
            return self.queue_manager
        
        async with self._queue_manager_lock:
            if self.queue_manager:  # 双重检查
                return self.queue_manager
            
            try:
                # 异步创建队列管理器
                config_data = self.config.get('queue_manager', {})
                
                hybrid_config = HybridQueueConfig(
                    primary_backend=QueueBackendType(
                        config_data.get('primary_backend', 'nats')
                    ),
                    backup_backends=[
                        QueueBackendType(backend) 
                        for backend in config_data.get('backup_backends', ['redis_streams'])
                    ],
                    local_fallback=config_data.get('local_fallback', True),
                    enable_dual_write=config_data.get('enable_dual_write', True),
                    dual_write_async=True,  # 强制异步双写
                    ignore_backup_failures=True,
                    
                    # 故障转移配置
                    health_check_interval=config_data.get('health_check_interval', 5.0),
                    failure_threshold=config_data.get('failure_threshold', 3),
                    circuit_breaker_timeout=config_data.get('circuit_breaker_timeout', 30.0),
                    
                    # 性能配置
                    max_latency_ms=config_data.get('max_latency_ms', 100.0),
                    max_error_rate=config_data.get('max_error_rate', 0.05),
                    
                    # 后端配置
                    backend_configs=self._build_backend_configs(config_data)
                )
                
                self.queue_manager = QueueManager(hybrid_config)
                
                # 异步初始化
                if not await self.queue_manager.initialize():
                    logger.warning("队列管理器初始化失败，使用本地降级模式")
                    # 创建本地降级队列管理器
                    fallback_config = HybridQueueConfig(
                        primary_backend=QueueBackendType.LOCAL_MEMORY,
                        backup_backends=[],
                        local_fallback=True
                    )
                    self.queue_manager = QueueManager(fallback_config)
                    await self.queue_manager.initialize()
                
                return self.queue_manager
                
            except Exception as e:
                logger.error(f"创建队列管理器失败: {e}")
                # 返回本地降级版本
                fallback_config = HybridQueueConfig(
                    primary_backend=QueueBackendType.LOCAL_MEMORY,
                    backup_backends=[],
                    local_fallback=True
                )
                self.queue_manager = QueueManager(fallback_config)
                await self.queue_manager.initialize()
                return self.queue_manager
    
    def _build_backend_configs(self, config_data: Dict) -> Dict:
        """构建后端配置"""
        return {
            QueueBackendType.NATS: {
                'nats': {
                    'host_id': self.host_id,
                    'servers': config_data.get('nats_servers', ['nats://localhost:4222']),
                    'user': config_data.get('nats_user'),
                    'password': config_data.get('nats_password')
                }
            },
            QueueBackendType.REDIS_STREAMS: {
                'redis': {
                    'host': config_data.get('redis_host', 'localhost'),
                    'port': config_data.get('redis_port', 6379),
                    'db': config_data.get('redis_db', 0),
                    'password': config_data.get('redis_password'),
                    'consumer_group': f'mt5-workers-{self.host_id}'
                }
            }
        }
    
    async def _load_routing_config_async(self):
        """异步加载路由配置"""
        try:
            # 在后台线程中加载配置，避免阻塞
            await asyncio.get_event_loop().run_in_executor(
                self._executor,
                self._load_routing_config_sync
            )
        except Exception as e:
            logger.warning(f"加载路由配置失败: {e}")
            self._initialize_default_routes()
    
    def _load_routing_config_sync(self):
        """同步加载路由配置（在线程池中执行）"""
        try:
            from src.core.config_manager import get_config_manager
            config_manager = get_config_manager()
            
            routing_config = config_manager.get('messaging.routing', {})
            rules_config = routing_config.get('rules', [])
            
            for rule_data in rules_config:
                try:
                    rule = RouteRule(
                        source=rule_data.get('source', '*'),
                        target=rule_data.get('target', '*'),
                        host_id=rule_data.get('host_id', self.host_id),
                        priority=MessagePriority[rule_data.get('priority', 'REALTIME_QUERY')],
                        route_type=rule_data.get('route_type', 'local'),
                        enabled=rule_data.get('enabled', True),
                        retry_count=rule_data.get('retry_count', 3),
                        timeout_seconds=rule_data.get('timeout_seconds', 30.0),
                        weight=rule_data.get('weight', 1)
                    )
                    self.route_rules.append(rule)
                except Exception as e:
                    logger.warning(f"解析路由规则失败: {rule_data}, 错误: {e}")
            
            logger.info(f"加载配置驱动路由规则: {len(self.route_rules)}条")
            
        except Exception as e:
            logger.warning(f"加载路由配置失败: {e}")
    
    def _initialize_default_routes(self):
        """初始化默认路由规则"""
        default_rules = [
            RouteRule(
                source="monitor",
                target="executor", 
                host_id=self.host_id,
                priority=MessagePriority.RISK_COMMAND,
                route_type="local",
                retry_count=2,
                timeout_seconds=10.0
            ),
            RouteRule(
                source="*",
                target="*",
                host_id="*",
                priority=MessagePriority.SIGNAL_EXECUTION,
                route_type="remote",
                retry_count=3,
                timeout_seconds=30.0
            ),
            RouteRule(
                source="coordinator",
                target="*",
                host_id="*",
                priority=MessagePriority.SYSTEM_CRITICAL,
                route_type="broadcast",
                retry_count=1,
                timeout_seconds=15.0
            )
        ]
        
        self.route_rules.extend(default_rules)
        logger.info(f"默认路由规则已加载: {len(default_rules)} 条")
    
    async def start(self) -> bool:
        """异步启动路由器"""
        try:
            self.running = True
            
            # 1. 异步加载配置（非阻塞）
            config_task = asyncio.create_task(self._load_routing_config_async())
            
            # 2. 异步创建队列管理器（非阻塞）
            queue_task = asyncio.create_task(self._get_or_create_queue_manager())
            
            # 3. 启动后台任务
            self._background_tasks = [
                asyncio.create_task(self._cache_cleaner_task()),
                asyncio.create_task(self._batch_retry_processor_task()),
                asyncio.create_task(self._rate_limit_cleaner_task())
            ]
            
            # 4. 等待关键组件（带超时）
            try:
                await asyncio.wait_for(
                    asyncio.gather(config_task, queue_task),
                    timeout=10.0
                )
            except asyncio.TimeoutError:
                logger.warning("部分组件初始化超时，使用默认配置继续")
                if not self.route_rules:
                    self._initialize_default_routes()
            
            logger.info("🚀 优化消息路由器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动优化路由器失败: {e}")
            return False
    
    async def route_signal(self, signal_data: Dict[str, Any]) -> bool:
        """优化的信号路由 - 异步非阻塞"""
        start_time = time.perf_counter()
        
        try:
            # 更新统计（原子操作）
            self.stats['total_messages'] += 1
            
            # 生成消息ID
            message_id = signal_data.get('message_id', f"msg_{int(time.time() * 1000000)}")
            
            # 解析路由信息
            target_host = signal_data.get('target_host_id', self.host_id)
            target_account = signal_data.get('target_account', 'unknown') 
            source_type = signal_data.get('source_type', 'unknown')
            
            # 异步查找匹配的路由规则（使用缓存）
            matching_rule = await self._find_matching_rule_async(source_type, target_account, target_host)
            
            if not matching_rule:
                logger.warning(f"未找到匹配的路由规则: {source_type} -> {target_account}@{target_host}")
                self.stats['failed_routes'] += 1
                return False
            
            # 异步检查速率限制
            if target_account and not await self._check_rate_limit_async(target_account):
                logger.warning(f"速率限制触发: {target_account}")
                return await self._handle_rate_limited_message(signal_data, matching_rule.priority)
            
            # 异步执行路由
            success = await self._execute_route_async(signal_data, matching_rule)
            
            if success:
                self.stats['successful_routes'] += 1
                route_time = (time.perf_counter() - start_time) * 1000
                self._update_avg_route_time(route_time)
                
                # 异步记录指标
                asyncio.create_task(self._record_metrics_async(route_time, matching_rule.route_type))
            else:
                self.stats['failed_routes'] += 1
                
                # 异步安排重试
                if matching_rule.retry_count > 0:
                    asyncio.create_task(self._schedule_retry_async(signal_data, matching_rule, 1))
            
            return success
            
        except Exception as e:
            logger.error(f"信号路由失败: {e}")
            self.stats['failed_routes'] += 1
            return False
    
    async def _find_matching_rule_async(self, source: str, target: str, host_id: str) -> Optional[RouteRule]:
        """异步查找匹配的路由规则（多层缓存）"""
        cache_key = f"{source}:{target}:{host_id}"
        current_time = time.time()
        
        # 1. 检查热点缓存（最快路径，无锁）
        if cache_key in self.hot_cache:
            self.stats['cache_hits'] += 1
            return self.hot_cache[cache_key]
        
        # 2. 检查普通缓存
        async with self._cache_lock:
            if cache_key in self.rule_cache:
                cached_rule, timestamp, access_count = self.rule_cache[cache_key]
                if current_time - timestamp < self.rule_cache_ttl:
                    # 更新访问计数
                    new_access_count = access_count + 1
                    self.rule_cache[cache_key] = (cached_rule, timestamp, new_access_count)
                    
                    # 如果访问频繁，升级到热点缓存
                    if new_access_count >= self.hot_cache_threshold and cached_rule:
                        self.hot_cache[cache_key] = cached_rule
                        logger.debug(f"规则升级到热点缓存: {cache_key}")
                    
                    self.stats['cache_hits'] += 1
                    return cached_rule if cached_rule and cached_rule.enabled else None
                else:
                    del self.rule_cache[cache_key]
        
        self.stats['cache_misses'] += 1
        
        # 3. 在线程池中执行模式匹配（CPU密集型）
        matching_rule = await asyncio.get_event_loop().run_in_executor(
            self._executor,
            self._find_matching_rule_sync,
            source, target, host_id
        )
        
        # 4. 更新缓存（带大小限制）
        async with self._cache_lock:
            # 如果缓存已满，清理最少使用的条目
            if len(self.rule_cache) >= self.rule_cache_max_size:
                self._evict_lru_cache_entries()
            
            self.rule_cache[cache_key] = (matching_rule, current_time, 1)
        
        return matching_rule
    
    def _evict_lru_cache_entries(self):
        """清理最少使用的缓存条目"""
        # 按访问次数排序，清理最少使用的25%
        sorted_items = sorted(
            self.rule_cache.items(), 
            key=lambda x: x[1][2]  # 按访问次数排序
        )
        
        evict_count = len(sorted_items) // 4
        for key, _ in sorted_items[:evict_count]:
            del self.rule_cache[key]
        
        logger.debug(f"清理LRU缓存条目: {evict_count}个")
    
    def _find_matching_rule_sync(self, source: str, target: str, host_id: str) -> Optional[RouteRule]:
        """同步查找匹配规则（在线程池中执行）"""
        for rule in self.route_rules:
            if rule.enabled and rule.matches_pattern(source, target, host_id):
                return rule
        return None
    
    async def _analyze_message_priority_async(self, signal_data: Dict[str, Any]) -> MessagePriority:
        """异步分析消息优先级（多层缓存）"""
        # 生成更精确的缓存键
        cmd_type = signal_data.get('command_type', 'unknown')
        action = signal_data.get('action', 'unknown')
        symbol = signal_data.get('symbol', '')
        cache_key = f"{cmd_type}:{action}:{symbol}" if symbol else f"{cmd_type}:{action}"
        current_time = time.time()
        
        # 1. 检查热点优先级缓存（最快路径）
        if cache_key in self.priority_hot_cache:
            return self.priority_hot_cache[cache_key]
        
        # 2. 检查普通优先级缓存
        if cache_key in self.priority_cache:
            priority, timestamp, access_count = self.priority_cache[cache_key]
            if current_time - timestamp < self.priority_cache_ttl:
                # 更新访问计数
                new_access_count = access_count + 1
                self.priority_cache[cache_key] = (priority, timestamp, new_access_count)
                
                # 升级到热点缓存
                if new_access_count >= self.hot_cache_threshold:
                    self.priority_hot_cache[cache_key] = priority
                    logger.debug(f"优先级升级到热点缓存: {cache_key}")
                
                return priority
        
        # 3. 分析优先级
        method = signal_data.get('command_type', 'route_message')
        priority = PriorityAnalyzer.get_message_priority(method, signal_data)
        
        # 4. 更新缓存
        self.priority_cache[cache_key] = (priority, current_time, 1)
        
        return priority
    
    async def _execute_route_async(self, signal_data: Dict[str, Any], rule: RouteRule) -> bool:
        """异步执行路由"""
        # 确保队列管理器可用
        queue_manager = await self._get_or_create_queue_manager()
        
        # 根据路由类型执行
        if rule.route_type == "local":
            return await self._route_local_async(signal_data, rule, queue_manager)
        elif rule.route_type == "remote":
            return await self._route_remote_async(signal_data, rule, queue_manager)
        elif rule.route_type == "broadcast":
            return await self._route_broadcast_async(signal_data, rule, queue_manager)
        else:
            logger.error(f"未支持的路由类型: {rule.route_type}")
            return False
    
    async def _route_local_async(self, signal_data: Dict[str, Any], rule: RouteRule, 
                                 queue_manager: QueueManager) -> bool:
        """异步本地路由"""
        try:
            # 异步分析优先级
            priority = rule.priority
            if priority == MessagePriority.REALTIME_QUERY:
                priority = await self._analyze_message_priority_async(signal_data)
            
            # 创建消息信封
            message = MessageEnvelope(
                id=signal_data.get('message_id', f"local_{int(time.time() * 1000000)}"),
                subject=signal_data.get('subject', 'local.route'),
                payload=signal_data,
                timestamp=time.time()
            )
            
            # 异步发布
            success = await queue_manager.publish(
                subject=message.subject,
                message=message,
                priority=priority
            )
            
            if success:
                self.stats['local_routes'] += 1
            
            return success
            
        except Exception as e:
            logger.error(f"本地路由失败: {e}")
            return False
    
    async def _route_remote_async(self, signal_data: Dict[str, Any], rule: RouteRule,
                                  queue_manager: QueueManager) -> bool:
        """异步远程路由"""
        try:
            # 设置目标主机信息
            enhanced_data = signal_data.copy()
            enhanced_data['source_host_id'] = self.host_id
            enhanced_data['target_host_id'] = signal_data.get('target_host_id', 'remote')
            
            # 创建消息信封
            message = MessageEnvelope(
                id=enhanced_data.get('message_id', f"remote_{int(time.time() * 1000000)}"),
                subject=enhanced_data.get('subject', 'remote.route'),
                payload=enhanced_data,
                timestamp=time.time()
            )
            
            # 异步发布（带超时）
            try:
                success = await asyncio.wait_for(
                    queue_manager.publish(
                        subject=message.subject,
                        message=message,
                        priority=rule.priority
                    ),
                    timeout=rule.timeout_seconds
                )
                
                if success:
                    self.stats['remote_routes'] += 1
                
                return success
                
            except asyncio.TimeoutError:
                logger.warning(f"远程路由超时")
                return False
                
        except Exception as e:
            logger.error(f"远程路由失败: {e}")
            return False
    
    async def _route_broadcast_async(self, signal_data: Dict[str, Any], rule: RouteRule,
                                     queue_manager: QueueManager) -> bool:
        """异步广播路由"""
        # 创建副本
        local_data = signal_data.copy()
        remote_data = signal_data.copy()
        remote_data['broadcast'] = True
        
        # 并行执行
        local_task = asyncio.create_task(self._route_local_async(local_data, rule, queue_manager))
        remote_task = asyncio.create_task(self._route_remote_async(remote_data, rule, queue_manager))
        
        # 等待完成
        results = await asyncio.gather(local_task, remote_task, return_exceptions=True)
        
        # 只要有一个成功就认为广播成功
        success = any(result is True for result in results if not isinstance(result, Exception))
        
        if success:
            self.stats['broadcast_routes'] += 1
        
        return success
    
    async def _check_rate_limit_async(self, account_id: str) -> bool:
        """异步检查速率限制"""
        async with self._rate_limit_lock:
            if account_id in self.cooldown_accounts:
                return False
            
            current_time = time.time()
            
            # 获取或创建配置
            if account_id not in self.rate_limits:
                self.rate_limits[account_id] = RateLimitConfig()
            
            config = self.rate_limits[account_id]
            call_history = self.api_call_history[account_id]
            
            # 清理过期记录
            window_start = current_time - config.window_size
            call_history[:] = [t for t in call_history if t >= window_start]
            
            # 检查限制
            if len(call_history) >= config.max_calls_per_second:
                self.stats['rate_limited_calls'] += 1
                self.cooldown_accounts.add(account_id)
                
                # 异步安排冷却结束
                asyncio.create_task(self._remove_from_cooldown_async(account_id, config.cooldown_period))
                
                return False
            
            # 记录调用
            call_history.append(current_time)
            return True
    
    async def _remove_from_cooldown_async(self, account_id: str, delay: int):
        """异步移除冷却"""
        await asyncio.sleep(delay)
        async with self._rate_limit_lock:
            self.cooldown_accounts.discard(account_id)
    
    async def _schedule_retry_async(self, signal_data: Dict[str, Any], rule: RouteRule, attempt: int):
        """异步安排重试（批量处理）"""
        if attempt > rule.retry_count:
            return
        
        delay = 1.0 * (2 ** (attempt - 1))  # 指数退避
        retry_time = time.time() + delay
        
        async with self._retry_lock:
            self.retry_batch.append((signal_data, rule, attempt, retry_time))
            
            # 如果批次满了，立即处理
            if len(self.retry_batch) >= self.retry_batch_size:
                asyncio.create_task(self._process_retry_batch())
    
    async def _batch_retry_processor_task(self):
        """批量重试处理任务"""
        while self.running:
            try:
                await asyncio.sleep(1)  # 每秒检查
                
                current_time = time.time()
                
                async with self._retry_lock:
                    # 筛选需要重试的项
                    ready_retries = [
                        item for item in self.retry_batch
                        if item[3] <= current_time
                    ]
                    
                    # 保留未到期的项
                    self.retry_batch[:] = [
                        item for item in self.retry_batch
                        if item[3] > current_time
                    ]
                
                # 批量处理重试
                if ready_retries:
                    await self._process_retries_batch(ready_retries)
                    
            except Exception as e:
                logger.error(f"批量重试处理异常: {e}")
    
    async def _process_retries_batch(self, retries: List[Tuple[Dict, RouteRule, int, float]]):
        """批量处理重试"""
        # 创建并发任务
        tasks = []
        for signal_data, rule, attempt, _ in retries:
            task = asyncio.create_task(self._retry_single(signal_data, rule, attempt))
            tasks.append(task)
        
        # 并发执行（限制并发数）
        for i in range(0, len(tasks), 10):  # 每批10个
            batch = tasks[i:i+10]
            await asyncio.gather(*batch, return_exceptions=True)
    
    async def _retry_single(self, signal_data: Dict[str, Any], rule: RouteRule, attempt: int):
        """单个重试"""
        logger.debug(f"执行重试: 尝试 {attempt}")
        success = await self.route_signal(signal_data)
        
        if not success and attempt < rule.retry_count:
            await self._schedule_retry_async(signal_data, rule, attempt + 1)
        
        self.stats['retries_attempted'] += 1
    
    async def _cache_cleaner_task(self):
        """缓存清理任务"""
        while self.running:
            try:
                await asyncio.sleep(60)  # 每分钟清理
                
                current_time = time.time()
                
                # 清理规则缓存（保留热点缓存）
                async with self._cache_lock:
                    expired_keys = [
                        key for key, (_, timestamp, _) in self.rule_cache.items()
                        if current_time - timestamp > self.rule_cache_ttl
                    ]
                    for key in expired_keys:
                        del self.rule_cache[key]
                
                # 清理优先级缓存（保留热点缓存）
                expired_keys = [
                    key for key, (_, timestamp, _) in self.priority_cache.items()
                    if current_time - timestamp > self.priority_cache_ttl
                ]
                for key in expired_keys:
                    del self.priority_cache[key]
                
                # 统计缓存状态
                cache_stats = {
                    'rule_cache_size': len(self.rule_cache),
                    'hot_cache_size': len(self.hot_cache),
                    'priority_cache_size': len(self.priority_cache),
                    'priority_hot_cache_size': len(self.priority_hot_cache)
                }
                logger.debug(f"缓存状态: {cache_stats}")
                    
            except Exception as e:
                logger.error(f"缓存清理异常: {e}")
    
    async def _rate_limit_cleaner_task(self):
        """速率限制清理任务"""
        while self.running:
            try:
                await asyncio.sleep(60)  # 每分钟清理
                
                current_time = time.time()
                
                async with self._rate_limit_lock:
                    for account_id in list(self.api_call_history.keys()):
                        call_history = self.api_call_history[account_id]
                        
                        # 清理5分钟前的记录
                        cutoff_time = current_time - 300
                        call_history[:] = [t for t in call_history if t >= cutoff_time]
                        
                        # 删除空记录
                        if not call_history:
                            del self.api_call_history[account_id]
                            
            except Exception as e:
                logger.error(f"速率限制清理异常: {e}")
    
    async def _handle_rate_limited_message(self, signal_data: Dict[str, Any], 
                                         priority: MessagePriority) -> bool:
        """处理被速率限制的消息"""
        try:
            # 获取队列管理器
            queue_manager = await self._get_or_create_queue_manager()
            
            # 降级处理
            signal_data['rate_limited'] = True
            signal_data['delayed'] = True
            
            # 创建消息信封
            message = MessageEnvelope(
                id=signal_data.get('message_id', f"limited_{int(time.time() * 1000000)}"),
                subject='rate_limited',
                payload=signal_data,
                timestamp=time.time()
            )
            
            # 使用低优先级发布
            return await queue_manager.publish(
                subject=message.subject,
                message=message,
                priority=MessagePriority.BACKGROUND_TASK
            )
            
        except Exception as e:
            logger.error(f"处理速率限制消息失败: {e}")
            return False
    
    def _update_avg_route_time(self, route_time: float):
        """更新平均路由时间"""
        current_avg = self.stats['avg_route_time_ms']
        total_messages = self.stats['successful_routes']
        
        if total_messages > 0:
            self.stats['avg_route_time_ms'] = (
                (current_avg * (total_messages - 1) + route_time) / total_messages
            )
    
    async def _record_metrics_async(self, route_time: float, route_type: str):
        """异步记录指标"""
        try:
            metrics.record("route_time_ms", route_time, {
                'route_type': route_type,
                'host': self.host_id
            })
        except Exception as e:
            logger.debug(f"记录指标失败: {e}")
    
    async def stop(self):
        """停止路由器"""
        if not self.running:
            return
        
        try:
            self.running = False
            
            # 取消后台任务
            for task in self._background_tasks:
                task.cancel()
            
            if self._background_tasks:
                await asyncio.gather(*self._background_tasks, return_exceptions=True)
            
            # 关闭队列管理器
            if self.queue_manager:
                await self.queue_manager.shutdown()
            
            # 关闭线程池
            self._executor.shutdown(wait=False)
            
            # 清理资源
            self.route_rules.clear()
            self.rule_cache.clear()
            self.priority_cache.clear()
            self.api_call_history.clear()
            self.retry_batch.clear()
            
            logger.info("🛑 优化消息路由器已停止")
            
        except Exception as e:
            logger.error(f"停止优化路由器失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取路由器状态"""
        queue_status = {}
        if self.queue_manager:
            queue_status = self.queue_manager.get_status()
        
        return {
            'running': self.running,
            'host_id': self.host_id,
            'routes': {
                'route_rules': len(self.route_rules),
                'rule_cache_size': len(self.rule_cache),
                'cache_hit_rate': self._calculate_cache_hit_rate()
            },
            'rate_limiting': {
                'tracked_accounts': len(self.api_call_history),
                'cooldown_accounts': len(self.cooldown_accounts),
                'rate_limited_calls': self.stats['rate_limited_calls']
            },
            'retry_management': {
                'pending_retries': len(self.retry_batch),
                'retries_attempted': self.stats['retries_attempted']
            },
            'performance': {
                'total_messages': self.stats['total_messages'],
                'successful_routes': self.stats['successful_routes'],
                'failed_routes': self.stats['failed_routes'],
                'avg_route_time_ms': self.stats['avg_route_time_ms'],
                'cache_hits': self.stats['cache_hits'],
                'cache_misses': self.stats['cache_misses']
            },
            'queue_manager': queue_status
        }
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        total = self.stats['cache_hits'] + self.stats['cache_misses']
        if total == 0:
            return 0.0
        return (self.stats['cache_hits'] / total) * 100.0


# 导出优化的路由器作为默认实现
MessageRouter = OptimizedMessageRouter