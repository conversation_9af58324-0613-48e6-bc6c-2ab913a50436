#!/usr/bin/env python3
"""
MT5连接诊断脚本 - 详细检查连接问题
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv
import yaml

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)

def check_mt5_installation():
    """检查MT5安装"""
    logger.info("🔍 检查MT5安装...")
    
    try:
        import MetaTrader5 as mt5
        logger.info("✅ MetaTrader5模块导入成功")
        
        # 检查MT5版本
        if hasattr(mt5, 'version'):
            version = mt5.version()
            logger.info(f"MT5版本: {version}")
        
        return True
    except ImportError as e:
        logger.error(f"❌ MetaTrader5模块导入失败: {e}")
        return False

def check_environment_variables():
    """检查环境变量"""
    logger.info("🔍 检查环境变量...")
    
    # 加载.env文件
    load_dotenv()
    
    required_vars = ['MT5_ACC001_PASSWORD', 'MT5_ACC002_PASSWORD']
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"✅ {var}: {'*' * len(value)}")
        else:
            logger.error(f"❌ {var}: 未设置")
    
    return all(os.getenv(var) for var in required_vars)

def check_account_configs():
    """检查账户配置"""
    logger.info("🔍 检查账户配置...")
    
    accounts = ['ACC001', 'ACC002']
    
    for account_id in accounts:
        config_path = f"config/accounts/{account_id}.yaml"
        logger.info(f"检查配置文件: {config_path}")
        
        if not Path(config_path).exists():
            logger.error(f"❌ 配置文件不存在: {config_path}")
            continue
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            mt5_config = config.get('mt5', {}).get('connection', {})
            
            login = mt5_config.get('login')
            server = mt5_config.get('server')
            terminal_path = mt5_config.get('terminal_path')
            
            logger.info(f"  {account_id}:")
            logger.info(f"    登录号: {login}")
            logger.info(f"    服务器: {server}")
            logger.info(f"    终端路径: {terminal_path}")
            
            # 检查终端路径是否存在
            if terminal_path and Path(terminal_path).exists():
                logger.info(f"    ✅ 终端路径存在")
            else:
                logger.warning(f"    ⚠️ 终端路径不存在或未配置")
            
        except Exception as e:
            logger.error(f"❌ 读取配置文件失败: {e}")

def test_basic_mt5_connection():
    """测试基本MT5连接"""
    logger.info("🔍 测试基本MT5连接...")
    
    try:
        import MetaTrader5 as mt5
        
        # 尝试初始化MT5
        logger.info("尝试初始化MT5...")
        if not mt5.initialize():
            error = mt5.last_error()
            logger.error(f"❌ MT5初始化失败: {error}")
            return False
        
        logger.info("✅ MT5初始化成功")
        
        # 获取终端信息
        terminal_info = mt5.terminal_info()
        if terminal_info:
            logger.info(f"终端信息:")
            logger.info(f"  路径: {terminal_info.path}")
            logger.info(f"  数据路径: {terminal_info.data_path}")
            logger.info(f"  公司: {terminal_info.company}")
            logger.info(f"  连接状态: {terminal_info.connected}")
        
        # 尝试登录测试账户
        load_dotenv()
        
        # 测试ACC001
        login1 = ********
        password1 = os.getenv('MT5_ACC001_PASSWORD')
        server1 = 'TradeMaxGlobal-Demo'
        
        if password1:
            logger.info(f"尝试登录账户: {login1}@{server1}")
            if mt5.login(login1, password1, server1):
                logger.info("✅ ACC001登录成功")
                
                # 获取账户信息
                account_info = mt5.account_info()
                if account_info:
                    logger.info(f"账户信息:")
                    logger.info(f"  余额: {account_info.balance}")
                    logger.info(f"  权益: {account_info.equity}")
                    logger.info(f"  保证金: {account_info.margin}")
                    logger.info(f"  服务器: {account_info.server}")
                
                mt5.logout()
            else:
                error = mt5.last_error()
                logger.error(f"❌ ACC001登录失败: {error}")
        
        # 测试ACC002
        login2 = ********
        password2 = os.getenv('MT5_ACC002_PASSWORD')
        server2 = 'TradeMaxGlobal-Demo'
        
        if password2:
            logger.info(f"尝试登录账户: {login2}@{server2}")
            if mt5.login(login2, password2, server2):
                logger.info("✅ ACC002登录成功")
                
                # 获取账户信息
                account_info = mt5.account_info()
                if account_info:
                    logger.info(f"账户信息:")
                    logger.info(f"  余额: {account_info.balance}")
                    logger.info(f"  权益: {account_info.equity}")
                    logger.info(f"  保证金: {account_info.margin}")
                    logger.info(f"  服务器: {account_info.server}")
                
                mt5.logout()
            else:
                error = mt5.last_error()
                logger.error(f"❌ ACC002登录失败: {error}")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        logger.error(f"❌ MT5连接测试异常: {e}")
        return False

def check_mt5_processes():
    """检查MT5进程"""
    logger.info("🔍 检查MT5进程...")
    
    try:
        import psutil
        
        mt5_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if 'terminal64' in proc.info['name'].lower() or 'metatrader' in proc.info['name'].lower():
                    mt5_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if mt5_processes:
            logger.info(f"找到 {len(mt5_processes)} 个MT5进程:")
            for proc in mt5_processes:
                logger.info(f"  PID: {proc['pid']}, 名称: {proc['name']}, 路径: {proc.get('exe', 'N/A')}")
        else:
            logger.warning("⚠️ 未找到MT5进程")
        
        return len(mt5_processes) > 0
        
    except ImportError:
        logger.warning("⚠️ psutil未安装，无法检查进程")
        return None
    except Exception as e:
        logger.error(f"❌ 检查进程时出错: {e}")
        return False

def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard'
    })
    
    logger.info("=" * 60)
    logger.info("🔧 MT5连接诊断")
    logger.info("=" * 60)
    
    # 运行诊断检查
    checks = [
        ("MT5安装检查", check_mt5_installation),
        ("环境变量检查", check_environment_variables),
        ("账户配置检查", check_account_configs),
        ("MT5进程检查", check_mt5_processes),
        ("基本连接测试", test_basic_mt5_connection)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        logger.info(f"运行检查: {check_name}")
        try:
            result = check_func()
            results[check_name] = result
            if result:
                logger.info(f"✅ {check_name}: 通过")
            elif result is False:
                logger.error(f"❌ {check_name}: 失败")
            else:
                logger.warning(f"⚠️ {check_name}: 跳过")
        except Exception as e:
            logger.error(f"💥 {check_name}: 异常 - {e}")
            results[check_name] = False
        
        logger.info("-" * 40)
    
    # 总结
    logger.info("=" * 60)
    logger.info("📊 诊断结果总结")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result is True)
    total = len([r for r in results.values() if r is not None])
    
    for check_name, result in results.items():
        if result is True:
            status = "✅ 通过"
        elif result is False:
            status = "❌ 失败"
        else:
            status = "⚠️ 跳过"
        logger.info(f"  {check_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 个检查通过")
    
    if passed == total:
        logger.info("🎉 所有检查通过！MT5连接应该正常")
    else:
        logger.error("💥 部分检查失败，需要解决问题后重试")

if __name__ == "__main__":
    main()
