#!/usr/bin/env python3
"""
简化的系统测试脚本
用于验证系统基本功能
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)


async def test_basic_imports():
    """测试基本导入"""
    logger.info("🧪 测试基本导入...")
    
    try:
        from src.core.trading_system import TradingSystem
        logger.info("✅ TradingSystem 导入成功")
        
        from src.core.signal_types import UnifiedTradeSignal
        logger.info("✅ UnifiedTradeSignal 导入成功")
        
        from src.messaging.jetstream_client import JetStreamClient
        logger.info("✅ JetStreamClient 导入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 导入失败: {e}")
        return False


async def test_jetstream_connection():
    """测试 JetStream 连接"""
    logger.info("🧪 测试 JetStream 连接...")
    
    try:
        from src.messaging.jetstream_client import JetStreamClient
        
        # 创建客户端
        client = JetStreamClient(['nats://localhost:4222'])
        
        # 尝试连接
        success = await client.connect()
        
        if success:
            logger.info("✅ JetStream 连接成功")
            
            # 获取流信息
            stream_info = await client.get_stream_info()
            if stream_info:
                logger.info(f"✅ 流信息: {stream_info['name']}, 消息数: {stream_info['messages']}")
            
            # 断开连接
            await client.disconnect()
            return True
        else:
            logger.error("❌ JetStream 连接失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ JetStream 连接异常: {e}")
        return False


async def test_system_creation():
    """测试系统创建"""
    logger.info("🧪 测试系统创建...")
    
    try:
        from src.core.trading_system import TradingSystem
        
        # 创建系统
        system = TradingSystem(
            config_path='config/optimized_system.yaml',
            host_id='TEST-001',
            dry_run=True
        )
        
        logger.info("✅ 系统创建成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 系统创建失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def test_system_startup():
    """测试系统启动"""
    logger.info("🧪 测试系统启动...")
    
    try:
        from src.core.trading_system import TradingSystem
        
        # 创建系统
        system = TradingSystem(
            config_path='config/optimized_system.yaml',
            host_id='TEST-001',
            dry_run=True
        )
        
        # 启动系统
        success = await system.start()
        
        if success:
            logger.info("✅ 系统启动成功")
            
            # 检查系统状态
            status = system.get_system_status()
            logger.info(f"系统状态: {status}")
            
            # 停止系统
            await system.stop()
            logger.info("✅ 系统停止成功")
            return True
        else:
            logger.error("❌ 系统启动失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 系统启动异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard'
    })
    
    logger.info("=" * 60)
    logger.info("🚀 MT5系统简化测试")
    logger.info("=" * 60)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("JetStream连接", test_jetstream_connection),
        ("系统创建", test_system_creation),
        ("系统启动", test_system_startup)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"运行测试: {test_name}")
        try:
            results[test_name] = await test_func()
            if results[test_name]:
                logger.info(f"✅ {test_name}: 通过")
            else:
                logger.error(f"❌ {test_name}: 失败")
        except Exception as e:
            logger.error(f"💥 {test_name}: 异常 - {e}")
            results[test_name] = False
        
        logger.info("-" * 40)
        await asyncio.sleep(1)  # 短暂暂停
    
    # 总结
    logger.info("=" * 60)
    logger.info("📊 测试结果总结:")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！系统基本功能正常")
        return 0
    else:
        logger.error("💥 部分测试失败，需要进一步调试")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
