#!/usr/bin/env python3
"""
企业级密码管理系统
支持多种安全存储方式：环境变量、加密文件、密钥管理服务、硬件安全模块
"""

import os
import json
import base64
import hashlib
import logging
from typing import Optional, Dict, Any, Union
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from dataclasses import dataclass
from enum import Enum
import asyncio
import aiofiles

logger = logging.getLogger(__name__)

class PasswordStorageType(Enum):
    """密码存储类型"""
    ENVIRONMENT = "environment"           # 环境变量
    ENCRYPTED_FILE = "encrypted_file"     # 加密文件
    KEYRING = "keyring"                  # 系统密钥环
    VAULT = "vault"                      # HashiCorp Vault
    AWS_SECRETS = "aws_secrets"          # AWS Secrets Manager
    AZURE_KEYVAULT = "azure_keyvault"    # Azure Key Vault
    REDIS_ENCRYPTED = "redis_encrypted"  # 加密Redis存储
    HSM = "hsm"                          # 硬件安全模块

@dataclass
class PasswordConfig:
    """密码配置"""
    account_id: str
    storage_type: PasswordStorageType
    storage_config: Dict[str, Any]
    fallback_storage: Optional[PasswordStorageType] = None
    encryption_key_source: str = "auto"  # auto, file, environment, hsm
    rotation_enabled: bool = False
    rotation_interval_days: int = 30

class SecurePasswordManager:
    """
    安全密码管理器
    支持多种存储后端和加密方式
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/security/password_config.yaml"
        self.encryption_key = None
        self.storage_backends = {}
        self._init_storage_backends()
        
    def _init_storage_backends(self):
        """初始化存储后端"""
        self.storage_backends = {
            PasswordStorageType.ENVIRONMENT: EnvironmentPasswordStorage(),
            PasswordStorageType.ENCRYPTED_FILE: EncryptedFilePasswordStorage(),
            PasswordStorageType.KEYRING: KeyringPasswordStorage(),
            PasswordStorageType.VAULT: VaultPasswordStorage(),
            PasswordStorageType.AWS_SECRETS: AWSSecretsPasswordStorage(),
            PasswordStorageType.AZURE_KEYVAULT: AzureKeyVaultPasswordStorage(),
            PasswordStorageType.REDIS_ENCRYPTED: RedisEncryptedPasswordStorage(),
            PasswordStorageType.HSM: HSMPasswordStorage(),
        }
    
    async def get_password(self, account_id: str, config: Optional[PasswordConfig] = None) -> Optional[str]:
        """
        获取账户密码
        支持多种存储后端和故障转移
        """
        if not config:
            config = await self._load_password_config(account_id)
            
        if not config:
            # 默认配置：环境变量 + 文件备份
            config = PasswordConfig(
                account_id=account_id,
                storage_type=PasswordStorageType.ENVIRONMENT,
                storage_config={},
                fallback_storage=PasswordStorageType.ENCRYPTED_FILE
            )
        
        try:
            # 主存储
            primary_backend = self.storage_backends[config.storage_type]
            password = await primary_backend.get_password(account_id, config.storage_config)
            
            if password:
                logger.debug(f"从主存储获取密码成功: {account_id} ({config.storage_type.value})")
                return password
                
        except Exception as e:
            logger.warning(f"主存储获取密码失败 {account_id}: {e}")
        
        # 备用存储
        if config.fallback_storage:
            try:
                fallback_backend = self.storage_backends[config.fallback_storage]
                password = await fallback_backend.get_password(account_id, {})
                
                if password:
                    logger.info(f"从备用存储获取密码成功: {account_id} ({config.fallback_storage.value})")
                    return password
                    
            except Exception as e:
                logger.warning(f"备用存储获取密码失败 {account_id}: {e}")
        
        logger.error(f"所有存储方式都无法获取密码: {account_id}")
        return None
    
    async def set_password(self, account_id: str, password: str, config: Optional[PasswordConfig] = None):
        """设置账户密码"""
        if not config:
            config = await self._load_password_config(account_id)
            
        if not config:
            # 默认存储到加密文件
            config = PasswordConfig(
                account_id=account_id,
                storage_type=PasswordStorageType.ENCRYPTED_FILE,
                storage_config={}
            )
        
        try:
            backend = self.storage_backends[config.storage_type]
            await backend.set_password(account_id, password, config.storage_config)
            logger.info(f"密码设置成功: {account_id} ({config.storage_type.value})")
            
            # 同时保存到备用存储
            if config.fallback_storage:
                try:
                    fallback_backend = self.storage_backends[config.fallback_storage]
                    await fallback_backend.set_password(account_id, password, {})
                    logger.debug(f"备用存储密码设置成功: {account_id}")
                except Exception as e:
                    logger.warning(f"备用存储密码设置失败: {e}")
                    
        except Exception as e:
            logger.error(f"密码设置失败 {account_id}: {e}")
            raise
    
    async def _load_password_config(self, account_id: str) -> Optional[PasswordConfig]:
        """加载密码配置"""
        try:
            if not os.path.exists(self.config_path):
                return None
                
            async with aiofiles.open(self.config_path, 'r', encoding='utf-8') as f:
                import yaml
                config_data = yaml.safe_load(await f.read())
                
            account_config = config_data.get('passwords', {}).get(account_id)
            if not account_config:
                return None
                
            return PasswordConfig(
                account_id=account_id,
                storage_type=PasswordStorageType(account_config['storage_type']),
                storage_config=account_config.get('storage_config', {}),
                fallback_storage=PasswordStorageType(account_config['fallback_storage']) if account_config.get('fallback_storage') else None,
                encryption_key_source=account_config.get('encryption_key_source', 'auto'),
                rotation_enabled=account_config.get('rotation_enabled', False),
                rotation_interval_days=account_config.get('rotation_interval_days', 30)
            )
            
        except Exception as e:
            logger.warning(f"加载密码配置失败: {e}")
            return None

class PasswordStorageBackend:
    """密码存储后端基类"""
    
    async def get_password(self, account_id: str, config: Dict[str, Any]) -> Optional[str]:
        raise NotImplementedError
        
    async def set_password(self, account_id: str, password: str, config: Dict[str, Any]):
        raise NotImplementedError

class EnvironmentPasswordStorage(PasswordStorageBackend):
    """环境变量密码存储"""
    
    async def get_password(self, account_id: str, config: Dict[str, Any]) -> Optional[str]:
        env_var = f"MT5_{account_id.upper()}_PASSWORD"
        return os.getenv(env_var)
    
    async def set_password(self, account_id: str, password: str, config: Dict[str, Any]):
        env_var = f"MT5_{account_id.upper()}_PASSWORD"
        os.environ[env_var] = password

class EncryptedFilePasswordStorage(PasswordStorageBackend):
    """加密文件密码存储"""
    
    def __init__(self):
        self.passwords_file = "config/security/passwords.enc"
        self.key_file = "config/security/master.key"
        
    def _get_encryption_key(self) -> bytes:
        """获取加密密钥"""
        key_path = Path(self.key_file)
        
        if key_path.exists():
            with open(key_path, 'rb') as f:
                return f.read()
        else:
            # 生成新密钥
            key = Fernet.generate_key()
            key_path.parent.mkdir(parents=True, exist_ok=True)
            with open(key_path, 'wb') as f:
                f.write(key)
            os.chmod(key_path, 0o600)  # 只有所有者可读写
            return key
    
    def _derive_key_from_master(self, master_password: str, salt: bytes) -> bytes:
        """从主密码派生密钥"""
        password = master_password.encode()
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return base64.urlsafe_b64encode(kdf.derive(password))
    
    async def get_password(self, account_id: str, config: Dict[str, Any]) -> Optional[str]:
        try:
            if not os.path.exists(self.passwords_file):
                return None
                
            # 获取加密密钥
            if config.get('use_master_password'):
                master_password = os.getenv('MT5_MASTER_PASSWORD')
                if not master_password:
                    logger.error("主密码未设置")
                    return None
                    
                async with aiofiles.open(self.passwords_file, 'rb') as f:
                    data = await f.read()
                
                salt = data[:16]  # 前16字节是盐
                encrypted_data = data[16:]
                key = self._derive_key_from_master(master_password, salt)
            else:
                key = self._get_encryption_key()
                async with aiofiles.open(self.passwords_file, 'rb') as f:
                    encrypted_data = await f.read()
            
            # 解密数据
            f = Fernet(key)
            decrypted_data = f.decrypt(encrypted_data)
            passwords = json.loads(decrypted_data.decode())
            
            return passwords.get(account_id)
            
        except Exception as e:
            logger.error(f"从加密文件读取密码失败: {e}")
            return None
    
    async def set_password(self, account_id: str, password: str, config: Dict[str, Any]):
        try:
            # 读取现有密码
            passwords = {}
            if os.path.exists(self.passwords_file):
                existing_password = await self.get_password(account_id, config)
                if existing_password:
                    # 如果能读取到，说明文件格式正确，重新读取所有密码
                    if config.get('use_master_password'):
                        master_password = os.getenv('MT5_MASTER_PASSWORD')
                        async with aiofiles.open(self.passwords_file, 'rb') as f:
                            data = await f.read()
                        salt = data[:16]
                        encrypted_data = data[16:]
                        key = self._derive_key_from_master(master_password, salt)
                    else:
                        key = self._get_encryption_key()
                        async with aiofiles.open(self.passwords_file, 'rb') as f:
                            encrypted_data = await f.read()
                    
                    f = Fernet(key)
                    decrypted_data = f.decrypt(encrypted_data)
                    passwords = json.loads(decrypted_data.decode())
            
            # 更新密码
            passwords[account_id] = password
            
            # 加密并保存
            if config.get('use_master_password'):
                master_password = os.getenv('MT5_MASTER_PASSWORD')
                if not master_password:
                    raise ValueError("主密码未设置")
                    
                salt = os.urandom(16)
                key = self._derive_key_from_master(master_password, salt)
                f = Fernet(key)
                encrypted_data = f.encrypt(json.dumps(passwords).encode())
                
                # 保存 salt + encrypted_data
                Path(self.passwords_file).parent.mkdir(parents=True, exist_ok=True)
                async with aiofiles.open(self.passwords_file, 'wb') as file:
                    await file.write(salt + encrypted_data)
            else:
                key = self._get_encryption_key()
                f = Fernet(key)
                encrypted_data = f.encrypt(json.dumps(passwords).encode())
                
                Path(self.passwords_file).parent.mkdir(parents=True, exist_ok=True)
                async with aiofiles.open(self.passwords_file, 'wb') as file:
                    await file.write(encrypted_data)
            
            # 设置文件权限
            os.chmod(self.passwords_file, 0o600)
            
        except Exception as e:
            logger.error(f"保存密码到加密文件失败: {e}")
            raise

class KeyringPasswordStorage(PasswordStorageBackend):
    """系统密钥环密码存储"""
    
    async def get_password(self, account_id: str, config: Dict[str, Any]) -> Optional[str]:
        try:
            import keyring
            service_name = config.get('service_name', 'MT5TradingSystem')
            return keyring.get_password(service_name, account_id)
        except ImportError:
            logger.warning("keyring库未安装，无法使用系统密钥环")
            return None
        except Exception as e:
            logger.error(f"从系统密钥环获取密码失败: {e}")
            return None
    
    async def set_password(self, account_id: str, password: str, config: Dict[str, Any]):
        try:
            import keyring
            service_name = config.get('service_name', 'MT5TradingSystem')
            keyring.set_password(service_name, account_id, password)
        except ImportError:
            logger.warning("keyring库未安装，无法使用系统密钥环")
            raise
        except Exception as e:
            logger.error(f"保存密码到系统密钥环失败: {e}")
            raise

class VaultPasswordStorage(PasswordStorageBackend):
    """HashiCorp Vault密码存储"""
    
    async def get_password(self, account_id: str, config: Dict[str, Any]) -> Optional[str]:
        try:
            import hvac
            
            client = hvac.Client(
                url=config['vault_url'],
                token=config.get('vault_token') or os.getenv('VAULT_TOKEN')
            )
            
            secret_path = f"{config.get('secret_path', 'secret/mt5')}/{account_id}"
            response = client.secrets.kv.v2.read_secret_version(path=secret_path)
            
            return response['data']['data'].get('password')
            
        except ImportError:
            logger.warning("hvac库未安装，无法使用Vault")
            return None
        except Exception as e:
            logger.error(f"从Vault获取密码失败: {e}")
            return None
    
    async def set_password(self, account_id: str, password: str, config: Dict[str, Any]):
        try:
            import hvac
            
            client = hvac.Client(
                url=config['vault_url'],
                token=config.get('vault_token') or os.getenv('VAULT_TOKEN')
            )
            
            secret_path = f"{config.get('secret_path', 'secret/mt5')}/{account_id}"
            client.secrets.kv.v2.create_or_update_secret(
                path=secret_path,
                secret={'password': password}
            )
            
        except ImportError:
            logger.warning("hvac库未安装，无法使用Vault")
            raise
        except Exception as e:
            logger.error(f"保存密码到Vault失败: {e}")
            raise

class AWSSecretsPasswordStorage(PasswordStorageBackend):
    """AWS Secrets Manager密码存储"""
    
    async def get_password(self, account_id: str, config: Dict[str, Any]) -> Optional[str]:
        try:
            import boto3
            
            client = boto3.client(
                'secretsmanager',
                region_name=config.get('region', 'us-east-1')
            )
            
            secret_name = f"{config.get('secret_prefix', 'mt5-trading')}/{account_id}"
            response = client.get_secret_value(SecretId=secret_name)
            
            secret_data = json.loads(response['SecretString'])
            return secret_data.get('password')
            
        except ImportError:
            logger.warning("boto3库未安装，无法使用AWS Secrets Manager")
            return None
        except Exception as e:
            logger.error(f"从AWS Secrets Manager获取密码失败: {e}")
            return None
    
    async def set_password(self, account_id: str, password: str, config: Dict[str, Any]):
        try:
            import boto3
            
            client = boto3.client(
                'secretsmanager',
                region_name=config.get('region', 'us-east-1')
            )
            
            secret_name = f"{config.get('secret_prefix', 'mt5-trading')}/{account_id}"
            secret_value = json.dumps({'password': password})
            
            try:
                client.update_secret(
                    SecretId=secret_name,
                    SecretString=secret_value
                )
            except client.exceptions.ResourceNotFoundException:
                client.create_secret(
                    Name=secret_name,
                    SecretString=secret_value
                )
                
        except ImportError:
            logger.warning("boto3库未安装，无法使用AWS Secrets Manager")
            raise
        except Exception as e:
            logger.error(f"保存密码到AWS Secrets Manager失败: {e}")
            raise

class AzureKeyVaultPasswordStorage(PasswordStorageBackend):
    """Azure Key Vault密码存储"""
    
    async def get_password(self, account_id: str, config: Dict[str, Any]) -> Optional[str]:
        try:
            from azure.keyvault.secrets import SecretClient
            from azure.identity import DefaultAzureCredential
            
            credential = DefaultAzureCredential()
            client = SecretClient(
                vault_url=config['vault_url'],
                credential=credential
            )
            
            secret_name = f"{config.get('secret_prefix', 'mt5')}-{account_id}"
            secret = client.get_secret(secret_name)
            
            return secret.value
            
        except ImportError:
            logger.warning("azure-keyvault-secrets库未安装，无法使用Azure Key Vault")
            return None
        except Exception as e:
            logger.error(f"从Azure Key Vault获取密码失败: {e}")
            return None
    
    async def set_password(self, account_id: str, password: str, config: Dict[str, Any]):
        try:
            from azure.keyvault.secrets import SecretClient
            from azure.identity import DefaultAzureCredential
            
            credential = DefaultAzureCredential()
            client = SecretClient(
                vault_url=config['vault_url'],
                credential=credential
            )
            
            secret_name = f"{config.get('secret_prefix', 'mt5')}-{account_id}"
            client.set_secret(secret_name, password)
            
        except ImportError:
            logger.warning("azure-keyvault-secrets库未安装，无法使用Azure Key Vault")
            raise
        except Exception as e:
            logger.error(f"保存密码到Azure Key Vault失败: {e}")
            raise

class RedisEncryptedPasswordStorage(PasswordStorageBackend):
    """加密Redis密码存储"""
    
    async def get_password(self, account_id: str, config: Dict[str, Any]) -> Optional[str]:
        try:
            import redis
            
            r = redis.Redis(
                host=config.get('host', 'localhost'),
                port=config.get('port', 6379),
                password=config.get('redis_password'),
                db=config.get('db', 0)
            )
            
            encrypted_password = r.get(f"mt5:password:{account_id}")
            if not encrypted_password:
                return None
            
            # 解密
            key = config['encryption_key'].encode()
            f = Fernet(key)
            password = f.decrypt(encrypted_password).decode()
            
            return password
            
        except ImportError:
            logger.warning("redis库未安装，无法使用Redis存储")
            return None
        except Exception as e:
            logger.error(f"从Redis获取密码失败: {e}")
            return None
    
    async def set_password(self, account_id: str, password: str, config: Dict[str, Any]):
        try:
            import redis
            
            r = redis.Redis(
                host=config.get('host', 'localhost'),
                port=config.get('port', 6379),
                password=config.get('redis_password'),
                db=config.get('db', 0)
            )
            
            # 加密
            key = config['encryption_key'].encode()
            f = Fernet(key)
            encrypted_password = f.encrypt(password.encode())
            
            # 存储
            r.set(
                f"mt5:password:{account_id}", 
                encrypted_password,
                ex=config.get('ttl', 3600 * 24 * 30)  # 默认30天过期
            )
            
        except ImportError:
            logger.warning("redis库未安装，无法使用Redis存储")
            raise
        except Exception as e:
            logger.error(f"保存密码到Redis失败: {e}")
            raise

class HSMPasswordStorage(PasswordStorageBackend):
    """硬件安全模块(HSM)密码存储"""
    
    async def get_password(self, account_id: str, config: Dict[str, Any]) -> Optional[str]:
        # HSM实现取决于具体的HSM厂商和SDK
        logger.warning("HSM密码存储需要根据具体HSM厂商实现")
        return None
    
    async def set_password(self, account_id: str, password: str, config: Dict[str, Any]):
        logger.warning("HSM密码存储需要根据具体HSM厂商实现")
        raise NotImplementedError("HSM implementation required")

# 全局密码管理器实例
_password_manager = None

def get_password_manager() -> SecurePasswordManager:
    """获取全局密码管理器实例"""
    global _password_manager
    if _password_manager is None:
        _password_manager = SecurePasswordManager()
    return _password_manager

async def get_account_password(account_id: str) -> Optional[str]:
    """获取账户密码的便捷函数"""
    manager = get_password_manager()
    return await manager.get_password(account_id)

async def set_account_password(account_id: str, password: str):
    """设置账户密码的便捷函数"""
    manager = get_password_manager()
    return await manager.set_password(account_id, password)