#!/usr/bin/env python3
"""
消息路由器优化效果测试
验证阻塞问题是否得到解决
"""

import asyncio
import time
import json
from typing import Dict, Any, List
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.messaging.message_router import OptimizedMessageRouter
from src.messaging.priority_queue import MessagePriority


class MessageRouterOptimizationTest:
    """消息路由器优化测试类"""
    
    def __init__(self):
        self.test_results = {
            'startup_time': None,
            'route_performance': {},
            'cache_performance': {},
            'rate_limit_performance': {},
            'concurrent_performance': {},
            'memory_usage': {}
        }
    
    async def test_startup_performance(self) -> Dict[str, Any]:
        """测试启动性能"""
        print("🧪 测试启动性能...")
        
        config = {
            'host_id': 'test-host-01',
            'queue_manager': {
                'primary_backend': 'local_memory',
                'backup_backends': [],
                'local_fallback': True
            },
            'messaging': {
                'routing': {
                    'rules': [
                        {
                            'source': 'test_source',
                            'target': 'test_target',
                            'host_id': 'test-host-01',
                            'priority': 'SIGNAL_EXECUTION',
                            'route_type': 'local'
                        }
                    ]
                }
            }
        }
        
        # 测试启动时间
        start_time = time.perf_counter()
        
        router = OptimizedMessageRouter(config, 'test-host-01')
        startup_success = await router.start()
        
        startup_time = (time.perf_counter() - start_time) * 1000  # 毫秒
        
        result = {
            'startup_success': startup_success,
            'startup_time_ms': startup_time,
            'non_blocking': startup_time < 100,  # 启动应该在100ms内完成
            'status': router.get_status()
        }
        
        await router.stop()
        
        self.test_results['startup_time'] = result
        print(f"✅ 启动测试完成: {startup_time:.2f}ms")
        
        return result
    
    async def test_route_performance(self) -> Dict[str, Any]:
        """测试路由性能"""
        print("🧪 测试路由性能...")
        
        config = {
            'host_id': 'test-host-01',
            'queue_manager': {
                'primary_backend': 'local_memory',
                'backup_backends': [],
                'local_fallback': True
            }
        }
        
        router = OptimizedMessageRouter(config, 'test-host-01')
        await router.start()
        
        # 测试单个路由性能
        test_signal = {
            'message_id': 'test_001',
            'source_type': 'monitor',
            'target_account': 'ACC001',
            'target_host_id': 'test-host-01',
            'command_type': 'place_order',
            'data': {'symbol': 'EURUSD', 'volume': 0.1}
        }
        
        # 单次路由测试
        start_time = time.perf_counter()
        success = await router.route_signal(test_signal)
        single_route_time = (time.perf_counter() - start_time) * 1000
        
        # 批量路由测试
        batch_size = 100
        start_time = time.perf_counter()
        
        tasks = []
        for i in range(batch_size):
            signal = test_signal.copy()
            signal['message_id'] = f'test_{i:03d}'
            tasks.append(router.route_signal(signal))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        batch_time = time.perf_counter() - start_time
        
        success_count = sum(1 for r in results if r is True)
        avg_batch_time = (batch_time * 1000) / batch_size
        
        result = {
            'single_route_time_ms': single_route_time,
            'single_route_success': success,
            'batch_size': batch_size,
            'batch_total_time_ms': batch_time * 1000,
            'batch_success_count': success_count,
            'batch_success_rate': (success_count / batch_size) * 100,
            'avg_batch_route_time_ms': avg_batch_time,
            'throughput_per_second': batch_size / batch_time,
            'performance_target_met': single_route_time < 1.0  # 目标<1ms
        }
        
        await router.stop()
        
        self.test_results['route_performance'] = result
        print(f"✅ 路由性能测试完成: 单次{single_route_time:.2f}ms, 批量平均{avg_batch_time:.2f}ms")
        
        return result
    
    async def test_cache_performance(self) -> Dict[str, Any]:
        """测试缓存性能"""
        print("🧪 测试缓存性能...")
        
        config = {
            'host_id': 'test-host-01',
            'queue_manager': {
                'primary_backend': 'local_memory',
                'backup_backends': [],
                'local_fallback': True
            }
        }
        
        router = OptimizedMessageRouter(config, 'test-host-01')
        await router.start()
        
        # 准备测试信号
        test_signals = []
        for i in range(10):
            signal = {
                'message_id': f'cache_test_{i}',
                'source_type': 'monitor',
                'target_account': f'ACC{i % 3:03d}',  # 只有3个不同的账户，会产生缓存命中
                'target_host_id': 'test-host-01',
                'command_type': 'place_order'
            }
            test_signals.append(signal)
        
        # 第一轮：填充缓存
        for signal in test_signals:
            await router.route_signal(signal)
        
        # 第二轮：测试缓存命中
        start_time = time.perf_counter()
        for signal in test_signals:
            await router.route_signal(signal)
        cache_test_time = (time.perf_counter() - start_time) * 1000
        
        status = router.get_status()
        cache_hit_rate = status['routes'].get('cache_hit_rate', 0)
        
        result = {
            'cache_test_time_ms': cache_test_time,
            'cache_hit_rate': cache_hit_rate,
            'cache_size': status['routes'].get('rule_cache_size', 0),
            'cache_effective': cache_hit_rate > 50,  # 缓存命中率应该>50%
            'avg_cached_route_time_ms': cache_test_time / len(test_signals)
        }
        
        await router.stop()
        
        self.test_results['cache_performance'] = result
        print(f"✅ 缓存性能测试完成: 命中率{cache_hit_rate:.1f}%")
        
        return result
    
    async def test_rate_limit_performance(self) -> Dict[str, Any]:
        """测试速率限制性能"""
        print("🧪 测试速率限制性能...")
        
        config = {
            'host_id': 'test-host-01',
            'queue_manager': {
                'primary_backend': 'local_memory',
                'backup_backends': [],
                'local_fallback': True
            }
        }
        
        router = OptimizedMessageRouter(config, 'test-host-01')
        await router.start()
        
        # 设置较低的速率限制进行测试
        from src.messaging.message_router import RateLimitConfig
        test_config = RateLimitConfig(
            max_calls_per_second=5,
            burst_size=2,
            cooldown_period=2
        )
        router.set_rate_limit('TEST_ACC', test_config)
        
        # 发送超过限制的消息
        test_signals = []
        for i in range(10):  # 发送10个消息，但限制是5个/秒
            signal = {
                'message_id': f'rate_test_{i}',
                'source_type': 'monitor',
                'target_account': 'TEST_ACC',
                'target_host_id': 'test-host-01',
                'command_type': 'place_order'
            }
            test_signals.append(signal)
        
        # 快速发送消息
        start_time = time.perf_counter()
        results = []
        for signal in test_signals:
            result = await router.route_signal(signal)
            results.append(result)
        total_time = (time.perf_counter() - start_time) * 1000
        
        success_count = sum(1 for r in results if r)
        rate_limited_count = len(results) - success_count
        
        status = router.get_status()
        
        result = {
            'total_messages': len(test_signals),
            'successful_messages': success_count,
            'rate_limited_messages': rate_limited_count,
            'rate_limit_triggered': rate_limited_count > 0,
            'total_time_ms': total_time,
            'rate_limit_calls': status['rate_limiting']['rate_limited_calls'],
            'cooldown_accounts': status['rate_limiting']['cooldown_accounts'],
            'non_blocking': total_time < 100  # 速率限制处理应该是非阻塞的
        }
        
        await router.stop()
        
        self.test_results['rate_limit_performance'] = result
        print(f"✅ 速率限制测试完成: 限制了{rate_limited_count}个消息")
        
        return result
    
    async def test_concurrent_performance(self) -> Dict[str, Any]:
        """测试并发性能"""
        print("🧪 测试并发性能...")
        
        config = {
            'host_id': 'test-host-01',
            'queue_manager': {
                'primary_backend': 'local_memory',
                'backup_backends': [],
                'local_fallback': True
            }
        }
        
        router = OptimizedMessageRouter(config, 'test-host-01')
        await router.start()
        
        # 创建并发任务
        async def concurrent_routing_task(task_id: int, message_count: int):
            """并发路由任务"""
            results = []
            for i in range(message_count):
                signal = {
                    'message_id': f'concurrent_{task_id}_{i}',
                    'source_type': 'monitor',
                    'target_account': f'ACC{task_id:03d}',
                    'target_host_id': 'test-host-01',
                    'command_type': 'place_order'
                }
                result = await router.route_signal(signal)
                results.append(result)
            return results
        
        # 启动多个并发任务
        concurrent_tasks = 10
        messages_per_task = 20
        
        start_time = time.perf_counter()
        
        tasks = [
            concurrent_routing_task(i, messages_per_task) 
            for i in range(concurrent_tasks)
        ]
        
        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = (time.perf_counter() - start_time) * 1000
        
        # 统计结果
        total_messages = concurrent_tasks * messages_per_task
        successful_messages = 0
        
        for task_result in task_results:
            if isinstance(task_result, list):
                successful_messages += sum(1 for r in task_result if r)
        
        result = {
            'concurrent_tasks': concurrent_tasks,
            'messages_per_task': messages_per_task,
            'total_messages': total_messages,
            'successful_messages': successful_messages,
            'success_rate': (successful_messages / total_messages) * 100,
            'total_time_ms': total_time,
            'throughput_per_second': total_messages / (total_time / 1000),
            'avg_message_time_ms': total_time / total_messages,
            'concurrent_performance_good': total_time < 2000  # 应该在2秒内完成
        }
        
        await router.stop()
        
        self.test_results['concurrent_performance'] = result
        print(f"✅ 并发性能测试完成: {total_messages}消息在{total_time:.1f}ms内完成")
        
        return result
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始消息路由器优化测试...")
        print("=" * 60)
        
        # 运行所有测试
        await self.test_startup_performance()
        await self.test_route_performance()
        await self.test_cache_performance()
        await self.test_rate_limit_performance()
        await self.test_concurrent_performance()
        
        return self.test_results
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 优化效果总结")
        print("=" * 60)
        
        # 启动性能
        startup = self.test_results.get('startup_time', {})
        if startup.get('non_blocking', False):
            print("✅ 启动性能: 非阻塞启动成功")
        else:
            print("❌ 启动性能: 仍存在阻塞问题")
        
        # 路由性能
        route_perf = self.test_results.get('route_performance', {})
        if route_perf.get('performance_target_met', False):
            print("✅ 路由性能: 达到微秒级目标")
        else:
            print("❌ 路由性能: 未达到性能目标")
        
        # 缓存性能
        cache_perf = self.test_results.get('cache_performance', {})
        if cache_perf.get('cache_effective', False):
            print("✅ 缓存机制: 有效提升性能")
        else:
            print("❌ 缓存机制: 效果不明显")
        
        # 速率限制
        rate_limit = self.test_results.get('rate_limit_performance', {})
        if rate_limit.get('non_blocking', False):
            print("✅ 速率限制: 非阻塞处理")
        else:
            print("❌ 速率限制: 存在阻塞")
        
        # 并发性能
        concurrent = self.test_results.get('concurrent_performance', {})
        if concurrent.get('concurrent_performance_good', False):
            print("✅ 并发性能: 表现良好")
        else:
            print("❌ 并发性能: 需要改进")
        
        print("\n🎯 关键指标:")
        if startup:
            print(f"  - 启动时间: {startup.get('startup_time_ms', 0):.2f}ms")
        if route_perf:
            print(f"  - 单次路由: {route_perf.get('single_route_time_ms', 0):.2f}ms")
            print(f"  - 吞吐量: {route_perf.get('throughput_per_second', 0):.0f} msg/s")
        if cache_perf:
            print(f"  - 缓存命中率: {cache_perf.get('cache_hit_rate', 0):.1f}%")
        if concurrent:
            print(f"  - 并发吞吐量: {concurrent.get('throughput_per_second', 0):.0f} msg/s")


async def main():
    """主测试函数"""
    tester = MessageRouterOptimizationTest()
    results = await tester.run_all_tests()
    tester.print_summary()
    
    # 保存结果到文件
    with open('message_router_optimization_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 详细结果已保存到: message_router_optimization_results.json")


if __name__ == "__main__":
    asyncio.run(main())