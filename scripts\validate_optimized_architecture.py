#!/usr/bin/env python3
"""
优化架构验证脚本
验证所有新架构组件是否正确集成并能够正常工作
"""
import asyncio
import sys
import traceback
from pathlib import Path
from typing import Dict, Any, List
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import setup_logging
from src.core.mt5_process_launcher import MT5ProcessLauncher
from src.core.mt5_account_monitor import MT5AccountMonitor
from src.core.mt5_account_executor import MT5AccountExecutor
from src.messaging.distributed_signal_router import DistributedSignalRouter
from src.messaging.zero_copy_messaging import ZeroCopyMessageBus, get_zero_copy_message_bus
from src.utils.unified_memory_pool import UnifiedMemoryPool, get_unified_memory_pool
from src.core.unified_coordinator import UnifiedMT5Coordinator

logger = setup_logging("architecture_validator")

class ArchitectureValidator:
    """架构验证器"""
    
    def __init__(self):
        self.test_config = {
            'host_id': 'validation-host-01',
            'account_id': 'VALIDATION_ACC',
            'terminal_path': '/mock/terminal/path',
            'zero_copy_enabled': True,
            'memory_pool_size': 16 * 1024 * 1024,  # 16MB
            'enable_monitor': True,
            'enable_executor': True,
            'local_accounts': ['VALIDATION_ACC', 'LOCAL_ACC_001'],
            'remote_account_mappings': {
                'REMOTE_ACC_001': 'remote-host-01'
            }
        }
    
    async def validate_all(self) -> Dict[str, bool]:
        """验证所有架构组件"""
        logger.info("🚀 开始架构验证")
        
        results = {}
        
        # 1. 验证内存池
        results['memory_pool'] = await self.validate_memory_pool()
        
        # 2. 验证零拷贝消息总线
        results['zero_copy_bus'] = await self.validate_zero_copy_bus()
        
        # 3. 验证分布式信号路由器
        results['distributed_router'] = await self.validate_distributed_router()
        
        # 4. 验证进程启动器
        results['process_launcher'] = await self.validate_process_launcher()
        
        # 5. 验证监控器和执行器
        results['monitor_executor'] = await self.validate_monitor_executor()
        
        # 6. 验证统一协调器
        results['unified_coordinator'] = await self.validate_unified_coordinator()
        
        # 7. 集成测试
        results['integration'] = await self.validate_integration()
        
        # 总结结果
        self.print_validation_summary(results)
        
        return results
    
    async def validate_memory_pool(self) -> bool:
        """验证统一内存池"""
        logger.info("📦 验证统一内存池")
        
        try:
            # 获取内存池实例
            pool = get_unified_memory_pool()
            await pool.start()
            
            # 测试内存分配
            buffer1 = await pool.allocate(1024)
            buffer2 = await pool.allocate(2048)
            
            if not buffer1 or not buffer2:
                logger.error("❌ 内存分配失败")
                return False
            
            # 测试统计信息
            stats = pool.get_performance_stats()
            if stats['global_stats']['allocations_count'] < 2:
                logger.error("❌ 内存池统计异常")
                return False
            
            # 释放内存
            await pool.deallocate(buffer1)
            await pool.deallocate(buffer2)
            
            await pool.stop()
            logger.info("✅ 统一内存池验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 内存池验证失败: {e}")
            return False
    
    async def validate_zero_copy_bus(self) -> bool:
        """验证零拷贝消息总线"""
        logger.info("🚌 验证零拷贝消息总线")
        
        try:
            bus = get_zero_copy_message_bus("validation_bus")
            await bus.start()
            
            # 测试消息发送和接收
            received_messages = []
            
            async def test_handler(header, payload):
                received_messages.append({'header': header, 'payload': payload})
            
            from src.messaging.zero_copy_messaging import MessageType as ZeroCopyMessageType
            await bus.subscribe(ZeroCopyMessageType.TRADE_SIGNAL, test_handler)
            
            # 发送测试消息
            test_data = {'test': 'message', 'id': 12345}
            await bus.send_message(
                message_type=ZeroCopyMessageType.TRADE_SIGNAL,
                data=test_data,
                target_id=1001,
                priority=1
            )
            
            # 等待消息处理
            await asyncio.sleep(0.1)
            
            if len(received_messages) != 1:
                logger.error(f"❌ 消息接收异常: 期望1条，实际{len(received_messages)}条")
                return False
            
            if received_messages[0]['payload']['id'] != 12345:
                logger.error("❌ 消息内容验证失败")
                return False
            
            await bus.stop()
            logger.info("✅ 零拷贝消息总线验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 零拷贝消息总线验证失败: {e}")
            return False
    
    async def validate_distributed_router(self) -> bool:
        """验证分布式信号路由器"""
        logger.info("🌐 验证分布式信号路由器")
        
        try:
            # 创建零拷贝总线用于路由器
            bus = get_zero_copy_message_bus("router_bus")
            await bus.start()
            
            config = {
                **self.test_config,
                'zero_copy_bus': bus,
                'queue_manager': None  # 简化测试
            }
            
            router = DistributedSignalRouter('validation-host', config)
            await router.start()
            
            # 测试路由统计
            stats = router.get_routing_stats()
            if 'host_id' not in stats:
                logger.error("❌ 路由器统计信息异常")
                return False
            
            # 测试账户映射更新
            router.update_account_mapping('TEST_ACC', 'validation-host')
            if 'TEST_ACC' not in router.local_accounts:
                logger.error("❌ 账户映射更新失败")
                return False
            
            await router.stop()
            await bus.stop()
            logger.info("✅ 分布式信号路由器验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 分布式信号路由器验证失败: {e}")
            return False
    
    async def validate_process_launcher(self) -> bool:
        """验证进程启动器"""
        logger.info("🚀 验证进程启动器")
        
        try:
            # 注意：这里不实际启动进程，只验证初始化
            launcher = MT5ProcessLauncher('VALIDATION_ACC', self.test_config)
            
            # 验证基本属性
            if launcher.account_id != 'VALIDATION_ACC':
                logger.error("❌ 进程启动器账户ID设置错误")
                return False
            
            if launcher.host_id != self.test_config['host_id']:
                logger.error("❌ 进程启动器主机ID设置错误")
                return False
            
            if launcher.monitor_port < 20000 or launcher.executor_port < 21000:
                logger.error("❌ 进程启动器端口分配异常")
                return False
            
            logger.info("✅ 进程启动器验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 进程启动器验证失败: {e}")
            return False
    
    async def validate_monitor_executor(self) -> bool:
        """验证监控器和执行器"""
        logger.info("👁️ 验证监控器和执行器")
        
        try:
            # 准备组件
            pool = get_unified_memory_pool()
            await pool.start()
            
            bus = get_zero_copy_message_bus("me_bus")
            await bus.start()
            
            config = {
                **self.test_config,
                'memory_pool': pool,
                'zero_copy_bus': bus,
                'rpc_client': None  # 简化测试
            }
            
            # 创建监控器
            monitor = MT5AccountMonitor('VALIDATION_ACC', config)
            
            # 创建执行器
            executor = MT5AccountExecutor('VALIDATION_ACC', config)
            
            # 验证基本属性
            if monitor.account_id != 'VALIDATION_ACC':
                logger.error("❌ 监控器账户ID设置错误")
                return False
            
            if executor.account_id != 'VALIDATION_ACC':
                logger.error("❌ 执行器账户ID设置错误")
                return False
            
            if monitor.zero_copy_bus != bus:
                logger.error("❌ 监控器零拷贝总线设置错误")
                return False
            
            if executor.zero_copy_bus != bus:
                logger.error("❌ 执行器零拷贝总线设置错误")
                return False
            
            # 验证健康检查（未启动状态）
            monitor_health = await monitor.health_check()
            executor_health = await executor.health_check()
            
            if monitor_health or executor_health:
                logger.warning("⚠️ 未启动的组件健康检查返回True（可能正常）")
            
            await bus.stop()
            await pool.stop()
            logger.info("✅ 监控器和执行器验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 监控器和执行器验证失败: {e}")
            return False
    
    async def validate_unified_coordinator(self) -> bool:
        """验证统一协调器"""
        logger.info("🎯 验证统一协调器")
        
        try:
            # 创建协调器（不实际启动，只验证初始化）
            config_path = str(project_root / "config" / "core" / "system.yaml")
            
            # 检查配置文件是否存在
            if not Path(config_path).exists():
                logger.warning(f"⚠️ 配置文件不存在: {config_path}，使用模拟路径")
                config_path = "/mock/config/path"
            
            coordinator = UnifiedMT5Coordinator('validation-host', config_path)
            
            # 验证基本属性
            if coordinator.host_id != 'validation-host':
                logger.error("❌ 协调器主机ID设置错误")
                return False
            
            if coordinator.config_path != config_path:
                logger.error("❌ 协调器配置路径设置错误")
                return False
            
            if coordinator.running:
                logger.error("❌ 协调器初始状态应为未运行")
                return False
            
            logger.info("✅ 统一协调器验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 统一协调器验证失败: {e}")
            return False
    
    async def validate_integration(self) -> bool:
        """验证组件集成"""
        logger.info("🔗 验证组件集成")
        
        try:
            # 创建完整的组件栈
            pool = get_unified_memory_pool()
            await pool.start()
            
            bus = get_zero_copy_message_bus("integration_bus")
            await bus.start()
            
            # 创建路由器
            router_config = {
                **self.test_config,
                'zero_copy_bus': bus,
                'queue_manager': None
            }
            
            router = DistributedSignalRouter('integration-host', router_config)
            await router.start()
            
            # 创建监控器和执行器
            component_config = {
                **self.test_config,
                'memory_pool': pool,
                'zero_copy_bus': bus,
                'rpc_client': None
            }
            
            monitor = MT5AccountMonitor('INTEGRATION_ACC', component_config)
            executor = MT5AccountExecutor('INTEGRATION_ACC', component_config)
            
            # 验证组件间的连接
            if monitor.memory_pool != pool:
                logger.error("❌ 监控器内存池连接错误")
                return False
            
            if executor.zero_copy_bus != bus:
                logger.error("❌ 执行器零拷贝总线连接错误")
                return False
            
            # 测试信号流（简化版）
            from src.messaging.message_types import TradeSignal, OrderTypeEnum, TradeAction
            
            signal = TradeSignal(
                signal_id='integration_test',
                account_id='MASTER_ACC',
                symbol='EURUSD',
                action=TradeAction.OPEN,
                order_type=OrderTypeEnum.BUY,
                volume=0.1,
                timestamp=asyncio.get_event_loop().time()
            )
            
            # 路由信号
            results = await router.route_signal(signal, ['INTEGRATION_ACC'])
            
            if not isinstance(results, dict):
                logger.error("❌ 信号路由返回结果类型错误")
                return False
            
            # 清理
            await router.stop()
            await bus.stop()
            await pool.stop()
            
            logger.info("✅ 组件集成验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 组件集成验证失败: {e}")
            return False
    
    def print_validation_summary(self, results: Dict[str, bool]):
        """打印验证摘要"""
        logger.info("=" * 60)
        logger.info("🎯 架构验证结果摘要")
        logger.info("=" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r)
        failed_tests = total_tests - passed_tests
        
        for component, passed in results.items():
            status = "✅ 通过" if passed else "❌ 失败"
            logger.info(f"{component:20} : {status}")
        
        logger.info("-" * 60)
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过数量: {passed_tests}")
        logger.info(f"失败数量: {failed_tests}")
        logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests == 0:
            logger.info("🎉 所有架构组件验证通过！")
        else:
            logger.warning(f"⚠️ {failed_tests} 个组件验证失败，需要检查")
        
        logger.info("=" * 60)


async def main():
    """主函数"""
    try:
        validator = ArchitectureValidator()
        results = await validator.validate_all()
        
        # 返回适当的退出码
        failed_tests = sum(1 for r in results.values() if not r)
        return 0 if failed_tests == 0 else 1
        
    except Exception as e:
        logger.error(f"❌ 验证过程异常: {e}")
        logger.debug(traceback.format_exc())
        return 1


if __name__ == '__main__':
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("⚠️ 验证被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
        sys.exit(1)