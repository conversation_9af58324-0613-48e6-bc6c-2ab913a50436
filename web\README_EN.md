# MT5 Trading System - Web Interface

<div align="center">

English | [简体中文](README.md)

</div>

This directory contains the web interface components for the MT5 dynamic pairing trading system, including backend API and frontend application.

## Directory Structure

```
web/
├── backend/                      # Backend API extensions
│   ├── api/
│   │   ├── __init__.py
│   │   ├── app.py              # FastAPI application
│   │   ├── routers/            # API routes
│   │   │   ├── __init__.py
│   │   │   ├── accounts.py    # Account management API
│   │   │   ├── pairings.py    # Pairing management API
│   │   │   ├── monitoring.py  # Monitoring API
│   │   │   ├── dashboard.py   # Dashboard API
│   │   │   └── websocket.py   # WebSocket endpoints
│   │   ├── models/             # Data models
│   │   │   ├── __init__.py
│   │   │   ├── requests.py    # Request models
│   │   │   └── responses.py   # Response models
│   │   └── services/           # Business services
│   │       ├── __init__.py
│   │       ├── pairing_service.py
│   │       └── dashboard_service.py
│   └── requirements.txt
│
├── frontend/                     # React frontend application
│   ├── public/
│   │   └── index.html
│   ├── src/
│   │   ├── components/          # React components
│   │   │   ├── Dashboard/      # Dashboard
│   │   │   │   └── index.js
│   │   │   ├── Accounts/       # Account management
│   │   │   │   └── index.js
│   │   │   ├── Pairings/       # Pairing management
│   │   │   │   └── index.js
│   │   │   ├── Monitoring/     # Real-time monitoring
│   │   │   │   └── index.js
│   │   │   └── Common/         # Common components
│   │   │       └── index.js
│   │   ├── services/           # API services
│   │   │   └── index.js
│   │   ├── hooks/              # Custom hooks
│   │   │   └── index.js
│   │   ├── utils/              # Utility functions
│   │   │   └── index.js
│   │   ├── store/              # Redux state management
│   │   │   └── index.js
│   │   ├── App.js
│   │   └── index.js
│   ├── package.json
│   └── README.md
│
└── static/                       # Static resources
    ├── css/
    │   └── style.css
    ├── js/
    │   └── app.js
    └── images/
        └── .gitkeep
```

## Feature Description

### Backend API
- **Dedicated Web API Service**: Provides customized API interfaces for the frontend
- **Route Management**: API routes organized by functional modules
- **Data Models**: Data validation models for requests and responses
- **Business Services**: Service layer handling complex business logic

### Frontend Application
- **Component Architecture**: React components organized by functional areas
- **State Management**: Global state management using Redux
- **API Services**: Communication interfaces with backend API
- **Custom Hooks**: Reusable React logic
- **Utility Functions**: Common tools and helper functions

### Static Resources
- **CSS Styles**: Global styles and themes
- **JavaScript**: Native JS functionality
- **Image Resources**: Icons, logos and other static images

## Development Guide

### Start Backend API
```bash
cd web/backend
pip install -r requirements.txt
uvicorn api.app:app --reload --port 8001
```

### Start Frontend Application
```bash
cd web/frontend
npm install
npm start
```

### Development Notes
1. Backend API runs on port 8001
2. Frontend development server runs on port 3000
3. All API routes are prefixed with `/api/v1/`
4. WebSocket connections are used for real-time data pushing

## Relationship with Main System

This web interface is an extension module of the main system:
- Calls main system functionality through APIs
- Provides user-friendly operation interface
- Displays system status and data in real-time
- Can be deployed and maintained independently