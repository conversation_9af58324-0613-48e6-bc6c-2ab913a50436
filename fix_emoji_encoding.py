#!/usr/bin/env python3
"""
修复测试文件中的emoji编码问题
"""

def fix_emoji_encoding():
    """替换文件中的emoji为安全字符"""

    # 读取文件
    with open('test_core_trading_components.py', 'r', encoding='utf-8') as f:
        content = f.read()

    # 修复所有的{VAR}格式为f"{VAR}"格式
    import re

    # 找到所有 logger.info("{VAR}...") 的模式并修复为 logger.info(f"{VAR}...")
    patterns = [
        (r'logger\.info\("(\{[A-Z_]+\}[^"]*)"', r'logger.info(f"\1"'),
        (r'logger\.error\("(\{[A-Z_]+\}[^"]*)"', r'logger.error(f"\1"'),
        (r'logger\.warning\("(\{[A-Z_]+\}[^"]*)"', r'logger.warning(f"\1"'),
        (r'print\("(\{[A-Z_]+\}[^"]*)"', r'print(f"\1"'),
        (r'status = "(\{[A-Z_]+\}[^"]*)"', r'status = f"\1"'),
    ]

    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)

    # 写回文件
    with open('test_core_trading_components.py', 'w', encoding='utf-8') as f:
        f.write(content)

    print("Emoji编码问题修复完成!")

if __name__ == "__main__":
    fix_emoji_encoding()
