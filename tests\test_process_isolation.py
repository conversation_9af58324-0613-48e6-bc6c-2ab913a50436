# tests/test_process_isolation.py
"""
进程隔离测试用例
验证MT5进程隔离效果，确保账户间完全隔离，无状态冲突
"""

import pytest
import asyncio
import time
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 导入要测试的模块
from src.core.mt5_process_manager import MT5ProcessManager, MT5Process
from src.core.mt5_client import MT5Client, create_mt5_client
from src.messaging.message_types import (
    CommandRequest, CommandResponse, ProcessStatus,
    ProcessState, CommandType
)


class TestProcessIsolation:
    """进程隔离测试类"""
    
    @pytest.fixture
    def process_manager(self):
        """创建进程管理器实例"""
        manager = MT5ProcessManager()
        yield manager
        # 清理
        if manager.running:
            manager.shutdown()
    
    @pytest.fixture
    def mock_mt5(self):
        """模拟MT5模块"""
        with patch('src.core.mt5_process_manager.mt5') as mock:
            mock.initialize.return_value = True
            mock.account_info.return_value = Mock(
                login=12345,
                balance=10000.0,
                equity=10000.0,
                server="TestServer",
                trade_allowed=True
            )
            mock.terminal_info.return_value = Mock()
            mock.last_error.return_value = (0, "Success")
            yield mock
    
    def test_process_manager_initialization(self, process_manager):
        """测试进程管理器初始化"""
        assert process_manager is not None
        assert not process_manager.running
        assert len(process_manager.processes) == 0
        assert len(process_manager.command_queues) == 0
        assert len(process_manager.result_queues) == 0
    
    def test_process_manager_start_stop(self, process_manager):
        """测试进程管理器启动和停止"""
        # 启动
        process_manager.start_manager()
        assert process_manager.running
        assert process_manager.command_result_handler_thread is not None
        assert process_manager.monitor_thread is not None
        
        # 停止
        process_manager.shutdown()
        assert not process_manager.running
    
    @patch('src.core.mt5_process_manager.MT5Process')
    def test_add_account_success(self, mock_process_class, process_manager):
        """测试成功添加账户"""
        # 模拟进程
        mock_process = Mock()
        mock_process.is_alive.return_value = True
        mock_process.pid = 12345
        mock_process_class.return_value = mock_process
        
        process_manager.start_manager()
        
        account_config = {
            'login': 12345,
            'password': 'test_password',
            'server': 'TestServer',
            'terminal_path': '/path/to/terminal'
        }
        
        result = process_manager.add_account('TEST_ACCOUNT', account_config)
        
        assert result is True
        assert 'TEST_ACCOUNT' in process_manager.processes
        assert 'TEST_ACCOUNT' in process_manager.command_queues
        assert 'TEST_ACCOUNT' in process_manager.result_queues
        assert 'TEST_ACCOUNT' in process_manager.process_status
    
    def test_multiple_accounts_isolation(self, process_manager):
        """测试多账户隔离"""
        with patch('src.core.mt5_process_manager.MT5Process') as mock_process_class:
            # 创建多个模拟进程
            mock_processes = []
            for i in range(3):
                mock_process = Mock()
                mock_process.is_alive.return_value = True
                mock_process.pid = 12345 + i
                mock_processes.append(mock_process)
            
            mock_process_class.side_effect = mock_processes
            
            process_manager.start_manager()
            
            # 添加多个账户
            accounts = ['ACC001', 'ACC002', 'ACC003']
            for i, account in enumerate(accounts):
                config = {
                    'login': 12345 + i,
                    'password': f'password_{i}',
                    'server': 'TestServer',
                    'terminal_path': '/path/to/terminal'
                }
                result = process_manager.add_account(account, config)
                assert result is True
            
            # 验证每个账户都有独立的进程和队列
            assert len(process_manager.processes) == 3
            assert len(process_manager.command_queues) == 3
            assert len(process_manager.result_queues) == 3
            
            # 验证进程ID不同（确保隔离）
            pids = [p.pid for p in process_manager.processes.values()]
            assert len(set(pids)) == 3  # 所有PID都不同
    
    def test_process_restart_mechanism(self, process_manager):
        """测试进程重启机制"""
        with patch('src.core.mt5_process_manager.MT5Process') as mock_process_class:
            # 第一次创建进程
            mock_process1 = Mock()
            mock_process1.is_alive.return_value = True
            mock_process1.pid = 12345
            
            # 第二次创建进程（重启后）
            mock_process2 = Mock()
            mock_process2.is_alive.return_value = True
            mock_process2.pid = 12346
            
            mock_process_class.side_effect = [mock_process1, mock_process2]
            
            process_manager.start_manager()
            
            account_config = {
                'login': 12345,
                'password': 'test_password',
                'server': 'TestServer',
                'terminal_path': '/path/to/terminal'
            }
            
            # 添加账户
            process_manager.add_account('TEST_ACCOUNT', account_config)
            
            # 启用自动重启
            process_manager.enable_auto_restart('TEST_ACCOUNT')
            
            # 模拟进程死亡
            mock_process1.is_alive.return_value = False
            
            # 触发重启
            process_manager._handle_dead_process('TEST_ACCOUNT')
            
            # 验证重启策略被更新
            policy = process_manager.restart_policy.get('TEST_ACCOUNT')
            assert policy is not None
            assert policy['enabled'] is True
    
    def test_command_request_response(self, process_manager):
        """测试命令请求响应机制"""
        process_manager.start_manager()
        
        # 创建命令请求
        request = CommandRequest(
            type=CommandType.GET_ACCOUNT_INFO,
            params={},
            request_id="test_request_123"
        )
        
        # 验证请求结构
        assert request.type == CommandType.GET_ACCOUNT_INFO
        assert request.request_id == "test_request_123"
        assert isinstance(request.timestamp, datetime)
    
    def test_process_status_tracking(self, process_manager):
        """测试进程状态跟踪"""
        with patch('src.core.mt5_process_manager.MT5Process') as mock_process_class:
            mock_process = Mock()
            mock_process.is_alive.return_value = True
            mock_process.pid = 12345
            mock_process_class.return_value = mock_process
            
            process_manager.start_manager()
            
            account_config = {
                'login': 12345,
                'password': 'test_password',
                'server': 'TestServer'
            }
            
            process_manager.add_account('TEST_ACCOUNT', account_config)
            
            # 检查状态
            status = process_manager.get_process_status('TEST_ACCOUNT')
            assert status is not None
            assert status.account_name == 'TEST_ACCOUNT'
            assert status.pid == 12345
            assert status.is_alive is True
            assert status.status == ProcessState.CONNECTING
    
    def test_system_health_monitoring(self, process_manager):
        """测试系统健康监控"""
        with patch('src.core.mt5_process_manager.MT5Process') as mock_process_class:
            mock_process = Mock()
            mock_process.is_alive.return_value = True
            mock_process.pid = 12345
            mock_process_class.return_value = mock_process
            
            process_manager.start_manager()
            
            # 添加测试账户
            account_config = {
                'login': 12345,
                'password': 'test_password',
                'server': 'TestServer'
            }
            process_manager.add_account('TEST_ACCOUNT', account_config)
            
            # 获取系统健康状态
            health = process_manager.get_system_health()
            
            assert health.total_processes == 1
            assert health.running_processes == 1
            assert health.connected_processes == 0  # 初始状态未连接
            assert health.uptime_seconds >= 0
    
    def test_mt5_client_process_isolation(self):
        """测试MT5客户端进程隔离"""
        with patch('src.core.mt5_client.MT5ProcessManager') as mock_manager_class:
            mock_manager = Mock()
            mock_manager.start_terminal_process_new.return_value = asyncio.Future()
            mock_manager.start_terminal_process_new.return_value.set_result(True)
            mock_manager.is_account_connected.return_value = True
            mock_manager_class.return_value = mock_manager
            
            # 创建两个不同的客户端
            client1 = create_mt5_client(
                account_id='ACC001',
                login=12345,
                password='password1',
                server='Server1'
            )
            
            client2 = create_mt5_client(
                account_id='ACC002',
                login=67890,
                password='password2',
                server='Server2'
            )
            
            # 验证客户端使用相同的进程管理器（单例模式）
            assert client1._process_manager is client2._process_manager
            
            # 但账户ID不同，确保隔离
            assert client1.account_id != client2.account_id
            assert client1.login != client2.login
    
    def test_error_handling_and_recovery(self, process_manager):
        """测试错误处理和恢复"""
        with patch('src.core.mt5_process_manager.MT5Process') as mock_process_class:
            # 模拟进程启动失败
            mock_process = Mock()
            mock_process.is_alive.return_value = False
            mock_process_class.return_value = mock_process
            
            process_manager.start_manager()
            
            account_config = {
                'login': 12345,
                'password': 'test_password',
                'server': 'TestServer'
            }
            
            # 尝试添加账户（应该失败）
            result = process_manager.add_account('TEST_ACCOUNT', account_config)
            
            assert result is False
            assert 'TEST_ACCOUNT' not in process_manager.processes


@pytest.mark.asyncio
class TestAsyncProcessIsolation:
    """异步进程隔离测试"""
    
    async def test_concurrent_operations(self):
        """测试并发操作的隔离性"""
        with patch('src.core.mt5_client.MT5ProcessManager') as mock_manager_class:
            mock_manager = Mock()
            
            # 模拟异步连接
            async def mock_connect(*args, **kwargs):
                await asyncio.sleep(0.1)  # 模拟连接延迟
                return True
            
            mock_manager.start_terminal_process_new = mock_connect
            mock_manager.is_account_connected.return_value = True
            mock_manager_class.return_value = mock_manager
            
            # 创建多个客户端并发连接
            clients = []
            for i in range(5):
                client = create_mt5_client(
                    account_id=f'ACC{i:03d}',
                    login=12345 + i,
                    password=f'password{i}',
                    server=f'Server{i}'
                )
                clients.append(client)
            
            # 并发连接
            connect_tasks = [client.connect() for client in clients]
            results = await asyncio.gather(*connect_tasks)
            
            # 验证所有连接都成功
            assert all(results)
            
            # 验证每个客户端都有独立的账户ID
            account_ids = [client.account_id for client in clients]
            assert len(set(account_ids)) == 5  # 所有账户ID都不同


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
