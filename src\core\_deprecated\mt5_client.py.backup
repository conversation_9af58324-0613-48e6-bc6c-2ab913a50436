# src/core/mt5_client.py
"""
MT5客户端 - 进程隔离版本
集成process.py的优秀技术，实现真正的进程级隔离
每个账户独立MT5进程，消除状态冲突
"""
import asyncio
import os
import time
import threading
import logging
import uuid
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from enum import Enum

try:
    import MetaTrader5 as mt5
except ImportError:
    mt5 = None
    print("警告: MetaTrader5模块未安装")

from .mt5_process_manager import MT5ProcessManager
from ..messaging.message_types import (
    CommandRequest, CommandResponse, ProcessStatus,
    TradeRequest, TradeResult, AccountInfo, Position,
    CommandType, ProcessState, MT5ConnectionConfig
)
from ..messaging.jetstream_client import JetStreamClient
from ..messaging.message_codec import MessageCodec

logger = logging.getLogger(__name__)


# 交易相关数据类
class TradeAction(Enum):
    DEAL = mt5.TRADE_ACTION_DEAL if mt5 else 1
    PENDING = mt5.TRADE_ACTION_PENDING if mt5 else 5
    SLTP = mt5.TRADE_ACTION_SLTP if mt5 else 6
    MODIFY = mt5.TRADE_ACTION_MODIFY if mt5 else 7
    REMOVE = mt5.TRADE_ACTION_REMOVE if mt5 else 8


class OrderType(Enum):
    BUY = mt5.ORDER_TYPE_BUY if mt5 else 0
    SELL = mt5.ORDER_TYPE_SELL if mt5 else 1
    BUY_LIMIT = mt5.ORDER_TYPE_BUY_LIMIT if mt5 else 2
    SELL_LIMIT = mt5.ORDER_TYPE_SELL_LIMIT if mt5 else 3
    BUY_STOP = mt5.ORDER_TYPE_BUY_STOP if mt5 else 4
    SELL_STOP = mt5.ORDER_TYPE_SELL_STOP if mt5 else 5


@dataclass
class TradeRequest:
    """交易请求"""
    action: TradeAction
    symbol: str
    volume: float
    type: OrderType
    price: Optional[float] = None
    sl: Optional[float] = None
    tp: Optional[float] = None
    position: Optional[int] = None  
    deviation: int = 20
    magic: int = 12345
    comment: str = ""
    type_time: int = mt5.ORDER_TIME_GTC if mt5 else 0
    type_filling: int = mt5.ORDER_FILLING_IOC if mt5 else 1


@dataclass
class TradeResult:
    """交易结果"""
    retcode: int
    deal: int = 0
    order: int = 0
    volume: float = 0.0
    price: float = 0.0
    bid: float = 0.0
    ask: float = 0.0
    comment: str = ""
    request_id: int = 0
    retcode_external: int = 0


@dataclass
class Position:
    """持仓信息"""
    ticket: int
    symbol: str
    type: int
    volume: float
    price_open: float
    price_current: float
    profit: float
    swap: float
    comment: str


class MT5Client:
    """
    MT5客户端 - 进程隔离版本
    集成process.py的优秀技术，每个账户独立MT5进程，消除状态冲突
    """

    # 全局进程管理器（单例模式）
    _global_process_manager: Optional[MT5ProcessManager] = None
    _manager_lock = threading.Lock()

    def __init__(self, account_id: str, login: int, password: str,
                 server: str, terminal_path: Optional[str] = None):
        self.account_id = account_id
        self.login = login
        self.password = password
        self.server = server
        self.terminal_path = terminal_path

        # 获取或创建进程管理器
        self._process_manager = self._get_or_create_process_manager()

        # 连接状态（通过进程管理器获取）
        self.connected = False
        self.account_info = None
        self.connection_time = None

        # 统计信息
        self._stats = {
            'connection_attempts': 0,
            'connection_success': 0,
            'connection_failed': 0,
            'commands_sent': 0,
            'commands_success': 0,
            'commands_failed': 0
        }

        logger.info(f"MT5Client初始化（进程隔离模式）: {account_id}")

    @classmethod
    def _get_or_create_process_manager(cls) -> MT5ProcessManager:
        """获取或创建进程管理器（单例模式）"""
        with cls._manager_lock:
            if cls._global_process_manager is None:
                cls._global_process_manager = MT5ProcessManager()
                cls._global_process_manager.start_manager()
                logger.info("全局MT5进程管理器已创建并启动")
            return cls._global_process_manager
    
    async def connect(self) -> bool:
        """
        连接到MT5终端 - 进程隔离连接
        每个账户独立进程，消除状态冲突
        """
        try:
            self._stats['connection_attempts'] += 1
            logger.info(f"开始连接MT5（进程隔离模式）: {self.account_id}")

            # 准备账户配置
            account_config = {
                'login': self.login,
                'password': self.password,
                'server': self.server,
                'terminal_path': self.terminal_path
            }

            # 通过进程管理器启动独立进程
            success = await self._process_manager.start_terminal_process_new(
                self.account_id, account_config
            )

            if success:
                self.connected = True
                self.connection_time = time.time()
                self._stats['connection_success'] += 1
                logger.info(f"✅ MT5连接成功（进程隔离）: {self.account_id}")

                # 获取账户信息
                await self._update_account_info()
                return True
            else:
                self._stats['connection_failed'] += 1
                logger.error(f"❌ MT5连接失败（进程隔离）: {self.account_id}")
                return False

        except Exception as e:
            self._stats['connection_failed'] += 1
            logger.error(f"连接异常: {self.account_id} - {e}")
            return False

    async def _update_account_info(self):
        """更新账户信息"""
        try:
            response = await self._process_manager.send_command_async(
                self.account_id, 'get_account_info'
            )
            if response.status == 'success' and response.data:
                self.account_info = response.data
                logger.info(f"账户信息已更新: {self.account_id}")
        except Exception as e:
            logger.warning(f"更新账户信息失败: {self.account_id} - {e}")

    async def disconnect(self) -> bool:
        """断开连接"""
        try:
            success = await self._process_manager.stop_terminal_process_new(self.account_id)
            if success:
                self.connected = False
                self.account_info = None
                self.connection_time = None
                logger.info(f"✅ MT5断开连接成功: {self.account_id}")
            return success
        except Exception as e:
            logger.error(f"断开连接异常: {self.account_id} - {e}")
            return False

    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._process_manager.is_account_connected(self.account_id)

    async def get_account_info(self) -> Optional[Dict[str, Any]]:
        """获取账户信息"""
        try:
            self._stats['commands_sent'] += 1
            response = await self._process_manager.send_command_async(
                self.account_id, 'get_account_info'
            )
            if response.status == 'success':
                self._stats['commands_success'] += 1
                return response.data
            else:
                self._stats['commands_failed'] += 1
                logger.error(f"获取账户信息失败: {response.error_message}")
                return None
        except Exception as e:
            self._stats['commands_failed'] += 1
            logger.error(f"获取账户信息异常: {e}")
            return None

    async def get_positions(self) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        try:
            self._stats['commands_sent'] += 1
            response = await self._process_manager.send_command_async(
                self.account_id, 'get_positions'
            )
            if response.status == 'success':
                self._stats['commands_success'] += 1
                return response.data or []
            else:
                self._stats['commands_failed'] += 1
                logger.error(f"获取持仓信息失败: {response.error_message}")
                return []
        except Exception as e:
            self._stats['commands_failed'] += 1
            logger.error(f"获取持仓信息异常: {e}")
            return []

    async def send_order(self, request: TradeRequest) -> TradeResult:
        """发送交易订单"""
        try:
            self._stats['commands_sent'] += 1

            # 转换为字典格式
            request_dict = {
                'action': request.action,
                'symbol': request.symbol,
                'volume': request.volume,
                'type': request.type,
                'price': request.price,
                'sl': request.sl,
                'tp': request.tp,
                'deviation': request.deviation,
                'magic': request.magic,
                'comment': request.comment
            }

            response = await self._process_manager.send_command_async(
                self.account_id, 'send_order', {'request': request_dict}
            )

            if response.status == 'success':
                self._stats['commands_success'] += 1
                data = response.data or {}
                return TradeResult(
                    retcode=data.get('retcode', 10020),
                    deal=data.get('deal', 0),
                    order=data.get('order', 0),
                    volume=data.get('volume', 0.0),
                    price=data.get('price', 0.0),
                    comment=data.get('comment', '')
                )
            else:
                self._stats['commands_failed'] += 1
                logger.error(f"发送订单失败: {response.error_message}")
                return TradeResult(retcode=10020, comment=response.error_message or "Unknown error")

        except Exception as e:
            self._stats['commands_failed'] += 1
            logger.error(f"发送订单异常: {e}")
            return TradeResult(retcode=10020, comment=f"异常: {str(e)}")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        process_status = self._process_manager.get_process_status(self.account_id)
        return {
            'client_stats': self._stats.copy(),
            'process_status': process_status.model_dump() if process_status else None,
            'connected': self.is_connected(),
            'connection_time': self.connection_time
        }

    # ==================== 兼容性方法 ====================
    # 保持与旧版本API的兼容性，但内部使用进程隔离

    async def close_position(self, position_id: int, volume: Optional[float] = None) -> TradeResult:
        """关闭持仓"""
        try:
            self._stats['commands_sent'] += 1

            params = {'position_id': position_id}
            if volume is not None:
                params['volume'] = volume

            response = await self._process_manager.send_command_async(
                self.account_id, 'close_position', params
            )

            if response.status == 'success':
                self._stats['commands_success'] += 1
                data = response.data or {}
                return TradeResult(
                    retcode=data.get('retcode', 10020),
                    deal=data.get('deal', 0),
                    order=data.get('order', 0),
                    volume=data.get('volume', 0.0),
                    price=data.get('price', 0.0),
                    comment=data.get('comment', '')
                )
            else:
                self._stats['commands_failed'] += 1
                return TradeResult(retcode=10020, comment=response.error_message or "Unknown error")

        except Exception as e:
            self._stats['commands_failed'] += 1
            logger.error(f"关闭持仓异常: {e}")
            return TradeResult(retcode=10020, comment=f"异常: {str(e)}")

    async def shutdown(self):
        """关闭客户端"""
        await self.disconnect()


# ==================== 工厂函数 ====================

def create_mt5_client(account_id: str, login: int, password: str,
                     server: str, terminal_path: Optional[str] = None) -> MT5Client:
    """
    创建MT5客户端的工厂函数
    自动使用进程隔离模式
    """
    return MT5Client(
        account_id=account_id,
        login=login,
        password=password,
        server=server,
        terminal_path=terminal_path
    )


# ==================== 向后兼容性 ====================


# 为了兼容性，保留旧的枚举和数据类
class TradeActionCompat(Enum):
    DEAL = mt5.TRADE_ACTION_DEAL if mt5 else 1
    PENDING = mt5.TRADE_ACTION_PENDING if mt5 else 5
    SLTP = mt5.TRADE_ACTION_SLTP if mt5 else 6
    MODIFY = mt5.TRADE_ACTION_MODIFY if mt5 else 7
    REMOVE = mt5.TRADE_ACTION_REMOVE if mt5 else 8

class OrderTypeCompat(Enum):
    BUY = mt5.ORDER_TYPE_BUY if mt5 else 0
    SELL = mt5.ORDER_TYPE_SELL if mt5 else 1
    BUY_LIMIT = mt5.ORDER_TYPE_BUY_LIMIT if mt5 else 2
    SELL_LIMIT = mt5.ORDER_TYPE_SELL_LIMIT if mt5 else 3
    BUY_STOP = mt5.ORDER_TYPE_BUY_STOP if mt5 else 4
    SELL_STOP = mt5.ORDER_TYPE_SELL_STOP if mt5 else 5

# 别名以保持兼容性
TradeActionLegacy = TradeActionCompat
OrderTypeLegacy = OrderTypeCompat