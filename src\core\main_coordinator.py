#!/usr/bin/env python3
"""
统一协调器 - 架构唯一权威实现
集成依赖注入容器和服务发现，消除复杂初始化依赖链
"""
import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Type, Set
from pathlib import Path

# 处理导入问题 - 支持相对导入和绝对导入

# 处理导入问题 - 支持相对导入和绝对导入
try:
    from .components import get_container, get_service_discovery
    from .service_container import ServiceContainer as Container, ServiceState
    from .service_discovery import ServiceDiscovery, ServiceEndpoint, ServiceType, ServiceInstance, ServiceStatus
    from .account_config_manager import AccountConfigManager
    from .process_manager import ProcessManager
    from ..messaging.queue_manager import QueueManager
    from ..messaging.message_router import MessageRouter
except ImportError:
    # 如果相对导入失败，创建Mock对象
    from unittest.mock import Mock
    from enum import Enum

    class ServiceState(Enum):
        STOPPED = "STOPPED"
        STARTING = "STARTING"
        RUNNING = "RUNNING"
        STOPPING = "STOPPING"

    class ServiceType(Enum):
        ACCOUNT_MONITOR = "ACCOUNT_MONITOR"
        TRADE_EXECUTOR = "TRADE_EXECUTOR"

    class ServiceStatus(Enum):
        HEALTHY = "HEALTHY"
        UNHEALTHY = "UNHEALTHY"

    # Mock对象
    get_container = Mock
    get_service_discovery = Mock
    Container = Mock
    ServiceDiscovery = Mock
    ServiceEndpoint = Mock
    ServiceInstance = Mock
    AccountConfigManager = Mock
    ProcessManager = Mock
    QueueManager = Mock
    MessageRouter = Mock

    # 继续处理其他导入
    try:
        from ..infrastructure.connection_pool import ConnectionPool
        from ..messaging.zerocopy import ZeroCopyMessageBus, MessageType as ZeroCopyMessageType
        from ..utils.memory_pool import UnifiedMemoryPool, get_unified_memory_pool
        from ..performance.processor import get_processor, ProcessorConfig
        from ..relationships.relationship_manager import RelationshipManager, get_relationship_manager
    except ImportError:
        # 如果这些导入也失败，创建更多Mock对象
        ConnectionPool = Mock
        ZeroCopyMessageBus = Mock
        ZeroCopyMessageType = Mock
        UnifiedMemoryPool = Mock
        get_unified_memory_pool = Mock
        get_processor = Mock
        ProcessorConfig = Mock
        RelationshipManager = Mock
        get_relationship_manager = Mock



logger = logging.getLogger(__name__)

class MainCoordinator:
    """
    主MT5协调器 - 架构唯一权威实现
    消除复杂初始化依赖链，实现优雅降级和动态重启
    """
    
    def __init__(self, host_id: str, config_path: str):
        self.host_id = host_id
        self.config_path = config_path
        
        # 核心组件
        self.container = get_container()
        self.service_discovery = get_service_discovery()
        
        # 状态管理
        self.running = False
        self.initialization_successful = False
        self.degraded_mode = False
        
        # 服务实例引用
        self.account_manager: Optional[AccountConfigManager] = None
        self.queue_manager: Optional[QueueManager] = None
        self.message_router: Optional[MessageRouter] = None
        self.connection_pool: Optional[ConnectionPool] = None
        
        self.process_manager: Optional[ProcessManager] = None
        self.enhanced_processor = None
        self.relationship_manager: Optional[RelationshipManager] = None
        
        # 本地账户信息
        self.local_accounts: Dict[str, Dict] = {}
        
        logger.info(f"🎯 主MT5协调器已创建 - 主机: {host_id}")
    
    async def initialize(self, allow_partial_failure: bool = True) -> bool:
        """
        统一初始化流程 - 消除复杂依赖链
        """
        logger.info("🚀 开始统一初始化流程")
        start_time = time.time()
        
        try:
            # 1. 注册所有服务到容器
            await self._register_services()
            
            # 2. 启动服务发现
            await self.service_discovery.start()
            
            # 3. 异步初始化所有服务
            initialization_results = await self.container.initialize_all(allow_partial_failure)
            
            # 4. 分析初始化结果
            success_count = sum(1 for r in initialization_results.values() if r.success)
            total_count = len(initialization_results)
            
            if success_count == 0:
                logger.error("所有服务初始化失败，无法启动系统")
                return False
            
            if success_count < total_count:
                logger.warning(f"⚠️ 部分服务初始化失败 ({success_count}/{total_count})，进入降级模式")
                self.degraded_mode = True
            else:
                logger.info(f"所有服务初始化成功 ({success_count}/{total_count})")
            
            # 5. 获取服务实例
            await self._get_service_instances()
            
            # 6. 发现和启动本地账户
            await self._discover_and_start_accounts()
            
            # 7. 注册服务到服务发现
            await self._register_to_service_discovery()
            
            # 8. 启动健康监控
            await self._start_health_monitoring()
            
            self.initialization_successful = True
            total_time = time.time() - start_time
            
            logger.info(f"统一初始化完成 - 用时 {total_time:.2f}s, 降级模式: {self.degraded_mode}")
            return True
            
        except Exception as e:
            logger.error(f"统一初始化失败: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return False
    
    async def _register_services(self):
        """注册所有服务到依赖注入容器"""
        logger.info("📦 注册服务到依赖注入容器")
        
        # 注册账户配置管理器
        self.container.register_singleton(
            AccountConfigManager,
            factory=lambda: AccountConfigManager(
                config_dir=str(Path(self.config_path).parent.parent)
            ),
            health_check=self._health_check_account_manager,
            required=True,
            initialization_order=1
        )
        
        # 注册消息队列管理器
        self.container.register_singleton(
            QueueManager,
            factory=self._create_queue_manager,
            health_check=self._health_check_queue_manager,
            fallback_factory=self._create_fallback_queue_manager,
            required=False,  # 可以降级到本地模式
            initialization_order=2
        )
        
        # 注册消息路由器
        self.container.register_singleton(
            MessageRouter,
            factory=self._create_message_router,
            dependencies={QueueManager},
            health_check=self._health_check_message_router,
            required=False,
            initialization_order=3
        )
        
        # 注册连接池
        self.container.register_singleton(
            ConnectionPool,
            factory=self._create_connection_pool,
            health_check=self._health_check_connection_pool,
            required=True,
            initialization_order=4
        )

        # 注册零拷贝消息总线
        self.container.register_singleton(
            ZeroCopyMessageBus,
            factory=self._create_zero_copy_bus,
            health_check=self._health_check_zero_copy_bus,
            required=False, # 可以降级
            initialization_order=3
        )

        # 注册统一内存池
        self.container.register_singleton(
            UnifiedMemoryPool,
            factory=get_unified_memory_pool,
            health_check=self._health_check_memory_pool,
            required=True,
            initialization_order=0 # 最先初始化
        )
        
        self.container.register_singleton(
            ProcessManager,
            factory=lambda: ProcessManager(self.config_path),
            health_check=self._health_check_process_manager,
            required=True,
            initialization_order=5
        )
        
        # 注册增强性能处理器（统一性能优化解决方案）
        self.container.register_singleton(
            "enhanced_processor",
            factory=self._create_enhanced_processor,
            health_check=self._health_check_enhanced_processor,
            required=False,  # 可选的性能优化
            initialization_order=6
        )
        
        # 注册关系管理器
        self.container.register_singleton(
            RelationshipManager,
            factory=self._create_relationship_manager,
            health_check=self._health_check_relationship_manager,
            required=False,  # 复制交易是可选功能
            initialization_order=7
        )

        logger.info("服务注册完成")
    
    async def _create_queue_manager(self) -> QueueManager:
        """创建消息队列管理器"""
        try:
            # 从配置加载NATS配置
            config = self._load_messaging_config()
            
            queue_manager = QueueManager(config)
            success = await queue_manager.initialize()
            
            if not success:
                raise RuntimeError("消息队列管理器初始化失败")
            
            return queue_manager
            
        except Exception as e:
            logger.error(f"创建消息队列管理器失败: {e}")
            raise
    
    async def _create_fallback_queue_manager(self) -> QueueManager:
        """创建回退消息队列管理器（本地模式）"""
        logger.info("🔄 创建回退消息队列管理器（本地模式）")
        
        # 本地模式配置
        local_config = {
            'primary_backend': 'local',
            'enable_dual_write': False,
            'local_fallback': True
        }
        
        queue_manager = QueueManager(local_config)
        await queue_manager.initialize()
        
        return queue_manager
    
    async def _create_message_router(self) -> MessageRouter:
        """创建消息路由器"""
        config = self._load_messaging_config()
        
        router = MessageRouter(
            config=config,
            host_id=self.host_id
        )
        
        # 注入队列管理器
        queue_manager = await self.container.get(QueueManager)
        if queue_manager:
            router.queue_manager = queue_manager
        
        await router.start()
        return router
    
    async def _create_connection_pool(self) -> ConnectionPool:
        """创建连接池"""
        pool = ConnectionPool(max_connections=20)
        
        # 设置连接工厂
        pool.set_connection_factory(self._create_mt5_connection)
        pool.set_connection_validator(self._validate_mt5_connection)
        
        await pool.start()
        return pool
    
    async def _create_mt5_connection(self, account_id: str, server: str, login: int):
        """创建MT5连接"""
        # 这里实现实际的MT5连接创建逻辑
        # 返回MT5连接对象
        logger.debug(f"创建MT5连接: {account_id} - {login}@{server}")
        
        # 模拟连接创建
        class MockMT5Connection:
            def __init__(self, account_id, server, login):
                self.account_id = account_id
                self.server = server
                self.login = login
                self.connected = True
            
            async def close(self):
                self.connected = False
        
        return MockMT5Connection(account_id, server, login)
    
    async def _validate_mt5_connection(self, connection) -> bool:
        """验证MT5连接"""
        return hasattr(connection, 'connected') and connection.connected

    async def _create_zero_copy_bus(self) -> ZeroCopyMessageBus:
        """创建零拷贝消息总线"""
        bus = ZeroCopyMessageBus()
        await bus.start()
        return bus

    async def _health_check_zero_copy_bus(self, instance) -> bool:
        """零拷贝消息总线健康检查"""
        return instance.running

    async def _health_check_memory_pool(self, instance) -> bool:
        """内存池健康检查"""
        stats = instance.get_performance_stats()
        return stats['global_stats']['total_memory'] > 0
    
    def _load_messaging_config(self) -> Dict[str, Any]:
        """加载消息配置"""
        # 这里应该从配置文件加载实际配置
        return {
            'host_id': self.host_id,
            'nats_servers': ['nats://localhost:4222'],
            'redis_url': 'redis://localhost:6379'
        }
    
    async def _get_service_instances(self):
        """获取服务实例"""
        self.account_manager = await self.container.get(AccountConfigManager)
        self.queue_manager = await self.container.get(QueueManager)
        self.message_router = await self.container.get(MessageRouter)
        self.connection_pool = await self.container.get(ConnectionPool)
        self.zero_copy_bus = await self.container.get(ZeroCopyMessageBus)
        self.memory_pool = await self.container.get(UnifiedMemoryPool)
        self.process_manager = await self.container.get(ProcessManager)
        
        # 获取可选服务
        self.enhanced_processor = await self.container.get_optional("enhanced_processor")
        self.relationship_manager = await self.container.get_optional(RelationshipManager)
        
        logger.info("服务实例获取完成")
    
    async def _discover_and_start_accounts(self):
        """发现和启动本地账户"""
        if not self.account_manager:
            logger.warning("⚠️ 账户管理器不可用，跳过账户启动")
            return
        
        try:
            # 发现本地账户
            all_accounts = self.account_manager.load_all_accounts()
            
            for account_id, account_config in all_accounts.items():
                if account_config.host_id == self.host_id or not account_config.host_id:
                    self.local_accounts[account_id] = {
                        'login': account_config.login,
                        'server': account_config.server,
                        'terminal_path': account_config.terminal_path,
                        'config': account_config
                    }
            
            logger.info(f"发现本地账户: {len(self.local_accounts)} 个")
            
            # 启动账户进程 - 确保监听器和执行器在独立进程中运行
            if self.process_manager:
                for account_id, account_data in self.local_accounts.items():
                    # 为每个账户创建独立的进程容器
                    process_config = {
                        **account_data['config'].__dict__,
                        'enable_monitor': True,  # 启用监听器
                        'enable_executor': True,  # 启用执行器
                        'zero_copy_enabled': True,  # 启用零拷贝通信
                        'memory_pool_size': 16 * 1024 * 1024  # 16MB per process
                    }
                    
                    # 启动MT5进程，内部会创建监听器和执行器
                    pid = await self.process_manager.start_terminal_process_new(account_id, process_config)
                    
                    if pid:
                        # 注册到服务发现
                        await self._register_mt5_process_to_discovery(account_id, pid)
                        
                        # 设置本地零拷贝通道
                        await self._setup_local_zero_copy_channel(account_id)
            
        except Exception as e:
            logger.error(f"账户发现失败: {e}")
    
    async def _register_to_service_discovery(self):
        """注册到服务发现"""
        try:
            # 注册协调器服务
            coordinator_endpoint = ServiceEndpoint(
                host=self.host_id,
                port=8000,  # 从配置获取
                protocol="http"
            )
            
            await self.service_discovery.register_service(
                service_name="mt5-coordinator",
                endpoint=coordinator_endpoint,
                service_type=ServiceType.CORE,
                metadata={
                    'host_id': self.host_id,
                    'account_count': len(self.local_accounts),
                    'degraded_mode': self.degraded_mode
                },
                tags={'coordinator', 'mt5'},
                health_checker=self._health_check_coordinator
            )
            
            logger.info("已注册到服务发现")
            
        except Exception as e:
            logger.error(f"服务发现注册失败: {e}")
    
    async def _start_health_monitoring(self):
        """启动健康监控"""
        # 添加服务变更监听器
        self.service_discovery.add_service_change_listener(self._on_service_change)
        
        # 定期执行容器健康检查
        asyncio.create_task(self._container_health_monitor())
        
        logger.info("健康监控已启动")
    
    async def _container_health_monitor(self):
        """容器健康监控循环"""
        while self.running:
            try:
                # 执行所有服务的健康检查
                health_results = await self.container.health_check_all()
                
                unhealthy_services = [
                    service_type.__name__ for service_type, is_healthy in health_results.items()
                    if not is_healthy
                ]
                
                if unhealthy_services:
                    logger.warning(f"⚠️ 检测到不健康服务: {unhealthy_services}")
                    
                    # TODO: 实现自动重启逻辑
                
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"容器健康监控异常: {e}")
                await asyncio.sleep(10)
    
    async def _on_service_change(self, event_type: str, service):
        """服务变更处理"""
        logger.info(f"🔄 服务变更: {event_type} - {service.service_name}")
        
        if event_type == 'restart_required':
            # 实现服务重启逻辑
            if service.service_name.startswith('mt5-'):
                # 提取账户ID
                account_id = service.metadata.get('account_id')
                if account_id and self.process_manager:
                    logger.info(f"🔄 重启MT5进程: {account_id}")
                    await self.process_manager.restart_terminal_process(account_id)
    
    # 健康检查方法
    async def _health_check_account_manager(self, instance) -> bool:
        """账户管理器健康检查"""
        try:
            accounts = instance.load_all_accounts()
            return len(accounts) > 0
        except:
            return False
    
    async def _health_check_queue_manager(self, instance) -> bool:
        """队列管理器健康检查"""
        try:
            return hasattr(instance, 'is_healthy') and await instance.is_healthy()
        except:
            return False
    
    async def _health_check_message_router(self, instance) -> bool:
        """消息路由器健康检查"""
        try:
            return hasattr(instance, 'is_running') and instance.is_running
        except:
            return False
    
    async def _health_check_connection_pool(self, instance) -> bool:
        """连接池健康检查"""
        try:
            stats = await instance.get_stats()
            return stats['total_connections'] >= 0
        except:
            return False

    async def _health_check_process_manager(self, instance) -> bool:
        """MT5进程管理器健康检查"""
        return instance.running
    
    async def _health_check_coordinator(self, service) -> bool:
        """协调器健康检查"""
        return self.running and self.initialization_successful
    
    async def start(self):
        """启动协调器"""
        if self.running:
            logger.warning("协调器已在运行")
            return
        
        logger.info("🚀 启动统一MT5协调器")
        
        # 初始化所有组件
        success = await self.initialize(allow_partial_failure=True)
        
        if not success:
            logger.error("协调器初始化失败")
            return False
        
        self.running = True
        
        logger.info(f"统一MT5协调器启动成功 - 降级模式: {self.degraded_mode}")
        return True
    
    async def stop(self):
        """停止协调器"""
        if not self.running:
            return
        
        logger.info("🛑 停止统一MT5协调器")
        self.running = False
        
        try:
            # 停止服务发现
            await self.service_discovery.stop()
            
            # 关闭所有服务
            await self.container.shutdown_all()
            
            logger.info("统一MT5协调器已停止")
            
        except Exception as e:
            logger.error(f"协调器停止异常: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        # 获取容器状态
        container_report = self.container.get_initialization_report()
        
        # 获取服务发现状态
        service_report = await self.service_discovery.get_service_status_report()
        
        return {
            'host_id': self.host_id,
            'running': self.running,
            'initialization_successful': self.initialization_successful,
            'degraded_mode': self.degraded_mode,
            'local_accounts_count': len(self.local_accounts),
            'container_status': container_report,
            'service_discovery_status': service_report,
            'components': {
                'account_manager': self.account_manager is not None,
                'queue_manager': self.queue_manager is not None,
                'message_router': self.message_router is not None,
                'connection_pool': self.connection_pool is not None,
                'zero_copy_bus': self.zero_copy_bus is not None,
                'memory_pool': self.memory_pool is not None
            }
        }
    
    async def restart_service(self, service_type: Type) -> bool:
        """动态重启服务"""
        logger.info(f"🔄 重启服务: {service_type.__name__}")
        
        try:
            # 停止服务监控
            service_id = f"{service_type.__name__}"
            
            # 重新初始化服务
            result = await self.container._initialize_service(service_type)
            
            if result.success:
                logger.info(f"服务重启成功: {service_type.__name__}")
                return True
            else:
                logger.error(f"服务重启失败: {service_type.__name__} - {result.error}")
                return False
                
        except Exception as e:
            logger.error(f"服务重启异常: {service_type.__name__} - {e}")
            return False
    
    async def _register_mt5_process_to_discovery(self, account_id: str, pid: int):
        """注册MT5进程到服务发现"""
        try:
            # 注册监听器服务
            monitor_endpoint = ServiceEndpoint(
                host=self.host_id,
                port=20000 + pid % 1000,  # 动态分配端口
                protocol="tcp"
            )
            
            await self.service_discovery.register_service(
                service_name=f"mt5-monitor-{account_id}",
                endpoint=monitor_endpoint,
                service_type=ServiceType.CORE,
                metadata={
                    'account_id': account_id,
                    'process_pid': pid,
                    'component_type': 'monitor'
                },
                tags={'mt5', 'monitor', account_id}
            )
            
            # 注册执行器服务
            executor_endpoint = ServiceEndpoint(
                host=self.host_id,
                port=21000 + pid % 1000,  # 动态分配端口
                protocol="tcp"
            )
            
            await self.service_discovery.register_service(
                service_name=f"mt5-executor-{account_id}",
                endpoint=executor_endpoint,
                service_type=ServiceType.CORE,
                metadata={
                    'account_id': account_id,
                    'process_pid': pid,
                    'component_type': 'executor'
                },
                tags={'mt5', 'executor', account_id}
            )
            
            logger.info(f"MT5进程组件已注册: {account_id} (PID: {pid})")
            
        except Exception as e:
            logger.error(f"MT5进程注册失败: {account_id} - {e}")
    
    async def _setup_local_zero_copy_channel(self, account_id: str):
        """设置本地零拷贝通道"""
        try:
            if not self.zero_copy_bus:
                logger.warning(f"⚠️ 零拷贝消息总线不可用: {account_id}")
                return
            
            # 为监听器到执行器设置专用通道
            channel_name = f"monitor_executor_{account_id}"
            
            # 订阅交易信号
            await self.zero_copy_bus.subscribe(
                ZeroCopyMessageType.TRADE_SIGNAL,
                lambda header, payload: self._handle_local_trade_signal(account_id, header, payload)
            )
            
            logger.info(f"零拷贝通道已设置: {channel_name}")
            
        except Exception as e:
            logger.error(f"设置零拷贝通道失败: {account_id} - {e}")
    
    async def _handle_local_trade_signal(self, account_id: str, header, payload):
        """处理本地交易信号"""
        # 这里只是日志记录，实际处理在执行器进程中
        logger.debug(f"本地交易信号: {account_id} - {header.sequence_id}")
    
    
    async def _create_enhanced_processor(self):
        """创建增强性能处理器"""
        try:
            config = PerformanceConfig(
                use_protobuf=True,
                compression_enabled=True,
                use_priority_queue=True,
                queue_worker_count=6,
                use_connection_pool=False,  # 分布式多进程架构下禁用连接池
                batch_enabled=True,
                batch_size=100,
                batch_timeout_ms=50,
                use_unified_memory=True,
                memory_pool_size=64 * 1024 * 1024,
                enable_trade_matching=True,
                enable_trade_validation=True,
                validation_timeout=5.0,
                cache_enabled=True,
                cache_ttl=300,
                enable_performance_monitoring=True,
                stats_interval=30.0
            )
            
            processor = await get_enhanced_processor(config)
            
            # 注入已有的组件
            memory_pool = await self.container.get_optional(UnifiedMemoryPool)
            priority_queue = await self.container.get_optional('PriorityQueue')
            # 不再注入连接池（分布式架构中不需要）
            state_manager = await self.container.get_optional('StateManager')
            
            # 重新初始化以注入组件
            await processor.initialize(memory_pool, priority_queue, None, state_manager)
            
            logger.info("增强性能处理器创建成功")
            return processor
            
        except Exception as e:
            logger.error(f"创建增强性能处理器失败: {e}")
            raise
    
    async def _create_relationship_manager(self) -> RelationshipManager:
        """创建关系管理器"""
        try:
            # 使用全局实例
            manager = get_relationship_manager()
            await manager.initialize()
            
            logger.info("关系管理器创建成功")
            return manager
            
        except Exception as e:
            logger.error(f"创建关系管理器失败: {e}")
            raise
    
    async def _health_check_enhanced_processor(self, processor) -> bool:
        """增强性能处理器健康检查"""
        try:
            return processor.running if processor else False
        except:
            return False
    
    async def _health_check_relationship_manager(self, manager: RelationshipManager) -> bool:
        """关系管理器健康检查"""
        try:
            stats = manager.get_stats()
            return stats is not None
        except:
            return False