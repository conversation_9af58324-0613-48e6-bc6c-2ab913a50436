#!/usr/bin/env python3
"""
统一系统组件 - SSOT
提供全局唯一的系统组件实例
"""
import asyncio
from contextlib import asynccontextmanager
from typing import Optional

from service_container import ServiceContainer as Container
from service_discovery import ServiceDiscovery

# 全局实例
_global_container: Optional[Container] = None
_global_service_discovery: Optional[ServiceDiscovery] = None

def get_container() -> Container:
    """获取全局容器实例"""
    global _global_container
    if _global_container is None:
        _global_container = Container()
    return _global_container

def get_service_discovery() -> ServiceDiscovery:
    """获取全局服务发现实例"""
    global _global_service_discovery
    if _global_service_discovery is None:
        _global_service_discovery = ServiceDiscovery()
    return _global_service_discovery

def reset_container():
    """重置容器（主要用于测试）"""
    global _global_container
    _global_container = None

def reset_service_discovery():
    """重置服务发现（主要用于测试）"""
    global _global_service_discovery
    _global_service_discovery = None

@asynccontextmanager
async def container_scope():
    """容器作用域上下文管理器"""
    container = get_container()
    try:
        yield container
    finally:
        await container.shutdown_all()
