#!/usr/bin/env python3
"""
统一服务发现和健康检查机制 - 架构唯一权威实现
支持组件热插拔和动态重启
硬迁移：零向后兼容，强制统一，SSOT
"""
import asyncio
import json
import time
import logging
from typing import Dict, Any, Optional, List, Set, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import socket
import uuid
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    """服务状态"""
    UNKNOWN = "unknown"
    STARTING = "starting"
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"
    STOPPING = "stopping"
    STOPPED = "stopped"

class ServiceType(Enum):
    """服务类型"""
    CORE = "core"
    MESSAGING = "messaging"
    DATABASE = "database"
    CACHE = "cache"
    EXTERNAL = "external"
    MONITORING = "monitoring"

@dataclass
class ServiceEndpoint:
    """服务端点"""
    host: str
    port: int
    protocol: str = "http"
    path: str = ""
    
    @property
    def url(self) -> str:
        return f"{self.protocol}://{self.host}:{self.port}{self.path}"

@dataclass
class ServiceInstance:
    """服务实例"""
    service_id: str
    service_name: str
    service_type: ServiceType
    endpoint: ServiceEndpoint
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: Set[str] = field(default_factory=set)
    status: ServiceStatus = ServiceStatus.UNKNOWN
    last_heartbeat: float = field(default_factory=time.time)
    health_check_url: Optional[str] = None
    health_check_interval: float = 30.0
    health_check_timeout: float = 5.0
    registration_time: float = field(default_factory=time.time)
    failure_count: int = 0
    max_failures: int = 3

@dataclass
class HealthCheckResult:
    """健康检查结果"""
    service_id: str
    status: ServiceStatus
    response_time: float
    error: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)

class ServiceRegistry(ABC):
    """服务注册表抽象基类"""
    
    @abstractmethod
    async def register(self, service: ServiceInstance) -> bool:
        """注册服务"""
        pass
    
    @abstractmethod
    async def unregister(self, service_id: str) -> bool:
        """注销服务"""
        pass
    
    @abstractmethod
    async def discover(self, service_name: str, tags: Set[str] = None) -> List[ServiceInstance]:
        """发现服务"""
        pass
    
    @abstractmethod
    async def get_all_services(self) -> Dict[str, ServiceInstance]:
        """获取所有服务"""
        pass

class InMemoryServiceRegistry(ServiceRegistry):
    """内存服务注册表"""
    
    def __init__(self):
        self._services: Dict[str, ServiceInstance] = {}
        self._services_by_name: Dict[str, Set[str]] = {}
        self._lock = asyncio.Lock()
    
    async def register(self, service: ServiceInstance) -> bool:
        """注册服务"""
        async with self._lock:
            try:
                self._services[service.service_id] = service
                
                if service.service_name not in self._services_by_name:
                    self._services_by_name[service.service_name] = set()
                
                self._services_by_name[service.service_name].add(service.service_id)
                
                logger.info(f"✅ 服务注册成功: {service.service_name} ({service.service_id})")
                return True
                
            except Exception as e:
                logger.error(f"❌ 服务注册失败: {service.service_id} - {e}")
                return False
    
    async def unregister(self, service_id: str) -> bool:
        """注销服务"""
        async with self._lock:
            try:
                service = self._services.pop(service_id, None)
                if service:
                    if service.service_name in self._services_by_name:
                        self._services_by_name[service.service_name].discard(service_id)
                        
                        if not self._services_by_name[service.service_name]:
                            del self._services_by_name[service.service_name]
                    
                    logger.info(f"✅ 服务注销成功: {service.service_name} ({service_id})")
                    return True
                else:
                    logger.warning(f"⚠️ 服务不存在: {service_id}")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ 服务注销失败: {service_id} - {e}")
                return False
    
    async def discover(self, service_name: str, tags: Set[str] = None) -> List[ServiceInstance]:
        """发现服务"""
        async with self._lock:
            try:
                service_ids = self._services_by_name.get(service_name, set())
                services = []
                
                for service_id in service_ids:
                    service = self._services.get(service_id)
                    if service:
                        # 标签过滤
                        if tags and not tags.issubset(service.tags):
                            continue
                        
                        # 只返回健康的服务
                        if service.status in [ServiceStatus.HEALTHY, ServiceStatus.DEGRADED]:
                            services.append(service)
                
                return services
                
            except Exception as e:
                logger.error(f"❌ 服务发现失败: {service_name} - {e}")
                return []
    
    async def get_all_services(self) -> Dict[str, ServiceInstance]:
        """获取所有服务"""
        async with self._lock:
            return self._services.copy()

class ServiceDiscovery:
    """
    服务发现管理器 - 架构唯一权威实现
    消除服务依赖的复杂性，实现动态服务管理
    """
    
    def __init__(self, registry: ServiceRegistry = None):
        self._registry = registry or InMemoryServiceRegistry()
        self._health_checkers: Dict[str, Callable] = {}
        self._monitoring_tasks: Dict[str, asyncio.Task] = {}
        self._running = False
        
        # 健康检查配置
        self._default_health_check_interval = 30.0
        self._default_health_check_timeout = 5.0
        self._default_max_failures = 3
        
        # 服务变更监听器
        self._service_change_listeners: List[Callable] = []
        
        # 线程池用于阻塞性健康检查
        self._thread_pool = ThreadPoolExecutor(max_workers=8, thread_name_prefix="health-check")
        
        logger.info("🔍 服务发现管理器已创建")
    
    async def start(self):
        """启动服务发现"""
        if self._running:
            logger.warning("服务发现已在运行")
            return
        
        self._running = True
        
        # 启动健康检查监控
        self._monitoring_task = asyncio.create_task(self._health_monitoring_loop())
        
        logger.info("🚀 服务发现已启动")
    
    async def stop(self):
        """停止服务发现"""
        if not self._running:
            return
        
        self._running = False
        
        # 停止健康检查监控
        if hasattr(self, '_monitoring_task') and self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        # 停止所有服务监控任务
        for task in self._monitoring_tasks.values():
            task.cancel()
        
        if self._monitoring_tasks:
            await asyncio.gather(*self._monitoring_tasks.values(), return_exceptions=True)
        
        self._monitoring_tasks.clear()
        
        # 关闭线程池
        self._thread_pool.shutdown(wait=True)
        
        logger.info("🛑 服务发现已停止")
    
    async def register_service(self,
                              service_name: str,
                              endpoint: ServiceEndpoint,
                              service_type: ServiceType = ServiceType.CORE,
                              metadata: Dict[str, Any] = None,
                              tags: Set[str] = None,
                              health_check_url: str = None,
                              health_check_interval: float = None,
                              health_checker: Callable = None) -> str:
        """
        注册服务
        
        Returns:
            服务ID
        """
        service_id = f"{service_name}-{uuid.uuid4().hex[:8]}"
        
        service = ServiceInstance(
            service_id=service_id,
            service_name=service_name,
            service_type=service_type,
            endpoint=endpoint,
            metadata=metadata or {},
            tags=tags or set(),
            health_check_url=health_check_url,
            health_check_interval=health_check_interval or self._default_health_check_interval
        )
        
        # 注册到注册表
        success = await self._registry.register(service)
        if not success:
            raise RuntimeError(f"服务注册失败: {service_name}")
        
        # 注册健康检查器
        if health_checker:
            self._health_checkers[service_id] = health_checker
        
        # 启动健康检查监控
        if self._running:
            await self._start_service_monitoring(service_id)
        
        # 通知监听器
        await self._notify_service_change('registered', service)
        
        logger.info(f"✅ 服务注册成功: {service_name} ({service_id})")
        return service_id
    
    async def unregister_service(self, service_id: str) -> bool:
        """注销服务"""
        # 获取服务信息
        all_services = await self._registry.get_all_services()
        service = all_services.get(service_id)
        
        # 停止监控
        await self._stop_service_monitoring(service_id)
        
        # 从注册表移除
        success = await self._registry.unregister(service_id)
        
        # 清理健康检查器
        self._health_checkers.pop(service_id, None)
        
        # 通知监听器
        if service:
            await self._notify_service_change('unregistered', service)
        
        return success
    
    async def discover_services(self, service_name: str, tags: Set[str] = None) -> List[ServiceInstance]:
        """发现服务"""
        return await self._registry.discover(service_name, tags)
    
    async def get_healthy_service(self, service_name: str, tags: Set[str] = None) -> Optional[ServiceInstance]:
        """获取一个健康的服务实例（负载均衡）"""
        services = await self.discover_services(service_name, tags)
        
        if not services:
            return None
        
        # 简单轮询负载均衡
        healthy_services = [s for s in services if s.status == ServiceStatus.HEALTHY]
        
        if not healthy_services:
            # 如果没有完全健康的，尝试降级服务
            degraded_services = [s for s in services if s.status == ServiceStatus.DEGRADED]
            if degraded_services:
                return degraded_services[0]
            return None
        
        # 选择失败次数最少的服务
        return min(healthy_services, key=lambda s: s.failure_count)
    
    async def check_service_health(self, service_id: str) -> HealthCheckResult:
        """检查单个服务健康状态"""
        all_services = await self._registry.get_all_services()
        service = all_services.get(service_id)
        
        if not service:
            return HealthCheckResult(
                service_id=service_id,
                status=ServiceStatus.UNKNOWN,
                response_time=0.0,
                error="服务不存在"
            )
        
        start_time = time.time()
        
        try:
            # 使用自定义健康检查器
            if service_id in self._health_checkers:
                health_checker = self._health_checkers[service_id]
                
                if asyncio.iscoroutinefunction(health_checker):
                    is_healthy = await health_checker(service)
                else:
                    is_healthy = await asyncio.get_event_loop().run_in_executor(
                        self._thread_pool,
                        health_checker,
                        service
                    )
                
                response_time = time.time() - start_time
                
                if is_healthy:
                    return HealthCheckResult(
                        service_id=service_id,
                        status=ServiceStatus.HEALTHY,
                        response_time=response_time
                    )
                else:
                    return HealthCheckResult(
                        service_id=service_id,
                        status=ServiceStatus.UNHEALTHY,
                        response_time=response_time,
                        error="健康检查失败"
                    )
            
            # 使用HTTP健康检查
            elif service.health_check_url:
                return await self._http_health_check(service)
            
            # 使用端口可达性检查
            else:
                return await self._port_health_check(service)
                
        except Exception as e:
            response_time = time.time() - start_time
            
            return HealthCheckResult(
                service_id=service_id,
                status=ServiceStatus.UNHEALTHY,
                response_time=response_time,
                error=str(e)
            )
    
    async def _http_health_check(self, service: ServiceInstance) -> HealthCheckResult:
        """HTTP健康检查"""
        import aiohttp
        
        start_time = time.time()
        
        try:
            timeout = aiohttp.ClientTimeout(total=service.health_check_timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(service.health_check_url) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        try:
                            data = await response.json()
                            return HealthCheckResult(
                                service_id=service.service_id,
                                status=ServiceStatus.HEALTHY,
                                response_time=response_time,
                                details=data
                            )
                        except:
                            return HealthCheckResult(
                                service_id=service.service_id,
                                status=ServiceStatus.HEALTHY,
                                response_time=response_time
                            )
                    else:
                        return HealthCheckResult(
                            service_id=service.service_id,
                            status=ServiceStatus.UNHEALTHY,
                            response_time=response_time,
                            error=f"HTTP {response.status}"
                        )
                        
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            return HealthCheckResult(
                service_id=service.service_id,
                status=ServiceStatus.UNHEALTHY,
                response_time=response_time,
                error="健康检查超时"
            )
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheckResult(
                service_id=service.service_id,
                status=ServiceStatus.UNHEALTHY,
                response_time=response_time,
                error=str(e)
            )
    
    async def _port_health_check(self, service: ServiceInstance) -> HealthCheckResult:
        """端口可达性健康检查"""
        start_time = time.time()
        
        try:
            # 尝试连接端口
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(service.endpoint.host, service.endpoint.port),
                timeout=service.health_check_timeout
            )
            
            writer.close()
            await writer.wait_closed()
            
            response_time = time.time() - start_time
            
            return HealthCheckResult(
                service_id=service.service_id,
                status=ServiceStatus.HEALTHY,
                response_time=response_time
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            
            return HealthCheckResult(
                service_id=service.service_id,
                status=ServiceStatus.UNHEALTHY,
                response_time=response_time,
                error=str(e)
            )
    
    async def _start_service_monitoring(self, service_id: str):
        """启动服务监控"""
        if service_id in self._monitoring_tasks:
            return
        
        task = asyncio.create_task(self._service_health_monitor(service_id))
        self._monitoring_tasks[service_id] = task
        
        logger.debug(f"🔍 启动服务监控: {service_id}")
    
    async def _stop_service_monitoring(self, service_id: str):
        """停止服务监控"""
        task = self._monitoring_tasks.pop(service_id, None)
        if task:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            
            logger.debug(f"🛑 停止服务监控: {service_id}")
    
    async def _service_health_monitor(self, service_id: str):
        """服务健康监控循环"""
        while self._running:
            try:
                all_services = await self._registry.get_all_services()
                service = all_services.get(service_id)
                
                if not service:
                    break
                
                # 执行健康检查
                health_result = await self.check_service_health(service_id)
                
                # 更新服务状态
                old_status = service.status
                service.status = health_result.status
                service.last_heartbeat = time.time()
                
                if health_result.status == ServiceStatus.UNHEALTHY:
                    service.failure_count += 1
                else:
                    service.failure_count = 0
                
                # 如果状态发生变化，通知监听器
                if old_status != service.status:
                    await self._notify_service_change('status_changed', service)
                    
                    logger.info(f"🔄 服务状态变化: {service.service_name} ({service_id}) "
                              f"{old_status.value} → {service.status.value}")
                
                # 如果失败次数过多，考虑重启
                if (service.failure_count >= service.max_failures and 
                    service.status == ServiceStatus.UNHEALTHY):
                    
                    await self._attempt_service_restart(service)
                
                # 等待下次检查
                await asyncio.sleep(service.health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 服务健康监控异常: {service_id} - {e}")
                await asyncio.sleep(5)  # 错误时短暂等待
    
    async def _attempt_service_restart(self, service: ServiceInstance):
        """尝试重启服务"""
        logger.warning(f"🔄 尝试重启失败服务: {service.service_name} ({service.service_id})")
        
        try:
            # 通知监听器服务需要重启
            await self._notify_service_change('restart_required', service)
            
            # 重置失败计数
            service.failure_count = 0
            service.status = ServiceStatus.STARTING
            
        except Exception as e:
            logger.error(f"❌ 服务重启失败: {service.service_id} - {e}")
    
    async def _health_monitoring_loop(self):
        """全局健康监控循环"""
        logger.info("🔍 启动全局健康监控")
        
        while self._running:
            try:
                # 获取所有服务
                all_services = await self._registry.get_all_services()
                
                # 为新服务启动监控
                for service_id in all_services:
                    if service_id not in self._monitoring_tasks:
                        await self._start_service_monitoring(service_id)
                
                # 清理已注销服务的监控任务
                to_remove = []
                for service_id in self._monitoring_tasks:
                    if service_id not in all_services:
                        to_remove.append(service_id)
                
                for service_id in to_remove:
                    await self._stop_service_monitoring(service_id)
                
                # 每分钟检查一次
                await asyncio.sleep(60)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 全局健康监控异常: {e}")
                await asyncio.sleep(10)
        
        logger.info("🛑 全局健康监控停止")
    
    def add_service_change_listener(self, listener: Callable):
        """添加服务变更监听器"""
        self._service_change_listeners.append(listener)
    
    async def _notify_service_change(self, event_type: str, service: ServiceInstance):
        """通知服务变更"""
        for listener in self._service_change_listeners:
            try:
                if asyncio.iscoroutinefunction(listener):
                    await listener(event_type, service)
                else:
                    await asyncio.get_event_loop().run_in_executor(
                        self._thread_pool,
                        listener,
                        event_type,
                        service
                    )
            except Exception as e:
                logger.error(f"❌ 服务变更监听器异常: {e}")
    
    async def get_service_status_report(self) -> Dict[str, Any]:
        """获取服务状态报告"""
        all_services = await self._registry.get_all_services()
        
        status_counts = {}
        service_types = {}
        
        for service in all_services.values():
            # 统计状态
            status = service.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            
            # 统计类型
            service_type = service.service_type.value
            service_types[service_type] = service_types.get(service_type, 0) + 1
        
        total_services = len(all_services)
        healthy_services = status_counts.get('healthy', 0)
        unhealthy_services = status_counts.get('unhealthy', 0)
        
        return {
            'total_services': total_services,
            'healthy_services': healthy_services,
            'unhealthy_services': unhealthy_services,
            'health_rate': healthy_services / total_services * 100 if total_services > 0 else 0,
            'status_distribution': status_counts,
            'service_type_distribution': service_types,
            'monitoring_tasks': len(self._monitoring_tasks),
            'service_details': {
                service_id: {
                    'name': service.service_name,
                    'type': service.service_type.value,
                    'status': service.status.value,
                    'endpoint': service.endpoint.url,
                    'failure_count': service.failure_count,
                    'last_heartbeat': service.last_heartbeat,
                    'uptime': time.time() - service.registration_time
                }
                for service_id, service in all_services.items()
            }
        }

# 全局服务发现实例
_global_service_discovery: Optional[ServiceDiscovery] = None



