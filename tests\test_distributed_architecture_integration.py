#!/usr/bin/env python3
"""
分布式架构集成验证测试
验证优化后的架构组件是否正确集成和工作
"""
import asyncio
import pytest
import json
import os
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.mt5_process_launcher import MT5ProcessLauncher
from src.core.mt5_account_monitor import MT5AccountMonitor
from src.core.mt5_account_executor import MT5AccountExecutor
from src.messaging.distributed_signal_router import DistributedSignalRouter
from src.messaging.zero_copy_messaging import ZeroCopyMessageBus, MessageType as ZeroCopyMessageType
from src.utils.unified_memory_pool import UnifiedMemoryPool


class TestDistributedArchitectureIntegration:
    """分布式架构集成测试"""
    
    @pytest.fixture
    async def mock_config(self):
        """模拟配置"""
        return {
            'host_id': 'test-host-01',
            'account_id': 'TEST_ACC_001',
            'terminal_path': '/mock/terminal/path',
            'zero_copy_enabled': True,
            'memory_pool_size': 1024 * 1024,  # 1MB for test
            'enable_monitor': True,
            'enable_executor': True,
            'local_accounts': ['TEST_ACC_001', 'TEST_ACC_002'],
            'remote_account_mappings': {
                'REMOTE_ACC_001': 'remote-host-01',
                'REMOTE_ACC_002': 'remote-host-02'
            }
        }
    
    @pytest.fixture
    async def memory_pool(self):
        """内存池实例"""
        pool = UnifiedMemoryPool()
        await pool.start()
        yield pool
        await pool.stop()
    
    @pytest.fixture
    async def zero_copy_bus(self):
        """零拷贝消息总线实例"""
        bus = ZeroCopyMessageBus("test_bus")
        await bus.start()
        yield bus
        await bus.stop()
    
    @pytest.fixture
    async def distributed_router(self, mock_config, zero_copy_bus):
        """分布式信号路由器实例"""
        config = {
            **mock_config,
            'zero_copy_bus': zero_copy_bus,
            'queue_manager': Mock()  # 模拟队列管理器
        }
        
        router = DistributedSignalRouter(mock_config['host_id'], config)
        await router.start()
        yield router
        await router.stop()
    
    @pytest.mark.asyncio
    async def test_mt5_process_launcher_initialization(self, mock_config):
        """测试MT5进程启动器初始化"""
        # 模拟依赖组件
        with patch('src.core.mt5_process_launcher.UnifiedDependencyContainer') as mock_container, \
             patch('src.core.mt5_process_launcher.UnifiedServiceDiscovery') as mock_discovery, \
             patch('src.core.mt5_process_launcher.get_unified_memory_pool') as mock_pool, \
             patch('src.core.mt5_process_launcher.get_zero_copy_message_bus') as mock_bus:
            
            # 设置模拟返回值
            mock_container.return_value = Mock()
            mock_discovery.return_value = Mock()
            mock_pool.return_value = Mock()
            mock_bus.return_value = Mock()
            
            # 创建启动器
            launcher = MT5ProcessLauncher('TEST_ACC_001', mock_config)
            
            # 验证基本属性
            assert launcher.account_id == 'TEST_ACC_001'
            assert launcher.config == mock_config
            assert launcher.host_id == mock_config['host_id']
            
            # 验证端口分配
            assert launcher.monitor_port >= 20000
            assert launcher.executor_port >= 21000
    
    @pytest.mark.asyncio
    async def test_monitor_executor_integration(self, mock_config, memory_pool, zero_copy_bus):
        """测试监控器和执行器集成"""
        # 创建增强配置
        enhanced_config = {
            **mock_config,
            'memory_pool': memory_pool,
            'zero_copy_bus': zero_copy_bus,
            'rpc_client': Mock()  # 模拟RPC客户端
        }
        
        # 设置RPC客户端的模拟返回值
        enhanced_config['rpc_client'].health_check = AsyncMock(return_value={'status': 'success'})
        
        # 创建监控器
        monitor = MT5AccountMonitor('TEST_ACC_001', enhanced_config)
        
        # 创建执行器
        executor = MT5AccountExecutor('TEST_ACC_001', enhanced_config)
        
        # 验证基本配置
        assert monitor.account_id == 'TEST_ACC_001'
        assert monitor.zero_copy_bus == zero_copy_bus
        assert monitor.memory_pool == memory_pool
        
        assert executor.account_id == 'TEST_ACC_001'
        assert executor.zero_copy_bus == zero_copy_bus
        assert executor.memory_pool == memory_pool
        
        # 测试健康检查
        monitor_health = await monitor.health_check()
        executor_health = await executor.health_check()
        
        # 监控器未启动时健康检查应该失败
        assert not monitor_health
        assert not executor_health
    
    @pytest.mark.asyncio
    async def test_distributed_signal_routing(self, distributed_router, mock_config):
        """测试分布式信号路由"""
        from src.messaging.message_types import TradeSignal, OrderTypeEnum, TradeAction
        
        # 创建测试信号
        signal = TradeSignal(
            signal_id='test_signal_001',
            account_id='MASTER_ACC',
            symbol='EURUSD',
            action=TradeAction.OPEN,
            order_type=OrderTypeEnum.BUY,
            volume=0.1,
            price=1.2000,
            sl=1.1950,
            tp=1.2050,
            timestamp=asyncio.get_event_loop().time()
        )
        
        # 测试本地和远程目标混合路由
        targets = ['TEST_ACC_001', 'TEST_ACC_002', 'REMOTE_ACC_001']
        
        # 执行路由
        results = await distributed_router.route_signal(signal, targets)
        
        # 验证结果结构
        assert isinstance(results, dict)
        assert len(results) == len(targets)
        
        # 验证统计更新
        stats = distributed_router.get_routing_stats()
        assert stats['total_signals'] > 0
        assert stats['host_id'] == mock_config['host_id']
    
    @pytest.mark.asyncio
    async def test_zero_copy_message_flow(self, zero_copy_bus):
        """测试零拷贝消息流"""
        received_messages = []
        
        async def message_handler(header: Dict[str, Any], payload: Dict[str, Any]):
            received_messages.append({'header': header, 'payload': payload})
        
        # 订阅消息
        await zero_copy_bus.subscribe(ZeroCopyMessageType.TRADE_SIGNAL, message_handler)
        
        # 发送测试消息
        test_payload = {
            'signal_id': 'test_001',
            'account_id': 'TEST_ACC',
            'symbol': 'EURUSD',
            'action': 'BUY',
            'volume': 0.1
        }
        
        await zero_copy_bus.send_message(
            message_type=ZeroCopyMessageType.TRADE_SIGNAL,
            data=test_payload,
            target_id=12345,
            priority=1
        )
        
        # 等待消息处理
        await asyncio.sleep(0.1)
        
        # 验证消息接收
        assert len(received_messages) == 1
        received = received_messages[0]
        assert received['payload']['signal_id'] == 'test_001'
        assert received['payload']['symbol'] == 'EURUSD'
    
    @pytest.mark.asyncio
    async def test_memory_pool_performance(self, memory_pool):
        """测试内存池性能"""
        # 分配多个内存块
        buffers = []
        for i in range(10):
            buffer = await memory_pool.allocate(1024)  # 1KB per buffer
            assert buffer is not None
            buffers.append(buffer)
        
        # 验证内存池统计
        stats = memory_pool.get_performance_stats()
        assert stats['global_stats']['allocations_count'] >= 10
        assert stats['global_stats']['total_memory'] > 0
        
        # 释放内存
        for buffer in buffers:
            await memory_pool.deallocate(buffer)
        
        # 验证释放
        updated_stats = memory_pool.get_performance_stats()
        assert updated_stats['global_stats']['deallocations_count'] >= 10
    
    @pytest.mark.asyncio
    async def test_component_health_checks(self, mock_config, memory_pool, zero_copy_bus):
        """测试组件健康检查"""
        # 创建配置
        config = {
            **mock_config,
            'memory_pool': memory_pool,
            'zero_copy_bus': zero_copy_bus,
            'rpc_client': Mock()
        }
        
        # 设置RPC客户端模拟
        config['rpc_client'].health_check = AsyncMock(return_value={'status': 'success'})
        
        # 创建组件
        monitor = MT5AccountMonitor('TEST_ACC_001', config)
        executor = MT5AccountExecutor('TEST_ACC_001', config)
        
        # 启动组件
        monitor.running = True  # 模拟运行状态
        executor.running = True
        
        # 执行健康检查
        monitor_health = await monitor.health_check()
        executor_health = await executor.health_check()
        
        # 验证健康状态
        assert monitor_health
        assert executor_health
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, distributed_router):
        """测试错误处理和恢复"""
        from src.messaging.message_types import TradeSignal, OrderTypeEnum, TradeAction
        
        # 创建无效信号（缺少必要字段）
        invalid_signal = TradeSignal(
            signal_id='invalid_signal',
            account_id='',  # 空账户ID
            symbol='',      # 空交易品种
            action=TradeAction.OPEN,
            order_type=OrderTypeEnum.BUY,
            volume=0,       # 无效手数
            timestamp=asyncio.get_event_loop().time()
        )
        
        # 尝试路由无效信号
        results = await distributed_router.route_signal(invalid_signal, ['INVALID_ACC'])
        
        # 验证错误处理
        assert isinstance(results, dict)
        
        # 验证错误统计
        stats = distributed_router.get_routing_stats()
        assert stats['routing_errors'] >= 0  # 应该记录错误
    
    @pytest.mark.asyncio
    async def test_concurrent_signal_processing(self, distributed_router):
        """测试并发信号处理"""
        from src.messaging.message_types import TradeSignal, OrderTypeEnum, TradeAction
        
        # 创建多个信号
        signals = []
        for i in range(5):
            signal = TradeSignal(
                signal_id=f'concurrent_signal_{i}',
                account_id='MASTER_ACC',
                symbol='EURUSD',
                action=TradeAction.OPEN,
                order_type=OrderTypeEnum.BUY,
                volume=0.1,
                timestamp=asyncio.get_event_loop().time()
            )
            signals.append(signal)
        
        # 并发路由信号
        tasks = []
        for signal in signals:
            task = distributed_router.route_signal(signal, ['TEST_ACC_001'])
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证结果
        assert len(results) == 5
        for result in results:
            if isinstance(result, dict):
                assert 'TEST_ACC_001' in result
            else:
                # 如果有异常，也是可以接受的（测试环境限制）
                assert isinstance(result, Exception)
    
    def test_configuration_validation(self, mock_config):
        """测试配置验证"""
        # 测试完整配置
        launcher = MT5ProcessLauncher('TEST_ACC_001', mock_config)
        assert launcher.account_id == 'TEST_ACC_001'
        
        # 测试最小配置
        minimal_config = {
            'host_id': 'test-host',
            'account_id': 'TEST_ACC'
        }
        
        minimal_launcher = MT5ProcessLauncher('TEST_ACC', minimal_config)
        assert minimal_launcher.host_id == 'test-host'
    
    @pytest.mark.asyncio
    async def test_integration_cleanup(self, memory_pool, zero_copy_bus):
        """测试集成清理"""
        # 验证组件可以正确关闭
        assert memory_pool.running
        assert zero_copy_bus.running
        
        # 组件将在fixture teardown中自动清理
        # 这里主要验证它们当前是活跃的


if __name__ == '__main__':
    # 运行测试
    import pytest
    pytest.main([__file__, '-v', '--tb=short'])