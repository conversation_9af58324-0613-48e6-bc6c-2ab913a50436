#!/usr/bin/env python3
"""
统一配置管理器测试脚本
测试配置热重载、版本控制和配置验证功能
"""
import asyncio
import time
import logging
import json
import tempfile
import shutil
from typing import Dict, Any
from pathlib import Path
import sys
import yaml

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from src.core.unified_config_manager import (
    UnifiedConfigManager, ConfigScope, ConfigSource, ConfigChange
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UnifiedConfigTester:
    """统一配置管理器测试器"""
    
    def __init__(self):
        self.test_results = {
            'hot_reload_tests': [],
            'version_control_tests': [],
            'validation_tests': [],
            'performance_tests': [],
            'integration_tests': []
        }
        
        # 创建临时测试环境
        self.temp_dir = Path(tempfile.mkdtemp(prefix="config_test_"))
        self.config_manager = UnifiedConfigManager(str(self.temp_dir))
        
        logger.info(f"测试环境创建: {self.temp_dir}")
    
    async def setup_test_environment(self):
        """设置测试环境"""
        # 创建测试配置文件
        await self._create_test_configs()
        
        # 初始化配置管理器
        success = await self.config_manager.initialize(enable_hot_reload=True)
        if not success:
            raise RuntimeError("配置管理器初始化失败")
        
        logger.info("测试环境设置完成")
    
    async def _create_test_configs(self):
        """创建测试配置文件"""
        # 系统配置
        system_config = {
            'host_id': 'test-node-01',
            'environment': 'testing',
            'log_level': 'INFO',
            'version': '1.0.0'
        }
        
        system_file = self.temp_dir / "core" / "system.yaml"
        system_file.parent.mkdir(parents=True, exist_ok=True)
        with open(system_file, 'w', encoding='utf-8') as f:
            yaml.dump(system_config, f)
        
        # 基础设施配置
        infrastructure_config = {
            'nats': {
                'servers': ['nats://localhost:4222'],
                'timeout': 30
            },
            'redis': {
                'url': 'redis://localhost:6379',
                'pool_size': 10
            }
        }
        
        infra_file = self.temp_dir / "core" / "infrastructure.yaml"
        with open(infra_file, 'w', encoding='utf-8') as f:
            yaml.dump(infrastructure_config, f)
        
        # 账户配置
        account_config = {
            'account_id': 'TEST_ACC_001',
            'login': 100001,
            'server': 'MetaQuotes-Demo',
            'terminal_path': '/opt/mt5/terminal_001',
            'host_id': 'test-node-01'
        }
        
        account_file = self.temp_dir / "accounts" / "TEST_ACC_001.yaml"
        account_file.parent.mkdir(parents=True, exist_ok=True)
        with open(account_file, 'w', encoding='utf-8') as f:
            yaml.dump(account_config, f)
    
    async def test_hot_reload_functionality(self) -> Dict[str, Any]:
        """测试热重载功能"""
        logger.info("🧪 测试配置热重载功能")
        
        result = {
            'test_name': 'hot_reload',
            'success': False,
            'subtests': []
        }
        
        try:
            # 1. 测试文件修改检测
            change_detected = await self._test_file_change_detection()
            result['subtests'].append({
                'name': 'file_change_detection',
                'success': change_detected,
                'description': '文件修改检测'
            })
            
            # 2. 测试配置重载
            reload_success = await self._test_config_reload()
            result['subtests'].append({
                'name': 'config_reload',
                'success': reload_success,
                'description': '配置重载'
            })
            
            # 3. 测试缓存失效
            cache_invalidation = await self._test_cache_invalidation()
            result['subtests'].append({
                'name': 'cache_invalidation',
                'success': cache_invalidation,
                'description': '缓存失效'
            })
            
            result['success'] = all(test['success'] for test in result['subtests'])
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"热重载测试失败: {e}")
        
        return result
    
    async def _test_file_change_detection(self) -> bool:
        """测试文件变更检测"""
        # 添加变更监听器
        changes_detected = []
        
        def change_listener(change: ConfigChange):
            changes_detected.append(change)
        
        self.config_manager.add_change_listener(change_listener)
        
        # 修改系统配置文件
        system_file = self.temp_dir / "core" / "system.yaml"
        with open(system_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        config['version'] = '1.0.1'  # 修改版本号
        config['test_timestamp'] = time.time()  # 添加时间戳
        
        with open(system_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f)
        
        # 等待文件监控检测到变更（最多等待5秒）
        for _ in range(50):  # 5秒，每100ms检查一次
            await asyncio.sleep(0.1)
            if changes_detected:
                break
        
        # 验证变更检测
        if changes_detected:
            logger.info(f"检测到 {len(changes_detected)} 个配置变更")
            return True
        else:
            logger.warning("未检测到配置变更")
            return False
    
    async def _test_config_reload(self) -> bool:
        """测试配置重载"""
        # 获取重载前的配置
        old_config = await self.config_manager.get_config(ConfigScope.SYSTEM, 'system')
        old_version = old_config.get('version') if old_config else None
        
        # 等待重载完成（文件监控有延迟）
        await asyncio.sleep(2)
        
        # 获取重载后的配置
        new_config = await self.config_manager.get_config(ConfigScope.SYSTEM, 'system')
        new_version = new_config.get('version') if new_config else None
        
        # 验证配置已更新
        if new_version != old_version and new_version == '1.0.1':
            logger.info(f"配置重载成功: {old_version} → {new_version}")
            return True
        else:
            logger.warning(f"配置重载失败: {old_version} → {new_version}")
            return False
    
    async def _test_cache_invalidation(self) -> bool:
        """测试缓存失效"""
        # 先读取配置建立缓存
        config1 = await self.config_manager.get_config(ConfigScope.SYSTEM, 'system', use_cache=True)
        
        # 直接更新配置（绕过文件）
        await self.config_manager.set_config(
            ConfigScope.SYSTEM, 
            'system', 
            {**config1, 'cache_test': 'updated'}, 
            persist=False
        )
        
        # 再次读取，应该返回更新后的配置
        config2 = await self.config_manager.get_config(ConfigScope.SYSTEM, 'system', use_cache=True)
        
        if config2.get('cache_test') == 'updated':
            logger.info("缓存失效测试成功")
            return True
        else:
            logger.warning("缓存失效测试失败")
            return False
    
    async def test_version_control(self) -> Dict[str, Any]:
        """测试版本控制功能"""
        logger.info("🧪 测试版本控制功能")
        
        result = {
            'test_name': 'version_control',
            'success': False,
            'subtests': []
        }
        
        try:
            # 1. 测试版本计算
            version_calculation = await self._test_version_calculation()
            result['subtests'].append({
                'name': 'version_calculation',
                'success': version_calculation,
                'description': '版本计算'
            })
            
            # 2. 测试变更历史
            change_history = await self._test_change_history()
            result['subtests'].append({
                'name': 'change_history',
                'success': change_history,
                'description': '变更历史'
            })
            
            # 3. 测试配置导出
            config_export = await self._test_config_export()
            result['subtests'].append({
                'name': 'config_export',
                'success': config_export,
                'description': '配置导出'
            })
            
            result['success'] = all(test['success'] for test in result['subtests'])
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"版本控制测试失败: {e}")
        
        return result
    
    async def _test_version_calculation(self) -> bool:
        """测试版本计算"""
        # 获取初始版本
        version1 = self.config_manager.get_config_version(ConfigScope.SYSTEM)
        
        # 修改配置
        await self.config_manager.set_config(
            ConfigScope.SYSTEM,
            'test_key',
            'test_value',
            persist=False
        )
        
        # 获取新版本
        version2 = self.config_manager.get_config_version(ConfigScope.SYSTEM)
        
        # 验证版本变化
        if version2 and version2.version != (version1.version if version1 else None):
            logger.info(f"版本计算成功: {version1.version if version1 else 'None'} → {version2.version}")
            return True
        else:
            logger.warning("版本计算失败")
            return False
    
    async def _test_change_history(self) -> bool:
        """测试变更历史"""
        # 获取初始历史长度
        initial_history = self.config_manager.get_change_history()
        initial_count = len(initial_history)
        
        # 进行几次配置更改
        for i in range(3):
            await self.config_manager.set_config(
                ConfigScope.SYSTEM,
                f'history_test_{i}',
                f'value_{i}',
                persist=False
            )
        
        # 获取更新后的历史
        updated_history = self.config_manager.get_change_history()
        
        # 验证历史记录增加
        if len(updated_history) >= initial_count + 3:
            logger.info(f"变更历史测试成功: {initial_count} → {len(updated_history)}")
            return True
        else:
            logger.warning(f"变更历史测试失败: {initial_count} → {len(updated_history)}")
            return False
    
    async def _test_config_export(self) -> bool:
        """测试配置导出"""
        try:
            # 测试YAML导出
            yaml_export = await self.config_manager.export_config(ConfigScope.SYSTEM, "yaml")
            yaml_data = yaml.safe_load(yaml_export)
            
            # 测试JSON导出
            json_export = await self.config_manager.export_config(ConfigScope.SYSTEM, "json")
            json_data = json.loads(json_export)
            
            # 验证导出数据
            if yaml_data and json_data and 'system' in yaml_data:
                logger.info("配置导出测试成功")
                return True
            else:
                logger.warning("配置导出测试失败")
                return False
                
        except Exception as e:
            logger.error(f"配置导出异常: {e}")
            return False
    
    async def test_config_validation(self) -> Dict[str, Any]:
        """测试配置验证功能"""
        logger.info("🧪 测试配置验证功能")
        
        result = {
            'test_name': 'config_validation',
            'success': False,
            'subtests': []
        }
        
        try:
            # 1. 测试有效配置
            valid_config_test = await self._test_valid_config()
            result['subtests'].append({
                'name': 'valid_config',
                'success': valid_config_test,
                'description': '有效配置验证'
            })
            
            # 2. 测试无效配置
            invalid_config_test = await self._test_invalid_config()
            result['subtests'].append({
                'name': 'invalid_config',
                'success': invalid_config_test,
                'description': '无效配置拒绝'
            })
            
            # 3. 测试配置约束
            constraint_test = await self._test_config_constraints()
            result['subtests'].append({
                'name': 'config_constraints',
                'success': constraint_test,
                'description': '配置约束验证'
            })
            
            result['success'] = all(test['success'] for test in result['subtests'])
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"配置验证测试失败: {e}")
        
        return result
    
    async def _test_valid_config(self) -> bool:
        """测试有效配置"""
        valid_system_config = {
            'host_id': 'valid-test-node',
            'environment': 'testing',
            'log_level': 'DEBUG'
        }
        
        success = await self.config_manager.set_config(
            ConfigScope.SYSTEM,
            'valid_test',
            valid_system_config,
            persist=False
        )
        
        return success
    
    async def _test_invalid_config(self) -> bool:
        """测试无效配置"""
        # 缺少必需字段的配置
        invalid_system_config = {
            'environment': 'invalid_env',  # 无效的环境值
            'log_level': 'DEBUG'
            # 缺少 host_id
        }
        
        success = await self.config_manager.set_config(
            ConfigScope.SYSTEM,
            'invalid_test',
            invalid_system_config,
            persist=False
        )
        
        # 应该失败
        return not success
    
    async def _test_config_constraints(self) -> bool:
        """测试配置约束"""
        # 测试账户配置约束
        invalid_account_config = {
            'account_id': 'TEST_ACC_002',
            'login': 'invalid_login',  # 应该是整数
            'server': 'MetaQuotes-Demo',
            'terminal_path': '/nonexistent/path'  # 不存在的路径
        }
        
        success = await self.config_manager.set_config(
            ConfigScope.ACCOUNT,
            'TEST_ACC_002',
            invalid_account_config,
            persist=False
        )
        
        # 应该失败
        return not success
    
    async def test_performance(self) -> Dict[str, Any]:
        """测试性能"""
        logger.info("🧪 测试配置管理器性能")
        
        result = {
            'test_name': 'performance',
            'success': False,
            'metrics': {}
        }
        
        try:
            # 1. 配置读取性能
            read_perf = await self._test_read_performance()
            result['metrics']['read_performance'] = read_perf
            
            # 2. 配置写入性能
            write_perf = await self._test_write_performance()
            result['metrics']['write_performance'] = write_perf
            
            # 3. 缓存性能
            cache_perf = await self._test_cache_performance()
            result['metrics']['cache_performance'] = cache_perf
            
            # 评估性能
            result['success'] = (
                read_perf['avg_time'] < 0.01 and  # 读取 < 10ms
                write_perf['avg_time'] < 0.1 and  # 写入 < 100ms
                cache_perf['speedup'] > 2         # 缓存加速 > 2x
            )
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"性能测试失败: {e}")
        
        return result
    
    async def _test_read_performance(self) -> Dict[str, float]:
        """测试读取性能"""
        times = []
        iterations = 100
        
        for i in range(iterations):
            start_time = time.time()
            await self.config_manager.get_config(ConfigScope.SYSTEM, 'system')
            end_time = time.time()
            times.append(end_time - start_time)
        
        return {
            'avg_time': sum(times) / len(times),
            'min_time': min(times),
            'max_time': max(times),
            'total_ops': iterations
        }
    
    async def _test_write_performance(self) -> Dict[str, float]:
        """测试写入性能"""
        times = []
        iterations = 50
        
        for i in range(iterations):
            start_time = time.time()
            await self.config_manager.set_config(
                ConfigScope.SYSTEM,
                f'perf_test_{i}',
                f'value_{i}',
                persist=False
            )
            end_time = time.time()
            times.append(end_time - start_time)
        
        return {
            'avg_time': sum(times) / len(times),
            'min_time': min(times),
            'max_time': max(times),
            'total_ops': iterations
        }
    
    async def _test_cache_performance(self) -> Dict[str, float]:
        """测试缓存性能"""
        # 无缓存读取
        start_time = time.time()
        for _ in range(100):
            await self.config_manager.get_config(ConfigScope.SYSTEM, 'system', use_cache=False)
        no_cache_time = time.time() - start_time
        
        # 有缓存读取
        start_time = time.time()
        for _ in range(100):
            await self.config_manager.get_config(ConfigScope.SYSTEM, 'system', use_cache=True)
        cache_time = time.time() - start_time
        
        speedup = no_cache_time / cache_time if cache_time > 0 else 0
        
        return {
            'no_cache_time': no_cache_time,
            'cache_time': cache_time,
            'speedup': speedup
        }
    
    async def cleanup(self):
        """清理测试环境"""
        try:
            await self.config_manager.shutdown()
            shutil.rmtree(self.temp_dir)
            logger.info(f"测试环境已清理: {self.temp_dir}")
        except Exception as e:
            logger.error(f"清理失败: {e}")
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🚀 开始统一配置管理器测试套件")
        
        all_results = {
            'test_suite': 'unified_config_manager',
            'timestamp': time.time(),
            'test_results': {}
        }
        
        try:
            # 设置测试环境
            await self.setup_test_environment()
            
            # 运行测试
            tests = [
                ('hot_reload', self.test_hot_reload_functionality()),
                ('version_control', self.test_version_control()),
                ('config_validation', self.test_config_validation()),
                ('performance', self.test_performance())
            ]
            
            for test_name, test_coro in tests:
                try:
                    result = await test_coro
                    all_results['test_results'][test_name] = result
                    
                    status = "✅ 通过" if result['success'] else "❌ 失败"
                    logger.info(f"{test_name}: {status}")
                    
                except Exception as e:
                    logger.error(f"{test_name} 异常: {e}")
                    all_results['test_results'][test_name] = {
                        'test_name': test_name,
                        'success': False,
                        'error': str(e)
                    }
            
            # 生成报告
            await self._generate_test_report(all_results)
            
        finally:
            await self.cleanup()
        
        return all_results
    
    async def _generate_test_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        report_file = f"reports/unified_config_test_report_{int(time.time())}.json"
        
        # 确保报告目录存在
        Path("reports").mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 测试报告已生成: {report_file}")
        
        # 打印摘要
        self._print_test_summary(results)
    
    def _print_test_summary(self, results: Dict[str, Any]):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("🔧 统一配置管理器测试摘要")
        print("="*60)
        
        total_tests = len(results['test_results'])
        passed_tests = sum(1 for r in results['test_results'].values() if r.get('success', False))
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {total_tests - passed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n详细结果:")
        for test_name, test_result in results['test_results'].items():
            status = "✅" if test_result.get('success', False) else "❌"
            print(f"  {status} {test_name}")
            
            if 'subtests' in test_result:
                for subtest in test_result['subtests']:
                    sub_status = "✅" if subtest['success'] else "❌"
                    print(f"    {sub_status} {subtest['name']}: {subtest['description']}")
            
            if 'metrics' in test_result:
                metrics = test_result['metrics']
                if 'read_performance' in metrics:
                    print(f"    📊 读取性能: {metrics['read_performance']['avg_time']*1000:.2f}ms")
                if 'cache_performance' in metrics:
                    print(f"    🚀 缓存加速: {metrics['cache_performance']['speedup']:.1f}x")
        
        print("="*60)

async def main():
    """主函数"""
    tester = UnifiedConfigTester()
    results = await tester.run_all_tests()
    
    # 评估测试结果
    all_passed = all(
        test_result.get('success', False)
        for test_result in results['test_results'].values()
    )
    
    if all_passed:
        logger.info("🎉 所有测试通过!")
        return 0
    else:
        logger.error("❌ 部分测试失败!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)