{"dashboard": {"id": null, "title": "MT5分布式交易系统 - 混合架构监控", "tags": ["mt5", "trading", "hybrid-architecture"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "系统概览", "type": "stat", "targets": [{"expr": "up{job=\"mt5-system\"}", "legendFormat": "系统状态"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "数据平面 - 交易信号吞吐量", "type": "graph", "targets": [{"expr": "rate(mt5_signals_processed_total[5m])", "legendFormat": "{{account_id}} - 信号处理速率"}], "yAxes": [{"label": "信号/秒", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "数据平面 - 延迟监控", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(mt5_signal_latency_seconds_bucket[5m]))", "legendFormat": "P95延迟"}, {"expr": "histogram_quantile(0.50, rate(mt5_signal_latency_seconds_bucket[5m]))", "legendFormat": "P50延迟"}], "yAxes": [{"label": "秒", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "控制平面 - 系统健康状态", "type": "table", "targets": [{"expr": "mt5_host_status", "legendFormat": "{{host_id}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "主账户监控器状态", "type": "stat", "targets": [{"expr": "mt5_master_monitor_active", "legendFormat": "{{account_id}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}}, {"id": 6, "title": "从账户执行器状态", "type": "stat", "targets": [{"expr": "mt5_slave_executor_active", "legendFormat": "{{account_id}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 16}}, {"id": 7, "title": "NATS JetStream状态", "type": "stat", "targets": [{"expr": "nats_jetstream_streams", "legendFormat": "活跃流数量"}], "gridPos": {"h": 8, "w": 8, "x": 16, "y": 16}}, {"id": 8, "title": "错误率监控", "type": "graph", "targets": [{"expr": "rate(mt5_errors_total[5m])", "legendFormat": "{{component}} - 错误率"}], "yAxes": [{"label": "错误/秒", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 9, "title": "系统资源使用", "type": "graph", "targets": [{"expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "CPU使用率"}, {"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "legendFormat": "内存使用率"}], "yAxes": [{"label": "百分比", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s", "schemaVersion": 27, "version": 1}}