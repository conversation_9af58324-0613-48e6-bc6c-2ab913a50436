#!/usr/bin/env python3
"""
MT5分离架构集成测试报告生成器
生成详细的测试报告，包括性能指标、错误统计和建议
"""

import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import psutil
import os


class TestReporter:
    """测试报告生成器"""
    
    def __init__(self, output_dir: str = "test_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.test_results = []
        self.performance_metrics = {}
        self.error_statistics = {}
        self.system_info = self._collect_system_info()
        self.start_time = time.time()
        
    def _collect_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        try:
            return {
                'platform': os.name,
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'python_version': f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {'error': str(e)}
    
    def add_test_result(self, test_name: str, status: str, duration: float, 
                       details: Dict[str, Any] = None, error: str = None):
        """添加测试结果"""
        result = {
            'test_name': test_name,
            'status': status,  # 'PASS', 'FAIL', 'SKIP'
            'duration': duration,
            'timestamp': datetime.now().isoformat(),
            'details': details or {},
            'error': error
        }
        self.test_results.append(result)
    
    def add_performance_metric(self, metric_name: str, value: float, unit: str = ""):
        """添加性能指标"""
        self.performance_metrics[metric_name] = {
            'value': value,
            'unit': unit,
            'timestamp': datetime.now().isoformat()
        }
    
    def add_error_statistic(self, error_type: str, count: int, details: str = ""):
        """添加错误统计"""
        self.error_statistics[error_type] = {
            'count': count,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_summary(self) -> Dict[str, Any]:
        """生成测试摘要"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        skipped_tests = len([r for r in self.test_results if r['status'] == 'SKIP'])
        
        total_duration = sum(r['duration'] for r in self.test_results)
        avg_duration = total_duration / total_tests if total_tests > 0 else 0
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'skipped_tests': skipped_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'total_duration': total_duration,
            'average_duration': avg_duration,
            'test_execution_time': time.time() - self.start_time
        }
    
    def generate_json_report(self) -> str:
        """生成JSON格式报告"""
        report = {
            'report_info': {
                'generated_at': datetime.now().isoformat(),
                'report_version': '1.0',
                'generator': 'MT5 Test Reporter'
            },
            'system_info': self.system_info,
            'summary': self.generate_summary(),
            'test_results': self.test_results,
            'performance_metrics': self.performance_metrics,
            'error_statistics': self.error_statistics
        }
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_report_{timestamp}.json"
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return str(filepath)
    
    def generate_text_report(self) -> str:
        """生成文本格式报告"""
        summary = self.generate_summary()
        
        report_lines = [
            "=" * 80,
            "MT5分离架构集成测试报告",
            "=" * 80,
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"测试执行时间: {summary['test_execution_time']:.2f} 秒",
            "",
            "系统信息:",
            f"  平台: {self.system_info.get('platform', 'Unknown')}",
            f"  CPU核心数: {self.system_info.get('cpu_count', 'Unknown')}",
            f"  内存总量: {self.system_info.get('memory_total_gb', 0):.1f} GB",
            f"  Python版本: {self.system_info.get('python_version', 'Unknown')}",
            "",
            "测试摘要:",
            f"  总测试数: {summary['total_tests']}",
            f"  通过: {summary['passed_tests']} ({summary['success_rate']:.1f}%)",
            f"  失败: {summary['failed_tests']}",
            f"  跳过: {summary['skipped_tests']}",
            f"  总耗时: {summary['total_duration']:.2f} 秒",
            f"  平均耗时: {summary['average_duration']:.3f} 秒",
            "",
        ]
        
        # 添加性能指标
        if self.performance_metrics:
            report_lines.extend([
                "性能指标:",
                "-" * 40
            ])
            for metric_name, metric_data in self.performance_metrics.items():
                value = metric_data['value']
                unit = metric_data['unit']
                report_lines.append(f"  {metric_name}: {value:.2f} {unit}")
            report_lines.append("")
        
        # 添加错误统计
        if self.error_statistics:
            report_lines.extend([
                "错误统计:",
                "-" * 40
            ])
            for error_type, error_data in self.error_statistics.items():
                count = error_data['count']
                details = error_data['details']
                report_lines.append(f"  {error_type}: {count} 次")
                if details:
                    report_lines.append(f"    详情: {details}")
            report_lines.append("")
        
        # 添加详细测试结果
        report_lines.extend([
            "详细测试结果:",
            "-" * 40
        ])
        
        for result in self.test_results:
            status_symbol = "✅" if result['status'] == 'PASS' else "❌" if result['status'] == 'FAIL' else "⏭️"
            report_lines.append(
                f"  {status_symbol} {result['test_name']} ({result['duration']:.3f}s)"
            )
            if result['error']:
                report_lines.append(f"    错误: {result['error']}")
        
        report_lines.extend([
            "",
            "=" * 80,
            "报告结束"
        ])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_report_{timestamp}.txt"
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        return str(filepath)
    
    def print_summary(self):
        """打印测试摘要到控制台"""
        summary = self.generate_summary()
        
        print("\n" + "=" * 60)
        print("📊 测试执行摘要")
        print("=" * 60)
        print(f"总测试数: {summary['total_tests']}")
        print(f"✅ 通过: {summary['passed_tests']} ({summary['success_rate']:.1f}%)")
        print(f"❌ 失败: {summary['failed_tests']}")
        print(f"⏭️ 跳过: {summary['skipped_tests']}")
        print(f"⏱️ 总耗时: {summary['total_duration']:.2f} 秒")
        print(f"📈 平均耗时: {summary['average_duration']:.3f} 秒")
        
        if self.performance_metrics:
            print(f"\n📊 关键性能指标:")
            for metric_name, metric_data in self.performance_metrics.items():
                value = metric_data['value']
                unit = metric_data['unit']
                print(f"  {metric_name}: {value:.2f} {unit}")
        
        if self.error_statistics:
            print(f"\n🚨 错误统计:")
            for error_type, error_data in self.error_statistics.items():
                print(f"  {error_type}: {error_data['count']} 次")


# 全局测试报告器实例
_global_reporter = None

def get_test_reporter() -> TestReporter:
    """获取全局测试报告器实例"""
    global _global_reporter
    if _global_reporter is None:
        _global_reporter = TestReporter()
    return _global_reporter

def reset_test_reporter():
    """重置全局测试报告器"""
    global _global_reporter
    _global_reporter = None
