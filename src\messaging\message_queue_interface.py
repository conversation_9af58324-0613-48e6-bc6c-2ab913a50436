#!/usr/bin/env python3
"""
消息队列抽象接口
提供统一的消息队列操作界面，支持多种后端实现
- NATS JetStream
- Redis Streams
- 本地内存队列（降级模式）
- 混合模式（双写备份 + 故障转移）
"""

import asyncio
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass
from datetime import datetime
import time

from .message_types import MessageEnvelope
from .priority_queue import MessagePriority


class QueueBackendType(Enum):
    """队列后端类型"""
    NATS = "nats"
    REDIS_STREAMS = "redis_streams"
    LOCAL_MEMORY = "local_memory"
    HYBRID = "hybrid"


class QueueStatus(Enum):
    """队列状态"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    DEGRADED = "degraded"  # 降级模式
    FAILED = "failed"


@dataclass
class QueueMetrics:
    """队列性能指标"""
    messages_sent: int = 0
    messages_received: int = 0
    messages_failed: int = 0
    avg_latency_ms: float = 0.0
    queue_depth: int = 0
    last_activity: Optional[float] = None
    uptime_seconds: float = 0.0
    error_rate: float = 0.0


@dataclass
class QueueConfig:
    """队列配置"""
    backend_type: QueueBackendType
    connection_params: Dict[str, Any]
    max_retries: int = 3
    retry_delay_ms: int = 100
    batch_size: int = 100
    max_queue_depth: int = 10000
    enable_persistence: bool = True
    enable_compression: bool = False
    priority_levels: int = 5
    
    # 故障转移配置
    failover_threshold_ms: int = 1000  # 延迟阈值
    health_check_interval_ms: int = 5000
    auto_failover: bool = True


class MessageQueueInterface(ABC):
    """
    消息队列抽象接口
    
    提供统一的消息发布、订阅、管理界面
    支持优先级、批量操作、故障恢复等高级功能
    """
    
    def __init__(self, config: QueueConfig):
        self.config = config
        self.status = QueueStatus.DISCONNECTED
        self.metrics = QueueMetrics()
        self._subscribers: Dict[str, List[Callable]] = {}
        self._start_time = time.time()
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接到消息队列后端"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """断开连接"""
        pass
    
    @abstractmethod
    async def publish(
        self, 
        subject: str, 
        message: MessageEnvelope,
        priority: MessagePriority = MessagePriority.REALTIME_QUERY
    ) -> bool:
        """
        发布消息
        
        Args:
            subject: 主题/频道
            message: 消息内容
            priority: 消息优先级
            
        Returns:
            bool: 发布是否成功
        """
        pass
    
    @abstractmethod
    async def publish_batch(
        self, 
        messages: List[tuple[str, MessageEnvelope, MessagePriority]]
    ) -> int:
        """
        批量发布消息
        
        Args:
            messages: (subject, message, priority) 元组列表
            
        Returns:
            int: 成功发布的消息数量
        """
        pass
    
    @abstractmethod
    async def subscribe(
        self, 
        subject: str, 
        callback: Callable[[MessageEnvelope], None],
        queue_group: Optional[str] = None
    ) -> bool:
        """
        订阅消息
        
        Args:
            subject: 主题/频道
            callback: 消息处理回调
            queue_group: 队列组（负载均衡）
            
        Returns:
            bool: 订阅是否成功
        """
        pass
    
    @abstractmethod
    async def unsubscribe(self, subject: str) -> bool:
        """取消订阅"""
        pass
    
    @abstractmethod
    async def request(
        self, 
        subject: str, 
        message: MessageEnvelope,
        timeout_ms: int = 5000
    ) -> Optional[MessageEnvelope]:
        """
        请求-响应模式
        
        Args:
            subject: 请求主题
            message: 请求消息
            timeout_ms: 超时时间（毫秒）
            
        Returns:
            响应消息或None（超时/失败）
        """
        pass
    
    @abstractmethod
    async def get_queue_depth(self, subject: str) -> int:
        """获取队列深度"""
        pass
    
    @abstractmethod
    async def purge_queue(self, subject: str) -> bool:
        """清空队列"""
        pass
    
    # 状态和监控方法
    
    def get_status(self) -> QueueStatus:
        """获取队列状态"""
        return self.status
    
    def get_metrics(self) -> QueueMetrics:
        """获取性能指标"""
        self.metrics.uptime_seconds = time.time() - self._start_time
        if self.metrics.messages_sent > 0:
            self.metrics.error_rate = self.metrics.messages_failed / self.metrics.messages_sent
        return self.metrics
    
    def is_healthy(self) -> bool:
        """检查队列健康状态"""
        return self.status in [QueueStatus.CONNECTED, QueueStatus.DEGRADED]
    
    async def health_check(self) -> bool:
        """执行健康检查"""
        try:
            # 发送测试消息
            test_message = MessageEnvelope(
                id=f"health-check-{int(time.time() * 1000)}",
                subject="health.check",
                payload={"timestamp": time.time(), "type": "health_check"},
                timestamp=time.time()
            )
            
            start_time = time.perf_counter()
            success = await self.publish("health.check", test_message)
            latency_ms = (time.perf_counter() - start_time) * 1000
            
            if success and latency_ms < self.config.failover_threshold_ms:
                return True
            else:
                return False
                
        except Exception:
            return False
    
    # 辅助方法
    
    def _update_metrics(self, operation: str, success: bool, latency_ms: float = 0):
        """更新性能指标"""
        if operation == "publish":
            if success:
                self.metrics.messages_sent += 1
            else:
                self.metrics.messages_failed += 1
        elif operation == "receive":
            if success:
                self.metrics.messages_received += 1
        
        if latency_ms > 0:
            # 简单的移动平均
            if self.metrics.avg_latency_ms == 0:
                self.metrics.avg_latency_ms = latency_ms
            else:
                self.metrics.avg_latency_ms = (self.metrics.avg_latency_ms * 0.9) + (latency_ms * 0.1)
        
        self.metrics.last_activity = time.time()


class QueueManager:
    """
    队列管理器
    
    负责管理多个队列后端实例，提供故障转移和负载均衡功能
    """
    
    def __init__(self):
        self._queues: Dict[str, MessageQueueInterface] = {}
        self._primary_queue: Optional[str] = None
        self._fallback_queues: List[str] = []
        self._health_check_task: Optional[asyncio.Task] = None
    
    def register_queue(self, name: str, queue: MessageQueueInterface, is_primary: bool = False):
        """注册队列实例"""
        self._queues[name] = queue
        if is_primary:
            self._primary_queue = name
        else:
            if name not in self._fallback_queues:
                self._fallback_queues.append(name)
    
    def get_active_queue(self) -> Optional[MessageQueueInterface]:
        """获取当前活跃的队列"""
        # 优先使用主队列
        if self._primary_queue and self._primary_queue in self._queues:
            primary = self._queues[self._primary_queue]
            if primary.is_healthy():
                return primary
        
        # 尝试备用队列
        for fallback_name in self._fallback_queues:
            if fallback_name in self._queues:
                fallback = self._queues[fallback_name]
                if fallback.is_healthy():
                    return fallback
        
        return None
    
    async def start_health_monitoring(self):
        """启动健康监控"""
        if self._health_check_task:
            return
            
        self._health_check_task = asyncio.create_task(self._health_check_loop())
    
    async def stop_health_monitoring(self):
        """停止健康监控"""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                for name, queue in self._queues.items():
                    try:
                        is_healthy = await queue.health_check()
                        if not is_healthy and queue.status == QueueStatus.CONNECTED:
                            queue.status = QueueStatus.DEGRADED
                        elif is_healthy and queue.status == QueueStatus.DEGRADED:
                            queue.status = QueueStatus.CONNECTED
                    except Exception as e:
                        queue.status = QueueStatus.FAILED
                
                # 等待下次检查
                await asyncio.sleep(5.0)  # 5秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception:
                await asyncio.sleep(1.0)  # 出错时短暂等待
    
    async def shutdown(self):
        """关闭所有队列"""
        await self.stop_health_monitoring()
        
        for queue in self._queues.values():
            try:
                await queue.disconnect()
            except Exception:
                pass
        
        self._queues.clear()