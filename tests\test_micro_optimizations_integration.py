#!/usr/bin/env python3
"""
微优化功能集成测试
测试内存池、异步批量命令和LRU缓存的协同工作
"""
import pytest
import asyncio
import time
import threading
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.unified_batch_processor import (
    UnifiedBatchProcessor, BatchConfig, get_memory_pool
)
from src.infrastructure.connection_pool import (
    MT5ConnectionPool, BatchCommand, BatchResult
)
from src.messaging.protobuf_codec import (
    ProtobufCodec, ProtobufConfig
)


class TestMicroOptimizationsIntegration:
    """测试微优化功能集成"""

    @pytest.fixture
    def optimized_batch_processor(self):
        """创建优化的批处理器"""
        config = BatchConfig(
            default_size=10,
            max_size=50,
            min_size=5,
            timeout_ms=1000,
            auto_adjust=True,
            performance_mode="balanced",
            ml_enabled=False,  # 简化测试
            concurrency_level=2
        )
        return UnifiedBatchProcessor(config)

    @pytest.fixture
    def optimized_connection_pool(self):
        """创建优化的连接池"""
        pool = MT5ConnectionPool(max_connections=5, max_idle_time=300)
        pool.batch_size = 10
        pool.batch_timeout = 1.0
        return pool

    @pytest.fixture
    def optimized_codec(self):
        """创建优化的编解码器"""
        config = ProtobufConfig(
            compression_enabled=True,
            cache_enabled=True,
            cache_max_size=100,
            cache_ttl_seconds=300,
            use_fallback_json=True
        )
        return ProtobufCodec(config)

    @pytest.fixture
    def mock_mt5_factory(self):
        """模拟MT5连接工厂"""
        async def factory(account_id, server=None, login=None):
            mock_connection = Mock()
            mock_connection.account_id = account_id
            
            # 模拟各种MT5操作
            async def mock_order_send(**kwargs):
                await asyncio.sleep(0.01)  # 模拟网络延迟
                return {'retcode': 10009, 'order': 123456}
            
            async def mock_positions_get(**kwargs):
                await asyncio.sleep(0.005)
                return [{'ticket': 123, 'symbol': 'EURUSD', 'volume': 0.1}]
            
            async def mock_account_info(**kwargs):
                await asyncio.sleep(0.003)
                return {'balance': 10000, 'equity': 10100}
            
            mock_connection.order_send = mock_order_send
            mock_connection.positions_get = mock_positions_get
            mock_connection.account_info = mock_account_info
            
            return mock_connection
        
        return factory

    @pytest.mark.asyncio
    async def test_end_to_end_optimization_pipeline(self, optimized_batch_processor, 
                                                   optimized_connection_pool, 
                                                   optimized_codec, 
                                                   mock_mt5_factory):
        """测试端到端优化流水线"""
        # 设置连接池
        optimized_connection_pool.set_connection_factory(mock_mt5_factory)
        optimized_connection_pool.set_connection_validator(AsyncMock(return_value=True))
        
        # 启动组件
        await optimized_batch_processor.start()
        await optimized_connection_pool.start()
        
        try:
            # 准备测试数据
            trade_signals = []
            for i in range(20):
                signal = {
                    'signal_id': f'signal_{i:03d}',
                    'account_id': 'TEST_ACCOUNT',
                    'symbol': 'EURUSD',
                    'action': 'BUY',
                    'volume': 0.1,
                    'price': 1.1000 + i * 0.0001,
                    'timestamp': int(time.time() * 1000)
                }
                trade_signals.append(signal)
            
            # 第一阶段：编码和缓存测试
            encoded_signals = []
            encoding_start = time.time()
            
            for signal in trade_signals:
                # 编码信号（第一次会创建，后续会复用缓存）
                encoded = optimized_codec.encode_trade_signal(signal)
                encoded_signals.append(encoded)
            
            encoding_time = time.time() - encoding_start
            
            # 解码测试（应该利用缓存）
            decoding_start = time.time()
            decoded_signals = []
            
            for encoded in encoded_signals:
                # 第一次解码
                decoded = optimized_codec.decode_trade_signal(encoded)
                decoded_signals.append(decoded)
                
                # 第二次解码（应该命中缓存）
                cached_decoded = optimized_codec.decode_trade_signal(encoded)
                assert cached_decoded == decoded
            
            decoding_time = time.time() - decoding_start
            
            # 验证缓存效果
            cache_stats = optimized_codec.get_cache_stats()
            assert cache_stats is not None
            assert cache_stats['hits'] > 0  # 应该有缓存命中
            
            # 第二阶段：批量处理测试（使用内存池）
            batch_start = time.time()
            
            # 提交到批处理器（会使用内存池）
            for signal in decoded_signals:
                await optimized_batch_processor.submit(signal)
            
            # 等待处理完成
            await asyncio.sleep(2.0)
            
            batch_time = time.time() - batch_start
            
            # 获取批处理器指标
            batch_metrics = optimized_batch_processor.get_metrics()
            assert batch_metrics['total_items'] >= 20
            
            # 验证内存池效果
            memory_pool = get_memory_pool()
            pool_stats = memory_pool.get_pool_stats()
            assert pool_stats['total_reused'] > 0  # 应该有对象复用
            
            # 第三阶段：连接池批量命令测试
            commands = []
            for i, signal in enumerate(decoded_signals[:10]):  # 测试前10个
                command = BatchCommand(
                    command_id=f"cmd_{i:03d}",
                    command_type="order_send",
                    parameters={
                        'account_id': signal['account_id'],
                        'symbol': signal['symbol'],
                        'volume': signal['volume'],
                        'price': signal['price']
                    }
                )
                commands.append(command)
            
            # 执行批量命令
            command_start = time.time()
            results = await optimized_connection_pool.batch_execute_commands(commands)
            command_time = time.time() - command_start
            
            # 验证结果
            assert len(results) == 10
            successful_results = [r for r in results if r.success]
            assert len(successful_results) >= 8  # 至少80%成功率
            
            # 性能验证
            print(f"\n性能指标:")
            print(f"编码时间: {encoding_time:.3f}s ({len(trade_signals)} 个信号)")
            print(f"解码时间: {decoding_time:.3f}s (包含缓存)")
            print(f"批处理时间: {batch_time:.3f}s")
            print(f"批量命令时间: {command_time:.3f}s ({len(commands)} 个命令)")
            
            print(f"\n缓存统计: {cache_stats}")
            print(f"内存池统计: {pool_stats}")
            print(f"批处理统计: {batch_metrics}")
            
            # 性能断言
            assert encoding_time < 1.0  # 编码应该很快
            assert decoding_time < 1.0  # 解码应该很快（有缓存）
            assert command_time < 5.0   # 批量命令应该在合理时间内完成
        
        finally:
            # 清理
            await optimized_batch_processor.stop()
            await optimized_connection_pool.stop()

    @pytest.mark.asyncio
    async def test_memory_pool_integration_with_batch_processor(self, optimized_batch_processor):
        """测试内存池与批处理器的集成"""
        await optimized_batch_processor.start()
        
        try:
            # 获取内存池统计（初始状态）
            memory_pool = get_memory_pool()
            initial_stats = memory_pool.get_pool_stats()
            
            # 提交大量项目以触发内存池使用
            items = []
            for i in range(100):
                item = {
                    'id': i,
                    'data': f'test_data_{i}',
                    'timestamp': time.time()
                }
                items.append(item)
                await optimized_batch_processor.submit(item)
            
            # 等待处理完成
            await asyncio.sleep(3.0)
            
            # 检查内存池使用情况
            final_stats = memory_pool.get_pool_stats()
            
            # 应该有对象被创建和复用
            assert final_stats['total_created'] > initial_stats['total_created']
            assert final_stats['total_reused'] > initial_stats['total_reused']
            
            # 检查批处理器指标
            batch_metrics = optimized_batch_processor.get_metrics()
            assert batch_metrics['total_items'] >= 100
            
            # 验证内存池的hit rate
            if final_stats['total_reused'] > 0:
                assert final_stats['hit_rate_percent'] > 0
        
        finally:
            await optimized_batch_processor.stop()

    @pytest.mark.asyncio
    async def test_cache_integration_with_high_frequency_operations(self, optimized_codec):
        """测试缓存与高频操作的集成"""
        # 创建一些重复的信号数据
        base_signals = []
        for i in range(5):
            signal = {
                'signal_id': f'base_signal_{i}',
                'symbol': 'EURUSD',
                'action': 'BUY',
                'volume': 0.1
            }
            base_signals.append(signal)
        
        # 编码基础信号
        encoded_base_signals = []
        for signal in base_signals:
            encoded = optimized_codec.encode_trade_signal(signal)
            encoded_base_signals.append(encoded)
        
        # 高频解码测试（模拟重复解码相同数据）
        decode_start = time.time()
        
        for _ in range(100):  # 每个信号解码20次
            for encoded in encoded_base_signals:
                decoded = optimized_codec.decode_trade_signal(encoded)
                assert decoded is not None
        
        decode_time = time.time() - decode_start
        
        # 检查缓存效果
        cache_stats = optimized_codec.get_cache_stats()
        
        # 应该有很高的缓存命中率
        assert cache_stats['hits'] > cache_stats['misses']
        assert cache_stats['hit_rate_percent'] > 80  # 应该有80%以上的命中率
        
        print(f"\n高频解码测试:")
        print(f"解码时间: {decode_time:.3f}s (500次解码)")
        print(f"平均每次解码: {decode_time/500*1000:.3f}ms")
        print(f"缓存命中率: {cache_stats['hit_rate_percent']:.1f}%")
        
        # 性能应该很好
        assert decode_time < 2.0  # 500次解码应该在2秒内完成

    @pytest.mark.asyncio
    async def test_concurrent_optimizations_stress_test(self, optimized_batch_processor, 
                                                       optimized_connection_pool, 
                                                       optimized_codec, 
                                                       mock_mt5_factory):
        """测试并发场景下的优化效果"""
        # 设置连接池
        optimized_connection_pool.set_connection_factory(mock_mt5_factory)
        optimized_connection_pool.set_connection_validator(AsyncMock(return_value=True))
        
        # 启动组件
        await optimized_batch_processor.start()
        await optimized_connection_pool.start()
        
        try:
            # 并发任务1：高频编解码
            async def codec_worker(worker_id):
                results = []
                for i in range(50):
                    signal = {
                        'signal_id': f'worker_{worker_id}_signal_{i}',
                        'symbol': 'EURUSD',
                        'action': 'BUY'
                    }
                    
                    # 编码解码
                    encoded = optimized_codec.encode_trade_signal(signal)
                    decoded = optimized_codec.decode_trade_signal(encoded)
                    results.append(decoded)
                
                return results
            
            # 并发任务2：批量处理
            async def batch_worker(worker_id):
                for i in range(30):
                    item = {
                        'worker_id': worker_id,
                        'item_id': i,
                        'data': f'batch_data_{worker_id}_{i}'
                    }
                    await optimized_batch_processor.submit(item)
            
            # 并发任务3：批量命令
            async def command_worker(worker_id):
                commands = []
                for i in range(10):
                    command = BatchCommand(
                        command_id=f"worker_{worker_id}_cmd_{i}",
                        command_type="account_info",
                        parameters={'account_id': f'test_account_{worker_id}'}
                    )
                    commands.append(command)
                
                return await optimized_connection_pool.batch_execute_commands(commands)
            
            # 启动并发任务
            start_time = time.time()
            
            codec_tasks = [asyncio.create_task(codec_worker(i)) for i in range(3)]
            batch_tasks = [asyncio.create_task(batch_worker(i)) for i in range(3)]
            command_tasks = [asyncio.create_task(command_worker(i)) for i in range(2)]
            
            # 等待所有任务完成
            codec_results = await asyncio.gather(*codec_tasks)
            await asyncio.gather(*batch_tasks)
            command_results = await asyncio.gather(*command_tasks)
            
            total_time = time.time() - start_time
            
            # 验证结果
            assert len(codec_results) == 3
            assert len(command_results) == 2
            
            for worker_results in codec_results:
                assert len(worker_results) == 50
            
            for worker_commands in command_results:
                assert len(worker_commands) == 10
                successful = [r for r in worker_commands if r.success]
                assert len(successful) >= 8  # 至少80%成功
            
            # 等待批处理完成
            await asyncio.sleep(2.0)
            
            # 检查各组件的状态
            cache_stats = optimized_codec.get_cache_stats()
            memory_stats = get_memory_pool().get_pool_stats()
            batch_metrics = optimized_batch_processor.get_metrics()
            
            print(f"\n并发压力测试结果:")
            print(f"总执行时间: {total_time:.3f}s")
            print(f"缓存命中率: {cache_stats['hit_rate_percent']:.1f}%")
            print(f"内存池复用率: {memory_stats['hit_rate_percent']:.1f}%")
            print(f"批处理吞吐量: {batch_metrics['throughput_items_per_sec']:.1f} items/s")
            
            # 性能断言
            assert total_time < 10.0  # 所有并发任务应该在10秒内完成
            assert cache_stats['hits'] > 0  # 应该有缓存命中
            assert memory_stats['total_reused'] > 0  # 应该有内存复用
        
        finally:
            await optimized_batch_processor.stop()
            await optimized_connection_pool.stop()

    @pytest.mark.asyncio
    async def test_optimization_health_monitoring(self, optimized_batch_processor, optimized_codec):
        """测试优化组件的健康监控"""
        await optimized_batch_processor.start()
        
        try:
            # 执行一些操作以生成健康数据
            for i in range(20):
                signal = {'signal_id': f'health_test_{i}', 'symbol': 'EURUSD'}
                
                # 编解码操作
                encoded = optimized_codec.encode_trade_signal(signal)
                decoded = optimized_codec.decode_trade_signal(encoded)
                
                # 批处理操作
                await optimized_batch_processor.submit(decoded)
            
            await asyncio.sleep(1.0)
            
            # 检查各组件健康状态
            cache_health = optimized_codec.get_cache_health()
            batch_health = optimized_batch_processor.get_health_status()
            memory_health = get_memory_pool().get_pool_stats()
            
            # 验证健康状态
            assert cache_health is not None
            assert 'status' in cache_health
            assert cache_health['status'] in ['excellent', 'good', 'fair', 'poor']
            
            assert batch_health is not None
            assert 'status' in batch_health
            assert batch_health['status'] in ['excellent', 'good', 'fair', 'poor']
            
            # 检查推荐信息
            if 'recommendations' in cache_health:
                assert isinstance(cache_health['recommendations'], list)
            
            if 'recommendations' in batch_health:
                assert isinstance(batch_health['recommendations'], list)
            
            print(f"\n健康状态监控:")
            print(f"缓存健康: {cache_health}")
            print(f"批处理健康: {batch_health}")
            print(f"内存池统计: {memory_health}")
        
        finally:
            await optimized_batch_processor.stop()

    def test_memory_efficiency_comparison(self):
        """测试内存效率对比"""
        # 不使用内存池的情况
        start_time = time.time()
        objects_without_pool = []
        
        for i in range(1000):
            # 直接创建对象
            obj = {
                'items': [],
                'metadata': {},
                'timestamp': time.time(),
                'processed': False
            }
            obj['items'].extend([f'item_{j}' for j in range(10)])
            objects_without_pool.append(obj)
        
        time_without_pool = time.time() - start_time
        
        # 使用内存池的情况
        memory_pool = get_memory_pool()
        memory_pool.cleanup_pools()  # 清理开始状态
        
        start_time = time.time()
        objects_with_pool = []
        
        for i in range(1000):
            # 使用内存池
            obj = memory_pool.get_optimized_object('batch_container')
            obj['items'].extend([f'item_{j}' for j in range(10)])
            objects_with_pool.append(obj)
            
            # 模拟对象使用完毕后归还
            if i % 10 == 0:  # 每10个对象归还一部分
                for returned_obj in objects_with_pool[-5:]:
                    memory_pool.return_object('batch_container', returned_obj)
        
        time_with_pool = time.time() - start_time
        
        # 获取内存池统计
        pool_stats = memory_pool.get_pool_stats()
        
        print(f"\n内存效率对比:")
        print(f"不使用内存池: {time_without_pool:.3f}s")
        print(f"使用内存池: {time_with_pool:.3f}s")
        print(f"内存池复用统计: {pool_stats}")
        
        # 内存池应该有一定的效率提升（通过对象复用）
        assert pool_stats['total_reused'] > 0
        assert pool_stats['hit_rate_percent'] > 0

    @pytest.mark.asyncio
    async def test_scalability_under_load(self, optimized_batch_processor, optimized_codec):
        """测试高负载下的可扩展性"""
        await optimized_batch_processor.start()
        
        try:
            # 模拟高负载场景
            load_sizes = [100, 500, 1000]
            performance_results = []
            
            for load_size in load_sizes:
                print(f"\n测试负载大小: {load_size}")
                
                start_time = time.time()
                
                # 编解码测试
                for i in range(load_size):
                    signal = {
                        'signal_id': f'load_test_{i}',
                        'symbol': 'EURUSD',
                        'action': 'BUY',
                        'timestamp': int(time.time() * 1000)
                    }
                    
                    encoded = optimized_codec.encode_trade_signal(signal)
                    decoded = optimized_codec.decode_trade_signal(encoded)
                    await optimized_batch_processor.submit(decoded)
                
                # 等待处理完成
                await asyncio.sleep(max(1.0, load_size / 100))
                
                processing_time = time.time() - start_time
                throughput = load_size / processing_time
                
                performance_results.append({
                    'load_size': load_size,
                    'processing_time': processing_time,
                    'throughput': throughput
                })
                
                print(f"处理时间: {processing_time:.3f}s")
                print(f"吞吐量: {throughput:.1f} items/s")
            
            # 验证可扩展性
            # 吞吐量不应该随负载大小线性下降
            small_throughput = performance_results[0]['throughput']
            large_throughput = performance_results[-1]['throughput']
            
            # 在大负载下，吞吐量下降不应该超过50%
            throughput_ratio = large_throughput / small_throughput
            assert throughput_ratio > 0.5, f"吞吐量下降过多: {throughput_ratio:.2f}"
            
            print(f"\n可扩展性测试结果:")
            for result in performance_results:
                print(f"负载 {result['load_size']}: {result['throughput']:.1f} items/s")
        
        finally:
            await optimized_batch_processor.stop()


if __name__ == '__main__':
    pytest.main([__file__])