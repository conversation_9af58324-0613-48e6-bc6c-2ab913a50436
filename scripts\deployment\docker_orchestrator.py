#!/usr/bin/env python3
"""
Docker编排优化工具
优化Docker Compose配置，管理容器生命周期，监控资源使用
"""
import asyncio
import json
import yaml
import docker
import time
import psutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ContainerMetrics:
    """容器指标"""
    container_id: str
    name: str
    status: str
    cpu_percent: float
    memory_usage: int
    memory_limit: int
    memory_percent: float
    network_rx: int
    network_tx: int
    disk_read: int
    disk_write: int
    timestamp: float


@dataclass
class ServiceHealth:
    """服务健康状态"""
    service_name: str
    container_count: int
    running_containers: int
    healthy_containers: int
    unhealthy_containers: int
    avg_cpu_percent: float
    avg_memory_percent: float
    total_restarts: int
    last_restart: Optional[float]
    status: str  # 'healthy', 'degraded', 'unhealthy'


class DockerOrchestrator:
    """Docker编排器"""
    
    def __init__(self, compose_file: str, config: Dict = None):
        self.compose_file = Path(compose_file)
        self.config = config or {}
        
        # Docker客户端
        self.docker_client = docker.from_env()
        
        # 状态跟踪
        self.services: Dict[str, ServiceHealth] = {}
        self.container_metrics: Dict[str, List[ContainerMetrics]] = {}
        self.monitoring_active = False
        
        # 配置参数
        self.metrics_interval = self.config.get('metrics_interval', 30)  # 秒
        self.health_check_interval = self.config.get('health_check_interval', 60)  # 秒
        self.auto_restart = self.config.get('auto_restart', True)
        self.resource_limits = self.config.get('resource_limits', {})
        
        # 加载Compose配置
        self.compose_config = self._load_compose_config()
    
    def _load_compose_config(self) -> Dict:
        """加载Docker Compose配置"""
        try:
            with open(self.compose_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载Compose配置失败: {e}")
            return {}
    
    async def start_services(self, services: List[str] = None) -> bool:
        """启动服务"""
        logger.info("🚀 启动Docker服务...")
        
        try:
            # 构建docker-compose命令
            cmd = ["docker-compose", "-f", str(self.compose_file)]
            
            if services:
                cmd.extend(["up", "-d"] + services)
            else:
                cmd.extend(["up", "-d"])
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("✅ 服务启动成功")
                
                # 等待服务稳定
                await asyncio.sleep(10)
                
                # 启动监控
                await self.start_monitoring()
                
                return True
            else:
                logger.error(f"❌ 服务启动失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"启动服务异常: {e}")
            return False
    
    async def stop_services(self, services: List[str] = None) -> bool:
        """停止服务"""
        logger.info("🛑 停止Docker服务...")
        
        try:
            # 停止监控
            await self.stop_monitoring()
            
            # 构建docker-compose命令
            cmd = ["docker-compose", "-f", str(self.compose_file)]
            
            if services:
                cmd.extend(["stop"] + services)
            else:
                cmd.extend(["down"])
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("✅ 服务停止成功")
                return True
            else:
                logger.error(f"❌ 服务停止失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"停止服务异常: {e}")
            return False
    
    async def restart_services(self, services: List[str] = None) -> bool:
        """重启服务"""
        logger.info("🔄 重启Docker服务...")
        
        try:
            # 构建docker-compose命令
            cmd = ["docker-compose", "-f", str(self.compose_file)]
            
            if services:
                cmd.extend(["restart"] + services)
            else:
                cmd.extend(["restart"])
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("✅ 服务重启成功")
                return True
            else:
                logger.error(f"❌ 服务重启失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"重启服务异常: {e}")
            return False
    
    async def start_monitoring(self):
        """启动监控"""
        if self.monitoring_active:
            return
        
        logger.info("📊 启动容器监控...")
        self.monitoring_active = True
        
        # 启动监控任务
        asyncio.create_task(self._metrics_collection_loop())
        asyncio.create_task(self._health_check_loop())
        asyncio.create_task(self._auto_healing_loop())
    
    async def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        logger.info("📊 停止容器监控")
    
    async def _metrics_collection_loop(self):
        """指标收集循环"""
        while self.monitoring_active:
            try:
                await self._collect_container_metrics()
                await asyncio.sleep(self.metrics_interval)
            except Exception as e:
                logger.error(f"指标收集错误: {e}")
                await asyncio.sleep(10)
    
    async def _collect_container_metrics(self):
        """收集容器指标"""
        try:
            containers = self.docker_client.containers.list()
            
            for container in containers:
                # 获取容器统计信息
                stats = container.stats(stream=False)
                
                # 计算CPU使用率
                cpu_percent = self._calculate_cpu_percent(stats)
                
                # 计算内存使用率
                memory_usage = stats['memory_stats'].get('usage', 0)
                memory_limit = stats['memory_stats'].get('limit', 0)
                memory_percent = (memory_usage / memory_limit * 100) if memory_limit > 0 else 0
                
                # 网络统计
                networks = stats.get('networks', {})
                network_rx = sum(net.get('rx_bytes', 0) for net in networks.values())
                network_tx = sum(net.get('tx_bytes', 0) for net in networks.values())
                
                # 磁盘IO统计
                blkio_stats = stats.get('blkio_stats', {})
                disk_read = sum(
                    stat.get('value', 0) 
                    for stat in blkio_stats.get('io_service_bytes_recursive', [])
                    if stat.get('op') == 'Read'
                )
                disk_write = sum(
                    stat.get('value', 0)
                    for stat in blkio_stats.get('io_service_bytes_recursive', [])
                    if stat.get('op') == 'Write'
                )
                
                # 创建指标对象
                metric = ContainerMetrics(
                    container_id=container.id[:12],
                    name=container.name,
                    status=container.status,
                    cpu_percent=cpu_percent,
                    memory_usage=memory_usage,
                    memory_limit=memory_limit,
                    memory_percent=memory_percent,
                    network_rx=network_rx,
                    network_tx=network_tx,
                    disk_read=disk_read,
                    disk_write=disk_write,
                    timestamp=time.time()
                )
                
                # 存储指标
                if container.name not in self.container_metrics:
                    self.container_metrics[container.name] = []
                
                self.container_metrics[container.name].append(metric)
                
                # 只保留最近100个指标
                if len(self.container_metrics[container.name]) > 100:
                    self.container_metrics[container.name] = self.container_metrics[container.name][-100:]
                
                logger.debug(f"收集容器指标: {container.name} - CPU: {cpu_percent:.1f}%, 内存: {memory_percent:.1f}%")
        
        except Exception as e:
            logger.error(f"收集容器指标失败: {e}")
    
    def _calculate_cpu_percent(self, stats: Dict) -> float:
        """计算CPU使用百分比"""
        try:
            cpu_stats = stats['cpu_stats']
            precpu_stats = stats['precpu_stats']
            
            cpu_delta = cpu_stats['cpu_usage']['total_usage'] - precpu_stats['cpu_usage']['total_usage']
            system_delta = cpu_stats['system_cpu_usage'] - precpu_stats['system_cpu_usage']
            
            if system_delta > 0 and cpu_delta > 0:
                cpu_percent = (cpu_delta / system_delta) * len(cpu_stats['cpu_usage']['percpu_usage']) * 100.0
                return round(cpu_percent, 2)
            
            return 0.0
            
        except (KeyError, ZeroDivisionError):
            return 0.0
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.monitoring_active:
            try:
                await self._check_service_health()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                logger.error(f"健康检查错误: {e}")
                await asyncio.sleep(30)
    
    async def _check_service_health(self):
        """检查服务健康状态"""
        try:
            # 获取Compose项目的容器
            project_name = self.compose_file.parent.name
            containers = self.docker_client.containers.list(
                filters={'label': f'com.docker.compose.project={project_name}'}
            )
            
            # 按服务分组
            services_containers = {}
            for container in containers:
                service_name = container.labels.get('com.docker.compose.service', 'unknown')
                if service_name not in services_containers:
                    services_containers[service_name] = []
                services_containers[service_name].append(container)
            
            # 分析每个服务的健康状态
            for service_name, service_containers in services_containers.items():
                health = await self._analyze_service_health(service_name, service_containers)
                self.services[service_name] = health
                
                logger.debug(f"服务健康状态: {service_name} - {health.status}")
        
        except Exception as e:
            logger.error(f"检查服务健康状态失败: {e}")
    
    async def _analyze_service_health(self, service_name: str, containers: List) -> ServiceHealth:
        """分析服务健康状态"""
        container_count = len(containers)
        running_containers = len([c for c in containers if c.status == 'running'])
        
        # 检查容器健康状态
        healthy_containers = 0
        unhealthy_containers = 0
        total_restarts = 0
        last_restart = None
        
        cpu_percents = []
        memory_percents = []
        
        for container in containers:
            # 检查健康状态
            health_status = container.attrs.get('State', {}).get('Health', {}).get('Status')
            if health_status == 'healthy' or (health_status is None and container.status == 'running'):
                healthy_containers += 1
            else:
                unhealthy_containers += 1
            
            # 重启次数
            restart_count = container.attrs.get('RestartCount', 0)
            total_restarts += restart_count
            
            # 最后重启时间
            started_at = container.attrs.get('State', {}).get('StartedAt')
            if started_at:
                started_time = datetime.fromisoformat(started_at.replace('Z', '+00:00')).timestamp()
                if last_restart is None or started_time > last_restart:
                    last_restart = started_time
            
            # 获取资源使用情况
            if container.name in self.container_metrics and self.container_metrics[container.name]:
                latest_metric = self.container_metrics[container.name][-1]
                cpu_percents.append(latest_metric.cpu_percent)
                memory_percents.append(latest_metric.memory_percent)
        
        # 计算平均资源使用率
        avg_cpu_percent = sum(cpu_percents) / len(cpu_percents) if cpu_percents else 0
        avg_memory_percent = sum(memory_percents) / len(memory_percents) if memory_percents else 0
        
        # 确定整体健康状态
        if running_containers == 0:
            status = 'unhealthy'
        elif healthy_containers == container_count:
            status = 'healthy'
        elif healthy_containers > 0:
            status = 'degraded'
        else:
            status = 'unhealthy'
        
        return ServiceHealth(
            service_name=service_name,
            container_count=container_count,
            running_containers=running_containers,
            healthy_containers=healthy_containers,
            unhealthy_containers=unhealthy_containers,
            avg_cpu_percent=avg_cpu_percent,
            avg_memory_percent=avg_memory_percent,
            total_restarts=total_restarts,
            last_restart=last_restart,
            status=status
        )
    
    async def _auto_healing_loop(self):
        """自动修复循环"""
        while self.monitoring_active:
            try:
                if self.auto_restart:
                    await self._perform_auto_healing()
                await asyncio.sleep(120)  # 每2分钟检查一次
            except Exception as e:
                logger.error(f"自动修复错误: {e}")
                await asyncio.sleep(60)
    
    async def _perform_auto_healing(self):
        """执行自动修复"""
        for service_name, health in self.services.items():
            if health.status == 'unhealthy':
                logger.warning(f"检测到不健康服务: {service_name}")
                
                # 尝试重启服务
                success = await self.restart_services([service_name])
                if success:
                    logger.info(f"✅ 自动修复服务成功: {service_name}")
                else:
                    logger.error(f"❌ 自动修复服务失败: {service_name}")
    
    async def scale_service(self, service_name: str, replicas: int) -> bool:
        """扩缩容服务"""
        logger.info(f"🔧 扩缩容服务: {service_name} -> {replicas} 副本")
        
        try:
            cmd = [
                "docker-compose", "-f", str(self.compose_file),
                "up", "-d", "--scale", f"{service_name}={replicas}"
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info(f"✅ 服务扩缩容成功: {service_name}")
                return True
            else:
                logger.error(f"❌ 服务扩缩容失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"扩缩容服务异常: {e}")
            return False
    
    async def get_service_logs(self, service_name: str, lines: int = 100) -> str:
        """获取服务日志"""
        try:
            cmd = [
                "docker-compose", "-f", str(self.compose_file),
                "logs", "--tail", str(lines), service_name
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return stdout.decode()
            else:
                return f"获取日志失败: {stderr.decode()}"
                
        except Exception as e:
            return f"获取日志异常: {e}"
    
    def get_cluster_status(self) -> Dict:
        """获取集群状态"""
        return {
            'timestamp': time.time(),
            'services': {
                name: asdict(health) for name, health in self.services.items()
            },
            'total_services': len(self.services),
            'healthy_services': len([s for s in self.services.values() if s.status == 'healthy']),
            'degraded_services': len([s for s in self.services.values() if s.status == 'degraded']),
            'unhealthy_services': len([s for s in self.services.values() if s.status == 'unhealthy']),
            'total_containers': sum(s.container_count for s in self.services.values()),
            'running_containers': sum(s.running_containers for s in self.services.values()),
            'system_metrics': self._get_system_metrics()
        }
    
    def _get_system_metrics(self) -> Dict:
        """获取系统指标"""
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0],
            'network_connections': len(psutil.net_connections()),
            'boot_time': psutil.boot_time()
        }
    
    def get_resource_usage_report(self) -> Dict:
        """获取资源使用报告"""
        report = {
            'timestamp': time.time(),
            'services': {},
            'total_cpu_percent': 0,
            'total_memory_usage': 0,
            'total_network_rx': 0,
            'total_network_tx': 0,
            'total_disk_read': 0,
            'total_disk_write': 0
        }
        
        for container_name, metrics_list in self.container_metrics.items():
            if metrics_list:
                latest_metric = metrics_list[-1]
                
                report['services'][container_name] = {
                    'cpu_percent': latest_metric.cpu_percent,
                    'memory_usage_mb': latest_metric.memory_usage / 1024 / 1024,
                    'memory_percent': latest_metric.memory_percent,
                    'network_rx_mb': latest_metric.network_rx / 1024 / 1024,
                    'network_tx_mb': latest_metric.network_tx / 1024 / 1024,
                    'disk_read_mb': latest_metric.disk_read / 1024 / 1024,
                    'disk_write_mb': latest_metric.disk_write / 1024 / 1024,
                    'status': latest_metric.status
                }
                
                # 累计总计
                report['total_cpu_percent'] += latest_metric.cpu_percent
                report['total_memory_usage'] += latest_metric.memory_usage
                report['total_network_rx'] += latest_metric.network_rx
                report['total_network_tx'] += latest_metric.network_tx
                report['total_disk_read'] += latest_metric.disk_read
                report['total_disk_write'] += latest_metric.disk_write
        
        # 转换为MB
        report['total_memory_usage_mb'] = report['total_memory_usage'] / 1024 / 1024
        report['total_network_rx_mb'] = report['total_network_rx'] / 1024 / 1024
        report['total_network_tx_mb'] = report['total_network_tx'] / 1024 / 1024
        report['total_disk_read_mb'] = report['total_disk_read'] / 1024 / 1024
        report['total_disk_write_mb'] = report['total_disk_write'] / 1024 / 1024
        
        return report
    
    async def optimize_resources(self):
        """优化资源配置"""
        logger.info("🔧 开始资源优化...")
        
        # 分析资源使用模式
        resource_analysis = self._analyze_resource_patterns()
        
        # 生成优化建议
        recommendations = self._generate_optimization_recommendations(resource_analysis)
        
        # 应用优化建议
        for recommendation in recommendations:
            await self._apply_optimization(recommendation)
        
        logger.info("✅ 资源优化完成")
    
    def _analyze_resource_patterns(self) -> Dict:
        """分析资源使用模式"""
        analysis = {}
        
        for container_name, metrics_list in self.container_metrics.items():
            if len(metrics_list) < 10:  # 需要足够的数据点
                continue
            
            recent_metrics = metrics_list[-30:]  # 最近30个数据点
            
            cpu_values = [m.cpu_percent for m in recent_metrics]
            memory_values = [m.memory_percent for m in recent_metrics]
            
            analysis[container_name] = {
                'avg_cpu': sum(cpu_values) / len(cpu_values),
                'max_cpu': max(cpu_values),
                'avg_memory': sum(memory_values) / len(memory_values),
                'max_memory': max(memory_values),
                'cpu_trend': 'increasing' if cpu_values[-5:] > cpu_values[:5] else 'stable',
                'memory_trend': 'increasing' if memory_values[-5:] > memory_values[:5] else 'stable'
            }
        
        return analysis
    
    def _generate_optimization_recommendations(self, analysis: Dict) -> List[Dict]:
        """生成优化建议"""
        recommendations = []
        
        for container_name, stats in analysis.items():
            # CPU优化建议
            if stats['avg_cpu'] > 80:
                recommendations.append({
                    'type': 'scale_up',
                    'container': container_name,
                    'reason': 'high_cpu_usage',
                    'current_cpu': stats['avg_cpu']
                })
            elif stats['avg_cpu'] < 20:
                recommendations.append({
                    'type': 'scale_down',
                    'container': container_name,
                    'reason': 'low_cpu_usage',
                    'current_cpu': stats['avg_cpu']
                })
            
            # 内存优化建议
            if stats['avg_memory'] > 85:
                recommendations.append({
                    'type': 'increase_memory',
                    'container': container_name,
                    'reason': 'high_memory_usage',
                    'current_memory': stats['avg_memory']
                })
        
        return recommendations
    
    async def _apply_optimization(self, recommendation: Dict):
        """应用优化建议"""
        logger.info(f"应用优化建议: {recommendation}")
        
        # 这里可以实现具体的优化逻辑
        # 例如调整容器资源限制、扩缩容等
        pass


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Docker编排优化工具')
    parser.add_argument('--compose-file', required=True, help='Docker Compose文件路径')
    parser.add_argument('--action', choices=['start', 'stop', 'restart', 'status', 'monitor'], 
                       default='status', help='执行的操作')
    parser.add_argument('--services', nargs='*', help='指定服务名称')
    parser.add_argument('--monitor-duration', type=int, default=300, help='监控持续时间(秒)')
    
    args = parser.parse_args()
    
    orchestrator = DockerOrchestrator(args.compose_file)
    
    try:
        if args.action == 'start':
            success = await orchestrator.start_services(args.services)
            print("✅ 服务启动成功" if success else "❌ 服务启动失败")
            
        elif args.action == 'stop':
            success = await orchestrator.stop_services(args.services)
            print("✅ 服务停止成功" if success else "❌ 服务停止失败")
            
        elif args.action == 'restart':
            success = await orchestrator.restart_services(args.services)
            print("✅ 服务重启成功" if success else "❌ 服务重启失败")
            
        elif args.action == 'status':
            status = orchestrator.get_cluster_status()
            print(json.dumps(status, indent=2, ensure_ascii=False))
            
        elif args.action == 'monitor':
            await orchestrator.start_monitoring()
            print(f"📊 开始监控 {args.monitor_duration} 秒...")
            await asyncio.sleep(args.monitor_duration)
            
            # 生成报告
            status = orchestrator.get_cluster_status()
            resource_report = orchestrator.get_resource_usage_report()
            
            print("\n=== 集群状态 ===")
            print(json.dumps(status, indent=2, ensure_ascii=False))
            print("\n=== 资源使用报告 ===")
            print(json.dumps(resource_report, indent=2, ensure_ascii=False))
            
            await orchestrator.stop_monitoring()
        
        return 0
        
    except Exception as e:
        logger.error(f"操作失败: {e}")
        print(f"❌ 操作失败: {e}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
