#!/usr/bin/env python3
"""
配置文件清理脚本
安全地替换旧配置文件为新配置文件
"""
import os
import shutil
from pathlib import Path
from datetime import datetime


class ConfigCleaner:
    """配置清理器"""
    
    def __init__(self, config_root: str = "config"):
        self.config_root = Path(config_root)
        
    def cleanup(self, dry_run: bool = False):
        """执行配置清理"""
        print("🧹 开始配置文件清理...")
        
        if dry_run:
            print("🔍 试运行模式，不会实际修改文件")
        
        # 1. 替换主配置文件
        self._replace_main_config(dry_run)
        
        # 2. 替换账户配置目录
        self._replace_accounts_directory(dry_run)
        
        # 3. 移除不需要的文件
        self._remove_obsolete_files(dry_run)
        
        # 4. 更新README文件
        self._update_readme_files(dry_run)
        
        print("✅ 配置清理完成！")
        
    def _replace_main_config(self, dry_run: bool):
        """替换主配置文件"""
        print("⚙️ 替换主配置文件...")
        
        old_config = self.config_root / "system.yaml"
        new_config = self.config_root / "system_distributed.yaml"
        
        if new_config.exists():
            if not dry_run:
                # 备份旧配置
                if old_config.exists():
                    backup_name = f"system_old_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
                    backup_path = self.config_root / "backup" / backup_name
                    backup_path.parent.mkdir(exist_ok=True)
                    shutil.move(old_config, backup_path)
                    print(f"  📦 旧配置已备份: {backup_name}")
                
                # 重命名新配置为主配置
                shutil.copy2(new_config, old_config)
                print(f"  ✅ 新配置已设为主配置: system.yaml")
            else:
                print(f"  🔍 将替换: {old_config} -> {new_config}")
        else:
            print(f"  ⚠️ 新配置文件不存在: {new_config}")
    
    def _replace_accounts_directory(self, dry_run: bool):
        """替换账户配置目录"""
        print("👥 替换账户配置目录...")
        
        old_accounts = self.config_root / "accounts"
        new_accounts = self.config_root / "accounts_new"
        
        if new_accounts.exists():
            if not dry_run:
                # 备份旧账户目录
                if old_accounts.exists():
                    backup_name = f"accounts_old_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    backup_path = self.config_root / "backup" / backup_name
                    backup_path.parent.mkdir(exist_ok=True)
                    shutil.move(old_accounts, backup_path)
                    print(f"  📦 旧账户目录已备份: {backup_name}")
                
                # 重命名新账户目录
                shutil.move(new_accounts, old_accounts)
                print(f"  ✅ 新账户目录已设为主目录: accounts/")
            else:
                print(f"  🔍 将替换: {old_accounts} -> {new_accounts}")
        else:
            print(f"  ⚠️ 新账户目录不存在: {new_accounts}")
    
    def _remove_obsolete_files(self, dry_run: bool):
        """移除过时的文件"""
        print("🗑️ 移除过时的文件...")
        
        # 需要移除的文件列表
        obsolete_files = [
            "distributed_example.yaml",  # 已经集成到主配置
            "active_pairings.yaml",      # 已迁移到 pairings/
            "pairing_rules.yaml",        # 已迁移到 pairings/
            "grades.yaml",               # 已废弃
            "network_topology.yaml"      # 已集成到主配置
        ]
        
        for file_name in obsolete_files:
            file_path = self.config_root / file_name
            if file_path.exists():
                if not dry_run:
                    # 移动到备份目录
                    backup_dir = self.config_root / "backup" / "obsolete_files"
                    backup_dir.mkdir(parents=True, exist_ok=True)
                    shutil.move(file_path, backup_dir / file_name)
                    print(f"  📦 已移除: {file_name}")
                else:
                    print(f"  🔍 将移除: {file_name}")
    
    def _update_readme_files(self, dry_run: bool):
        """更新README文件"""
        print("📖 更新README文件...")
        
        old_readme = self.config_root / "README.md"
        new_readme = self.config_root / "README_NEW_CONFIG.md"
        
        if new_readme.exists():
            if not dry_run:
                # 备份旧README
                if old_readme.exists():
                    backup_name = f"README_old_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
                    backup_path = self.config_root / "backup" / backup_name
                    backup_path.parent.mkdir(exist_ok=True)
                    shutil.move(old_readme, backup_path)
                    print(f"  📦 旧README已备份: {backup_name}")
                
                # 重命名新README
                shutil.copy2(new_readme, old_readme)
                print(f"  ✅ 新README已设为主README: README.md")
            else:
                print(f"  🔍 将替换: {old_readme} -> {new_readme}")
    
    def verify_cleanup(self):
        """验证清理结果"""
        print("🔍 验证清理结果...")
        
        # 检查主要文件是否存在
        required_files = [
            "system.yaml",
            "accounts/ACC001.yaml",
            "accounts/ACC002.yaml", 
            "accounts/ACC003.yaml",
            "roles/role_definitions.yaml",
            "pairings/dynamic_pairings.yaml"
        ]
        
        all_good = True
        for file_path in required_files:
            full_path = self.config_root / file_path
            if full_path.exists():
                print(f"  ✅ {file_path}")
            else:
                print(f"  ❌ {file_path} - 缺失")
                all_good = False
        
        # 检查新配置结构
        new_structure = {
            "accounts/": "账户配置目录",
            "roles/": "角色定义目录", 
            "pairings/": "配对管理目录",
            "backup/": "备份目录"
        }
        
        print("\n📁 目录结构检查:")
        for dir_path, description in new_structure.items():
            full_path = self.config_root / dir_path
            if full_path.exists():
                print(f"  ✅ {dir_path} - {description}")
            else:
                print(f"  ❌ {dir_path} - {description} (缺失)")
                all_good = False
        
        if all_good:
            print("\n🎉 配置清理验证通过！")
        else:
            print("\n⚠️ 配置清理存在问题，请检查")
        
        return all_good


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='配置文件清理工具')
    parser.add_argument('--config-root', default='config', help='配置文件根目录')
    parser.add_argument('--dry-run', action='store_true', help='试运行，不实际修改文件')
    parser.add_argument('--verify-only', action='store_true', help='仅验证，不执行清理')
    
    args = parser.parse_args()
    
    cleaner = ConfigCleaner(args.config_root)
    
    if args.verify_only:
        cleaner.verify_cleanup()
    else:
        cleaner.cleanup(args.dry_run)
        if not args.dry_run:
            cleaner.verify_cleanup()


if __name__ == "__main__":
    main()
