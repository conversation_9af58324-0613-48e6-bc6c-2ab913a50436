# src/distributed/account_registry.py
# 🚨 AUTO-MIGRATED: Old messaging components replaced with QueueManager
# See MIGRATION_GUIDE.md for details

"""
分布式账户注册表
全局账户发现和管理
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from enum import Enum

from ..messaging.queue_manager import QueueManager
from ..utils.logger import get_logger
from ..core.config_manager import ConfigManager

logger = get_logger(__name__)


class AccountType(Enum):
    """账户类型"""
    MASTER = "master"
    SLAVE = "slave"
    HYBRID = "hybrid"


class AccountStatus(Enum):
    """账户状态"""
    ONLINE = "online"
    OFFLINE = "offline"
    ERROR = "error"
    MAINTENANCE = "maintenance"


# Import unified Pydantic models instead of defining dataclasses
from ..messaging.message_types import AccountInfo, PairingRule


class DistributedAccountRegistry:
    """分布式账户注册表"""
    
    def __init__(self, queue_manager: JetStreamClient, 
                 config_manager: Config<PERSON><PERSON><PERSON>, local_host_id: str):
        self.jetstream = queue_manager
        self.config = config_manager
        self.local_host_id = local_host_id
        
        # 账户注册表
        self.accounts: Dict[str, AccountInfo] = {}
        self.local_accounts: Set[str] = set()
        
        # 配对规则
        self.pairing_rules: Dict[str, PairingRule] = {}
        
        # 运行状态
        self.running = False
        
        # 统计信息
        self.stats = {
            'total_accounts': 0,
            'local_accounts': 0,
            'master_accounts': 0,
            'slave_accounts': 0,
            'active_pairings': 0
        }
        
        logger.info(f"分布式账户注册表初始化完成 - 主机: {local_host_id}")
    
    async def start(self):
        """启动注册表"""
        if self.running:
            return
        
        self.running = True
        
        # 确保流存在
        await self._ensure_streams()
        
        # 设置订阅
        await self._setup_subscriptions()
        
        # 加载本地配对规则
        await self._load_pairing_rules()
        
        # 启动心跳任务
        asyncio.create_task(self._heartbeat_task())
        
        logger.info("分布式账户注册表已启动")
    
    async def stop(self):
        """停止注册表"""
        self.running = False
        
        # 注销本地账户
        for account_id in self.local_accounts:
            await self._unregister_account(account_id)
        
        logger.info("分布式账户注册表已停止")
    
    async def _ensure_streams(self):
        """确保JetStream流存在"""
        streams = [
            {
                'name': 'MT5_ACCOUNTS',
                'subjects': ['MT5.ACCOUNT.*'],
                'retention': 'limits',
                'max_age': 3600,
                'max_msgs': 50000
            },
            {
                'name': 'MT5_PAIRINGS',
                'subjects': ['MT5.PAIRING.*'],
                'retention': 'limits',
                'max_age': 3600,
                'max_msgs': 10000
            }
        ]
        
        for stream_config in streams:
            try:
                await self.jetstream.add_stream(**stream_config)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.error(f"创建流失败: {stream_config['name']} - {e}")
    
    async def _setup_subscriptions(self):
        """设置订阅"""
        # 订阅账户注册
        await self.jetstream.subscribe(
            "MT5.ACCOUNT.REGISTER",
            self._handle_account_register
        )
        
        # 订阅账户注销
        await self.jetstream.subscribe(
            "MT5.ACCOUNT.UNREGISTER",
            self._handle_account_unregister
        )
        
        # 订阅账户心跳
        await self.jetstream.subscribe(
            "MT5.ACCOUNT.HEARTBEAT.*",
            self._handle_account_heartbeat
        )
        
        # 订阅配对更新
        await self.jetstream.subscribe(
            "MT5.PAIRING.UPDATE",
            self._handle_pairing_update
        )
    
    async def register_account(self, account_id: str, account_type: AccountType, 
                              capabilities: Set[str] = None) -> bool:
        """注册账户"""
        try:
            if capabilities is None:
                capabilities = set()
            
            account_info = AccountInfo(
                account_id=account_id,
                account_type=account_type,
                host_id=self.local_host_id,
                status=AccountStatus.ONLINE,
                last_heartbeat=time.time(),
                capabilities=capabilities
            )
            
            # 添加到本地注册表
            self.accounts[account_id] = account_info
            self.local_accounts.add(account_id)
            
            # 广播注册信息
            await self._broadcast_account_register(account_info)
            
            # 更新统计
            self._update_stats()
            
            logger.info(f"账户注册成功: {account_id} ({account_type.value})")
            return True
            
        except Exception as e:
            logger.error(f"账户注册失败: {account_id} - {e}")
            return False
    
    async def unregister_account(self, account_id: str) -> bool:
        """注销账户"""
        try:
            if account_id in self.local_accounts:
                await self._unregister_account(account_id)
                return True
            else:
                logger.warning(f"尝试注销非本地账户: {account_id}")
                return False
                
        except Exception as e:
            logger.error(f"账户注销失败: {account_id} - {e}")
            return False
    
    async def _unregister_account(self, account_id: str):
        """内部注销账户"""
        if account_id in self.accounts:
            # 广播注销信息
            await self.jetstream.publish(
                "MT5.ACCOUNT.UNREGISTER",
                json.dumps({
                    'account_id': account_id,
                    'host_id': self.local_host_id,
                    'timestamp': time.time()
                }).encode('utf-8'))
            
            
            # 从本地移除
            del self.accounts[account_id]
            self.local_accounts.discard(account_id)
            
            # 更新统计
            self._update_stats()
            
            logger.info(f"账户注销: {account_id}")
    
    async def _broadcast_account_register(self, account_info: AccountInfo):
        """广播账户注册"""
        payload = {
            'account_info': asdict(account_info),
            'timestamp': time.time()
        }
        
        # 转换枚举为字符串
        payload['account_info']['account_type'] = account_info.account_type.value
        payload['account_info']['status'] = account_info.status.value
        payload['account_info']['capabilities'] = list(account_info.capabilities)
        
        await self.jetstream.publish(
            "MT5.ACCOUNT.REGISTER",
            json.dumps(payload).encode('utf-8')
        )
    
    async def _handle_account_register(self, msg):
        """处理账户注册消息"""
        try:
            # 统一处理消息数据
            if isinstance(msg.data, (bytes, str)):
                if isinstance(msg.data, bytes):
                    data = json.loads(msg.data.decode('utf-8'))
                else:
                    data = json.loads(msg.data)
            else:
                data = msg.data  # 已经是字典

            account_data = data['account_info']
            
            account_id = account_data['account_id']
            host_id = account_data['host_id']
            
            # 不处理本地账户
            if host_id != self.local_host_id:
                account_info = AccountInfo(
                    account_id=account_id,
                    account_type=AccountType(account_data['account_type']),
                    host_id=host_id,
                    status=AccountStatus(account_data['status']),
                    last_heartbeat=account_data['last_heartbeat'],
                    capabilities=set(account_data['capabilities']),
                    metadata=account_data.get('metadata', {})
                )
                
                self.accounts[account_id] = account_info
                self._update_stats()
                
                logger.debug(f"远程账户注册: {account_id}@{host_id}")
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理账户注册失败: {e}")
            await msg.nak()
    
    async def _handle_account_unregister(self, msg):
        """处理账户注销消息"""
        try:
            # 统一处理消息数据
            if isinstance(msg.data, (bytes, str)):
                if isinstance(msg.data, bytes):
                    data = json.loads(msg.data.decode('utf-8'))
                else:
                    data = json.loads(msg.data)
            else:
                data = msg.data  # 已经是字典

            account_id = data['account_id']
            host_id = data['host_id']
            
            # 不处理本地账户
            if host_id != self.local_host_id and account_id in self.accounts:
                del self.accounts[account_id]
                self._update_stats()
                
                logger.debug(f"远程账户注销: {account_id}@{host_id}")
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理账户注销失败: {e}")
            await msg.nak()
    
    async def _handle_account_heartbeat(self, msg):
        """处理账户心跳"""
        try:
            # 统一处理消息数据
            if isinstance(msg.data, (bytes, str)):
                if isinstance(msg.data, bytes):
                    data = json.loads(msg.data.decode('utf-8'))
                else:
                    data = json.loads(msg.data)
            else:
                data = msg.data  # 已经是字典

            account_id = data['account_id']
            
            if account_id in self.accounts:
                self.accounts[account_id].last_heartbeat = time.time()
                self.accounts[account_id].status = AccountStatus(data.get('status', 'online'))
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理账户心跳失败: {e}")
            await msg.nak()
    
    async def _load_pairing_rules(self):
        """加载配对规则"""
        try:
            pairings = self.config.get_active_pairings()
            
            for pairing_data in pairings:
                master_id = pairing_data.get('master_account_id')
                slaves = pairing_data.get('slaves', [])
                
                if master_id:
                    slave_ids = [s.get('slave_account_id') for s in slaves if s.get('enabled', True)]
                    
                    rule = PairingRule(
                        master_account=master_id,
                        slave_accounts=slave_ids,
                        copy_ratio=pairing_data.get('copy_ratio', 1.0),
                        enabled=pairing_data.get('enabled', True)
                    )
                    
                    self.pairing_rules[master_id] = rule
            
            logger.info(f"加载配对规则: {len(self.pairing_rules)} 个")
            
        except Exception as e:
            logger.error(f"加载配对规则失败: {e}")
    
    async def _handle_pairing_update(self, msg):
        """处理配对更新"""
        try:
            # 统一处理消息数据
            if isinstance(msg.data, (bytes, str)):
                if isinstance(msg.data, bytes):
                    data = json.loads(msg.data.decode('utf-8'))
                else:
                    data = json.loads(msg.data)
            else:
                data = msg.data  # 已经是字典

            # 处理配对规则更新
            # 这里可以实现动态配对规则更新
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理配对更新失败: {e}")
            await msg.nak()
    
    async def _heartbeat_task(self):
        """心跳任务"""
        while self.running:
            try:
                # 发送本地账户心跳
                for account_id in self.local_accounts:
                    if account_id in self.accounts:
                        heartbeat = {
                            'account_id': account_id,
                            'host_id': self.local_host_id,
                            'status': self.accounts[account_id].status.value,
                            'timestamp': time.time()
                        }
                        
                        await self.jetstream.publish(
                            f"MT5.ACCOUNT.HEARTBEAT.{account_id}",
                            json.dumps(heartbeat).encode('utf-8')
                        )
                
                # 清理离线账户
                await self._cleanup_offline_accounts()
                
                await asyncio.sleep(30)  # 每30秒心跳
                
            except Exception as e:
                logger.error(f"心跳任务错误: {e}")
                await asyncio.sleep(10)
    
    async def _cleanup_offline_accounts(self):
        """清理离线账户"""
        current_time = time.time()
        offline_accounts = []
        
        for account_id, account_info in self.accounts.items():
            if account_id not in self.local_accounts:  # 只清理远程账户
                if (current_time - account_info.last_heartbeat) > 120:  # 2分钟超时
                    offline_accounts.append(account_id)
        
        for account_id in offline_accounts:
            del self.accounts[account_id]
            logger.warning(f"账户离线: {account_id}")
        
        if offline_accounts:
            self._update_stats()
    
    def _update_stats(self):
        """更新统计信息"""
        self.stats['total_accounts'] = len(self.accounts)
        self.stats['local_accounts'] = len(self.local_accounts)
        self.stats['master_accounts'] = sum(1 for acc in self.accounts.values() 
                                          if acc.account_type == AccountType.MASTER)
        self.stats['slave_accounts'] = sum(1 for acc in self.accounts.values() 
                                         if acc.account_type == AccountType.SLAVE)
        self.stats['active_pairings'] = len(self.pairing_rules)
    
    def get_account_info(self, account_id: str) -> Optional[AccountInfo]:
        """获取账户信息"""
        return self.accounts.get(account_id)
    
    def get_accounts_by_type(self, account_type: AccountType) -> List[AccountInfo]:
        """按类型获取账户"""
        return [acc for acc in self.accounts.values() if acc.account_type == account_type]
    
    def get_accounts_by_host(self, host_id: str) -> List[AccountInfo]:
        """按主机获取账户"""
        return [acc for acc in self.accounts.values() if acc.host_id == host_id]
    
    def get_pairing_targets(self, master_account: str) -> List[str]:
        """获取主账户的配对目标"""
        rule = self.pairing_rules.get(master_account)
        return rule.slave_accounts if rule and rule.enabled else []
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        return {
            **self.stats,
            'running': self.running
        }