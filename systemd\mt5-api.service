[Unit]
Description=MT5 Trade Copier API Service
After=network.target redis.service nats.service
Wants=redis.service nats.service
StartLimitIntervalSec=60
StartLimitBurst=3

[Service]
Type=simple
User=mt5
Group=mt5
WorkingDirectory=/opt/mt5-copier
Environment="PYTHONPATH=/opt/mt5-copier"
Environment="MT5_CONFIG_PATH=/opt/mt5-copier/config/config.yaml"
Environment="MT5_LOG_LEVEL=INFO"
ExecStart=/opt/mt5-copier/venv/bin/python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
TimeoutStopSec=30
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mt5-api

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/mt5-copier/logs /opt/mt5-copier/data
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

[Install]
WantedBy=multi-user.target