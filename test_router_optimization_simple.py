#!/usr/bin/env python3
"""
简化的消息路由器优化测试
专注于核心功能验证
"""

import asyncio
import time
import sys
import os
from typing import Dict, Any, List

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

# Mock必要的依赖
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARN: {msg}")  
    def error(self, msg): print(f"ERROR: {msg}")
    def debug(self, msg): pass

class MockMetrics:
    def record(self, *args, **kwargs): pass

# 创建mock模块
import types
utils_module = types.ModuleType('utils')
utils_module.logger = types.ModuleType('logger')
utils_module.logger.get_logger = lambda name: MockLogger()
utils_module.metrics = types.ModuleType('metrics')
utils_module.metrics.get_metrics_collector = lambda: MockMetrics()
sys.modules['utils'] = utils_module
sys.modules['utils.logger'] = utils_module.logger
sys.modules['utils.metrics'] = utils_module.metrics

# 现在导入我们的优化路由器
try:
    from src.messaging.message_router import OptimizedMessageRouter
    print("✅ 成功导入优化的消息路由器")
except Exception as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


class SimpleOptimizationTest:
    """简化的优化测试"""
    
    def __init__(self):
        self.results = {}
    
    async def test_startup_time(self):
        """测试启动时间"""
        print("\n🧪 测试启动性能...")
        
        config = {
            'host_id': 'test-host',
            'queue_manager': {
                'primary_backend': 'local_memory',
                'backup_backends': [],
                'local_fallback': True
            }
        }
        
        start_time = time.perf_counter()
        
        router = OptimizedMessageRouter(config, 'test-host')
        success = await router.start()
        
        startup_time = (time.perf_counter() - start_time) * 1000
        
        print(f"  启动成功: {success}")
        print(f"  启动时间: {startup_time:.2f}ms")
        print(f"  非阻塞启动: {'✅' if startup_time < 100 else '❌'}")
        
        await router.stop()
        
        self.results['startup'] = {
            'success': success,
            'time_ms': startup_time,
            'non_blocking': startup_time < 100
        }
    
    async def test_routing_performance(self):
        """测试路由性能"""
        print("\n🧪 测试路由性能...")
        
        config = {
            'host_id': 'test-host',
            'queue_manager': {
                'primary_backend': 'local_memory',
                'backup_backends': [],
                'local_fallback': True
            }
        }
        
        router = OptimizedMessageRouter(config, 'test-host')
        await router.start()
        
        # 测试单次路由
        test_signal = {
            'message_id': 'test_001',
            'source_type': 'monitor',
            'target_account': 'ACC001',
            'target_host_id': 'test-host',
            'command_type': 'place_order'
        }
        
        start_time = time.perf_counter()
        success = await router.route_signal(test_signal)
        single_time = (time.perf_counter() - start_time) * 1000
        
        print(f"  单次路由成功: {success}")
        print(f"  单次路由时间: {single_time:.3f}ms")
        print(f"  性能目标达成: {'✅' if single_time < 1.0 else '❌'}")
        
        # 测试批量路由
        batch_size = 50
        start_time = time.perf_counter()
        
        tasks = []
        for i in range(batch_size):
            signal = test_signal.copy()
            signal['message_id'] = f'batch_{i:03d}'
            tasks.append(router.route_signal(signal))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        batch_time = time.perf_counter() - start_time
        
        success_count = sum(1 for r in results if r is True)
        throughput = batch_size / batch_time
        
        print(f"  批量测试大小: {batch_size}")
        print(f"  批量成功率: {success_count}/{batch_size} ({success_count/batch_size*100:.1f}%)")
        print(f"  批量总时间: {batch_time*1000:.1f}ms")
        print(f"  平均单次时间: {batch_time*1000/batch_size:.3f}ms")
        print(f"  吞吐量: {throughput:.0f} msg/s")
        
        await router.stop()
        
        self.results['routing'] = {
            'single_time_ms': single_time,
            'single_success': success,
            'batch_size': batch_size,
            'batch_success_rate': success_count/batch_size*100,
            'throughput_per_second': throughput,
            'performance_good': single_time < 1.0
        }
    
    async def test_cache_effectiveness(self):
        """测试缓存效果"""
        print("\n🧪 测试缓存效果...")
        
        config = {
            'host_id': 'test-host',
            'queue_manager': {
                'primary_backend': 'local_memory',
                'backup_backends': [],
                'local_fallback': True
            }
        }
        
        router = OptimizedMessageRouter(config, 'test-host')
        await router.start()
        
        # 创建重复的信号来测试缓存
        signals = []
        for i in range(20):
            signal = {
                'message_id': f'cache_test_{i}',
                'source_type': 'monitor',
                'target_account': f'ACC{i % 3:03d}',  # 只有3个不同账户
                'target_host_id': 'test-host',
                'command_type': 'place_order'
            }
            signals.append(signal)
        
        # 第一轮：填充缓存
        for signal in signals:
            await router.route_signal(signal)
        
        # 第二轮：测试缓存命中
        start_time = time.perf_counter()
        for signal in signals:
            await router.route_signal(signal)
        cache_time = (time.perf_counter() - start_time) * 1000
        
        status = router.get_status()
        
        try:
            cache_hit_rate = status['routes'].get('cache_hit_rate', 0)
            cache_size = status['routes'].get('rule_cache_size', 0)
        except:
            cache_hit_rate = 0
            cache_size = 0
        
        print(f"  缓存命中率: {cache_hit_rate:.1f}%")
        print(f"  缓存大小: {cache_size}")
        print(f"  缓存测试时间: {cache_time:.1f}ms")
        print(f"  平均缓存路由时间: {cache_time/len(signals):.3f}ms")
        print(f"  缓存有效: {'✅' if cache_hit_rate > 30 else '❌'}")
        
        await router.stop()
        
        self.results['cache'] = {
            'hit_rate': cache_hit_rate,
            'cache_size': cache_size,
            'avg_time_ms': cache_time/len(signals),
            'effective': cache_hit_rate > 30
        }
    
    async def test_concurrent_performance(self):
        """测试并发性能"""
        print("\n🧪 测试并发性能...")
        
        config = {
            'host_id': 'test-host',
            'queue_manager': {
                'primary_backend': 'local_memory',
                'backup_backends': [],
                'local_fallback': True
            }
        }
        
        router = OptimizedMessageRouter(config, 'test-host')
        await router.start()
        
        async def concurrent_task(task_id: int, count: int):
            results = []
            for i in range(count):
                signal = {
                    'message_id': f'concurrent_{task_id}_{i}',
                    'source_type': 'monitor',
                    'target_account': f'ACC{task_id:03d}',
                    'target_host_id': 'test-host',
                    'command_type': 'place_order'
                }
                result = await router.route_signal(signal)
                results.append(result)
            return results
        
        # 启动并发任务
        task_count = 5
        messages_per_task = 10
        
        start_time = time.perf_counter()
        
        tasks = [concurrent_task(i, messages_per_task) for i in range(task_count)]
        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = (time.perf_counter() - start_time) * 1000
        
        total_messages = task_count * messages_per_task
        successful = 0
        
        for result in task_results:
            if isinstance(result, list):
                successful += sum(1 for r in result if r)
        
        concurrent_throughput = total_messages / (total_time / 1000)
        
        print(f"  并发任务数: {task_count}")
        print(f"  每任务消息数: {messages_per_task}")
        print(f"  总消息数: {total_messages}")
        print(f"  成功消息数: {successful}")
        print(f"  成功率: {successful/total_messages*100:.1f}%")
        print(f"  总时间: {total_time:.1f}ms")
        print(f"  并发吞吐量: {concurrent_throughput:.0f} msg/s")
        print(f"  并发性能良好: {'✅' if total_time < 1000 else '❌'}")
        
        await router.stop()
        
        self.results['concurrent'] = {
            'task_count': task_count,
            'total_messages': total_messages,
            'success_rate': successful/total_messages*100,
            'total_time_ms': total_time,
            'throughput': concurrent_throughput,
            'performance_good': total_time < 1000
        }
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "="*60)
        print("📊 消息路由器优化效果总结")
        print("="*60)
        
        # 检查各项优化效果
        optimizations_successful = 0
        total_optimizations = 0
        
        if 'startup' in self.results:
            total_optimizations += 1
            if self.results['startup']['non_blocking']:
                print("✅ 启动优化: 非阻塞启动成功")
                optimizations_successful += 1
            else:
                print("❌ 启动优化: 仍存在阻塞问题")
        
        if 'routing' in self.results:
            total_optimizations += 1
            if self.results['routing']['performance_good']:
                print("✅ 路由优化: 性能达标 (<1ms)")
                optimizations_successful += 1
            else:
                print("❌ 路由优化: 性能未达标")
        
        if 'cache' in self.results:
            total_optimizations += 1
            if self.results['cache']['effective']:
                print("✅ 缓存优化: 缓存机制有效")
                optimizations_successful += 1
            else:
                print("❌ 缓存优化: 缓存效果不明显")
        
        if 'concurrent' in self.results:
            total_optimizations += 1
            if self.results['concurrent']['performance_good']:
                print("✅ 并发优化: 并发性能良好")
                optimizations_successful += 1
            else:
                print("❌ 并发优化: 并发性能需改进")
        
        print(f"\n🎯 优化成功率: {optimizations_successful}/{total_optimizations} ({optimizations_successful/total_optimizations*100:.0f}%)")
        
        print("\n📈 关键性能指标:")
        if 'startup' in self.results:
            print(f"  • 启动时间: {self.results['startup']['time_ms']:.2f}ms")
        if 'routing' in self.results:
            print(f"  • 单次路由: {self.results['routing']['single_time_ms']:.3f}ms")
            print(f"  • 路由吞吐量: {self.results['routing']['throughput_per_second']:.0f} msg/s")
        if 'cache' in self.results:
            print(f"  • 缓存命中率: {self.results['cache']['hit_rate']:.1f}%")
        if 'concurrent' in self.results:
            print(f"  • 并发吞吐量: {self.results['concurrent']['throughput']:.0f} msg/s")
        
        return optimizations_successful >= total_optimizations * 0.75  # 75%成功率为通过
    

async def main():
    """主测试函数"""
    print("🚀 开始消息路由器优化验证测试")
    print("="*60)
    
    tester = SimpleOptimizationTest()
    
    try:
        await tester.test_startup_time()
        await tester.test_routing_performance()
        await tester.test_cache_effectiveness()
        await tester.test_concurrent_performance()
        
        success = tester.print_summary()
        
        if success:
            print("\n🎉 消息路由器优化测试通过！")
            return 0
        else:
            print("\n⚠️  消息路由器优化测试部分失败，需要进一步改进")
            return 1
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)