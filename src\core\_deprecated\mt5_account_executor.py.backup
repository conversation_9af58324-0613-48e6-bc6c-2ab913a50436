#!/usr/bin/env python3
"""
MT5账户执行器 - 严格职责分离架构
仅负责执行交易命令，不进行任何监控活动
接收消息系统的执行指令，将结果反馈给消息系统
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime

# 移除基类依赖，直接实现执行功能
from ..messaging.message_types import TradeSignal, OrderTypeEnum, TradeAction
from ..utils.logger import get_logger
from ..messaging.hybrid_queue_manager import HybridQueueManager
from ..messaging.priority_queue import PriorityMessageQueue, MessagePriority, PriorityAnalyzer
from ..utils.batch_processor import get_trade_batch_processor

# 明确导入RPC客户端（用于架构验证）
try:
    from .mt5_rpc_client import MT5RPCClient
except ImportError:
    pass  # 运行时导入即可

logger = get_logger(__name__)


@dataclass
class ExecutionResult:
    """执行结果"""
    command_id: str
    account_id: str
    success: bool
    result_data: Dict[str, Any]
    timestamp: float
    error_message: Optional[str] = None


@dataclass
class ExecutionCommand:
    """执行命令"""
    command_id: str
    command_type: str  # open, close, modify
    account_id: str
    symbol: str
    volume: float
    order_type: OrderTypeEnum
    price: Optional[float] = None
    sl: Optional[float] = None
    tp: Optional[float] = None
    position_id: Optional[int] = None
    metadata: Dict[str, Any] = None


class MT5AccountExecutor:
    """
    MT5账户执行器 - 严格职责分离实现
    
    职责：
    执行开仓命令
    执行平仓命令  
    执行修改命令
    反馈执行结果
    
    职责边界：
    不监控任何数据
    不主动生成信号
    不处理跟单逻辑
    只响应执行命令
    """
    
    def __init__(self, account_id: str, account_config: Dict[str, Any],
                 queue_manager: HybridQueueManager,
                 rpc_client):
        # 基础属性
        self.account_config = account_config
        self.running = False
        self.start_time = None
        self.account_id = account_id
        self.queue_manager = queue_manager

        # 只使用RPC架构 - 移除双重模式
        self.rpc_client = rpc_client
        if not self.rpc_client:
            raise RuntimeError(f"执行器必须使用RPC架构: {self.account_id}")
        
        logger.info(f"执行器使用统一RPC架构: {self.account_id}")
        
        # 执行状态
        self.pending_commands: Dict[str, ExecutionCommand] = {}
        
        # 优先级队列配置 - 使用各优先级的队列大小（支持5级优先级）
        queue_sizes = {
            MessagePriority.CRITICAL: account_config.get('critical_queue_size', 100),
            MessagePriority.HIGH: account_config.get('high_queue_size', 200),
            MessagePriority.NORMAL: account_config.get('normal_queue_size', 300),
            MessagePriority.REALTIME_QUERY: account_config.get('realtime_query_queue_size', 250),
            MessagePriority.LOW: account_config.get('low_queue_size', 400)
        }
        
        # 创建优先级队列替换简单队列
        self.command_queue = PriorityMessageQueue(max_sizes=queue_sizes)
        self.queue_size = sum(queue_sizes.values())  # 总队列大小
        
        # 初始化交易批处理器
        self.trade_batch_processor = get_trade_batch_processor()
        self.trade_batch_processor.set_trade_executor(self)
        
        # 执行统计
        self.execution_stats = {
            'commands_received': 0,
            'commands_executed': 0,
            'commands_failed': 0,
            'execution_errors': 0,
            'last_execution_time': 0,
            'queue_full_count': 0,
            'queue_max_size': 0,
            'queue_current_size': 0
        }
        
        # 信号主题 - 使用配置驱动的主题模式
        try:
            from .config_manager import get_stream_config_manager
            stream_config_manager = get_stream_config_manager()
            
            # 使用配置的主题模式
            self.signal_topic_pattern = stream_config_manager.get_subject_pattern(
                'global_signals', priority='*', master_account=self.account_id
            )
            self.result_topic = stream_config_manager.get_subject_pattern(
                'control', host_id='*', command_type=f'RESULT.{self.account_id}'
            )
            
            # 如果配置获取失败，使用默认模式
            if not self.signal_topic_pattern:
                self.signal_topic_pattern = f"MT5.SIGNALS.*.{self.account_id}"
            if not self.result_topic:
                self.result_topic = f"MT5.CONTROL.RESULT.{self.account_id}"
                
            logger.debug(f"执行器主题配置: 信号={self.signal_topic_pattern}, 结果={self.result_topic}")
            
        except Exception as e:
            logger.warning(f"获取主题配置失败，使用默认: {e}")
            self.signal_topic_pattern = f"MT5.SIGNALS.*.{self.account_id}"
            self.result_topic = f"MT5.CONTROL.RESULT.{self.account_id}"

        # 从统一监控配置获取执行器配置
        try:
            from .config_manager import get_config_manager
            config_mgr = get_config_manager()

            # 获取执行器相关配置
            self.execution_timeout = config_mgr.get('monitoring.execution_timeout', 30.0)
            self.retry_attempts = config_mgr.get('monitoring.retry_attempts', 3)
            self.sleep_interval = config_mgr.get('monitoring.sleep_interval', 0.2)

            logger.info(f"执行器配置加载: {self.account_id} - 超时:{self.execution_timeout}s, 重试:{self.retry_attempts}次, 睡眠:{self.sleep_interval}s")

        except Exception as e:
            logger.warning(f"执行器配置加载失败，使用默认值: {e}")
            self.execution_timeout = 30.0
            self.retry_attempts = 3
            self.sleep_interval = 0.2

        logger.info(f"MT5账户执行器初始化: {account_id} - 纯执行模式 (优先级队列 + 批处理)")
    
    def _analyze_command_priority(self, command) -> MessagePriority:
        """分析命令优先级 - 统一使用PriorityAnalyzer"""
        try:
            # 构建参数字典供PriorityAnalyzer使用
            params = {
                'action': command.command_type,
                'signal_type': command.command_type,
                'type': getattr(command, 'order_type', None),
                'sl': getattr(command, 'sl', 0),
                'tp': getattr(command, 'tp', 0),
                'symbol': getattr(command, 'symbol', ''),
                'volume': getattr(command, 'volume', 0),
                'metadata': getattr(command, 'metadata', {}),
                'urgent': getattr(command, 'urgent', False),
                'command_id': getattr(command, 'command_id', '')
            }
            
            # 使用统一的优先级分析器（现在支持消息级别的逻辑）
            priority = PriorityAnalyzer.get_command_priority('send_order', params)
            
            logger.debug(f"命令优先级分析: {command.command_type} -> {priority.name}")
            return priority
            
        except Exception as e:
            logger.warning(f"优先级分析失败，使用NORMAL优先级: {e}")
            return MessagePriority.NORMAL
    
    async def execute_trade(self, trade_command) -> Dict[str, Any]:
        """执行交易 - 批处理接口实现"""
        try:
            result = await self._execute_command(trade_command)
            return {
                'success': True,
                'command_id': trade_command.command_id,
                'result': result
            }
        except Exception as e:
            logger.error(f"批处理执行交易失败: {e}")
            return {
                'success': False,
                'command_id': getattr(trade_command, 'command_id', 'unknown'),
                'error': str(e)
            }

    def _parse_order_type(self, order_type_value):
        """解析订单类型"""
        try:
            # 如果已经是OrderTypeEnum实例，直接返回
            if isinstance(order_type_value, OrderTypeEnum):
                return order_type_value

            # 如果是字符串，尝试转换
            if isinstance(order_type_value, str):
                # 标准化字符串格式
                order_type_str = order_type_value.upper().strip()

                # 映射常见的订单类型
                type_mapping = {
                    'BUY': OrderTypeEnum.BUY,
                    'SELL': OrderTypeEnum.SELL,
                    'BUY_LIMIT': OrderTypeEnum.BUY_LIMIT,
                    'SELL_LIMIT': OrderTypeEnum.SELL_LIMIT,
                    'BUY_STOP': OrderTypeEnum.BUY_STOP,
                    'SELL_STOP': OrderTypeEnum.SELL_STOP,
                    'BUY_STOP_LIMIT': OrderTypeEnum.BUY_STOP_LIMIT,
                    'SELL_STOP_LIMIT': OrderTypeEnum.SELL_STOP_LIMIT,
                    # 兼容数字类型
                    '0': OrderTypeEnum.BUY,
                    '1': OrderTypeEnum.SELL,
                }

                if order_type_str in type_mapping:
                    return type_mapping[order_type_str]
                else:
                    return OrderTypeEnum(order_type_str)

            if isinstance(order_type_value, (int, float)):
                if order_type_value == 0:
                    return OrderTypeEnum.BUY
                elif order_type_value == 1:
                    return OrderTypeEnum.SELL
                else:
                    logger.warning(f"未知的数字订单类型: {order_type_value}, 默认使用BUY")
                    return OrderTypeEnum.BUY

            logger.warning(f"无法解析订单类型: {order_type_value}, 默认使用BUY")
            return OrderTypeEnum.BUY

        except Exception as e:
            logger.error(f"解析订单类型失败: {order_type_value}, 错误: {e}")
            return OrderTypeEnum.BUY

    async def start(self):
        """启动执行器 - 实现启动接口"""
        return await self.start_executor()
    
    async def _ensure_execution_ready(self) -> bool:
        """确保执行器就绪状态 - 只使用RPC架构"""
        # 使用RPC验证连接健康
        try:
            health_result = await self.rpc_client.health_check(self.account_id)
            
            if health_result.get('status') == 'success':
                logger.info(f"执行器RPC连接验证成功: {self.account_id}")
                return True
            else:
                logger.warning(f"RPC连接状态异常，但允许启动: {self.account_id}")
                return True  # 允许启动，在循环中重试
        except Exception as e:
            logger.warning(f"RPC连接验证异常，但允许启动: {self.account_id} - {e}")
            return True  # 允许启动，在循环中重试
    
    async def start_executor(self) -> bool:
        """启动执行器 - 仅执行，不监控"""
        if self.running:
            logger.warning(f"执行器已在运行: {self.account_id}")
            return False
        
        try:
            if not await self._ensure_execution_ready():
                logger.error(f"执行器未就绪: {self.account_id}")
                return False
            
            self.running = True
            self.start_time = time.time()
            
            # 订阅优先级信号主题 - 使用通配符获取所有优先级
            await self.command_subscriber.subscribe(
                subject=self.signal_topic_pattern,
                callback=self._handle_priority_signal
            )
            
            asyncio.create_task(self._execution_loop())
            asyncio.create_task(self._queue_monitor_loop())
            
            await self._publish_execution_result("executor_started", self.account_id, True, {
                "account_id": self.account_id,
                "start_time": self.start_time
            })
            
            logger.info(f"MT5账户执行器已启动: {self.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动执行器失败: {e}")
            self.running = False
            return False
    
    async def stop_executor(self):
        """停止执行器"""
        if not self.running:
            return
        
        self.running = False
        
        while not self.command_queue.is_empty():
            try:
                priority_msg = await self.command_queue.dequeue(timeout=1.0)
                if priority_msg:
                    await self._execute_command(priority_msg.data)
            except asyncio.TimeoutError:
                break
        
        await self._publish_execution_result("executor_stopped", self.account_id, True, {
            "account_id": self.account_id,
            "stop_time": time.time(),
            "stats": self.execution_stats
        })
        
        logger.info(f"MT5账户执行器已停止: {self.account_id}")
    
    async def _handle_priority_signal(self, msg):
        """处理优先级信号消息 - 现代架构入口"""
        try:
            # 从主题中提取优先级
            subject = getattr(msg, 'subject', None)
            priority = MessagePriority.NORMAL  # 默认优先级
            
            if subject:
                # 从配置的信号主题模式中提取优先级
                parts = subject.split('.')
                if len(parts) >= 3:
                    priority_str = parts[2]  # CRITICAL, HIGH, NORMAL, LOW
                    try:
                        priority = MessagePriority[priority_str]
                        logger.debug(f"提取优先级: {priority_str} -> {priority}")
                    except KeyError:
                        logger.warning(f"未知优先级: {priority_str}，使用默认NORMAL")
            
            # 委托给原有的执行命令处理器，但加入优先级信息
            await self._handle_execution_command(msg, priority)
            
        except Exception as e:
            logger.error(f"处理优先级信号失败: {e}")
            await msg.nak()
    
    async def _handle_execution_command(self, msg, priority: MessagePriority = MessagePriority.NORMAL):
        """处理执行命令消息"""
        try:
            command_data = msg.data if isinstance(msg.data, dict) else msg.data.decode('utf-8')
            
            # 解析执行命令
            command = ExecutionCommand(
                command_id=command_data.get('command_id', ''),
                command_type=command_data.get('command_type', ''),
                account_id=command_data.get('account_id', ''),
                symbol=command_data.get('symbol', ''),
                volume=float(command_data.get('volume', 0)),
                order_type=self._parse_order_type(command_data.get('order_type', 'BUY')),
                price=command_data.get('price'),
                sl=command_data.get('sl'),
                tp=command_data.get('tp'),
                position_id=command_data.get('position_id'),
                metadata=command_data.get('metadata', {})
            )
            
            # 验证命令
            if command.account_id != self.account_id:
                logger.warning(f"命令账户不匹配: {command.account_id} != {self.account_id}")
                await msg.nak()
                return
            
            # 使用从消息主题提取的优先级（现代架构）
            # 如果是传统命令，则使用分析器判断
            if priority == MessagePriority.NORMAL and hasattr(self, '_analyze_command_priority'):
                analyzed_priority = self._analyze_command_priority(command)
                logger.debug(f"优先级对比: 主题={priority.name}, 分析={analyzed_priority.name}")
                priority = analyzed_priority  # 使用分析结果作为后备
            
            logger.info(f"处理优先级命令: {command.command_type} ({priority.name}) -> {command.account_id}")
            
            # 尝试添加到优先级队列（使用从主题提取的优先级）
            success = await self.command_queue.enqueue(
                priority=priority,
                data=command,
                message_id=f"{self.account_id}_{command.command_id}",
                callback=None
            )
            
            if success:
                self.execution_stats['commands_received'] += 1
                await msg.ack()
                
                queue_stats = self.command_queue.get_stats()
                logger.debug(f"接收执行命令: {command.command_type} - {command.symbol} "
                           f"(优先级: {priority.name}, 队列总数: {queue_stats['heap_size']})")
            else:
                logger.error(f"优先级队列已满，拒绝命令: {command.command_type} - {command.symbol} (优先级: {priority.name})")
                await msg.nak()
                self.execution_stats['commands_failed'] += 1
                
        except Exception as e:
            logger.error(f"处理执行命令失败: {e}")
            await msg.nak()
    
    async def _execution_loop(self):
        """执行主循环 - 纯执行逻辑"""
        logger.info(f"执行循环启动: {self.account_id}")
        
        while self.running:
            try:
                # 从优先级队列获取最高优先级的命令
                priority_msg = await self.command_queue.dequeue(timeout=1.0)
                
                if priority_msg is None:
                    continue  # 队列为空，继续等待
                
                command = priority_msg.data
                logger.debug(f"执行优先级命令: {command.command_type} (优先级: {priority_msg.priority.name})")
                
                # 使用批处理器处理命令
                await self.trade_batch_processor.process_item(command)
                
            except Exception as e:
                logger.error(f"执行循环异常: {e}")
                self.execution_stats['execution_errors'] += 1
                await asyncio.sleep(1)

    async def _queue_monitor_loop(self):
        """队列监控循环"""
        logger.info(f"队列监控启动: {self.account_id}")

        while self.running:
            try:
                # 获取优先级队列统计信息
                queue_stats = self.command_queue.get_stats()
                current_size = queue_stats['heap_size']
                
                self.execution_stats['queue_current_size'] = current_size
                self.execution_stats['queue_max_size'] = max(
                    self.execution_stats.get('queue_max_size', 0),
                    current_size
                )
                
                # 记录各优先级队列的大小
                self.execution_stats['priority_queue_sizes'] = queue_stats['queue_sizes']

                # 队列使用率警告
                usage_rate = current_size / self.queue_size if self.queue_size > 0 else 0
                if usage_rate > 0.8:
                    logger.warning(f"优先级队列使用率高: {current_size}/{self.queue_size} ({usage_rate:.1%})")
                    logger.debug(f"各优先级队列详情: {queue_stats['queue_sizes']}")
                elif usage_rate > 0.5:
                    logger.info(f"执行队列使用率: {current_size}/{self.queue_size} ({usage_rate:.1%})")

                await asyncio.sleep(10)  # 每10秒检查一次

            except Exception as e:
                logger.error(f"队列监控异常: {e}")
                await asyncio.sleep(5)

    async def _execute_command(self, command: ExecutionCommand):
        """执行单个命令"""
        start_time = time.time()
        
        try:
            logger.info(f"执行命令: {command.command_type} {command.symbol} {command.volume}")
            
            if command.command_type == "open":
                result = await self._execute_open_position(command)
            elif command.command_type == "close":
                result = await self._execute_close_position(command)
            elif command.command_type == "modify":
                result = await self._execute_modify_position(command)
            else:
                result = ExecutionResult(
                    command_id=command.command_id,
                    account_id=command.account_id,
                    success=False,
                    result_data={},
                    timestamp=time.time(),
                    error_message=f"未知命令类型: {command.command_type}"
                )
            
            await self._publish_execution_result(
                command.command_type, 
                command.command_id, 
                result.success, 
                result.result_data,
                result.error_message
            )
            
            if result.success:
                self.execution_stats['commands_executed'] += 1
            else:
                self.execution_stats['commands_failed'] += 1
            
            self.execution_stats['last_execution_time'] = time.time()
            
            execution_time = time.time() - start_time
            logger.debug(f"命令执行完成: {command.command_id} - 耗时: {execution_time:.3f}s")
            
        except Exception as e:
            logger.error(f"执行命令异常: {e}")
            self.execution_stats['commands_failed'] += 1
            
            await self._publish_execution_result(
                command.command_type,
                command.command_id,
                False,
                {},
                str(e)
            )
    
    async def _execute_open_position(self, command: ExecutionCommand) -> ExecutionResult:
        """执行开仓"""
        try:
            execution_volume = await self._calculate_execution_volume(
                command.volume, 
                command.symbol
            )
            
            execution_price = await self._get_execution_price(
                command.symbol, 
                command.order_type.value
            )
            
            # 只使用RPC架构执行交易
            order_type = 0 if command.order_type.value == 'BUY' else 1  # MT5 order types

            # 增强的MT5 API调用，支持错误验证和重试
            trade_result_dict = await self._execute_mt5_operation_with_validation(
                'send_order',
                account_id=self.account_id,
                symbol=command.symbol,
                order_type=order_type,
                volume=execution_volume,
                price=execution_price,
                sl=command.sl or 0.0,
                tp=command.tp or 0.0,
                comment=f"Execute_{command.command_id}"
            )

            # 验证和解析结果
            validation_result = await self._validate_trade_result(trade_result_dict, 'open_position')
            
            if validation_result['is_valid']:
                trade_result = type('TradeResult', (), {
                    'retcode': trade_result_dict.get('retcode', 10009),
                    'order': trade_result_dict.get('order', 0),
                    'deal': trade_result_dict.get('deal', 0),
                    'price': trade_result_dict.get('price', execution_price),
                    'comment': trade_result_dict.get('comment', '')
                })()
                
                success = True
                error_message = None
            else:
                trade_result = type('TradeResult', (), {
                    'retcode': validation_result.get('error_code', 10020),
                    'order': 0,
                    'deal': 0,
                    'price': 0.0,
                    'comment': validation_result['error_message']
                })()
                
                success = False
                error_message = validation_result['error_message']
                
                # 记录错误到统计
                self.execution_stats['execution_errors'] += 1
            
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=success,
                result_data={
                    'ticket': trade_result.order,
                    'deal': trade_result.deal,
                    'volume': execution_volume,
                    'price': trade_result.price or execution_price,
                    'symbol': command.symbol,
                    'retcode': trade_result.retcode,
                    'validation_risk': validation_result.get('risk_level', 'unknown')
                },
                timestamp=time.time(),
                error_message=error_message
            )
            
        except Exception as e:
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=False,
                result_data={},
                timestamp=time.time(),
                error_message=str(e)
            )
    
    async def _execute_close_position(self, command: ExecutionCommand) -> ExecutionResult:
        """执行平仓 - 只使用RPC架构"""
        try:
            # 只使用RPC架构执行平仓
            close_result_dict = await self.rpc_client.close_position(
                account_id=self.account_id,
                position_id=command.position_id,
                volume=command.volume
            )

            if close_result_dict.get("status") == "success":
                trade_result = type('TradeResult', (), {
                    'retcode': close_result_dict.get('retcode', 10009),
                    'deal': close_result_dict.get('deal', 0),
                    'comment': close_result_dict.get('comment', '')
                })()
            else:
                trade_result = type('TradeResult', (), {
                    'retcode': 10020,  # TRADE_RETCODE_ERROR
                    'deal': 0,
                    'comment': close_result_dict.get('error', 'RPC close failed')
                })()
            
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=trade_result.retcode == 10009,  # TRADE_RETCODE_DONE
                result_data={
                    'position_id': command.position_id,
                    'volume': command.volume,
                    'deal': trade_result.deal,
                    'retcode': trade_result.retcode
                },
                timestamp=time.time(),
                error_message=trade_result.comment if trade_result.retcode != 10009 else None
            )
            
        except Exception as e:
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=False,
                result_data={},
                timestamp=time.time(),
                error_message=str(e)
            )
    
    async def _execute_modify_position(self, command: ExecutionCommand) -> ExecutionResult:
        """执行修改持仓 - 只使用RPC架构"""
        try:
            # 只使用RPC架构执行修改持仓
            modify_result_dict = await self.rpc_client.modify_position(
                account_id=self.account_id,
                position_id=command.position_id,
                sl=command.sl or 0.0,
                tp=command.tp or 0.0,
                comment=f"Modify_{command.command_id}"
            )

            if modify_result_dict.get("status") == "success":
                trade_result = type('TradeResult', (), {
                    'retcode': modify_result_dict.get('retcode', 10009),
                    'comment': modify_result_dict.get('comment', '')
                })()
            else:
                trade_result = type('TradeResult', (), {
                    'retcode': 10020,  # TRADE_RETCODE_ERROR
                    'comment': modify_result_dict.get('error', 'RPC modify failed')
                })()
            
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=trade_result.retcode == 10009,  # TRADE_RETCODE_DONE
                result_data={
                    'position_id': command.position_id,
                    'sl': command.sl,
                    'tp': command.tp,
                    'retcode': trade_result.retcode
                },
                timestamp=time.time(),
                error_message=trade_result.comment if trade_result.retcode != 10009 else None
            )
            
        except Exception as e:
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=False,
                result_data={},
                timestamp=time.time(),
                error_message=str(e)
            )
    
    async def _calculate_execution_volume(self, signal_volume: float, symbol: str) -> float:
        """计算执行手数 - 基于风控规则"""
        try:
            # 简单实现：直接使用信号手数
            # 实际生产中这里应该有复杂的风控逻辑
            return signal_volume
        except Exception as e:
            logger.error(f"计算执行手数失败: {e}")
            return 0.0
    
    async def _get_execution_price(self, symbol: str, order_type: str) -> Optional[float]:
        """获取执行价格 - 基于市价或配置，只使用RPC架构"""
        try:
            # 对于市价单，返回None让MT5自动处理
            if order_type.upper() in ['BUY', 'SELL']:
                return None
            
            # 对于限价单，通过RPC获取当前市价
            try:
                price_result = await self.rpc_client.get_symbol_price(self.account_id, symbol)
                if price_result.get("status") == "success":
                    price_data = price_result.get("price", {})
                    # 根据订单类型返回买价或卖价
                    if order_type.upper() in ['BUY_LIMIT', 'BUY_STOP']:
                        return price_data.get('bid', None)
                    elif order_type.upper() in ['SELL_LIMIT', 'SELL_STOP']:
                        return price_data.get('ask', None)
            except Exception as rpc_error:
                logger.warning(f"RPC获取价格失败: {rpc_error}")
            
            return None
        except Exception as e:
            logger.error(f"获取执行价格失败: {e}")
            return None
    
    async def _publish_execution_result(self, command_type: str, command_id: str, 
                                      success: bool, data: Dict[str, Any], 
                                      error_message: Optional[str] = None):
        """发布执行结果"""
        try:
            result = {
                'command_type': command_type,
                'command_id': command_id,
                'account_id': self.account_id,
                'success': success,
                'data': data,
                'timestamp': time.time(),
                'error_message': error_message
            }
            
            await self.result_publisher.publish(
                subject=self.result_topic,
                data=result
            )
            
            logger.debug(f"发布执行结果: {command_type} - {success}")
            
        except Exception as e:
            logger.error(f"发布执行结果失败: {e}")
    
    async def _execute_mt5_operation_with_validation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """执行MT5操作并进行验证，支持错误处理和重试"""
        try:
            # 执行RPC操作
            if hasattr(self.rpc_client, operation):
                rpc_method = getattr(self.rpc_client, operation)
                result = await rpc_method(**kwargs)
            else:
                result = await self.rpc_client.call(operation, **kwargs)
            
            # 返回原始结果供进一步验证
            return result
            
        except Exception as e:
            logger.error(f"MT5操作执行异常: {operation}, 错误: {e}")
            return {
                'status': 'error',
                'error_code': -1,
                'error': f"执行异常: {str(e)}",
                'operation': operation
            }
    
    async def _validate_trade_result(self, result: Dict[str, Any], operation: str) -> Dict[str, Any]:
        """验证交易结果，返回验证信息"""
        try:
            # 导入原始验证器
            from .trade_validator import get_trade_validator
            validator = get_trade_validator()
            
            # 执行验证 - 使用原始验证器的API
            validation = await validator.validate_trade_result(
                result=result,
                expected_signal={'operation': operation},
                mt5_client=self.rpc_client
            )
            
            # 返回验证结果
            return {
                'is_valid': validation.is_valid,
                'error_code': validation.error_code,
                'error_message': validation.error_message,
                'should_retry': validation.can_retry,
                'retry_delay': validation.retry_delay,
                'risk_level': 'medium'  # 原始验证器没有risk_level，使用默认值
            }
            
        except Exception as e:
            logger.error(f"交易结果验证异常: {e}")
            return {
                'is_valid': False,
                'error_code': -1,
                'error_message': f"验证异常: {str(e)}",
                'should_retry': False,
                'retry_delay': 0,
                'risk_level': 'high'
            }
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        uptime = time.time() - (self.start_time or time.time())

        # 获取优先级队列统计信息
        queue_stats = self.command_queue.get_stats()
        
        # 获取验证器统计
        validator_stats = {}
        try:
            from .trade_validator import get_trade_validator
            validator = get_trade_validator()
            validator_stats = await validator.get_validation_stats()
        except Exception as e:
            logger.debug(f"获取验证器统计失败: {e}")
        
        return {
            **self.execution_stats,
            'account_id': self.account_id,
            'running': self.running,
            'uptime_seconds': uptime,
            'pending_commands': len(self.pending_commands),
            'queue_size': queue_stats['heap_size'],
            'priority_queue_stats': queue_stats,
            'signal_topic_pattern': self.signal_topic_pattern,
            'result_topic': self.result_topic,
            'rpc_enabled': self.rpc_client is not None,
            'validator_stats': validator_stats
        }

    async def execute_order_rpc(self, symbol: str, order_type: str, volume: float,
                               price: float = 0.0, sl: float = 0.0, tp: float = 0.0,
                               comment: str = "") -> Dict[str, Any]:
        """使用RPC直接执行订单"""
        if not self.rpc_client:
            return {"status": "failed", "error": "RPC client not available"}

        try:
            # 转换订单类型
            mt5_order_type = 0 if order_type.upper() == 'BUY' else 1

            result = await self.rpc_client.send_order(
                account_id=self.account_id,
                symbol=symbol,
                order_type=mt5_order_type,
                volume=volume,
                price=price,
                sl=sl,
                tp=tp,
                comment=comment
            )

            return result
        except Exception as e:
            logger.error(f"RPC订单执行失败: {self.account_id}, 错误: {e}")
            return {"status": "failed", "error": str(e)}
    
    # 重写父类方法，确保只执行
    async def execute_trade(self, signal: TradeSignal) -> bool:
        """执行交易 - 父类接口实现"""
        command = ExecutionCommand(
            command_id=f"trade_{signal.signal_id}",
            command_type=signal.action.value.lower(),
            account_id=self.account_id,
            symbol=signal.symbol,
            volume=signal.volume,
            order_type=signal.order_type,
            price=signal.price,
            sl=signal.sl,
            tp=signal.tp,
            position_id=signal.position_id
        )
        
        await self.command_queue.put(command)
        return True