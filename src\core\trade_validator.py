"""
生产级MT5交易结果验证器
提供完整的交易结果验证和错误处理机制
"""
import asyncio
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# 处理导入问题 - 支持相对导入和绝对导入
try:
    from ..utils.stats import get_stats
    from ..utils.logger import get_logger
except ImportError:
    # 如果相对导入失败，创建Mock对象
    from unittest.mock import Mock
    get_stats = Mock
    get_logger = lambda name: Mock()

logger = get_logger(__name__)


class TradeResultCode(Enum):
    """MT5交易结果代码"""
    # 成功代码
    TRADE_RETCODE_DONE = 10009              # 交易完成
    TRADE_RETCODE_DONE_PARTIAL = 10010      # 部分成交
    TRADE_RETCODE_PLACED = 10012            # 挂单已下
    TRADE_RETCODE_FILLED = 10013            # 完全成交
    
    # 可重试代码
    TRADE_RETCODE_REQUOTE = 10004           # 重新报价
    TRADE_RETCODE_REJECT = 10006            # 拒绝
    TRADE_RETCODE_CANCEL = 10007            # 取消
    TRADE_RETCODE_TIMEOUT = 10011           # 超时
    TRADE_RETCODE_PRICE_OFF = 10015         # 价格偏移
    TRADE_RETCODE_PRICE_CHANGED = 10016     # 价格改变
    
    # 失败代码
    TRADE_RETCODE_INVALID = 10013           # 无效请求
    TRADE_RETCODE_INVALID_VOLUME = 10014    # 无效手数
    TRADE_RETCODE_NO_MONEY = 10019          # 资金不足
    TRADE_RETCODE_MARKET_CLOSED = 10018     # 市场关闭
    TRADE_RETCODE_TRADE_DISABLED = 10017    # 交易禁用
    TRADE_RETCODE_INVALID_STOPS = 10026     # 无效止损/止盈
    TRADE_RETCODE_INVALID_PRICE = 10020     # 无效价格
    TRADE_RETCODE_POSITION_CLOSED = 10028   # 持仓已关闭


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    error_code: Optional[int] = None
    error_message: str = ""
    can_retry: bool = False
    retry_delay: float = 0.0
    suggested_action: str = ""
    validation_details: Dict = None


class TradeValidator:
    """交易验证器"""
    
    def __init__(self):
        self.stats = get_stats("trade_validator")
        
        # 成功代码集合
        self.success_codes = {
            TradeResultCode.TRADE_RETCODE_DONE.value,
            TradeResultCode.TRADE_RETCODE_DONE_PARTIAL.value,
            TradeResultCode.TRADE_RETCODE_PLACED.value,
            TradeResultCode.TRADE_RETCODE_FILLED.value,
        }
        
        # 可重试代码及延迟配置
        self.retry_codes = {
            TradeResultCode.TRADE_RETCODE_REQUOTE.value: (True, 1.0, "重新获取报价后重试"),
            TradeResultCode.TRADE_RETCODE_REJECT.value: (True, 2.0, "稍后重试交易"),
            TradeResultCode.TRADE_RETCODE_TIMEOUT.value: (True, 3.0, "网络超时，重新提交"),
            TradeResultCode.TRADE_RETCODE_PRICE_OFF.value: (True, 1.0, "价格偏移，重新报价"),
            TradeResultCode.TRADE_RETCODE_PRICE_CHANGED.value: (True, 0.5, "价格变动，立即重试"),
        }
        
        # 失败代码及处理建议
        self.failure_codes = {
            TradeResultCode.TRADE_RETCODE_NO_MONEY.value: "资金不足，检查账户余额",
            TradeResultCode.TRADE_RETCODE_MARKET_CLOSED.value: "市场关闭，等待开市",
            TradeResultCode.TRADE_RETCODE_TRADE_DISABLED.value: "交易被禁用，检查账户设置",
            TradeResultCode.TRADE_RETCODE_INVALID_VOLUME.value: "无效手数，调整交易量",
            TradeResultCode.TRADE_RETCODE_INVALID_STOPS.value: "无效止损止盈，检查设置",
            TradeResultCode.TRADE_RETCODE_INVALID_PRICE.value: "无效价格，获取当前报价",
            TradeResultCode.TRADE_RETCODE_POSITION_CLOSED.value: "持仓已关闭，无需操作",
        }
        
        logger.info("✅ MT5交易验证器初始化完成")
    
    async def validate_trade_result(self, 
                                  result: Dict,
                                  expected_signal: Dict,
                                  mt5_client = None) -> ValidationResult:
        """验证交易结果"""
        
        if not result:
            await self.stats.increment("validation_null_result")
            return ValidationResult(
                is_valid=False,
                error_message="交易结果为空",
                suggested_action="检查MT5连接和交易请求"
            )
        
        retcode = result.get('retcode', -1)
        
        # 基础返回码验证
        code_validation = await self._validate_return_code(retcode)
        if not code_validation.is_valid:
            await self.stats.increment("validation_failed_retcode")
            return code_validation
        
        # 如果是成功代码，进行详细验证
        if retcode in self.success_codes:
            detailed_validation = await self._detailed_success_validation(
                result, expected_signal, mt5_client
            )
            if detailed_validation.is_valid:
                await self.stats.increment("validation_success")
            else:
                await self.stats.increment("validation_failed_detailed")
            return detailed_validation
        
        # 如果是可重试代码
        if retcode in self.retry_codes:
            await self.stats.increment("validation_retry_needed")
            can_retry, delay, action = self.retry_codes[retcode]
            return ValidationResult(
                is_valid=False,
                error_code=retcode,
                error_message=f"交易需要重试: {action}",
                can_retry=can_retry,
                retry_delay=delay,
                suggested_action=action
            )
        
        # 未知错误代码
        await self.stats.increment("validation_unknown_code")
        return ValidationResult(
            is_valid=False,
            error_code=retcode,
            error_message=f"未知的返回代码: {retcode}",
            suggested_action="检查MT5文档或联系技术支持"
        )
    
    async def _validate_return_code(self, retcode: int) -> ValidationResult:
        """验证返回代码"""
        
        # 成功代码
        if retcode in self.success_codes:
            return ValidationResult(is_valid=True)
        
        # 失败代码
        if retcode in self.failure_codes:
            error_msg = self.failure_codes[retcode]
            return ValidationResult(
                is_valid=False,
                error_code=retcode,
                error_message=error_msg,
                can_retry=False,
                suggested_action=error_msg
            )
        
        # 可重试代码
        if retcode in self.retry_codes:
            can_retry, delay, action = self.retry_codes[retcode]
            return ValidationResult(
                is_valid=False,
                error_code=retcode,
                can_retry=can_retry,
                retry_delay=delay,
                suggested_action=action
            )
        
        # 未知代码
        return ValidationResult(
            is_valid=False,
            error_code=retcode,
            error_message=f"未知返回代码: {retcode}"
        )
    
    async def _detailed_success_validation(self,
                                         result: Dict,
                                         expected_signal: Dict,
                                         mt5_client) -> ValidationResult:
        """详细的成功验证"""
        
        validation_details = {}
        warnings = []
        
        # 1. 验证订单号是否存在
        order_id = result.get('order', 0)
        if order_id <= 0:
            return ValidationResult(
                is_valid=False,
                error_message="无效的订单号",
                suggested_action="检查交易是否真正执行"
            )
        
        validation_details['order_id'] = order_id
        
        # 2. 验证成交价格合理性
        executed_price = result.get('price', 0.0)
        expected_price = expected_signal.get('price', 0.0)
        
        if expected_price > 0 and executed_price > 0:
            price_diff = abs(executed_price - expected_price) / expected_price
            if price_diff > 0.01:  # 1%价格偏差
                warnings.append(f"成交价格偏差较大: 预期{expected_price}, 实际{executed_price}")
        
        validation_details['price_validation'] = {
            'expected_price': expected_price,
            'executed_price': executed_price,
            'price_diff_percent': price_diff * 100 if 'price_diff' in locals() else 0
        }
        
        # 3. 验证成交手数
        executed_volume = result.get('volume', 0.0)
        expected_volume = expected_signal.get('volume', 0.0)
        
        if expected_volume > 0:
            volume_diff = abs(executed_volume - expected_volume) / expected_volume
            if volume_diff > 0.05:  # 5%手数偏差
                warnings.append(f"成交手数偏差: 预期{expected_volume}, 实际{executed_volume}")
        
        validation_details['volume_validation'] = {
            'expected_volume': expected_volume,
            'executed_volume': executed_volume,
            'volume_diff_percent': volume_diff * 100 if 'volume_diff' in locals() else 0
        }
        
        # 4. 如果提供了MT5客户端，验证持仓是否真实存在
        if mt5_client and order_id > 0:
            position_exists = await self._verify_position_exists(
                mt5_client, order_id, expected_signal.get('symbol', '')
            )
            
            validation_details['position_verification'] = position_exists
            
            if not position_exists:
                return ValidationResult(
                    is_valid=False,
                    error_message="交易报告成功但持仓不存在",
                    suggested_action="检查MT5服务器同步状态",
                    validation_details=validation_details
                )
        
        # 如果有警告但验证通过
        return ValidationResult(
            is_valid=True,
            error_message="; ".join(warnings) if warnings else "",
            validation_details=validation_details
        )
    
    async def _verify_position_exists(self,
                                    mt5_client,
                                    order_id: int,
                                    symbol: str) -> bool:
        """验证持仓是否真实存在"""
        
        try:
            # 获取当前持仓
            positions = await mt5_client.get_positions()
            
            # 查找指定订单号的持仓
            for pos in positions:
                if pos.ticket == order_id and pos.symbol == symbol:
                    return True
            
            # 如果没找到，可能是瞬间开平仓，检查历史记录
            # 这里可以添加历史订单查询逻辑
            
            return False
            
        except Exception as e:
            logger.error(f"验证持仓存在时出错: {e}")
            return False  # 验证失败时保守处理
    
    async def suggest_retry_strategy(self,
                                   failed_result: Dict,
                                   retry_count: int) -> Optional[Dict]:
        """建议重试策略"""
        
        retcode = failed_result.get('retcode', -1)
        
        if retcode not in self.retry_codes:
            return None
        
        can_retry, base_delay, action = self.retry_codes[retcode]
        
        if not can_retry or retry_count >= 3:  # 最多重试3次
            return None
        
        # 指数退避延迟
        retry_delay = base_delay * (2 ** retry_count)
        
        return {
            'should_retry': True,
            'retry_delay': retry_delay,
            'retry_action': action,
            'retry_count': retry_count + 1,
            'max_retries': 3
        }
    
    async def get_validation_stats(self) -> Dict:
        """获取验证统计信息"""
        
        return await self.stats.get_all_stats()


class TradeRetryManager:
    """交易重试管理器"""
    
    def __init__(self, mt5_client, validator: TradeValidator):
        self.mt5_client = mt5_client
        self.validator = validator
        self.stats = get_stats("trade_retry")
        
        logger.info("✅ 交易重试管理器初始化完成")
    
    async def execute_with_retry(self,
                               trade_request: Dict,
                               max_retries: int = 3) -> Dict:
        """带重试的交易执行"""
        
        last_result = None
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                # 执行交易
                start_time = time.time()
                result = await self._execute_single_trade(trade_request)
                execution_time = time.time() - start_time
                
                # 验证结果
                validation = await self.validator.validate_trade_result(
                    result, trade_request, self.mt5_client
                )
                
                # 记录执行统计
                await self.stats.increment("trade_attempts")
                await self.stats.set_value("last_execution_time", execution_time)
                
                if validation.is_valid:
                    # 成功
                    await self.stats.increment("trade_success")
                    if retry_count > 0:
                        await self.stats.increment("retry_success")
                    
                    return {
                        'success': True,
                        'result': result,
                        'validation': validation,
                        'retry_count': retry_count,
                        'execution_time': execution_time
                    }
                
                # 验证失败，检查是否可以重试
                if not validation.can_retry or retry_count >= max_retries:
                    await self.stats.increment("trade_failed")
                    return {
                        'success': False,
                        'result': result,
                        'validation': validation,
                        'retry_count': retry_count,
                        'error': validation.error_message
                    }
                
                # 准备重试
                retry_count += 1
                await self.stats.increment("trade_retries")
                
                logger.info(f"交易重试 {retry_count}/{max_retries}: {validation.suggested_action}")
                
                # 等待重试延迟
                if validation.retry_delay > 0:
                    await asyncio.sleep(validation.retry_delay)
                
                # 更新交易请求（如果需要）
                trade_request = await self._update_request_for_retry(
                    trade_request, validation
                )
                
                last_result = result
                
            except Exception as e:
                logger.error(f"交易执行异常 (重试 {retry_count}/{max_retries}): {e}")
                
                if retry_count >= max_retries:
                    await self.stats.increment("trade_exceptions")
                    return {
                        'success': False,
                        'error': f"交易执行异常: {str(e)}",
                        'retry_count': retry_count
                    }
                
                retry_count += 1
                await asyncio.sleep(1.0)  # 异常后等待1秒
        
        # 重试耗尽
        await self.stats.increment("retry_exhausted")
        return {
            'success': False,
            'result': last_result,
            'retry_count': retry_count,
            'error': "重试次数耗尽"
        }
    
    async def _execute_single_trade(self, trade_request: Dict) -> Dict:
        """执行单次交易"""
        
        # 根据交易类型调用相应的MT5客户端方法
        signal_type = trade_request.get('signal_type', '')
        
        if signal_type == 'POSITION_OPEN':
            # 构建开仓请求
            from .mt5_client import TradeRequest, TradeAction, OrderType
            
            order_type = OrderType.BUY if trade_request['action'] == 'BUY' else OrderType.SELL
            
            request = TradeRequest(
                action=TradeAction.DEAL,
                symbol=trade_request['symbol'],
                volume=trade_request['volume'],
                type=order_type,
                price=trade_request.get('price'),
                sl=trade_request.get('sl'),
                tp=trade_request.get('tp'),
                magic=trade_request.get('magic', 12345),
                comment=trade_request.get('comment', '')
            )
            
            return await self.mt5_client.send_order(request)
            
        elif signal_type == 'POSITION_CLOSE':
            return await self.mt5_client.close_position(
                position_id=trade_request['position_id'],
                volume=trade_request.get('volume'),
                comment=trade_request.get('comment', '')
            )
            
        elif signal_type == 'POSITION_MODIFY':
            return await self.mt5_client.modify_position(
                position_id=trade_request['position_id'],
                sl=trade_request.get('sl'),
                tp=trade_request.get('tp')
            )
        
        else:
            raise ValueError(f"未知的信号类型: {signal_type}")
    
    async def _update_request_for_retry(self,
                                      original_request: Dict,
                                      validation: ValidationResult) -> Dict:
        """为重试更新交易请求"""
        
        updated_request = original_request.copy()
        
        # 根据错误代码调整请求
        if validation.error_code == TradeResultCode.TRADE_RETCODE_REQUOTE.value:
            # 重新报价，移除价格让MT5使用市价
            updated_request.pop('price', None)
        
        elif validation.error_code == TradeResultCode.TRADE_RETCODE_PRICE_OFF.value:
            # 价格偏移，增加滑点容忍度
            updated_request['deviation'] = updated_request.get('deviation', 20) + 10
        
        return updated_request


# 全局实例
_validator_instance = None
_retry_manager_instance = None


def get_trade_validator() -> TradeValidator:
    """获取交易验证器实例"""
    global _validator_instance
    if _validator_instance is None:
        _validator_instance = TradeValidator()
    return _validator_instance




def get_retry_manager(mt5_client) -> TradeRetryManager:
    """获取重试管理器实例"""
    global _retry_manager_instance
    if _retry_manager_instance is None:
        _retry_manager_instance = TradeRetryManager(mt5_client, get_trade_validator())
    return _retry_manager_instance


# 为TradeValidator添加缺失的方法
def _add_missing_methods():
    """为TradeValidator类添加缺失的方法"""

    async def validate_trade_request(self, request: dict) -> 'ValidationResult':
        """验证交易请求"""
        try:
            # 基本验证
            if not request.get('symbol'):
                return ValidationResult(False, "缺少交易品种")

            volume = request.get('volume', 0)
            if volume <= 0:
                return ValidationResult(False, "交易手数必须大于0")

            order_type = request.get('order_type', '')
            if not order_type:
                return ValidationResult(False, "缺少订单类型")

            # 验证订单类型
            valid_types = ['MARKET_BUY', 'MARKET_SELL', 'LIMIT_BUY', 'LIMIT_SELL']
            if order_type not in valid_types:
                return ValidationResult(False, f"无效的订单类型: {order_type}")

            return ValidationResult(True, "验证通过")

        except Exception as e:
            return ValidationResult(False, f"验证异常: {str(e)}")

    # 将方法添加到TradeValidator类
    TradeValidator.validate_trade_request = validate_trade_request


class ValidationResult:
    """验证结果类"""
    def __init__(self, is_valid: bool, message: str = ""):
        self.is_valid = is_valid
        self.message = message

    def __bool__(self):
        return self.is_valid


# 执行方法添加
_add_missing_methods()